/**
 * 
 */
var modal = {
		
		id: "",
		
		layout : '\
<div class="modal-wrap" id="::ID::">\
	<div class="modal">\
		<button type="button" class="btnPopupClose"><span class="hidden">팝업 닫기</span></button>\
		::CONTENTS::\
		<input id="data" type="hidden" value="::DATA::\" />\
	</div>\
</div>',

		showModal: function(id, contents, data) {
			
			modal.Id = id;
			var html = modal.layout
								.replace("::ID::", id)
								.replace("::CONTENTS::", contents)
								.replace("::DATA::", encodeURI(data));
			
			$("body").append(html);

			modal.eventClose();
		},
		
		eventClose: function() {
			
			$("#" + modal.Id + " .btnPopupClose").off("click");
			$("#" + modal.Id + " .btnPopupClose").click(function() {
				$("#" + modal.Id).remove();
			});
		}

}