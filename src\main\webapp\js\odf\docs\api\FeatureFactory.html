<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>GeOnPaas ui widgets: FeatureFactory</title>
    
      <link type="text/css" rel="stylesheet" href="styles/vendor/prism-tomorrow-night.css">
    
    <link type="text/css" rel="stylesheet" href="styles/styles.css">
    
    
    <style>
      :root {
      
      
        --nav-width: 370px;
      
      
        --nav-heading-margin-top: 0.5em;
      
      }
    </style>
    
</head>
<body>

<header class="layout-header">
  
  <h1>
    <a href="./index.html">
      GeOnPaas ui widgets
    </a>
  </h1>
  <nav class="layout-nav">
    <ul><li class="nav-heading">Classes</li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="BasemapControl.html">BasemapControl</a></span><span class="nav-desc"><p>배경지도 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="BookmarkControl.html">BookmarkControl</a></span><span class="nav-desc"><p>북마크 컨트롤 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ClearControl.html">ClearControl</a></span><span class="nav-desc"><p>지도 그리기 이벤트 초기화 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ColorFactory.html">ColorFactory</a></span><span class="nav-desc"><p>색 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Control.html">Control</a></span><span class="nav-desc"><p>사용자 정의 컨트롤 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Coordinate.html">Coordinate</a></span><span class="nav-desc"><p>좌표 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DivideMapControl.html">DivideMapControl</a></span><span class="nav-desc"><p>지도 분할 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DownloadControl.html">DownloadControl</a></span><span class="nav-desc"><p>다운로드 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DrawControl.html">DrawControl</a></span><span class="nav-desc"><p>그리기 도구 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Easing.html">Easing</a></span><span class="nav-desc"><p>애니메이션 효과</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="event.html">event</a></span><span class="nav-desc"><p>이벤트 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Extent.html">Extent</a></span><span class="nav-desc"><p>영역 관련 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Feature.html">Feature</a></span><span class="nav-desc"><p>Feature 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FeatureFactory.html">FeatureFactory</a></span><span class="nav-desc"><p>Feature 생성을 위한 FeatureFactory 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FormatFactory.html">FormatFactory</a></span><span class="nav-desc"></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FullScreenControl.html">FullScreenControl</a></span><span class="nav-desc"><p>전체화면 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="HomeControl.html">HomeControl</a></span><span class="nav-desc"><p>홈 이동 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Layer.html">Layer</a></span><span class="nav-desc"><p>레이어 관리 클래스로, 레이어는 odf.LayerFactory를 통해서만 생성가능하다.</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LayerFactory.html">LayerFactory</a></span><span class="nav-desc"><p>레이어 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LayerInfoControl.html">LayerInfoControl</a></span><span class="nav-desc"><p>레이어 정보 조회 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Map.html">Map</a></span><span class="nav-desc"><p>지도 생성, 조작, 컴퍼넌트, 레이어 추가 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Marker.html">Marker</a></span><span class="nav-desc"><p>마커 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MeasureControl.html">MeasureControl</a></span><span class="nav-desc"><p>지도 측정 도구 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MousePositionControl.html">MousePositionControl</a></span><span class="nav-desc"><p>마우스 좌표 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MoveControl.html">MoveControl</a></span><span class="nav-desc"><p>현재 화면 기준으로 이전/다음 화면 이동 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="OverviewMapControl.html">OverviewMapControl</a></span><span class="nav-desc"><p>인덱스맵 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Popup.html">Popup</a></span><span class="nav-desc"><p>팝업 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="PrintControl.html">PrintControl</a></span><span class="nav-desc"><p>프린트 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Projection.html">Projection</a></span><span class="nav-desc"><p>좌표 변환 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="RotationControl.html">RotationControl</a></span><span class="nav-desc"><p>화면을 회전 시키는 기능
alt + shift 드래그로 지도 회전</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ScaleControl.html">ScaleControl</a></span><span class="nav-desc"><p>축척 표시 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="SLD.html">SLD</a></span><span class="nav-desc"><p>WMS 스타일 관리 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Style.html">Style</a></span><span class="nav-desc"><p>스타일 관리 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="StyleFactory.html">StyleFactory</a></span><span class="nav-desc"><p>스타일 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="StyleFunction.html">StyleFunction</a></span><span class="nav-desc"><p>스타일 Function 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="SwiperControl.html">SwiperControl</a></span><span class="nav-desc"><p>지도 스와이퍼 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ZipControl.html">ZipControl</a></span><span class="nav-desc"><p>Server없이 Layer 생성하는 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ZoomControl.html">ZoomControl</a></span><span class="nav-desc"><p>지도 줌 설정클래스</p></span></li></ul><li class="nav-heading"><a href="global.html">Globals</a></li>
  </nav>
</header>


<main class="layout-main ">
  <div class="container">
    <p class="page-kind">Class</p>
    <h1 class="page-title">FeatureFactory</h1>
    




<section>


<header class="class">


    
        
        <!-- <h2>FeatureFactory</h2> -->

        

        

        
            
            <div class="class-description"><p>Feature 생성을 위한 FeatureFactory 클래스</p></div>
        
    
</header>

<article>
    <div class="container-overview">



    
        





    


    
    <h3 class="subtitle">Constructor</h3>
    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="FeatureFactory">new FeatureFactory<span class="signature">()</span><span class="return-type-signature"></span>
    </h4>

















<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    
    </div>

    

    

     

    

    


    

    
        <h3 class="subtitle">Methods</h3>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".fromGeoJson">fromGeoJson<span class="signature">(GeoJson)</span><span class="return-type-signature"> &rarr; {<a href="Feature.html">Feature</a>}</span>
    </h4>





<div class="method-description">
    
    <p>GeoJson을 이용한 Feature 생성</p>
<pre class="prettyprint source lang-javascript"><code>let feature = odf.FeatureFactory.fromGeoJson(geoJson);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">GeoJson</span>
            

            
                


    <span class="param-type">
        <code>JSON</code>
    </span>
    

            

            

            

            <div class="param-description"><p>Feature를 만들기 위한 GeoJson형태의 데이터</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Feature</code>
            
            
                <p>ODF_Feature 형태의 Feature</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".fromWKT">fromWKT<span class="signature">(wkt, property)</span><span class="return-type-signature"> &rarr; {<a href="Feature.html">Feature</a>}</span>
    </h4>





<div class="method-description">
    
    <p>WKT 이용하여 feature 생성</p>
<pre class="prettyprint source lang-javascript"><code>let feature = odf.FeatureFactory.fromWKT('MULTIPOLYGON(((948365.1609 1934203.3315,948359.501…8484.119 1934281.0984,948365.1609 1934203.3315)))', property);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">wkt</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            
                
            

            <div class="param-description"><p>WKT를 이용하여 Feature를 만들기 위한 String 형태의 WKT 데이터</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">property</span>
            

            
                


    <span class="param-type">
        <code>JSON</code>
    </span>
    

            

            

            
                
                    <span class="param-default">
                        null
                    </span>
                
            

            <div class="param-description"><p>JSON 형태의 속성값</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Feature</code>
            
            
                <p>ODF_Feature 형태의 Feature</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".produce">produce<span class="signature">(param)</span><span class="return-type-signature"> &rarr; {<a href="Feature.html">Feature</a>}</span>
    </h4>





<div class="method-description">
    
    <p>FeatureFactory를 이용한 Feature 생성</p>
<pre class="prettyprint source lang-javascript"><code>//점 feature 생성
let pointFeature = odf.FeatureFactory.produce({
  geometryType: 'point',
       coordinates: [1099747.3552171164, 1715412.1323066188],
});
</code></pre>
<pre class="prettyprint source lang-javascript"><code>//multiPoint feature 생성
let mPointFeature = odf.FeatureFactory.produce({
  geometryType: 'multipoint',
       coordinates: [
         [1101336.4199975003,1718354.3477830617],
         [1101355.5078627302,1718010.7662089246],
         [1101031.0141538228,1718182.556995993],
         [1101040.5580864379,1717838.975421856],
      ],
});
</code></pre>
<pre class="prettyprint source lang-javascript"><code>//선 feature 생성
let lineFeature = odf.FeatureFactory.produce({
  geometryType: 'linestring',
       coordinates: [
          [1097339.898215003, 1715974.1804632691],
          [1097492.6011368418, 1715057.9629322367],
          [1099573.178446894, 1715668.7746195917],
          [1100608.6951356127, 1715038.875067007],
          [1101305.4022165018, 1715778.529844663],
        ],
});
</code></pre>
<pre class="prettyprint source lang-javascript"><code>//multiline feature 생성
let mLineFeature = odf.FeatureFactory.produce({
  geometryType: 'multilinestring',
       coordinates: [
        [
          [1097802.7789468267,1717572.789176268],
          [1097621.4442271432,1717505.9816479636],
          [1097535.5488336089,1717315.1029956653],
          [1097549.8647325314,1717071.7327139848],
          [1097821.8668120564,1716947.6615899908],
          [1098093.8688915817,1717033.5569835252],
          [1098198.8521503457,1717367.5946250472],
          [1098031.8333295847,1717477.3498501189],
          [1097745.5153511371,1717472.5778838114],
          [1097630.988159758,1717291.243164128],
          [1097645.3040586805,1717133.7682759818],
          [1097812.3228794415,1717086.048612907],
          [1097974.5697338951,1717210.119736901],
          [1097955.4818686654,1717353.278726125],
          [1097817.094845749,1717310.3310293579],
          [1097840.9546772863,1717233.9795684384]
        ]
        ,[
          [1098590.1533875575,1717768.4397948738],
          [1098337.2391732621,1717749.3519296441],
          [1098284.74754388,1717568.0172099606],
          [1098408.818667874,1717200.5758042862],
          [1098833.523669238,1717114.6804107518],
          [1099105.5257487632,1717319.874961973],
          [1099134.157546608,1717658.6845698026],
          [1098924.1910290797,1717725.492098107],
          [1098509.0299603308,1717653.9126034952],
          [1098370.6429374146,1717458.2619848894],
          [1098518.5738929457,1717281.6992315133],
          [1098809.6638377008,1717286.4711978207],
          [1099014.8583889215,1717525.0695131938],
          [1098876.4713660053,1717630.0527719578],
          [1098628.3291180173,1717534.6134458086],
          [1098614.0132190948,1717381.91052397],
          [1098709.452545244,1717381.91052397],
          [1098823.9797366231,1717510.7536142713]
        ]
      ],
});
</code></pre>
<pre class="prettyprint source lang-javascript"><code>//면 feature 생성
let polygonFeature = odf.FeatureFactory.produce({
  geometryType: 'polygon',
       coordinates: [
          [
            [1099959.7077177984, 1716260.498441717],
            [1100098.0947407146, 1716608.8519821614],
            [1100675.5026639171, 1716794.9586681523],
            [1101190.875025123, 1716561.132319087],
            [1101114.5235642034, 1716308.2181047916],
            [1100661.1867649949, 1715859.6532718902],
            [1099773.6010318075, 1715530.3875966757],
            [1099353.667996751, 1716160.2871492603],
            [1099277.3165358317, 1716584.992150624],
            [1099329.8081652136, 1717028.785017218],
            [1099730.6533350402, 1717119.4523770595],
            [1100083.7788417924, 1717024.0130509103],
            [1100145.8144037894, 1716713.8352409257],
            [1099959.7077177984, 1716260.498441717],
          ],
        ],
});
</code></pre>
<pre class="prettyprint source lang-javascript"><code>//multipolygon 생성
let mPolygonFeature = odf.FeatureFactory.produce({
        geometryType: 'multipolygon',
        coordinates: [
          [
            [
              [1102899.2389631933, 1717024.0130509103],
              [1102999.45025565, 1717133.768275982],
              [1102975.5904241127, 1716985.8373204507],
              [1103142.6092448737, 1716947.661589991],
              [1102985.1343567276, 1716899.9419269164],
              [1103018.5381208798, 1716732.9231061554],
              [1102927.870761038, 1716847.4502975345],
              [1102846.7473338114, 1716728.151139848],
              [1102832.4314348889, 1716866.5381627642],
              [1102698.81637828, 1716966.749455221],
              [1102899.2389631933, 1717024.0130509103],
            ],
          ],
          [
            [
              [1103548.2263810078, 1717004.9251856806],
              [1103572.0862125452, 1717181.4879390565],
              [1103681.8414376169, 1717033.5569835252],
              [1103901.35188776, 1717071.7327139848],
              [1103786.8246963809, 1716938.117657376],
              [1103872.7200899152, 1716842.6783312266],
              [1103724.789134384, 1716880.8540616864],
              [1103610.2619430048, 1716670.8875441581],
              [1103605.4899766974, 1716866.538162764],
              [1103385.9795265542, 1716871.3101290714],
              [1103548.2263810078, 1717004.9251856806],
            ],
          ],
          [
            [
              [1103223.7326721007, 1716346.393835251],
              [1103223.7326721007, 1716494.324790782],
              [1103319.1719982498, 1716351.1658015584],
              [1103424.1552570139, 1716379.7975994032],
              [1103385.9795265542, 1716260.4984417167],
              [1103467.1029537811, 1716198.4628797197],
              [1103323.9439645573, 1716212.778778642],
              [1103228.504638408, 1716088.707654648],
              [1103199.8728405633, 1716212.778778642],
              [1103047.1699187246, 1716260.4984417167],
              [1103223.7326721007, 1716346.393835251],
            ],
          ],
        ],
      });
</code></pre>
<pre class="prettyprint source lang-javascript"><code>//원 feature 생성
let circleFeature = odf.FeatureFactory.produce({
  geometryType: 'circle',
       coordinates: [1099747.3552171164, 1715412.1323066188],
       circleSize : 1000
});
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">param</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#odf_feature_option">odf_feature_option</a></code>
    </span>
    |

    <span class="param-type">
        <code>ol.Feature</code>
    </span>
    

            

            

            

            <div class="param-description"><p>Feature 생성 옵션</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Feature</code>
            
            
                <p>생성된 피쳐</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".toWKT">toWKT<span class="signature">(feature)</span><span class="return-type-signature"> &rarr; {JSON}</span>
    </h4>





<div class="method-description">
    
    <p>feature 이용하여 WKT 생성</p>
<pre class="prettyprint source lang-javascript"><code>let feature = odf.FeatureFactory.toWKT(feature);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">feature</span>
            

            
                


    <span class="param-type">
        <code><a href="Feature.html">Feature</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>WKT를 만들기 위한 ODF_Feature형태의 데이터</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>JSON</code>
            
            
                <p>WKT, Property를 담은 JSON 형태 값</p>
            
        </li>
    
    </ul>


















    </article>

        
    

    

    
</article>

</section>




  </div>
</main>

<footer class="layout-footer">
  <div class="container">
    Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.11</a> on Tue Jan 21 2025 11:05:52 GMT+0900 (대한민국 표준시)
  </div>
</footer>



<script src="scripts/prism.dev.js"></script>
</body>
</html>