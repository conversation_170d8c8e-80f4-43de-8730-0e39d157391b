# Java 8 to 17, Spring 4.x to 6.x 업그레이드

## 주요 변경사항

### 1. 의존성 업데이트
- Spring Framework: 4.3.22 → 6.1.13
- egovframework.rte: 3.9.0 → 4.2.0
- thymeleaf: spring4 → spring6
- 기타 의존성은 mappick 프로젝트의 pom.xml 참고하여 업그레이드

### 2. Java Configuration
- XML 기반 설정을 Java Configuration으로 전환
- `egov-com-servlet.xml` → `WebMvcConfig.java`
- WEB-INF/config 디렉토리 내 XML 설정 파일들 삭제
- Custom ViewResolver 클래스들 제거 (기본 구현체로 대체)

### 3. 미사용 기능 정리
- context-common.xml Bean 설정 제거
- MessageSource: 전자정부 프레임워크 다국어 처리 기능
- leaveaTrace: 디버깅 및 모니터링 기능
- traceHandlerService: 디버깅 및 모니터링 기능
- 위의 기능들은 현재 프로젝트에서 미사용 중으로 판단하여 제거했습니다.
