<!doctype html>

<title>CodeMirror: Closure Stylesheets (GSS) mode</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../../doc/docs.css">

<link rel="stylesheet" href="../../lib/codemirror.css">
<link rel="stylesheet" href="../../addon/hint/show-hint.css">
<script src="../../lib/codemirror.js"></script>
<script src="css.js"></script>
<script src="../../addon/edit/matchbrackets.js"></script>
<script src="../../addon/hint/show-hint.js"></script>
<script src="../../addon/hint/css-hint.js"></script>
<style>.CodeMirror {background: #f8f8f8;}</style>
<div id=nav>
  <a href="https://codemirror.net"><h1>CodeMirror</h1><img id=logo src="../../doc/logo.png" alt=""></a>

  <ul>
    <li><a href="../../index.html">Home</a>
    <li><a href="../../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror">Code</a>
  </ul>
  <ul>
    <li><a href="../index.html">Language modes</a>
    <li><a class=active href="#">Closure Stylesheets (GSS)</a>
  </ul>
</div>

<article>
<h2>Closure Stylesheets (GSS) mode</h2>
<form><textarea id="code" name="code">
/* Some example Closure Stylesheets */

@provide 'some.styles';

@require 'other.styles';

@component {

@def FONT_FAMILY           "Times New Roman", Georgia, Serif;
@def FONT_SIZE_NORMAL      15px;
@def FONT_NORMAL           normal FONT_SIZE_NORMAL FONT_FAMILY;

@def BG_COLOR              rgb(235, 239, 249);

@def DIALOG_BORDER_COLOR   rgb(107, 144, 218);
@def DIALOG_BG_COLOR       BG_COLOR;

@def LEFT_HAND_NAV_WIDTH    180px;
@def LEFT_HAND_NAV_PADDING  3px;

@defmixin size(WIDTH, HEIGHT) {
  width: WIDTH;
  height: HEIGHT;
}

body {
  background-color: BG_COLOR;
  margin: 0;
  padding: 3em 6em;
  font: FONT_NORMAL;
  color: #000;
}

#navigation a {
  font-weight: bold;
  text-decoration: none !important;
}

.dialog {
  background-color: DIALOG_BG_COLOR;
  border: 1px solid DIALOG_BORDER_COLOR;
}

.content {
  position: absolute;
  margin-left: add(LEFT_HAND_NAV_PADDING,  /* padding left */
                   LEFT_HAND_NAV_WIDTH,
                   LEFT_HAND_NAV_PADDING); /* padding right */

}

.logo {
  @mixin size(150px, 55px);
  background-image: url('http://www.google.com/images/logo_sm.gif');
}

}
</textarea></form>
    <script>
      var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
        extraKeys: {"Ctrl-Space": "autocomplete"},
        lineNumbers: true,
        matchBrackets: true,
        mode: "text/x-gss"
      });
    </script>

    <p>A mode for <a href="https://github.com/google/closure-stylesheets">Closure Stylesheets</a> (GSS).</p>
    <p><strong>MIME type defined:</strong> <code>text/x-gss</code>.</p>

    <p><strong>Parsing/Highlighting Tests:</strong> <a href="../../test/index.html#gss_*">normal</a>,  <a href="../../test/index.html#verbose,gss_*">verbose</a>.</p>

  </article>
