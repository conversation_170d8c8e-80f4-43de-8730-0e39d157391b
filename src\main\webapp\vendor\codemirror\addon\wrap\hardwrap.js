// CodeMirror, copyright (c) by <PERSON><PERSON> and others
// Distributed under an MIT license: https://codemirror.net/LICENSE

(function(mod) {
  if (typeof exports == "object" && typeof module == "object") // CommonJS
    mod(require("../../lib/codemirror"));
  else if (typeof define == "function" && define.amd) // AMD
    define(["../../lib/codemirror"], mod);
  else // Plain browser env
    mod(CodeMirror);
})(function(CodeMirror) {
  "use strict";

  var Pos = CodeMirror.Pos;

  function findParagraph(cm, pos, options) {
    var startRE = options.paragraphStart || cm.getHelper(pos, "paragraphStart");
    for (var start = pos.line, first = cm.firstLine(); start > first; --start) {
      var line = cm.getLine(start);
      if (startRE && startRE.test(line)) break;
      if (!/\S/.test(line)) { ++start; break; }
    }
    var endRE = options.paragraphEnd || cm.getH<PERSON><PERSON>(pos, "paragraphEnd");
    for (var end = pos.line + 1, last = cm.lastLine(); end <= last; ++end) {
      var line = cm.getLine(end);
      if (endRE && endRE.test(line)) { ++end; break; }
      if (!/\S/.test(line)) break;
    }
    return {from: start, to: end};
  }

  function findBreakPoint(text, column, wrapOn, killTrailingSpace, forceBreak) {
    var at = column
    while (at < text.length && text.charAt(at) == " ") at++
    for (; at > 0; --at)
      if (wrapOn.test(text.slice(at - 1, at + 1))) break;

    if (at == 0 && !forceBreak) {
      // didn't find a break point before column, in non-forceBreak mode try to
      // find one after 'column'.
      for (at = column + 1; at < text.length - 1; ++at) {
        if (wrapOn.test(text.slice(at - 1, at + 1))) break;
      }
    }

    for (var first = true;; first = false) {
      var endOfText = at;
      if (killTrailingSpace)
        while (text.charAt(endOfText - 1) == " ") --endOfText;
      if (endOfText == 0 && first) at = column;
      else return {from: endOfText, to: at};
    }
  }

  function wrapRange(cm, from, to, options) {
    from = cm.clipPos(from); to = cm.clipPos(to);
    var column = options.column || 80;
    var wrapOn = options.wrapOn || /\s\S|-[^\.\d]/;
    var forceBreak = options.forceBreak !== false;
    var killTrailing = options.killTrailingSpace !== false;
    var changes = [], curLine = "", curNo = from.line;
    var lines = cm.getRange(from, to, false);
    if (!lines.length) return null;
    var leadingSpace = lines[0].match(/^[ \t]*/)[0];
    if (leadingSpace.length >= column) column = leadingSpace.length + 1

    for (var i = 0; i < lines.length; ++i) {
      var text = lines[i], oldLen = curLine.length, spaceInserted = 0;
      if (curLine && text && !wrapOn.test(curLine.charAt(curLine.length - 1) + text.charAt(0))) {
        curLine += " ";
        spaceInserted = 1;
      }
      var spaceTrimmed = "";
      if (i) {
        spaceTrimmed = text.match(/^\s*/)[0];
        text = text.slice(spaceTrimmed.length);
      }
      curLine += text;
      if (i) {
        var firstBreak = curLine.length > column && leadingSpace == spaceTrimmed &&
          findBreakPoint(curLine, column, wrapOn, killTrailing, forceBreak);
        // If this isn't broken, or is broken at a different point, remove old break
        if (!firstBreak || firstBreak.from != oldLen || firstBreak.to != oldLen + spaceInserted) {
          changes.push({text: [spaceInserted ? " " : ""],
                        from: Pos(curNo, oldLen),
                        to: Pos(curNo + 1, spaceTrimmed.length)});
        } else {
          curLine = leadingSpace + text;
          ++curNo;
        }
      }
      while (curLine.length > column) {
        var bp = findBreakPoint(curLine, column, wrapOn, killTrailing, forceBreak);
        if (bp.from != bp.to || forceBreak) {
          changes.push({text: ["", leadingSpace],
                        from: Pos(curNo, bp.from),
                        to: Pos(curNo, bp.to)});
          curLine = leadingSpace + curLine.slice(bp.to);
          ++curNo;
        } else {
          break;
        }
      }
    }
    if (changes.length) cm.operation(function() {
      for (var i = 0; i < changes.length; ++i) {
        var change = changes[i];
        if (change.text || CodeMirror.cmpPos(change.from, change.to))
          cm.replaceRange(change.text, change.from, change.to);
      }
    });
    return changes.length ? {from: changes[0].from, to: CodeMirror.changeEnd(changes[changes.length - 1])} : null;
  }

  CodeMirror.defineExtension("wrapParagraph", function(pos, options) {
    options = options || {};
    if (!pos) pos = this.getCursor();
    var para = findParagraph(this, pos, options);
    return wrapRange(this, Pos(para.from, 0), Pos(para.to - 1), options);
  });

  CodeMirror.commands.wrapLines = function(cm) {
    cm.operation(function() {
      var ranges = cm.listSelections(), at = cm.lastLine() + 1;
      for (var i = ranges.length - 1; i >= 0; i--) {
        var range = ranges[i], span;
        if (range.empty()) {
          var para = findParagraph(cm, range.head, {});
          span = {from: Pos(para.from, 0), to: Pos(para.to - 1)};
        } else {
          span = {from: range.from(), to: range.to()};
        }
        if (span.to.line >= at) continue;
        at = span.from.line;
        wrapRange(cm, span.from, span.to, {});
      }
    });
  };

  CodeMirror.defineExtension("wrapRange", function(from, to, options) {
    return wrapRange(this, from, to, options || {});
  });

  CodeMirror.defineExtension("wrapParagraphsInRange", function(from, to, options) {
    options = options || {};
    var cm = this, paras = [];
    for (var line = from.line; line <= to.line;) {
      var para = findParagraph(cm, Pos(line, 0), options);
      paras.push(para);
      line = para.to;
    }
    var madeChange = false;
    if (paras.length) cm.operation(function() {
      for (var i = paras.length - 1; i >= 0; --i)
        madeChange = madeChange || wrapRange(cm, Pos(paras[i].from, 0), Pos(paras[i].to - 1), options);
    });
    return madeChange;
  });
});
