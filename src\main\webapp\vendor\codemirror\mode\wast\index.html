<!DOCTYPE html>

<title>CodeMirror: Rust mode</title>
<meta charset="utf-8" />
<link rel=stylesheet href="../../doc/docs.css">

<link rel="stylesheet" href="../../lib/codemirror.css">
<script src="../../lib/codemirror.js"></script>
<script src="../../addon/mode/simple.js"></script>
<script src="wast.js"></script>
<style>
  .CodeMirror {
    border-top: 1px solid black;
    border-bottom: 1px solid black;
  }
</style>
<div id=nav>
  <a href="https://codemirror.net">
    <h1>CodeMirror</h1><img id=logo src="../../doc/logo.png" alt="">
  </a>

  <ul>
    <li><a href="../../index.html">Home</a>
    <li><a href="../../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror">Code</a>
  </ul>
  <ul>
    <li><a href="../index.html">Language modes</a>
    <li><a class=active href="#">WebAssembly</a>
  </ul>
</div>

<article>
  <h2>WebAssembly mode</h2>


  <div><textarea id="code" name="code">
/* Example WebAssembly */
(module $foo
 (export "fac" (func $fac))
 (export "plus" (func $plus))

 (func $fac (type $t0) (param $p0 i64) (result i64)
    (if $I0 (result i64)
      (i64.lt_s
        (local.get $p0)
        (i64.const 1))
      (then
        (i64.const 1))
      (else
        (i64.mul
          (local.get $p0)
          (call $fac
            (i64.sub
              (local.get $p0)
              (i64.const 1)))))))

 (func $plus (param $x i32) (param $y i32) (result i32)
  (i32.add
   (local.get $x)
   (local.get $y))))</textarea></div>

  <script>
    var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
      lineNumbers: true,
      lineWrapping: true,
      indentUnit: 4,
      mode: "wast"
    });
  </script>

  <p><strong>MIME types defined:</strong> <code>text/webassembly</code>.</p>
</article>