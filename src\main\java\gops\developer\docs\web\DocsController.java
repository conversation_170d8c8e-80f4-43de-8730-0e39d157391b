package gops.developer.docs.web;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import egovframework.com.cmm.service.EgovProperties;


@Controller
public class DocsController {

	private static final Logger LOGGER = LoggerFactory.getLogger(DocsController.class);
	
	/*
	 * VIEWS
	 */
	@RequestMapping(value = "/apidocs", method = RequestMethod.GET)
	public String apidocs(Model model) {
		
		String[] apiList = EgovProperties.getProperty("Service.API").split(",");
		String[] apiOrderList = EgovProperties.getProperty("Service.API.Order").split(",");
		model.addAttribute("apiList", apiList);
		model.addAttribute("apiOrderList", apiOrderList);
		
		return "docs/index.html";
	}

}