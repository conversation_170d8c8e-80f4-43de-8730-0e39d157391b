<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
</head>
<body>
	<div id="map" class="odf-view" style="height:550px;"></div>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::,::coordy::);
	var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);


	/*포인트 레이어 추가*/
	var pointLayer = odf.LayerFactory.produce('geoserver', {
		method : 'get',
		server : '::WfsAPI::',
		layer : '::pointLayer::',
		service : 'wfs',
	});
	pointLayer.setMap(map);
	map.setZoom(14);
    pointLayer.defineQuery({ condition: `"SHELTER_NM"='비산중학교 지하주차장'` });

	/*막대 차트 스타일 스타일 생성*/
	var chartStyle = odf.StyleFactory.produce({
		image : {
			chart : {
				type : 'bar',
				data : [1,5,2,3],//고정 데이터
				stroke : {
					color : '#000000',
					width:2,
				},
				//막대차트에서 사용할 색상
				//colors : 'pale' , // 'classic', 'dark', 'pale', 'pastel', 'neon'
				//아래와같이 직접 정의하여 사용할 수도 있음
				colors :['#FF4B4B','#FF7272','#FF9999','#FFC0C0','#FFE7E7'],
				barWidth: 20,//막대 개별 너비. 기본값 10
				barBufferSize: 5, //막대 간의 간격. 기본값 0
				barMaxHegiht: 100,//최대 막대 높이 . 기본값 50
				barMinHegiht: 1,//최소 막대 높이. 기본값 1
				rotation : Math.PI*90/180//기울기
			}
		},
	} );
	pointLayer.setStyle(chartStyle);
</script>
</html>

