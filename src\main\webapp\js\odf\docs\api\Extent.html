<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>GeOnPaas ui widgets: Extent</title>
    
      <link type="text/css" rel="stylesheet" href="styles/vendor/prism-tomorrow-night.css">
    
    <link type="text/css" rel="stylesheet" href="styles/styles.css">
    
    
    <style>
      :root {
      
      
        --nav-width: 370px;
      
      
        --nav-heading-margin-top: 0.5em;
      
      }
    </style>
    
</head>
<body>

<header class="layout-header">
  
  <h1>
    <a href="./index.html">
      GeOnPaas ui widgets
    </a>
  </h1>
  <nav class="layout-nav">
    <ul><li class="nav-heading">Classes</li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="BasemapControl.html">BasemapControl</a></span><span class="nav-desc"><p>배경지도 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="BookmarkControl.html">BookmarkControl</a></span><span class="nav-desc"><p>북마크 컨트롤 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ClearControl.html">ClearControl</a></span><span class="nav-desc"><p>지도 그리기 이벤트 초기화 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ColorFactory.html">ColorFactory</a></span><span class="nav-desc"><p>색 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Control.html">Control</a></span><span class="nav-desc"><p>사용자 정의 컨트롤 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Coordinate.html">Coordinate</a></span><span class="nav-desc"><p>좌표 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DivideMapControl.html">DivideMapControl</a></span><span class="nav-desc"><p>지도 분할 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DownloadControl.html">DownloadControl</a></span><span class="nav-desc"><p>다운로드 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DrawControl.html">DrawControl</a></span><span class="nav-desc"><p>그리기 도구 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Easing.html">Easing</a></span><span class="nav-desc"><p>애니메이션 효과</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="event.html">event</a></span><span class="nav-desc"><p>이벤트 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Extent.html">Extent</a></span><span class="nav-desc"><p>영역 관련 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Feature.html">Feature</a></span><span class="nav-desc"><p>Feature 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FeatureFactory.html">FeatureFactory</a></span><span class="nav-desc"><p>Feature 생성을 위한 FeatureFactory 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FormatFactory.html">FormatFactory</a></span><span class="nav-desc"></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FullScreenControl.html">FullScreenControl</a></span><span class="nav-desc"><p>전체화면 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="HomeControl.html">HomeControl</a></span><span class="nav-desc"><p>홈 이동 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Layer.html">Layer</a></span><span class="nav-desc"><p>레이어 관리 클래스로, 레이어는 odf.LayerFactory를 통해서만 생성가능하다.</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LayerFactory.html">LayerFactory</a></span><span class="nav-desc"><p>레이어 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LayerInfoControl.html">LayerInfoControl</a></span><span class="nav-desc"><p>레이어 정보 조회 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Map.html">Map</a></span><span class="nav-desc"><p>지도 생성, 조작, 컴퍼넌트, 레이어 추가 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Marker.html">Marker</a></span><span class="nav-desc"><p>마커 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MeasureControl.html">MeasureControl</a></span><span class="nav-desc"><p>지도 측정 도구 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MousePositionControl.html">MousePositionControl</a></span><span class="nav-desc"><p>마우스 좌표 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MoveControl.html">MoveControl</a></span><span class="nav-desc"><p>현재 화면 기준으로 이전/다음 화면 이동 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="OverviewMapControl.html">OverviewMapControl</a></span><span class="nav-desc"><p>인덱스맵 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Popup.html">Popup</a></span><span class="nav-desc"><p>팝업 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="PrintControl.html">PrintControl</a></span><span class="nav-desc"><p>프린트 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Projection.html">Projection</a></span><span class="nav-desc"><p>좌표 변환 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="RotationControl.html">RotationControl</a></span><span class="nav-desc"><p>화면을 회전 시키는 기능
alt + shift 드래그로 지도 회전</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ScaleControl.html">ScaleControl</a></span><span class="nav-desc"><p>축척 표시 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="SLD.html">SLD</a></span><span class="nav-desc"><p>WMS 스타일 관리 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Style.html">Style</a></span><span class="nav-desc"><p>스타일 관리 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="StyleFactory.html">StyleFactory</a></span><span class="nav-desc"><p>스타일 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="StyleFunction.html">StyleFunction</a></span><span class="nav-desc"><p>스타일 Function 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="SwiperControl.html">SwiperControl</a></span><span class="nav-desc"><p>지도 스와이퍼 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ZipControl.html">ZipControl</a></span><span class="nav-desc"><p>Server없이 Layer 생성하는 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ZoomControl.html">ZoomControl</a></span><span class="nav-desc"><p>지도 줌 설정클래스</p></span></li></ul><li class="nav-heading"><a href="global.html">Globals</a></li>
  </nav>
</header>


<main class="layout-main ">
  <div class="container">
    <p class="page-kind">Class</p>
    <h1 class="page-title">Extent</h1>
    




<section>


<header class="class">


    
        
        <!-- <h2>Extent</h2> -->

        

        

        
            
            <div class="class-description"><p>영역 관련 클래스</p></div>
        
    
</header>

<article>
    <div class="container-overview">



    
        





    


    
    <h3 class="subtitle">Constructor</h3>
    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="Extent">new Extent<span class="signature">()</span><span class="return-type-signature"></span>
    </h4>

















<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    
    </div>

    

    

     

    

    


    

    
        <h3 class="subtitle">Methods</h3>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".boundingBoxCoordinates">boundingBoxCoordinates<span class="signature">(extent)</span><span class="return-type-signature"> &rarr; {Array.&lt;<a href="global.html#odf_coordinate">odf_coordinate</a>>}</span>
    </h4>





<div class="method-description">
    
    <p>해당 영역을 box 형태의 좌표값으로 계산</p>
<pre class="prettyprint source lang-javascript"><code>      let boundBoxCoordinates = odf.Extent.boundingExtent([1097802.7789468267,1717572.789176268,1097621.4442271432,1717505.9816479636]]);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">extent</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#odf_extent">odf_extent</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>계산 대상 영역</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Array.&lt;odf_coordinate></code>
            
            
                <p>계산된 box 형태의 좌표값</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".boundingExtent">boundingExtent<span class="signature">(coordinates)</span><span class="return-type-signature"> &rarr; {<a href="global.html#odf_extent">odf_extent</a>}</span>
    </h4>





<div class="method-description">
    
    <p>주어진 모든 좌표를 포함하는 범위 계산</p>
<pre class="prettyprint source lang-javascript"><code>      let extent = odf.Extent.boundingExtent([
        [1097802.7789468267,1717572.789176268],
        [1097621.4442271432,1717505.9816479636],
        [1097535.5488336089,1717315.1029956653],
        [1097549.8647325314,1717071.7327139848],
        [1097821.8668120564,1716947.6615899908]
      ]);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">coordinates</span>
            

            
                


    <span class="param-type">
        <code>Array.&lt;<a href="global.html#odf_coordinate">odf_coordinate</a>></code>
    </span>
    

            

            

            

            <div class="param-description"></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>odf_extent</code>
            
            
                <p>계산된 extent 값</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".buffer">buffer<span class="signature">(extent, value, opt_extent)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>extent를 버퍼링</p>
<pre class="prettyprint source lang-javascript"><code>      let extent = [10,0,40,30];
      let bufferExtent = []
      odf.Extent.buffer(extent,30,bufferExtent);
      console.log(bufferExtent);//[-20,-30,70,60]
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">extent</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#odf_extent">odf_extent</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>확장 대상 extent. opt_extent 매개변수를 입력하지 않으면 extent에 적용됨</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">value</span>
            

            
                


    <span class="param-type">
        <code>Number</code>
    </span>
    

            

            

            

            <div class="param-description"><p>버퍼링할 값</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">opt_extent</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#odf_extent">odf_extent</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>확장된 extent값을 적용할 배열</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".containsCoordinate">containsCoordinate<span class="signature">(extent, coordinate)</span><span class="return-type-signature"> &rarr; {Boolean}</span>
    </h4>





<div class="method-description">
    
    <p>특정 좌표가 특정 영역에 포함되있는지 여부를 확인</p>
<pre class="prettyprint source lang-javascript"><code>let extent = [10,0,40,30];
      console.log(odf.Extent.containsCoordinate(extent,[11,25]));//true
      console.log(odf.Extent.containsCoordinate(extent,[11,40]));//false
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">extent</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#odf_extent">odf_extent</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>검사 대상 영역</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">coordinate</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#odf_coordinate">odf_coordinate</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>검사할 좌표</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Boolean</code>
            
            
                <ul>
<li>포함여부</li>
</ul>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".containsExtent">containsExtent<span class="signature">(extent1, extent2)</span><span class="return-type-signature"> &rarr; {Boolean}</span>
    </h4>





<div class="method-description">
    
    <p>extent2 영역이 extent1 영역에 포함되는지 여부 확인</p>
<pre class="prettyprint source lang-javascript"><code>let extent = [10,0,40,30];
      console.log(odf.Extent.buffer(extent,[11,25,12,26]));//true
      console.log(odf.Extent.buffer(extent,[11,40,12,42]));//false
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">extent1</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#odf_extent">odf_extent</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>검사 대상 영역</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">extent2</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#odf_extent">odf_extent</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>검사할 영역</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Boolean</code>
            
            
                <ul>
<li>포함여부</li>
</ul>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".containsXY">containsXY<span class="signature">(extent, x, y)</span><span class="return-type-signature"> &rarr; {Boolean}</span>
    </h4>





<div class="method-description">
    
    <p>[x,y] 좌표가 extent 영역에 포함되는지 여부 확인</p>
<pre class="prettyprint source lang-javascript"><code>let extent = [10,0,40,30];
      console.log(odf.Extent.containsXY(extent,11,20));//true
      console.log(odf.Extent.containsXY(extent,44,80));//false
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">extent</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#odf_extent">odf_extent</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>검사 대상 영역</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">x</span>
            

            
                


    <span class="param-type">
        <code>Number</code>
    </span>
    

            

            

            

            <div class="param-description"><p>검사할 좌표의 x좌표</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">y</span>
            

            
                


    <span class="param-type">
        <code>Number</code>
    </span>
    

            

            

            

            <div class="param-description"><p>검사할 좌표의 y좌표</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Boolean</code>
            
            
                <ul>
<li>포함여부</li>
</ul>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".createEmpty">createEmpty<span class="signature">()</span><span class="return-type-signature"> &rarr; {<a href="global.html#odf_extent">odf_extent</a>}</span>
    </h4>





<div class="method-description">
    
    <p>빈 영역 생성</p>
<pre class="prettyprint source lang-javascript"><code>      console.log(odf.Extent.createEmpty());//[Infinity, Infinity, -Infinity, -Infinity]
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>odf_extent</code>
            
            
                <ul>
<li>빈 영역</li>
</ul>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".equals">equals<span class="signature">(extent1, extent2)</span><span class="return-type-signature"> &rarr; {Boolean}</span>
    </h4>





<div class="method-description">
    
    <p>extent1과 extent2를 비교</p>
<pre class="prettyprint source lang-javascript"><code>let extent1 = [10,0,40,30];
let extent2 = [10,0,40,38];
      console.log(odf.Extent.equals(extent1,extent1));//true
      console.log(odf.Extent.equals(extent1,extent2));//false
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">extent1</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#odf_extent">odf_extent</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>비교대상 영역 1</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">extent2</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#odf_extent">odf_extent</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>비교대상 영역 2</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Boolean</code>
            
            
                <ul>
<li>비교결과</li>
</ul>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".extend">extend<span class="signature">(extent1, extent2)</span><span class="return-type-signature"> &rarr; {<a href="global.html#odf_extent">odf_extent</a>}</span>
    </h4>





<div class="method-description">
    
    <p>extent1과 extent2를 모두 포함하는 영역으로 확장. extent1의 값이 변경됨</p>
<pre class="prettyprint source lang-javascript"><code>let extent1 = [10,0,40,30];
let extent2 = [10,0,40,38];
      console.log(odf.Extent.extend(extent1,extent2));//[10,0,40,38]
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">extent1</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#odf_extent">odf_extent</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>확장 대상 영역 1. 확장된 영역이 덮어쓰기됨</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">extent2</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#odf_extent">odf_extent</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>확장 대상 영역 2</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>odf_extent</code>
            
            
                <ul>
<li>확장된 영역</li>
</ul>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".getArea">getArea<span class="signature">(extent)</span><span class="return-type-signature"> &rarr; {Number}</span>
    </h4>





<div class="method-description">
    
    <p>영역의 크기 계산</p>
<pre class="prettyprint source lang-javascript"><code>let extent = [10,0,40,30];
      console.log(odf.Extent.getArea(extent));//(40-10)*(30-0)=900
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">extent</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#odf_extent">odf_extent</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>크기 산정 대상 영역</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Number</code>
            
            
                <ul>
<li>범위의 크기</li>
</ul>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".getBottomLeft">getBottomLeft<span class="signature">(extent)</span><span class="return-type-signature"> &rarr; {<a href="global.html#odf_coordinate">odf_coordinate</a>}</span>
    </h4>





<div class="method-description">
    
    <p>영역의 왼쪽 아래 꼭지점 좌표 조회</p>
<pre class="prettyprint source lang-javascript"><code>let extent = [10,0,40,30];
      console.log(odf.Extent.getBottomLeft(extent));//[10, 0]
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">extent</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#odf_extent">odf_extent</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>대상 영역</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>odf_coordinate</code>
            
            
                <ul>
<li>영역의 왼쪽 아래 꼭지점 좌표</li>
</ul>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".getBottomRight">getBottomRight<span class="signature">(extent)</span><span class="return-type-signature"> &rarr; {<a href="global.html#odf_coordinate">odf_coordinate</a>}</span>
    </h4>





<div class="method-description">
    
    <p>영역의 오른쪽 아래 꼭지점 좌표 조회</p>
<pre class="prettyprint source lang-javascript"><code>let extent = [10,0,40,30];
      console.log(odf.Extent.getBottomRight(extent));//[40, 0]
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">extent</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#odf_extent">odf_extent</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>대상 영역</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>odf_coordinate</code>
            
            
                <ul>
<li>영역의 오른쪽 아래 꼭지점 좌표</li>
</ul>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".getCenter">getCenter<span class="signature">(extent)</span><span class="return-type-signature"> &rarr; {<a href="global.html#odf_coordinate">odf_coordinate</a>}</span>
    </h4>





<div class="method-description">
    
    <p>영역의 중심 좌표 조회</p>
<pre class="prettyprint source lang-javascript"><code>let extent = [10,0,40,30];
      console.log(odf.Extent.getTopRight(extent));//[25, 15]
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">extent</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#odf_extent">odf_extent</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>대상 영역</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>odf_coordinate</code>
            
            
                <ul>
<li>영역의 줌심 좌표</li>
</ul>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".getHeight">getHeight<span class="signature">(extent)</span><span class="return-type-signature"> &rarr; {Number}</span>
    </h4>





<div class="method-description">
    
    <p>영역의 높이 조회</p>
<pre class="prettyprint source lang-javascript"><code>let extent = [10,0,40,30];
      console.log(odf.Extent.getHeight(extent));//30
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">extent</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#odf_extent">odf_extent</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>대상 영역</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Number</code>
            
            
                <ul>
<li>영역의 높이</li>
</ul>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".getSize">getSize<span class="signature">(extent)</span><span class="return-type-signature"> &rarr; {Array.&lt;Number>}</span>
    </h4>





<div class="method-description">
    
    <p>영역의 높이,너비 조회</p>
<pre class="prettyprint source lang-javascript"><code>let extent = [10,0,40,30];
      console.log(odf.Extent.getSize(extent));//[30,30]
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">extent</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#odf_extent">odf_extent</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>대상 영역</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Array.&lt;Number></code>
            
            
                <ul>
<li>영역의 높이,너비</li>
</ul>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".getTopLeft">getTopLeft<span class="signature">(extent)</span><span class="return-type-signature"> &rarr; {<a href="global.html#odf_coordinate">odf_coordinate</a>}</span>
    </h4>





<div class="method-description">
    
    <p>영역의 왼쪽 위 꼭지점 좌표 조회</p>
<pre class="prettyprint source lang-javascript"><code>let extent = [10,0,40,30];
      console.log(odf.Extent.getTopLeft(extent));//[10, 30]
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">extent</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#odf_extent">odf_extent</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>대상 영역</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>odf_coordinate</code>
            
            
                <ul>
<li>영역의 왼쪽 위 꼭지점 좌표</li>
</ul>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".getTopRight">getTopRight<span class="signature">(extent)</span><span class="return-type-signature"> &rarr; {<a href="global.html#odf_coordinate">odf_coordinate</a>}</span>
    </h4>





<div class="method-description">
    
    <p>영역의 오른쪽 위 꼭지점 좌표 조회</p>
<pre class="prettyprint source lang-javascript"><code>let extent = [10,0,40,30];
      console.log(odf.Extent.getTopRight(extent));//[40, 30]
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">extent</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#odf_extent">odf_extent</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>대상 영역</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>odf_coordinate</code>
            
            
                <ul>
<li>영역의 오른쪽 위 꼭지점 좌표</li>
</ul>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".getWidth">getWidth<span class="signature">(extent)</span><span class="return-type-signature"> &rarr; {Number}</span>
    </h4>





<div class="method-description">
    
    <p>영역의 너비 조회</p>
<pre class="prettyprint source lang-javascript"><code>let extent = [10,0,40,30];
      console.log(odf.Extent.getWidth(extent));//30
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">extent</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#odf_extent">odf_extent</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>대상 영역</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Number</code>
            
            
                <ul>
<li>영역의 높이</li>
</ul>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".intersects">intersects<span class="signature">(extent1, extent2)</span><span class="return-type-signature"> &rarr; {Boolean}</span>
    </h4>





<div class="method-description">
    
    <p>extent1 영역과 extent2 영역의 교차 여부 조회</p>
<pre class="prettyprint source lang-javascript"><code>let extent1 = [10,0,40,30];
let extent2 = [20,10,50,40];
let extent3 = [60,50,80,70];
      console.log(odf.Extent.intersects(extent1,extent2));//true
      console.log(odf.Extent.intersects(extent1,extent3));//false
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">extent1</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#odf_extent">odf_extent</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>대상 영역 1</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">extent2</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#odf_extent">odf_extent</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>대상 영역 2</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Boolean</code>
            
            
                <ul>
<li>extent1 영역과 extent2 영역의 교차 여부</li>
</ul>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".isEmpty">isEmpty<span class="signature">(extent)</span><span class="return-type-signature"> &rarr; {Boolean}</span>
    </h4>





<div class="method-description">
    
    <p>영역이 비어있는지 확인</p>
<pre class="prettyprint source lang-javascript"><code>let extent1 = [10,0,40,30];
let extent2 = odf.Extent.createEmpty();
      console.log(odf.Extent.isEmpty(extent1));//false
      console.log(odf.Extent.isEmpty(extent2));//true
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">extent</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#odf_extent">odf_extent</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>비었는지 확인할 영역</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Boolean</code>
            
            
                <ul>
<li>비어있는지 여부</li>
</ul>
            
        </li>
    
    </ul>


















    </article>

        
    

    

    
</article>

</section>




  </div>
</main>

<footer class="layout-footer">
  <div class="container">
    Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.11</a> on Tue Jan 21 2025 11:05:52 GMT+0900 (대한민국 표준시)
  </div>
</footer>



<script src="scripts/prism.dev.js"></script>
</body>
</html>