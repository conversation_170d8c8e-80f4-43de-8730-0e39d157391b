<!doctype html>

<title>CodeMirror: Spreadsheet mode</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../../doc/docs.css">

<link rel="stylesheet" href="../../lib/codemirror.css">
<script src="../../lib/codemirror.js"></script>
<script src="../../addon/edit/matchbrackets.js"></script>
<script src="spreadsheet.js"></script>
<style>.CodeMirror {border-top: 1px solid black; border-bottom: 1px solid black;}</style>
<div id=nav>
  <a href="https://codemirror.net"><h1>CodeMirror</h1><img id=logo src="../../doc/logo.png" alt=""></a>

  <ul>
    <li><a href="../../index.html">Home</a>
    <li><a href="../../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror">Code</a>
  </ul>
  <ul>
    <li><a href="../index.html">Language modes</a>
    <li><a class=active href="#">Spreadsheet</a>
  </ul>
</div>

<article>
  <h2>Spreadsheet mode</h2>
  <form><textarea id="code" name="code">=IF(A1:B2, TRUE, FALSE) / 100</textarea></form>

  <script>
    var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
      lineNumbers: true,
      matchBrackets: true,
      extraKeys: {"Tab":  "indentAuto"}
    });
  </script>

  <p><strong>MIME types defined:</strong> <code>text/x-spreadsheet</code>.</p>
  
  <h3>The Spreadsheet Mode</h3>
  <p> Created by <a href="https://github.com/robertleeplummerjr">Robert Plummer</a></p>
</article>
