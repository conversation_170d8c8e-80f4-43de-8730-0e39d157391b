<!DOCTYPE HTML>
<html>
<head>
<meta charset="utf-8">
</head>
<link href="::OdfUrl::/odf.css" rel="stylesheet">
<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
<style>
  /* 피처 속성 정보 조회 css */
  #featureInfo>div,#featureInfo>div>div{
  	padding : 10px;
  }
  #featureInfo>div>div{
  	overflow-x : auto;
  }
  #featureInfo h5.title,featureInfo h6.title{
  	font-weight : bold;
  	margin-bottom: 5px;
  }
  #featureInfo table{
  	border-collapse: collapse;
  }
  #featureInfo td {
  	border: 1px solid #949494 !important;
    padding: 3px !important;
    text-align: center;
  }
</style>
<body>
	<div id="map" class="odf-view" style="height:550px;"></div>
	<div class="infoArea" style="margin-top: 15px;">
		<div id="featureInfo"></div>
	</div>
</body>
<script>

	// 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.)
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::,::coordy::);
	var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);

	// wfs 레이어 생성
	// LayerFactory의 produce 함수는 option이 다양하니 개발자지원센터 '지도문서'를 확인하세요
	var wfsLayer = odf.LayerFactory.produce('geoserver'/*레이어를 생성하기위 한 테이터 호출 방법*/, {
		method : 'get',//'post'
		server : '::WfsAPI::', // 레이어가 발행된 서버 주소
		layer : '::polygonLayer2::', // 발행된 레이어 명칭 (ex. 저장소명:레이어명)
		service : 'wfs', // 호출하고자 하는 레이여 형태(wms, wfs, wmts)
		//bbox : true, //기본값 false. true로 셋팅하면 화면에 보이는 영역만큼 도형 조회 요청함
		//geometryName, geometryType 정의시 DescribeFeatureType 요청 비활성화됨. 필수x
		geometryName : "the_geom",
		geometryType : "MultiPolygon",
		/*//서버에 요청시 사용하는 파라미터 편집(추가/삭제/수정)을 위한 필터
		// getFeature,GetCapabilities, GetStyles, DescribeFeatureType, DescribeFeatureType 요청에 사용
		parameterFilter : (parameters)=>{
			return {
				...parameters,
				newParam : 'newnew',
			};
		}*/
	}/*레이어 생성을 위한 옵션*/);
	wfsLayer.setMap(map);
	// 해당 layer가 한눈에 보이는 보여주는 extent로 화면 위치 이동 및 줌 레벨 변경
	wfsLayer.fit();

	// 레이어 삭제
	// map.removeLayer(wfsLayer.getODFId());

	/**
	// 레이어 필터
	wfsLayer.defineQuery({
		//"[[컬럼명]]"[[부호]]'[[비교 대상 값]]'
		condition : `"src_signgu"='41170'`
	});
	*/

	// 레이어 on/off
	// map.switchLayer(wfsLayer.getODFId()/*odf id*/, false/*on/off여부*/);

	// 레이어 z-index 조절
	// map.setZIndex(wfsLayer.getODFId(), 0);

	// 레이어 가시범위 설정
	// wfsLayer.setMinZoom(10);
	// wfsLayer.setMaxResolution(152.70292183870401);
	// wfsLayer.setMaxZoom(18);
	// wfsLayer.setMinResolution(0.5964957884324376);

	// 레이어 투명도 조절
	// wfsLayer.setOpacity(0.5);


	/**
	// 레이어 스타일 생성후 레이어에 적용
	var polygonStyle =odf.StyleFactory.produceFunction([
      {
    	  	seperatorFunc : 'default',
    	  	style : {
            fill : {
                color:[206, 252, 254, 0.8]
            },
            stroke : {
                color:[255, 204, 255],
                width:2
            },
            text :{
                font : '20px Courier New',//폰트 크기(필수) 및 글씨체(필수), 두께(옵션)
                fill : {color:'red'},
                stroke : {//text 안의 stroke는 width/lineCap/lineJoin/lineDash/lineDashOffset/miterLimit 옵션 적용  x
                    color:'blue',
                },
                padding : [5,5,5,5],//text와 background영역 사이의 여백 //placement :'line' 일 경우 미적용
                placement :'point',//텍스트를 나열하는 위치를 line을 따라 나타나게 할지, 특정 point에 나타나게 할지
                maxAngle : 90*Math.PI/180,//placement :'line' 일경우 적용
                overflow : false,//placement :'line' 일 경우 적용//텍스트를 나열한 길이보다 선이 짧을 경우, 넘치는 글자를 쭉 나열할지 여부
                scale : 0.8, //텍스트 크기를 정해진 값의 n배로 셋팅
                rotateWithView: true//지도가 회전할 때 텍스트도 적절하게 회전할지 여부
            }
        },
        callbackFunc : function(style, feature, resolution) {
        		// 텍스트 내용을 해당 레이어의 objectid 컬럼 값 으로 셋팅
        		style.getText().setText(feature.getProperties().objectid + '');
        }
      }
    ]);
	wfsLayer.setStyle(polygonStyle);
	*/





















	// 피처 속성 조회
	odf.event.addListener(map, 'click', function (evt) {
		var result = map.selectFeature({
			extractType: 'pixel',
			pixel: evt.pixel
		});

		var filteredResult = Object.entries(result).filter(([key, value]) => {
			return value.features.length > 0;
		})

		constructInfoDiv(filteredResult, 'featureInfo');

		//iframe 크기 조절
		if (parent.window.containerResize)
			parent.window.containerResize();
	}.bind(this));

	function constructInfoDiv(list, selector) {
		selector = document.getElementById(selector)
		selector.innerHTML = '';

		for (var i = 0; i < list.length; i++) {
			var [layerId, layerItem] = list[i];
			var layerDiv = document.createElement('div');
			var layerTitle = document.createElement('h5');
			layerTitle.classList.add('title')
			layerTitle.innerHTML = layerId;
			layerDiv.append(layerTitle);

			var featureLen = layerItem.features.length;

			for (var j = 0; j < featureLen; j++) {
				var featureDiv = document.createElement('div');
				var featureTitle = document.createElement('h6');
				featureTitle.classList.add('title')
				featureTitle.innerHTML = `도형-${j + 1}`;
				featureDiv.append(featureTitle);

				constructFeatureInfoTable(layerItem.features[j],featureDiv)

				layerDiv.append(featureDiv);
			}

			selector.append(layerDiv);
		}

	}

	function constructFeatureInfoTable(feature, target) {

		var featureTable = document.createElement('table');
		var properties = Object.entries(feature.getProperties());
		var thead = document.createElement('thead');
		var tbody = document.createElement('tbody');

		var headerRow = document.createElement('tr');
		var bodyRow = document.createElement('tr');

		for (var i = 0; i < properties.length; i++) {
			if(properties[i][0]!=='geometry'){
				var headerTd =  document.createElement('td');
				headerTd.innerText = properties[i][0];
				headerRow.append(headerTd);

				var bodyTd = document.createElement('td');
				bodyTd.innerText = properties[i][1]?properties[i][1]:'-';
				bodyRow.append(bodyTd);
			}
		}

		thead.append(headerRow);
		tbody.append(bodyRow);
		featureTable.append(thead);
		featureTable.append(tbody);
		target.append(featureTable);

	}
</script>
</html>
