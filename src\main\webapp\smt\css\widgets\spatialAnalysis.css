@charset "UTF-8";
.spatialAnalysis_cont button{
	position: relative;
    display: flex;
    align-items: center;
    height: 36px;
    padding: 0 15px;
    border-radius: 4px;
    box-sizing: border-box;
    font-family: "Pretendard";
    font-weight: normal;
    font-size: 14px;
    color: #fff;
    vertical-align: top;
    letter-spacing: 0px;
    transition: .4s;
}
.spatialAnalysis_cont .flex{
	display: flex;
}
/*직접선택 팝업 ui*/
.odf-map .spatialAnalysis_selectFeature{
    width: 270px;
    transition: .4s;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0.5px 0.9px 4px 0 rgb(0 0 0 / 27%);
}
.odf-map .spatialAnalysis_selectFeature:after
{
    position: absolute;
    left: 50%;
    bottom: -2px;
    display: block;
    content: '';
    box-shadow: -2px 4px 4px rgb(0 0 0 / 10%);
    border: 10px solid transparent;
    border-left: 10px solid #fff;
    border-bottom: 10px solid #fff;
    transform: rotate(-45deg) translateX(-50%);
}
.odf-map .spatialAnalysis_selectFeature .spatialAnalysis_body{
	max-height : 300px;
	overflow-y : auto;
}
.odf-map .spatialAnalysis_selectFeature .spatialAnalysis_body .spatialAnalysis_featureItem:first-of-type{
    border-top: 1px solid #eef1f8;
}
.odf-map .spatialAnalysis_selectFeature .spatialAnalysis_body .spatialAnalysis_featureItem{
	display: flex;
    align-items: center;
    justify-content: space-between;
    height: 38px;
    line-height: 38px;
    border-bottom: 1px solid #eef1f8;
    padding: 0 10px;
    transition: .3s;
}

.odf-map .spatialAnalysis_selectFeature .spatialAnalysis_body .spatialAnalysis_featureItem .lyrNm{
	
    margin-left: 10px;
    display: flex;
    align-items: center;
    font-size: 14px;
    font-family: 'Pretendard';
    font-weight: normal;
    color: #333;
}