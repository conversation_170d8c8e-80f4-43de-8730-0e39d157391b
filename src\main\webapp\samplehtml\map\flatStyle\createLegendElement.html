<!DOCTYPE HTML>
<html>

<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
</head>

<body>
	<div id="map" class="odf-view" style="height:550px;"></div>
	<div class="infoArea" id="legendArea"></div>
</body>
<script>


	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::,::coordy::);
	var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);

	/*포인트 레이어 추가*/
	var layer = odf.LayerFactory.produce('geoserver', {
		method: 'get',
		server: '::WfsAPI::',
		layer: '::pointLayer::', // 발행된 레이어 명칭 (ex. 저장소명:레이어명)
		crtfckey: '::crtfckey::',
		service: 'wfs',
	});
	layer.setMap(map);
	layer.fit();

	var attr = 'id';
	odf.event.addListener(layer, 'featureloadend', () => {
		var rangeCount = 10;
		//값의 종류 조회
		var range = layer.getAttributesFeaturesValueRange(attr);
		//선택된 색상계열로 feature의 개수 만큼 색 생성
		var colorList = odf.ColorFactory.produce(
			/*색 계열
			- 'red'
			- 'blue'
			- 'yellow'
			- 'green'
			- 'purple'
			- 'brown'
			- 'black'
			- 'random'*/
			'purple',
			rangeCount
		);

		//동적 스타일 생성
		var gap = (range.max - range.min) / rangeCount;
		var styleDefine = [];
		//여러 조건에서 다 참이면 맨 마지막 조건의 스타일이 적용
		for (var i = rangeCount; i > 0; i--) {
			styleDefine.push({
				filter: ['<', ['get', attr], gap * (i)],
				style: {
					'circle-radius': 10,//크기
					'circle-fill-color': colorList[i - 1].slice(),//채우기 색
					'circle-stroke-color': 'black',//운곽선 색
					'circle-stroke-width': 1,//운곽선 굵기
				}
			})
		}
		layer.setStyle(styleDefine);

		//범례 생성
		styleDefine.forEach(function (item) {
			var newDiv = document.createElement('div');
			//스타일 옵션으로 범례 element생성
			var styleElem = odf.StyleFactory.produceElement(item.style //스타일 생성 옵션
				, 15//element 크기 . 기본값 10
				, true//text 표시 여부. 기본값 false
			);
			styleElem.style.float = 'left';
			newDiv.appendChild(styleElem);
			var newSpan = document.createElement('span');
			newSpan.innerHTML = `${item.filter[1][1]} ${item.filter[0]} ${item.filter[2]}`;
			newDiv.appendChild(newSpan);
			newSpan.style['padding-left'] ='5px';
			document.querySelector('#legendArea').appendChild(newDiv);

		});

		//iframe 크기 조절
		if (parent.window.containerResize) parent.window.containerResize();
	});


</script>

</html>
