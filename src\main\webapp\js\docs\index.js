/**
 *
 */

$(document).ready(function() {

	menu.init("공통 코드 조회", docInit, loadDoc);

});

var menuJson = {};
var specJson = {};
function docInit() {
	$.each(gwPaths, function(index, gwPath) {

		//var docUrl = 'http://geon.wavus.co.kr' + gwPath.path.replace('/', ''); //농협
		var docUrl = contextPath + '/proxy/api' + gwPath.path;
		$.ajax({
			url: docUrl,
			type: 'GET',
			dataType: 'json',
			cache: false,
			async: false,
			success : function(spec){
				menuCombine(spec);
				specCombine(spec);
			},
			error : function(e){
			},
			complete : function(c){
			}
		});
	});
		menuSort();
}

var menuIndex = 0;
var menuList = [];
function menuCombine(spec) {

	$.each(spec.tags, function(idx, tag) {
		if(tag.name != 'API 사용 정보 관리' ){
		var refinedTagName = util.jq(tag.name.replace(/ /gi, "_"));

		menuList[menuIndex] = {
			name : tag.name,
			desc : tag.description,
			target : "operations-tag-" + refinedTagName,
			submenu : []
		};
		var orderCondition = getOrderCondition(spec, tag);

		subMenuCombine(orderCondition, spec,
				menuList[menuIndex].submenu, refinedTagName);

		menuIndex++;
		}
	});
}

function subMenuCombine(orderCondition, spec, submenu, refinedTagName) {
	for(var i=0;i <orderCondition.length; i++) {
		var condition = orderCondition[i];

		$.each(spec.paths, function(pathsKey, path) {

			$.each(path, function(pathKey, api) {

				if(condition.tagName == api.tags[0] &&
						condition.pathsKey == pathsKey &&
						condition.pathKey == pathKey) {

					submenu.push({
						name: api.summary,
						path: "",
						desc: util.removeStyle(api.description) + '<span class="hidden">' + spec.basePath + pathsKey + ' [' + pathKey + ']</span>',
						target: "operations-" + refinedTagName + "-" + api.operationId,
						grpId: menuIndex
					});

					return;
				}
			});
		});
	}
}

function menuSort() {

	for (var i = 0; i < menuOrder.length; i++) {

		var filtered = menuList.filter(function(it) {
			return it.name == menuOrder[i];
		});

		if(filtered.length> 0 && filtered[0].submenu) {

			for(var j = 0; j < filtered[0].submenu.length; j++) {
				filtered[0].submenu[j].grpId = i;
			}
			menuJson[i] = filtered[0];
		}
	}
}

// swagger spec json 조합
function specCombine(spec) {
	spec.basePath = spec.basePath == '/' ? '' :spec.basePath;
	$.each(spec.tags, function(idx, tag) {
		var orderCondition = getOrderCondition(spec,  tag);
		var paths = getPaths(spec, tag, orderCondition);
		let specSchema = getSpecSchema(spec, paths);
		specJson[tag.name] = {
				"swagger" : spec.swagger,
				"info": spec.info,
				"host": spec.host, //일반
				//"host": location.host + baseUrl + '/proxyUrl.jsp?url=http://' + spec.host, //농협
				//"host": location.host + baseUrl, //일반
				"openapi": spec.openapi, // openapi parameter
				"servers": spec.servers, // openapi parameter
				"components": {
					...spec.components,
					schemas: specSchema
				}, // openapi parameter
				"basePath": "",		//spec.basePath,
				"tags": [tag],
				"paths": paths,
				// "definitions": schema
		};
	});
}

// 정렬 기준 배열 생성
function getOrderCondition(spec,  tag) {
	var array = [];
	$.each(spec.paths, function(pathsKey, path) {

		$.each(path, function(pathKey, api) {

			if (api.tags[0] == tag.name) {
				array.push({
					summary : api.summary,
					pathsKey : pathsKey,
					pathKey : pathKey,
					tagName: tag.name
				});
			}
		});
	});

	var sortBySummary = function(a, b) {
		return a.summary.localeCompare(b.summary);
	}

	array.sort(sortBySummary);

	return array;
}

// tag에 해당하는 path(api) 리턴
function getPaths(spec, tag, orderCondition) {

	var paths = {};

	for(var i=0; i<orderCondition.length; i++) {
		var condition = orderCondition[i];

		$.each(spec.paths, function(pathsKey, path) {

			if(condition.pathsKey == pathsKey) {

				var makePath = {};

				$.each(path, function(pathKey, api) {
					if(condition.tagName == api.tags[0] &&
							condition.pathKey == pathKey) {

						if(!api.parameters) {
							api.parameters = [];
						}
						/*
						api.parameters.push({
	                        "name": "apikey",
	                        "in": "query",
	                        "description": "API Key",
	                        //"required": true,
	                        "type": "string"
	                    });
						api.responses["401"] = {
	                        "description": "인증 오류. API Key 확인."
	                    };
						*/

						var apiJson = {};
						apiJson[pathKey] = api;
						makePath = Object.assign({}, makePath, apiJson);
					}
				});

				if(Object.keys(makePath).length > 0) {
					const key = spec.basePath ? spec.basePath + pathsKey : pathsKey
					paths[key] = Object.assign({}, paths[key], makePath);
				}
			}
		});
	}
	return paths;
}

// paths(api)에 필요한 definitions만 필터링해서 리턴
function getSpecSchema(spec, paths) {
	var refineSchema = {};
	if (spec.components && spec.components.schemas) {
		$.each(spec.components.schemas, function(schemaKey, schema) {
			var pathJson = JSON.stringify(paths);
			var testRegex = RegExp("#/components/schemas/" + schemaKey);

			if (testRegex.test(pathJson) || pathJson.indexOf(schemaKey) > 0) {
				refineSchema[schemaKey] = schema;
				// 현재 모델 정의에서 참조하는 모델들은 모두 포함
				jsonRefParser.refModelCombine(spec.components.schemas, schema, refineSchema);
			}
		});
	}
	return refineSchema;
}

let lastGroupMenuName = "";
function loadDoc(menuName, target) {
	window.history.replaceState('', '', rootpath + '/apidocs');
	var groupMenu = menu.getGroupMenu(menuName);
	if(lastGroupMenuName != groupMenu.name) {
		// Begin Swagger UI call region
	    window.ui = SwaggerUIBundle({
	      spec: specJson[groupMenu.name],
	      dom_id: '#swagger-ui',
	      deepLinking: true,
	      showCommonExtensions: true,
	      presets: [
	        SwaggerUIBundle.presets.apis,
	        SwaggerUIStandalonePreset
	      ],
		  plugins: [
		    SwaggerUIBundle.plugins.DownloadUrl
		  ],
	      // layout: "StandaloneLayout",
	      layout: "BaseLayout",
	      onComplete : () => {
			  // onComplete 가 SwaggerUIBundle의 동적 Element 렌더링 상태를 보장하지않음
			  // 동적 Element 생성까지의 setTimeout 필요...
			  setTimeout(()=>{
				  loadExample(specJson[groupMenu.name]);
			  }, 200)
			  // site.eventTab();

		  }
	    })
	    // End Swagger UI call region
	}

	menu.menuSelected(menuName);
    lastGroupMenuName = groupMenu.name;
}

// 예제 코드 생성후 알맞은 위치에 붙여준다.
function loadExample(specJson) {

	// code block 위치선정 구분자를 만들때 사용
	var rowNumber = 0;

	$.each(specJson.tags, function(index, tag) {

		// tag와 일치하는 path만 가져오기
		var paths = filterPaths(specJson, tag.name);

		$.each(paths, function(pathKey, pathValue) {

			$.each(pathValue, function(apiKey, apiSpec) {

				// code block을 만들어서 맞는 위치에 넣어주고 display 이벤트 처리
				setCodeDom(specJson, rowNumber, pathKey, apiKey, apiSpec);
				rowNumber++;
			});

		});

	});
}

// tag와 일치하는 API들만 필터링. 예제 코드 표시시에 순서를 맞춰주기 위함.
function filterPaths(specJson, tagName) {

	var path = {};
	$.each(specJson.paths, function(pathKey, pathValue) {

		var isFiltered = false;

		$.each(pathValue, function(apiMethod, apiSpec) {

			if(apiSpec.tags == tagName) {
				isFiltered = true;
			}
		});

		if(isFiltered) {
			path[pathKey] = pathValue;
		}
	});

	return path;
}

// 예제 코드 dom을 알맞은 위치에 붙여준다.
function setCodeDom(specJson, rowNumber, pathKey, apiKey, apiSpec) {

	var $opblock = $(".opblock:eq(" + rowNumber + ")");
	$opblock.attr("rownum", rowNumber);

	var codeHtml = makeCodeBlock(specJson, rowNumber, pathKey, apiKey, apiSpec);
	$opblock.append(codeHtml);
	$("#codeblock" + rowNumber).hide();

	// API를 클릭시 코드블록 표시 토글
	$opblock.children(".opblock-summary").on("click", function() {

		var parent = $(this).parent();
		var rownum = parent.attr("rownum");

		// $noMarginDiv 는 swaggerUI가 동적으로 생성하는 dom 요소임.
		// swagger 버전이 올라가면서 토글 시 Element 를 지우고 다시 생성하는 듯
		// $noMarginDiv 가 생성될 때 까지 기다린 후에 코드블록 html 활성화
		setTimeout(() => {
			let $noMarginDiv = parent.find('div.no-margin');
			if ($noMarginDiv.length > 0) {
				$("#codeblock" + rownum).show();
				Prism.highlightAll();
				site.eventTab();
			}
		},100)

		if(parent.attr("class").indexOf("is-open") > -1) {
			$("#codeblock" + rownum).hide();
		}
	});
}

// 예제 코드 HTML 생성
function makeCodeBlock(specJson, rowNumber, pathKey, apiKey, apiSpec) {

	if(specJson.basePath[specJson.basePath.length - 1] == '/') {
		specJson.basePath = specJson.basePath.slice(0, -1);
	}

	jsonRefParser.init(specJson);

	var apiUrl = specJson.servers[0].url + specJson.basePath + pathKey;
	var javaCode = javaCodeGenerator.makeCode(apiUrl, apiKey, apiSpec);
	var jsCode = jsCodeGenerator.makeCode(apiUrl, apiKey, apiSpec);

	var javahtml = "<div class='tab-pannel current' id='java-tab-" + rowNumber + "'>"
					+ "<pre>"
					+ "<code class='line-numbers language-clike'>"
					+ javaCode
					+ "</code>"
					+ "</pre>"
					+ "</div>";

	var jshtml = "<div class='tab-pannel' id='js-tab-" + rowNumber + "'>"
					+ "<pre>"
					+ "<code class='line-numbers language-markup'>"
					+ jsCode
					+ "</code>"
					+ "</pre>"
					+ "</div>";

	var html = "<div class='codeblock' id='codeblock" + rowNumber + "'>"
				+ "<div class='tabs'>"
				+ "<button type='button' class='blueType' data-id='java-tab-" + rowNumber + "'>JAVA</button>"
				+ "<button type='button' class='greyType' data-id='js-tab-" + rowNumber + "'>Js</button>"
				+ "</div>"
				+ "<div class='tab-content'>"
				+ javahtml
				+ jshtml
				+ "</div>"
				+ "</div>";

	return html;
}
