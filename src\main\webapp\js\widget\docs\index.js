/**
 * 
 */

$(document).ready(function() {
	
	menu.init("AddressSearchWidget", makeMenuJson, loadOdf);
});

var menuJson = {
	0:{
		name: "Class",
		desc: "",
		submenu: []
	},
	1:{
		name: "global",
		path: odfBaseUrl + "global.html",
		desc: "",
		target: "",
		submenu: []
	}
};

function makeMenuJson() {
	$.ajax({
		url : encodeURI(odfBaseUrl + "AddressSearchWidget.html"),
		type : 'GET',
		cache: false,
		async : false,
		success : function(html) {
			html = htmlRefine(html);
			$("#menu-loader").html(html);
			// menu json 생성
			$("#menu-loader nav ul li a").each(function(index, item){
				var href = $(item).attr("href");
				var name = $(item).text();
				
				menuJson[0].submenu.push({ 
					name: name,
					path: odfBaseUrl + href,
					//desc : name, //임시로 밑에 desc 로직 jsdoc 템플릿 수정 필요
					target: "",
					grpId: 0  
				});
			});
			
			//$("#menu-loader nav ul span p").each(function(index, item){
			$(".layout-nav ul li span p").each(function(index, item){
				var name = $(item).text();
			
				menuJson[0].submenu[index].desc = name;
			});
		},
		error : function(xhr, status, err) {
			console.log(xhr);
			console.log(status);
			console.log(err);
		}
	});
}

// 문서에서 불필요한 부분 정제
function htmlRefine(html) {
	html = html.replace(/<link type="text\/css" rel="stylesheet" href="styles\/prettify-tomorrow.css">/gi, "")
					.replace(/<link type="text\/css" rel="stylesheet" href="styles\/jsdoc-default.css">/gi, "")
					.replace(/<script src="scripts/gi, '<script src="' + odfBaseUrl + 'scripts')
					.replace(/href="styles/gi, 'href="' + odfBaseUrl + 'styles')
					.replace(/<pre class="prettyprint source lang-javascript">/gi, '<pre class="line-numbers language-js">')
					.replace(/<nav>/gi, '<nav style="display:none;">')
					.replace("<script> prettyPrint(); </script>", "")
					;
	return html;
}

var lastPageUrl = "";
function loadOdf(menuName, target) {
	var pageUrl = menu.getMenu(menuName).path;
	
	// 같은 페이지인 경우 스크롤
	if(pageUrl == lastPageUrl) {
		
		menu.scrollToId(target, true);
		
	} else {
		
		$.ajax({
			url : encodeURI(pageUrl),
			type : 'GET',
			cache: false,
			async : false,
			success : function(html) {
				
				html = htmlRefine(html);
				$("#map-doc").html(html);
				
				Prism.highlightAll();
				menu.menuSelected(menuName);
				changeTagUrl();
				menu.scrollToId(target, true);
			},
			error : function(xhr, status, err) {
				console.log(xhr);
				console.log(status);
				console.log(err);
			}
		});
	}
	
	lastPageUrl = pageUrl;
}

function changeTagUrl(){
	$("span a").each(function(index, item){
		
		var taghref = $(item).attr("href").split('.html');
		var menu = taghref[0];
		var target = taghref[1] != undefined ? taghref[1].replace('#', '') : "";
		$(item).attr("href", 'javascript:menu.goPage("/widgetdocs", "' + taghref[0] + '","' + target + '");');
	});
}