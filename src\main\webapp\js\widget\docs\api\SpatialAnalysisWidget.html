<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>GeOnPaas ui widgets: SpatialAnalysisWidget</title>
    
      <link type="text/css" rel="stylesheet" href="styles/vendor/prism-tomorrow-night.css">
    
    <link type="text/css" rel="stylesheet" href="styles/styles.css">
    
    
    <style>
      :root {
      
      
        --nav-width: 370px;
      
      
        --nav-heading-margin-top: 0.5em;
      
      }
    </style>
    
</head>
<body>

<header class="layout-header">
  
  <h1>
    <a href="./index.html">
      GeOnPaas ui widgets
    </a>
  </h1>
  <nav class="layout-nav">
    <ul><li class="nav-heading">Classes</li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="AddressSearchWidget.html">AddressSearchWidget</a></span><span class="nav-desc"><p>주소 검색 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="AdministrativeDistrictSearchWidget.html">AdministrativeDistrictSearchWidget</a></span><span class="nav-desc"><p>행정구역 조회 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="AttributeEditorWidget.html">AttributeEditorWidget</a></span><span class="nav-desc"><p>속성 설정 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="BasemapWidget.html">BasemapWidget</a></span><span class="nav-desc"><p>배경지도 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="BookMarkControlWidget.html">BookMarkControlWidget</a></span><span class="nav-desc"><p>북마크컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="CCTVControlWidget.html">CCTVControlWidget</a></span><span class="nav-desc"><p>CCTV컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ChartWidget.html">ChartWidget</a></span><span class="nav-desc"><p>차트 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ClearControlWidget.html">ClearControlWidget</a></span><span class="nav-desc"><p>초기화컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ConditionFilterWidget.html">ConditionFilterWidget</a></span><span class="nav-desc"><p>조건식 편집기 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="CreateLayerWidget.html">CreateLayerWidget</a></span><span class="nav-desc"><p>레이어 생성 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DivideMapWidget.html">DivideMapWidget</a></span><span class="nav-desc"><p>분할지도 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DownloadControlWidget.html">DownloadControlWidget</a></span><span class="nav-desc"><p>다운로드컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DrawControlWidget.html">DrawControlWidget</a></span><span class="nav-desc"><p>그리기컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FeatureAttributeFormWidget.html">FeatureAttributeFormWidget</a></span><span class="nav-desc"><p>피쳐 속성 폼 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FullScreenControlWidget.html">FullScreenControlWidget</a></span><span class="nav-desc"><p>전체화면컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="GeocodingGridWidget.html">GeocodingGridWidget</a></span><span class="nav-desc"><p>지오코딩 그리드 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="GridWidget.html">GridWidget</a></span><span class="nav-desc"><p>속성테이블 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="HomeControlWidget.html">HomeControlWidget</a></span><span class="nav-desc"><p>홈이동컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LabelStyleWidget.html">LabelStyleWidget</a></span><span class="nav-desc"><p>레이어 스타일 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LayerSearchWidget.html">LayerSearchWidget</a></span><span class="nav-desc"><p>레이어검색 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LayerUploadWidget.html">LayerUploadWidget</a></span><span class="nav-desc"><p>레이어업로드 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LegendWidget.html">LegendWidget</a></span><span class="nav-desc"><p>범례 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MeasureControlWidget.html">MeasureControlWidget</a></span><span class="nav-desc"><p>측정컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MousePositionControlWidget.html">MousePositionControlWidget</a></span><span class="nav-desc"><p>현재위치컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MoveControlWidget.html">MoveControlWidget</a></span><span class="nav-desc"><p>이동컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="OneParcelWidget.html">OneParcelWidget</a></span><span class="nav-desc"><p>일필지 조회 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="OverViewMapControlWidget.html">OverViewMapControlWidget</a></span><span class="nav-desc"><p>지도오버뷰컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="PopupSettingsWidget.html">PopupSettingsWidget</a></span><span class="nav-desc"><p>팝업 설정 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="PopupWidget.html">PopupWidget</a></span><span class="nav-desc"><p>지도 위 클릭 시 피쳐 정보 표출 팝업 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="PrintControlWidget.html">PrintControlWidget</a></span><span class="nav-desc"><p>출력컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="RoadViewWidget.html">RoadViewWidget</a></span><span class="nav-desc"><p>로드뷰(카카오) 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="RotationControlWidget.html">RotationControlWidget</a></span><span class="nav-desc"><p>회전컨트롤 위젯 (생성 후 지도 객체에서 Alt + Shift + 지도 객체 Drag로 사용)</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ScaleControlWidget.html">ScaleControlWidget</a></span><span class="nav-desc"><p>축척컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="SpatialAnalysisWidget.html">SpatialAnalysisWidget</a></span><span class="nav-desc"><p>공간분석 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="StyleWidget.html">StyleWidget</a></span><span class="nav-desc"><p>레이어 스타일 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="SwiperControlWidget.html">SwiperControlWidget</a></span><span class="nav-desc"><p>스와이퍼컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="SwiperWidget.html">SwiperWidget</a></span><span class="nav-desc"><p>스와이퍼 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="TimeSliderControlWidget.html">TimeSliderControlWidget</a></span><span class="nav-desc"><p>타임슬라이더컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="TOCWidget.html">TOCWidget</a></span><span class="nav-desc"><p>TOC 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ZoomControlWidget.html">ZoomControlWidget</a></span><span class="nav-desc"><p>지도 확대/축소 조작 위젯</p></span></li></ul><li class="nav-heading"><a href="global.html">Globals</a></li>
  </nav>
</header>


<main class="layout-main ">
  <div class="container">
    <p class="page-kind">Class</p>
    <h1 class="page-title">SpatialAnalysisWidget</h1>
    




<section>


<header class="class">


    
        
        <!-- <h2>SpatialAnalysisWidget</h2> -->

        

        
            <h4 class="method-heading">Summary</h4>
            <div class="class-summary"><p>공간분석 위젯 생성자</p></div>
        

        
            <h4 class="method-heading">Description</h4>
            <div class="class-description"><p>공간분석 위젯</p></div>
        
    
</header>

<article>
    <div class="container-overview">



    
        





    


    
    <h3 class="subtitle">Constructor</h3>
    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="SpatialAnalysisWidget">new SpatialAnalysisWidget<span class="signature">(object)</span><span class="return-type-signature"></span>
    </h4>













    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">object</span>
            

            
                


    <span class="param-type">
        <code>Object</code>
    </span>
    

            

            

            

            <div class="param-description"></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">odf</span>
            

            
                


    <span class="param-type">
        <code>odf</code>
    </span>
    

            

            

            

            <div class="param-description"><p>odf 모듈</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">target</span>
            

            
                


    <span class="param-type">
        <code>HTMLElement</code>
    </span>
    

            

            

            

            <div class="param-description"><p>공간분석 위젯을 생성할 영역</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">options</span>
            

            
                


    <span class="param-type">
        <code>Object</code>
    </span>
    

            

            

            

            <div class="param-description"><p>공간분석 위젯 생성 옵션</p></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">useToolTip</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>분석 위젯 설명 툴팁 표출 여부 (기본값 false)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">publishDirect</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>바로발행 여부 defualt 값(기본값 true)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">detailTarget</span>
            

            
                


    <span class="param-type">
        <code>HTMLElement</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                

                
                </span>
            

            

            <div class="param-description"><p>공간분석 상세정보가 생성될 영역</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">gridTarget</span>
            

            
                


    <span class="param-type">
        <code>HTMLElement</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                

                
                </span>
            

            

            <div class="param-description"><p>분석 결과 grid가 표현될 영역</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">selectAnalysis</span>
            

            
                


    <span class="param-type">
        <code>function</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                

                
                </span>
            

            

            <div class="param-description"><p>분석 선택시 호출 callback Function</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">onClickPublish</span>
            

            
                


    <span class="param-type">
        <code>function</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                

                
                </span>
            

            

            <div class="param-description"><p>발행 버튼 클릭 시 호출 callback Function</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">isUse</span>
            

            
                


    <span class="param-type">
        <code>Object</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>사용여부 옵션</p></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">analysisLayerServiceType</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>발행된 레이어 서비스 타입 선택 옵션 (기본값 wfs) wfs/wms/all</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">publishDirectElement</span>
            

            
                


    <span class="param-type">
        <code>Object</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>바로발행 엘리먼트 사용여부 false인 경우 바로발행 체크여부 요소가 분석화면에 표출되지 않음 (정의하지 않으면 분석에 바로발행 checkbox를 사용하는 것으로 간주)</p></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">ag</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>데이터 요약분석(포인트 집계) 바로발행 체크박스를 사용할지 여부(기본값 true)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">join</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>데이터 요약분석(조인 피처) 바로발행 체크박스를 사용할지 여부(기본값 true)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">nrby</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>데이터 요약분석(주변 요약) 바로발행 체크박스를 사용할지 여부(기본값 true)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">range</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>데이터 요약분석(범위 내 요약) 바로발행 체크박스를 사용할지 여부(기본값 true)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">center</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>데이터 요약분석(중심 및 분산 요약) 바로발행 체크박스를 사용할지 여부(기본값 true)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">searchLegacy</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>위치 찾기 분석(기존 위치 찾기) 바로발행 체크박스를 사용할지 여부(기본값 true)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">searchNew</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>위치 찾기 분석(새 위치 파생 분석) 바로발행 체크박스를 사용할지 여부(기본값 true)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">searchCenter</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>위치 찾기 분석(중심 찾기) 바로발행 체크박스를 사용할지 여부(기본값 true)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">searchSimilar</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>위치 찾기 분석(유사한 위치 찾기) 바로발행 체크박스를 사용할지 여부(기본값 true)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">density</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>공간 패턴 분석(밀도맵) 바로발행 체크박스를 사용할지 여부(기본값 true)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">hotspot</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>공간 패턴 분석(핫스팟 찾기) 바로발행 체크박스를 사용할지 여부(기본값 true)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">gatherPoints</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>공간 패턴 분석(군집 찾기) 바로발행 체크박스를 사용할지 여부(기본값 true)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">searchOutliers</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>공간 패턴 분석(이상치 찾기 분석) 바로발행 체크박스를 사용할지 여부(기본값 true)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">connectDestination</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>근접도 분석(출발지와 목적지 연결) 바로발행 체크박스를 사용할지 여부(기본값 true)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">buffer</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>근접도 분석(버퍼 생성) 바로발행 체크박스를 사용할지 여부(기본값 true)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">drivingArea</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>근접도 분석(운전시간 영역 생성) 바로발행 체크박스를 사용할지 여부(기본값 true)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">findNearestPoint</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>근접도 분석(최근접 위치 찾기) 바로발행 체크박스를 사용할지 여부(기본값 true)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">findPath</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>근접도 분석(경로 계획) 바로발행 체크박스를 사용할지 여부(기본값 true)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">dsslve</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>데이터 관리 분석(경계 디졸브 실행) 바로발행 체크박스를 사용할지 여부(기본값 true)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">dvsion</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>데이터 관리 분석(공간 분할) 바로발행 체크박스를 사용할지 여부(기본값 true)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">merge</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>데이터 관리 분석(레이어 병합) 바로발행 체크박스를 사용할지 여부(기본값 true)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">'ovrlay/erase'</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>데이터 관리 분석(레이어 중첩(지우기)) 바로발행 체크박스를 사용할지 여부(기본값 true)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">'ovrlay/intsct'</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>데이터 관리 분석(레이어 중첩(교차)) 바로발행 체크박스를 사용할지 여부(기본값 true)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">'ovrlay/union'</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>데이터 관리 분석(레이어 중첩(유니온)) 바로발행 체크박스를 사용할지 여부(기본값 true)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">clustering</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>데이터 관리 분석(클러스터링) 바로발행 체크박스를 사용할지 여부(기본값 true)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">ar</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>데이터 관리 분석(면적 계산) 바로발행 체크박스를 사용할지 여부(기본값 true)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">lt</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>데이터 관리 분석(길이 계산) 바로발행 체크박스를 사용할지 여부(기본값 true)</p></div>
            
        </li>

    
</ul>
            
        </li>

    
</ul>
            
        </li>

    

        <li>
            
                <span class="param-name">validationCheck</span>
            

            
                


    <span class="param-type">
        <code>Object</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>분석위젯 내에서 사용하는 유효성검사 정의</p></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">publishDirect</span>
            

            
                


    <span class="param-type">
        <code>function</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>바로 발행 유효성 검사</p></div>
            
        </li>

    
</ul>
            
        </li>

    

        <li>
            
                <span class="param-name">spatialAnalysis</span>
            

            
                


    <span class="param-type">
        <code>Object</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>어떤 분석을 사용할지 정의</p>
<ul>
<li>정의하지 않으면 모든 분석 사용하는 것으로 간주</li>
</ul></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">ag</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>데이터 요약분석(포인트 집계)를 사용할지 여부(기본값 false)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">join</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>데이터 요약분석(조인 피처)를 사용할지 여부(기본값 false)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">nrby</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>데이터 요약분석(주변 요약)를 사용할지 여부(기본값 false)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">range</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>데이터 요약분석(범위 내 요약)를 사용할지 여부(기본값 false)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">center</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>데이터 요약분석(중심 및 분산 요약)를 사용할지 여부(기본값 false)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">searchLegacy</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>위치 찾기 분석(기존 위치 찾기)를 사용할지 여부(기본값 false)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">searchNew</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>위치 찾기 분석(새 위치 파생 분석)를 사용할지 여부(기본값 false)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">searchCenter</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>위치 찾기 분석(중심 찾기)를 사용할지 여부(기본값 false)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">searchSimilar</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>위치 찾기 분석(유사한 위치 찾기)를 사용할지 여부(기본값 false)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">density</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>공간 패턴 분석(밀도맵)를 사용할지 여부(기본값 false)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">hotspot</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>공간 패턴 분석(핫스팟 찾기)를 사용할지 여부(기본값 false)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">gatherPoints</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>공간 패턴 분석(군집 찾기)를 사용할지 여부(기본값 false)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">interpolatePoints</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>공간 패턴 분석(내삽 기능)를 사용할지 여부(기본값 false)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">searchOutliers</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>공간 패턴 분석(이상치 찾기 분석)를 사용할지 여부(기본값 false)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">connectDestination</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>근접도 분석(출발지와 목적지 연결)를 사용할지 여부(기본값 false)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">buffer</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>근접도 분석(버퍼 생성)를 사용할지 여부(기본값 false)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">drivingArea</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>근접도 분석(운전시간 영역 생성)를 사용할지 여부(기본값 false)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">findNearestPoint</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>근접도 분석(최근접 위치 찾기)를 사용할지 여부(기본값 false)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">findPath</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>근접도 분석(경로 계획)를 사용할지 여부(기본값 false)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">dsslve</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>데이터 관리 분석(경계 디졸브 실행)를 사용할지 여부(기본값 false)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">extrc</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>데이터 관리 분석(데이터 추출 기능)를 사용할지 여부(기본값 false)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">dvsion</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>데이터 관리 분석(공간 분할)를 사용할지 여부(기본값 false)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">merge</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>데이터 관리 분석(레이어 병합)를 사용할지 여부(기본값 false)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">'ovrlay/erase'</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>데이터 관리 분석(레이어 중첩(지우기))를 사용할지 여부(기본값 false)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">'ovrlay/intsct'</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>데이터 관리 분석(레이어 중첩(교차))를 사용할지 여부(기본값 false)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">'ovrlay/union'</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>데이터 관리 분석(레이어 중첩(유니온))를 사용할지 여부(기본값 false)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">clustering</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>데이터 관리 분석(클러스터링)를 사용할지 여부(기본값 false)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">ar</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>데이터 관리 분석(면적 계산)를 사용할지 여부(기본값 false)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">lt</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>데이터 관리 분석(길이 계산)를 사용할지 여부(기본값 false)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">file</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>좌표변환(파일 좌표 변환)를 사용할지 여부(기본값 false)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">single</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>좌표변환(단일 좌표 및 도분초 변환)를 사용할지 여부(기본값 false)</p></div>
            
        </li>

    
</ul>
            
        </li>

    
</ul>
            
        </li>

    

        <li>
            
                <span class="param-name">api</span>
            

            
                


    <span class="param-type">
        <code>Object</code>
    </span>
    

            

            

            

            <div class="param-description"><p>공간분석 위젯에서 사용할 api 정보</p></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">publishGeojsonLayer</span>
            

            
                


    <span class="param-type">
        <code>function</code>
    </span>
    |

    <span class="param-type">
        <code>UploadApi.publishGeojsonLayer</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                

                
                </span>
            

            

            <div class="param-description"><p>레이어 업로드 api</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">addressSearch</span>
            

            
                


    <span class="param-type">
        <code>function</code>
    </span>
    |

    <span class="param-type">
        <code>AddressApi.intSearch</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                

                
                </span>
            

            

            <div class="param-description"><p>주소 검색 api</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">ctpvAdministrativeDistrictSearch</span>
            

            
                


    <span class="param-type">
        <code>function</code>
    </span>
    |

    <span class="param-type">
        <code>AdministApi.ctpvAdministrativeDistrictSearch</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>시도 목록 조회 api (데이터 추출 분석 이용시 필수 값)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">sggAdministrativeDistrictSearch</span>
            

            
                


    <span class="param-type">
        <code>function</code>
    </span>
    |

    <span class="param-type">
        <code>AdministApi.sggAdministrativeDistrictSearch</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>시군구 목록 조회 api (데이터 추출 분석 이용시 필수 값)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">runAnalysis</span>
            

            
                


    <span class="param-type">
        <code>function</code>
    </span>
    |

    <span class="param-type">
        <code>AnalysisApi.runAnalysis</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>공간분석 api (좌표변환만 사용하는 경우 필수값 x)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">convertCoord</span>
            

            
                


    <span class="param-type">
        <code>function</code>
    </span>
    |

    <span class="param-type">
        <code>CoordApi.convertCoord</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>좌표변환 관련 api (좌표변환 사용시 필수값)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">getLayerList</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#oui_spatialAnalysisWidget_getLayerList">oui_spatialAnalysisWidget_getLayerList</a></code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>레이어 목록 조회 api (사용하는 분석에서 레이어 목록 조회를 이용하는 경우 필수)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">selectColumn</span>
            

            
                


    <span class="param-type">
        <code>function</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#oui_columnInfoApi_selectColumn">oui_columnInfoApi_selectColumn</a></code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>특정 레이어의 필드 목록 조회 api(사용하는 분석에서 필드 목록 조회를 이용하는 경우 필수)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">selectFilter</span>
            

            
                


    <span class="param-type">
        <code>function</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#oui_cqlInfoApi_selectFilter">oui_cqlInfoApi_selectFilter</a></code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>특정 레이어의 필터 정보 조회 api(사용하는 분석에서 레이어 목록 조회를 이용하는 경우 필수)</p></div>
            
        </li>

    
</ul>
            
        </li>

    
</ul>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>






    <h4 class="method-heading">Example</h4>
    
    <pre><code class="language-js">//00.01. 레이어 업로드 api 객체  생성
let uploadApi = oui.UploadApi(oui.HttpClient({
  baseURL: '[api 경로]',
}), {
  //비동기 작업 결과 리턴
  sendOpertNtcnInfo: ({ opertNtcnSn, lyrNm, lyrDc}) => {
    console.log(opertNtcnSn, layerName, layerDescription);
  },
  userId: '[사용자 id]'
});
//00.02. 주소 검색 api 객체 생성
let addressApi = oui.AddressApi(oui.HttpClient({
  baseURL: '[api 경로]',
}), {
  projection: '5186',
});
//00.03. 주소 검색 api 객체 생성
let administApi = oui.AdministApi(oui.HttpClient({
  baseURL: '[api 경로]',
}), {
  projection: '5186',//사용 좌표계
});
//00.04. 분석 api 객체 생성
let analysisApi = oui.AnalysisApi(oui.HttpClient({
  baseURL: '[api 경로]',
}), {
  projection: '5186',
  //비동기 작업 결과 리턴
  sendOpertNtcnInfo: ({ opertNtcnSn, lyrNm, lyrDc}) => {
    console.log(opertNtcnSn, layerName, layerDescription);
  }
});
//00.05. 좌표변환 api 객체 생성
let coordApi = oui.CoordApi(oui.HttpClient({
  baseURL: '[api 경로]',
}));
//00.06. 컬럼 정보 api 객체 생성
let columnInfoApi = oui.ColumnInfoApi(oui.HttpClient({
  baseURL: '[api 경로]',
}), {
  userId: [사용자id]
});
//00.07. 레이어 필터 정보 조회 api 객체 생성
let cqlInfoApi = oui.CqlInfoApi(oui.HttpClient({
  baseURL: '[api 경로]',
}), {
  userMapId: [웹맵id],
  userId: [사용자id]
});


//01. 분석 위젯 생성
let spatialAnalysisWidget = new oui.SpatialAnalysisWidget({
    odf,
    target: document.querySelector('#analysis_target'),
    options: {
      //바로발행 디폴트값
      publishDirect: false,
      //분석 상세 정보를 표현할 element
      detailTarget: document.querySelector('#analysis_detail_target'),
      //분석 결과 grid를 표출할 element
      gridTarget: document.querySelector('.odf-map'),
      //분석 선택시 호출 callback
      selectAnalysis: fn_selectAnalysis,
      //발행 클릭 시 호출 callback
      onClickPublish : function(){
        callLoadingBar({ status: true, message: "레이어를 업로드중입니다" });
      },
      isUse : { //사용여부
        publishDirectElement : {
          ag: false,//데이터 요약분석 - 포인트 집계
          join: false,//데이터 요약분석 - 조인 피처
          nrby: false,//데이터 요약분석 - 주변 요약
          range: false,//데이터 요약분석 - 범위 내 요약
          center: false,//데이터 요약분석 - 중심 및 분산 요약
          searchLegacy: false,//위치 찾기 분석 - 기존 위치 찾기
          searchNew: false,//위치 찾기 분석 - 새 위치 파생 분석
          searchCenter: false,//위치 찾기 분석 - 중심 찾기
          searchSimilar: false,//위치 찾기 분석 - 유사한 위치 찾기
          density: false,//공간 패턴 분석 - 밀도맵
          hotspot: false,//공간 패턴 분석 - 핫스팟 찾기
          gatherPoints: false,//공간 패턴 분석 - 군집 찾기
          searchOutliers: false,//공간 패턴 분석 - 이상치 찾기 분석
          connectDestination: false,//근접도 분석 - 출발지와 목적지 연결
          buffer: false,//근접도 분석 - 버퍼 생성
          drivingArea: false,//근접도 분석 - 운전시간 영역 생성
          findNearestPoint: false,//근접도 분석 - 최근접 위치 찾기
          findPath: false,//근접도 분석 - 경로 계획
          dsslve: false,//데이터 관리 분석 - 경계 디졸브 실행
          dvsion: false,//데이터 관리 분석 - 공간 분할
          merge: false,//데이터 관리 분석 - 레이어 병합
          'ovrlay/erase': false,//데이터 관리 분석 - 레이어 중첩(지우기)
          'ovrlay/intsct': false,//데이터 관리 분석 - 레이어 중첩(교차)
          'ovrlay/union': false,//데이터 관리 분석 - 레이어 중첩(유니온)
          clustering: false,//데이터 관리 분석 - 클러스터링
          ar: false,//데이터 관리 분석 - 면적 계산
          lt: false,//데이터 관리 분석 - 길이 계산
        } //바로발행 선택박스 사용여부 false인 경우 바로발행 체크여부 요소가 분석화면에 표출되지 않음
          //파일 좌표변환/ 단일 좌표변환 / 데이터 추출 은 사용불가 (발행 자체가 없음)
          //포인트 내삽도 사용불가 (무조건 발행해야함)
          //analysisList 기본값은 '전체'
          //publishDirectElement 기본값은 전부 바로발행 선택박스 '사용'

      }
      }
      //알림 옵션
      alertList: {
        startLoadingBar: (message) => {
          console.log(message);
        },
        endLoadingBar: (message) => {
          console.log(message);
        },
        customAlert: (message) => {
          alert(message);
        }
      },
      //분석 사용 여부 정의
      spatialAnalysis: {
        ag: false,//데이터 요약분석 - 포인트 집계
        join: false,//데이터 요약분석 - 조인 피처
        nrby: false,//데이터 요약분석 - 주변 요약
        range: true,//데이터 요약분석 - 범위 내 요약
        center: false,//데이터 요약분석 - 중심 및 분산 요약
        searchLegacy: false,//위치 찾기 분석 - 기존 위치 찾기
        searchNew: false,//위치 찾기 분석 - 새 위치 파생 분석
        searchCenter: false,//위치 찾기 분석 - 중심 찾기
        searchSimilar: false,//위치 찾기 분석 - 유사한 위치 찾기
        density: false,//공간 패턴 분석 - 밀도맵
        hotspot: false,//공간 패턴 분석 - 핫스팟 찾기
        gatherPoints: false,//공간 패턴 분석 - 군집 찾기
        interpolatePoints: false,//공간 패턴 분석 - 내삽 기능
        searchOutliers: false,//공간 패턴 분석 - 이상치 찾기 분석
        connectDestination: false,//근접도 분석 - 출발지와 목적지 연결
        buffer: false,//근접도 분석 - 버퍼 생성
        drivingArea: false,//근접도 분석 - 운전시간 영역 생성
        findNearestPoint: false,//근접도 분석 - 최근접 위치 찾기
        findPath: false,//근접도 분석 - 경로 계획
        dsslve: false,//데이터 관리 분석 - 경계 디졸브 실행
        extrc: false,//데이터 관리 분석 - 데이터 추출 기능
        dvsion: false,//데이터 관리 분석 - 공간 분할
        merge: false,//데이터 관리 분석 - 레이어 병합
        'ovrlay/erase': false,//데이터 관리 분석 - 레이어 중첩(지우기)
        'ovrlay/intsct': false,//데이터 관리 분석 - 레이어 중첩(교차)
        'ovrlay/union': false,//데이터 관리 분석 - 레이어 중첩(유니온)
        clustering: false,//데이터 관리 분석 - 클러스터링
        ar: false,//데이터 관리 분석 - 면적 계산
        lt: false,//데이터 관리 분석 - 길이 계산
        file: false,//좌표변환 - 파일 좌표 변환
        single: false//좌표변환 - 단일 좌표 및 도분초 변환
      }
    },
    api:
    {
      //레이어 업로드 api
      publishGeojsonLayer: uploadApi.publishGeojsonLayer,
      //주소 검색 api
      addressSearch: addressApi.intSearchFunction,
      //시도 목록 조회 function
      ctpvAdministrativeDistrictSearch: administApi.ctpvAdministrativeDistrictSearch,
      //시군구 목록 조회 function
      sggAdministrativeDistrictSearch: administApi.sggAdministrativeDistrictSearch,
      //공간분석 관련 api 사용
      runAnalysis: analysisApi.runAnalysis,
      //좌표변환 관련 api 사용
      convertCoord: coordApi.convertCoord,
      //특정 레이어의 필드 목록 조회
      selectColumn : columnInfoApi.selectColumn,
      //filter 정보 조회
      selectFilter: cqlInfoApi.selectFilter,
      //특정 레이어 목록 조회
      getLayerList: (params, callback) => {
        //01. 레이어 목록 조회

        //02. 벡터레이어이면서 클러스터레이어가 아닌 레이어 추출

        //03. params.geometryType이 정의되있다면 허용한 geometry 타입만 추출

        //04. callback으로 결과 레이어 반환
        callback(result);
      },
    }
  });
});</code></pre>
















    
    </div>

    

    

     

    

    


    

    
        <h3 class="subtitle">Methods</h3>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="addTo">addTo<span class="signature">(map)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>분석 위젯을 지도 객체와 연결 후 렌더링</p>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">map</span>
            

            
                


    <span class="param-type">
        <code>odf.Map</code>
    </span>
    

            

            

            

            <div class="param-description"><p>연결할 지도 객체</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>






    <h4 class="method-heading">Example</h4>
    
    <pre><code class="language-js">//01. 지도 객체 생성
  let map = new odf.Map(document.getElementById('map'),{
    ...
  });

  //02. 분석위젯 생성
  let spatialAnalysisWidget = new oui.SpatialAnalysisWidget({
      odf,
      target: document.querySelector('#analysis_target'),
      options: {...},
      api : {...},
  };

  //03. 분석위젯을 지도객체와 연결 및 렌더링
  spatialAnalysisWidget.addTo(map);</code></pre>
















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="remove">remove<span class="signature">()</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>분석 위젯을 지도 객체와 연결 해제 및 렌더링 해제</p>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>






    <h4 class="method-heading">Example</h4>
    
    <pre><code class="language-js">//01. 지도 객체 생성
  let map = new odf.Map(document.getElementById('map'),{
    ...
  });

  //02. 분석위젯 생성
  let spatialAnalysisWidget = new oui.SpatialAnalysisWidget({
      odf,
      target: document.querySelector('#analysis_target'),
      options: {...},
      api : {...},
  };

  //03. 분석위젯을 지도객체와 연결 및 렌더링
  spatialAnalysisWidget.addTo(map);

  //04. 분석위젯을 지도객체와 연결 해제 및 렌더링 해제
  spatialAnalysisWidget.remove();</code></pre>
















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="removeGrid">removeGrid<span class="signature">()</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>분석 위젯의 속성테이블 제거</p>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>






    <h4 class="method-heading">Example</h4>
    
    <pre><code class="language-js">//01. 지도 객체 생성
let map = new odf.Map(document.getElementById('map'),{
...
});

//02. 분석위젯 생성
let spatialAnalysisWidget = new oui.SpatialAnalysisWidget({
  odf,
  target: document.querySelector('#analysis_target'),
  options: {...},
  api : {...},
};

//03. 분석위젯을 지도객체와 연결 및 렌더링
spatialAnalysisWidget.addTo(map);

//04. 분석위젯의 속성테이블을 제거
spatialAnalysisWidget.removeGrid();</code></pre>
















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="removeTempAnalysisLayer">removeTempAnalysisLayer<span class="signature">()</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>분석 임시레이어(발행X) 제거</p>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>






    <h4 class="method-heading">Example</h4>
    
    <pre><code class="language-js">//01. 지도 객체 생성
  let map = new odf.Map(document.getElementById('map'),{
    ...
  });

  //02. 분석위젯 생성
  let spatialAnalysisWidget = new oui.SpatialAnalysisWidget({
      odf,
      target: document.querySelector('#analysis_target'),
      options: {...},
      api : {...},
  };

  //03. 분석위젯을 지도객체와 연결 및 렌더링
  spatialAnalysisWidget.addTo(map);

  //04.  분석 임시레이어(발행X) 제거
  spatialAnalysisWidget.removeTempAnalysisLayer();</code></pre>
















    </article>

        
    

    

    
</article>

</section>




  </div>
</main>

<footer class="layout-footer">
  <div class="container">
    Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.11</a> on Mon Dec 09 2024 10:44:32 GMT+0900 (대한민국 표준시)
  </div>
</footer>



<script src="scripts/prism.dev.js"></script>
</body>
</html>