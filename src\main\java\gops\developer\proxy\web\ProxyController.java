package gops.developer.proxy.web;

import java.io.BufferedOutputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.net.HttpURLConnection;
import java.net.URISyntaxException;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import egovframework.com.cmm.service.EgovProperties;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Controller
public class ProxyController {

	private final String contextPath = EgovProperties.getProperty("Url.ContextPath");
	private final String apiGwUrl = EgovProperties.getProperty("Url.APIGW");
	private final String apiKey = EgovProperties.getProperty("APIGW.Apikey");
	private final String odfServerUrl = EgovProperties.getProperty("Url.ODF");
	private final String typeDocs = EgovProperties.getProperty("Type.DOCS");

	private final String charSet = "UTF-8";
	private final String boundary = "^-----^";
	private final String LINE_FEED = "\r\n";
	private final String TWO_HYPEN = "--";
	private final String tail = LINE_FEED + TWO_HYPEN + boundary + TWO_HYPEN + LINE_FEED;

	@RequestMapping(value = "/proxy/api/**")
	@ResponseBody
	public ResponseEntity<byte[]> apiProxy(HttpServletRequest request, HttpServletResponse response, MultipartFile file)
			throws URISyntaxException, IOException {
		String apiUrl;
		if(typeDocs.equals("NH")) {
			 apiUrl = request.getRequestURI().replaceAll(contextPath +"/proxy/api/", ""); //농협

		}else {
			 apiUrl = request.getRequestURI().replaceAll(contextPath +"/proxy/api", "");
		}
//		String queryString = request.getQueryString();
//		queryString = StringUtils.isEmpty(queryString) ? "/" : "/?" + queryString;
		HttpMethod method = HttpMethod.valueOf(request.getMethod().toUpperCase());
		String openAPIPath = "/v3/api-docs";
		String callUrl;

		callUrl = apiGwUrl + apiUrl + openAPIPath;

		URL url = new URL(callUrl);
		HttpURLConnection conn = (HttpURLConnection) url.openConnection();
		conn.setRequestMethod(request.getMethod().toUpperCase());
		conn.setDoOutput(true);
		conn.setUseCaches(false);
		conn.setConnectTimeout(1000 * 60 * 5);

		if (method == HttpMethod.POST || method == HttpMethod.PUT) {

			Map<String, String[]> paramMap = request.getParameterMap();
			byte[] postDataBytes = null;
			if (file == null || file.getSize() == 0 || StringUtils.isEmpty(file.getName())) {

				StringBuilder paramBuilder = new StringBuilder();
				for (Map.Entry<String, String[]> param : paramMap.entrySet()) {

					String[] val = param.getValue();
					if(val !=null && val.length >0) {
						for(int i=0; i<val.length; i++) {
							if (paramBuilder.length() != 0) {
								paramBuilder.append("&");
							}
							paramBuilder.append(URLEncoder.encode(param.getKey(), charSet));
							paramBuilder.append("=");
							paramBuilder.append(URLEncoder.encode(val[i], charSet));
						}
					}
				}
				postDataBytes = paramBuilder.toString().getBytes();
				conn.setRequestProperty("Content-type", "application/x-www-form-urlencoded");
				conn.setRequestProperty("Content-Length", String.valueOf(postDataBytes.length));
				conn.getOutputStream().write(postDataBytes);

			} else {

				String paramsPart = "";
				String filePart = "";
				long fileLength = 0;

				ArrayList<String> paramHeaders = new ArrayList<>();
				for (Map.Entry<String, String[]> entry : paramMap.entrySet()) {

					String[] valArray = entry.getValue();
					if(valArray !=null && valArray.length >0) {
						for(int i=0; i<valArray.length; i++) {

							String param = TWO_HYPEN + boundary + LINE_FEED + "Content-Disposition: form-data; name=\""
									+ entry.getKey() + "\"" + LINE_FEED + "Content-Type: text/plain; charset=" + charSet
									+ LINE_FEED + LINE_FEED + valArray[i] + LINE_FEED;
							paramsPart += param;
							paramHeaders.add(param);
						}
					}
				}

				byte[] fileBytes = file.getBytes();

				String ContentType = URLConnection.guessContentTypeFromName(file.getName());
				ContentType = StringUtils.isEmpty(ContentType) ? "" : ContentType;
				String fileHeader = TWO_HYPEN + boundary + LINE_FEED + "Content-Disposition: attachment; name=\""
						+ file.getName() + "\"; filename=\"" + file.getOriginalFilename() + "\"" + LINE_FEED
						+ "Content-Type: " + ContentType + LINE_FEED
						+ "Content-Transfer-Encoding: binary" + LINE_FEED + LINE_FEED;
				fileLength += fileBytes.length + LINE_FEED.getBytes(charSet).length;
				filePart += fileHeader;

				String partData = paramsPart + filePart;

				long requestLength = partData.getBytes(charSet).length + fileLength + tail.getBytes(charSet).length;
		        conn.setRequestProperty("Content-length", "" + requestLength);
		        conn.setFixedLengthStreamingMode((int) requestLength);
		        conn.setRequestProperty("Content-type", "multipart/form-data; charset=utf-8; boundary=" + boundary);

		        OutputStream outputStream = new BufferedOutputStream(conn.getOutputStream());
		        PrintWriter writer = new PrintWriter(new OutputStreamWriter(outputStream, charSet), true);

		        for (int i = 0; i < paramHeaders.size(); i++) {
		            writer.append(paramHeaders.get(i));
		            writer.flush();
		        }

	            writer.append(fileHeader);
	            writer.flush();

	            outputStream.write(fileBytes);
	            outputStream.write(LINE_FEED.getBytes());
	            outputStream.flush();

		        writer.append(tail);
		        writer.flush();
		        writer.close();
			}

		}

		int responseCode = conn.getResponseCode();
		InputStream is;
		if (responseCode >= 200 && responseCode <= 300) {
			is = conn.getInputStream();
		} else {
			is = conn.getErrorStream();
		}

		MultiValueMap<String, String> responseHeader = new LinkedMultiValueMap<>();
		for(Map.Entry<String, List<String>> entry : conn.getHeaderFields().entrySet()) {
			String key = entry.getKey();
			List<String> valList = entry.getValue();
			String val = valList != null && valList.size() > 0 ? valList.get(0) : "";
			if(!StringUtils.isEmpty(key) && !StringUtils.isEmpty(val) && !key.equals(HttpHeaders.TRANSFER_ENCODING)) {
				responseHeader.add(key, val);
			}
		}
		ResponseEntity<byte[]> responseEntity = new ResponseEntity<>(HttpStatus.valueOf(responseCode));

		if (is != null) {
			byte[] responseBytes = streamToByteArray(is);
			responseEntity = new ResponseEntity<byte[]>(responseBytes, responseHeader, HttpStatus.valueOf(responseCode));
		}

		return responseEntity;
	}

	@GetMapping(value = "/proxy/mtb/**")
	@ResponseBody
	public ResponseEntity<byte[]> odfProxy(HttpServletRequest request, HttpServletResponse response)
			throws IOException {

		String odfUrl = request.getRequestURI().replaceAll("/proxy/mtb", "");
		String callUrl = odfServerUrl + odfUrl + "?crtfckey=" + apiKey;
		//String callUrl = odfServerUrl + odfUrl;

		URL url = new URL(callUrl);
		HttpURLConnection conn = (HttpURLConnection) url.openConnection();
		conn.setRequestMethod("GET");

		int responseCode = conn.getResponseCode();
		InputStream is;
		if (responseCode >= 200 && responseCode <= 300) {
			is = conn.getInputStream();
		} else {
			is = conn.getErrorStream();
		}

		ResponseEntity<byte[]> responseEntity = new ResponseEntity<>(HttpStatus.valueOf(responseCode));

		if (is != null) {
			byte[] responseBytes = streamToByteArray(is);
			responseEntity = new ResponseEntity<byte[]>(responseBytes, HttpStatus.valueOf(responseCode));
		}

		return responseEntity;
	}

	private byte[] streamToByteArray(InputStream inputStream) throws IOException {

		byte[] buffer = new byte[1024];
		ByteArrayOutputStream os = new ByteArrayOutputStream();

		int line = 0;
		while ((line = inputStream.read(buffer)) != -1) {
			os.write(buffer, 0, line);
		}
		inputStream.close();
		os.flush();
		os.close();

		return os.toByteArray();
	}

}
