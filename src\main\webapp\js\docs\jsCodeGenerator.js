var jsCodeGenerator = {

		getTemplate :  '\n\
	/* Javascript 샘플 코드 */\n\
	function run() {\n\
			\n\
		var xhr = new XMLHttpRequest();\n\
		var url = "::URL::";\n\
		var queryParams = "?";\n\
::PARAMS::\n\
		xhr.open("GET", url + queryParams);\n\
		xhr.onreadystatechange = function() {\n\
			if(this.readyState == 4) {\n\
				console.log("Status:" + this.status);\n\
				console.log("nHeaders:" + JSON.stringify(this.getAllResponseHeaders()));\n\
				console.log("nBody:" + this.responseText);\n\
				\n\
		        var response = "Status:" + this.status + "<br/>"\n\
						            + "nHeaders:" + JSON.stringify(this.getAllResponseHeaders()) + "<br/>"\n\
						            + "nBody:" + this.responseText;\n\
		        document.getElementById("response").innerHTML = response;\n\
			\n\
				// 결과 파일이 있는 경우 다운로드\n\
				var contentdisposition = this.getResponseHeader("content-disposition");\n\
				if(contentdisposition != undefined && contentdisposition != "") {\n\
					\n\
					var fileName = contentdisposition.substring(contentdisposition.indexOf("=")+1);\n\
					var blob = new Blob([this.response]);\n\
					var a = document.createElement("a");\n\
					a.text = "결과 다운로드";\n\
					a.href = (window.URL || window.webkitURL).createObjectURL(blob);\n\
					a.download = fileName;\n\
					document.getElementById("downlink").appendChild(a);\n\
				}\n\
			}\n\
		}\n\
		\n\
		xhr.send("");\n\
	}',

		postTemplate :  '\n\
	/* Javascript 샘플 코드 */\n\
	function run() {\n\
			\n\
		var xhr = new XMLHttpRequest();\n\
		var url = "::URL::";\n\
		var queryParams = "";\n\
::PARAMS::\n\
		xhr.open("POST", url);\n\
		xhr.onreadystatechange = function() {\n\
			if(this.readyState == 4) {\n\
				console.log("Status:" + this.status);\n\
				console.log("nHeaders:" + JSON.stringify(this.getAllResponseHeaders()));\n\
				console.log("nBody:" + this.responseText);\n\
		        var response = "Status:" + this.status + "<br/>"\n\
					            + "nHeaders:" + JSON.stringify(this.getAllResponseHeaders()) + "<br/>"\n\
					            + "nBody:" + this.responseText;\n\
		        document.getElementById("response").innerHTML = response;\n\
			\n\
				// 결과 파일이 있는 경우 다운로드\n\
				var contentdisposition = this.getResponseHeader("content-disposition");\n\
				if(contentdisposition != undefined && contentdisposition != "") {\n\
					\n\
					var fileName = contentdisposition.substring(contentdisposition.indexOf("=")+1);\n\
					var blob = new Blob([this.response]);\n\
					var a = document.createElement("a");\n\
					a.text = "결과 다운로드";\n\
					a.href = (window.URL || window.webkitURL).createObjectURL(blob);\n\
					a.download = fileName;\n\
					document.getElementById("downlink").appendChild(a);\n\
				}\n\
			}\n\
		}\n\
				\n\
	    xhr.setRequestHeader("content-Type", "application/x-www-form-urlencoded");\n\
		xhr.send(queryParams);\n\
	}',

putTemplate :  '\n\
	/* Javascript 샘플 코드 */\n\
	function run() {\n\
		\n\
		var xhr = new XMLHttpRequest();\n\
		var url = "::URL::";\n\
		var queryParams = "";\n\
::PARAMS::\n\
		xhr.open("PUT", url);\n\
		xhr.onreadystatechange = function() {\n\
			if(this.readyState == 4) {\n\
				console.log("Status:" + this.status);\n\
				console.log("nHeaders:" + JSON.stringify(this.getAllResponseHeaders()));\n\
				console.log("nBody:" + this.responseText);\n\
		\n\
		        var response = "Status:" + this.status + "<br/>"\n\
					            + "nHeaders:" + JSON.stringify(this.getAllResponseHeaders()) + "<br/>"\n\
					            + "nBody:" + this.responseText;\n\
				document.getElementById("response").innerHTML = response;\n\
		\n\
				// 결과 파일이 있는 경우 다운로드\n\
				var contentdisposition = this.getResponseHeader("content-disposition");\n\
				if(contentdisposition != undefined && contentdisposition != "") {\n\
					\n\
					var fileName = contentdisposition.substring(contentdisposition.indexOf("=")+1);\n\
					var blob = new Blob([this.response]);\n\
					var a = document.createElement("a");\n\
					a.text = "결과 다운로드";\n\
					a.href = (window.URL || window.webkitURL).createObjectURL(blob);\n\
					a.download = fileName;\n\
					document.getElementById("downlink").appendChild(a);\n\
				}\n\
			}\n\
		}\n\
		\n\
	    xhr.setRequestHeader("content-Type", "application/x-www-form-urlencoded");\n\
		xhr.send(queryParams);\n\
	}',

deleteTemplate :  '\n\
	/* Javascript 샘플 코드 */\n\
	function run() {\n\
		\n\
		var xhr = new XMLHttpRequest();\n\
		var url = "::URL::";\n\
		var queryParams = "?";\n\
::PARAMS::\n\
		xhr.open("DELETE", url + queryParams);\n\
		xhr.onreadystatechange = function() {\n\
			if(this.readyState == 4) {\n\
				console.log("Status:" + this.status);\n\
				console.log("nHeaders:" + JSON.stringify(this.getAllResponseHeaders()));\n\
				console.log("nBody:" + this.responseText);\n\
		\n\
		        var response = "Status:" + this.status + "<br/>"\n\
			                    + "nHeaders:" + JSON.stringify(this.getAllResponseHeaders()) + "<br/>"\n\
			                    + "nBody:" + this.responseText;\n\
		        document.getElementById("response").innerHTML = response;\n\
			}\n\
		}\n\
		\n\
		xhr.send("");\n\
	}',

		paramTemplate : "		::COMMENT::queryParams += '::SPCHAR::' + encodeURIComponent('::KEY::') + '=' + encodeURIComponent('::VALUE::');	// ::DESCRIPTION::\n",
		paramJsonTemplate : "		::COMMENT::queryParams += '::SPCHAR::' + encodeURIComponent('::KEY::') + '=' + encodeURIComponent(JSON.stringify(::VALUE::));	// ::DESCRIPTION::\n",

		markupTemplate : '\n\
<!DOCTYPE HTML>\n\
<html>\n\
<head>\n\
	<meta charset="utf-8">\n\
	<title>Test</title>\n\
</head>\n\
<body>\n\
	<div id="run"><a href="javascript:run();">실행</a></div>\n\
	<div id="downlink"></div>\n\
	<div id="response"></div>\n\
</body>\n\
<script>\n\
::CODE::\n\
</script>\n\
</html>',

		makeCode : function(apiUrl, apiKey, apiSpec) {
			//
			var url 			= jsCodeGenerator.makeUrl(apiUrl, apiSpec);
			var paramCode	= jsCodeGenerator.makeParam(apiKey, apiSpec);
			var template		= jsCodeGenerator.setTemplate(apiKey);
			var code			= template.replace("::URL::", url).replace("::PARAMS::", paramCode);
			var markup		= jsCodeGenerator.markupTemplate.replace("::CODE::", code).replace(/\</gi, "&lt;").replace(/\>/gi, "&gt;");;

			return markup;
		},

		makeUrl: function(apiUrl, apiSpec) {

			var url = apiUrl;

			if(apiSpec.parameters != null) {

				$.each(apiSpec.parameters, function(index, param) {

					if(param.name != null && param.in == "path") {

						url = url.replace("{" + param.name + "}", util.nullCheck(param.default));
					}
				});
			}

			return url;
		},

		makeParam: function (apiKey, apiSpec) {

			var paramCode = "";
			if(apiSpec.parameters != null) {

				var isFirst = true;
				$.each(apiSpec.parameters, function(index, param) {

					if(param.name != null && param.in == "query") {

						var description = param.description ? param.description.replace(/\r\n/gi, ' - ') : '';
						var value = util.nullCheck(param.example) != "" ? util.nullCheck(param.example) : util.nullCheck(param.default);
						value = value.toString().replace(/\r\n/gi, " ");
						var template = util.isJsonString(value) ? jsCodeGenerator.paramJsonTemplate : jsCodeGenerator.paramTemplate;
						var comment = util.nullCheck(value) == "" ? "//" : "";

						paramCode += template
											.replace("::COMMENT::", comment)
											.replace("::SPCHAR::", isFirst ? "" : "&")
											.replace("::KEY::", param.name)
											.replace("::VALUE::", value)
											.replace("::DESCRIPTION::", util.nullCheck(description) );

						isFirst = isFirst && comment == "//" ? true : false;
					} else if(param.name != null && param.in == "body") {

						if(param.schema != undefined) {

							var description = param.description ? param.description.replace(/\r\n/gi, ' - ') : '';
							var value = jsonRefParser.refParse(param.schema);
							value = JSON.stringify(value);
							value = value.toString().replace(/\"/gi, "'").replace(/\r\n/gi, " ");
							var template = util.isJsonString(value) ? jsCodeGenerator.paramJsonTemplate : jsCodeGenerator.paramTemplate;
							var comment = util.nullCheck(value) == "" ? "//" : "";

							paramCode += template
											.replace("::COMMENT::", comment)
											.replace("::SPCHAR::", isFirst ? "" : "&")
											.replace("::KEY::", param.name)
											.replace("::VALUE::", value)
											.replace("::DESCRIPTION::", util.nullCheck(description) );

							isFirst = isFirst && comment == "//" ? true : false;
						}
					}
				});
			}

			return paramCode;
		},

		setTemplate: function(apiKey) {

			var template = jsCodeGenerator.getTemplate;
			switch(apiKey) {
				case "get" : template = jsCodeGenerator.getTemplate; break;
				case "post" : template = jsCodeGenerator.postTemplate; break
				case "put" : template =  jsCodeGenerator.putTemplate; break
				case "delete" : template =  jsCodeGenerator.deleteTemplate; break
			}

			return template;
		}

}
