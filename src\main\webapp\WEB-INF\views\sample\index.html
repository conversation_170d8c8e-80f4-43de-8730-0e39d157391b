<th:block data-layout-decorate="~{_layout/default.html}">

	<!-- index.html 고유 CSS 추가 -->
    <th:block layout:fragment="css">
	<link rel="stylesheet" th:href="@{vendor/prism/prism.css}" />
	<link rel="stylesheet" th:href="${'js/odf'+'/odf.css'}" />
	<link rel="stylesheet" th:href="@{vendor/jqtree/dist/themes/default/style.min.css}"/>
	<link rel="stylesheet" th:href="@{vendor/colorpicker/css/colorpicker.css}"/>
	<link rel="stylesheet" th:href="@{css/toc.css}"/>
	<link rel="stylesheet" th:href="@{css/sample.css}"/>
    </th:block>

	<th:block layout:fragment="content">
		<h2 class="hidden">본문 영역</h2>

        <div class="sideMenu">
            <!-- 검색창 -->
            <div class="innerBox">
                <input id="txt-search" type="text" placeholder="| 검색" />
                <button type="button" class="btnSearchRemove"><span class="hidden">지우기</span></button>
            </div>

            <!-- 서브메뉴 -->
            <div class="listArea mScroll">
                <div class="tab tab1" id="submenu">
                </div>
            </div>
        </div>

        <div id="content" class="mScroll">
			<div class="btnGroup">
                <button type="button" class="btnTop"><span class="hidden">맨 위로</span></button>
                <button type="button" class="btnBottom"><span class="hidden">맨 아래로</span></button>
            </div>
			<div id="contents" class="innerContent">
				<section>
                    <h3 id="sample-title" class="titSec"></h3>

                    <div class="row">
                        <div class="detailSec">
                            <p id="sample-desc"></p>
                        </div>
                        <div class="mapArea">
                        	<iframe  id="sample-container"  class="container"  style="width:100%;"></iframe>
                        </div>
                    </div>
                </section>
                <section>
                    <h3 class="titSec">샘플 코드</h3>
                    <div class="posBtn">
                        <button id="showLive" type="button" class="whiteType try">직접해보기</button>
                    </div>
                    <div class="row">
                        <div class="codeArea large">
                            <div class="codeBox">
                            	<pre>
									<code id="sample-code" class="line-numbers language-markup">

									</code>
								</pre>
                            </div>
                        </div>
                    </div>
                </section>
			</div>
		</div>

	</th:block>

	<th:block layout:fragment="script-bottom">
		<script th:src="@{/js/basemap-config.js}"></script>
		<script th:src="@{/vendor/clipboard/clipboard.js}"></script>
		<script th:src="@{/vendor/prism/prism.js}"></script>
		<script th:src="@{/js/sample/index.js(ms=${ms})}"></script>
		
		<script>
			var DeveloperUrl = location.protocol + "//" + location.host + '[[${@egovProperties.getProperty('Url.ContextPath')}]]';

			var OdfUrl = DeveloperUrl + '/js/odf';
            var TifUrl = '[[${@egovProperties.getProperty('Url.Tif')}]]';
            var PbfUrl = '[[${@egovProperties.getProperty('Url.Pbf')}]]';
			var GeoserverUrl = '[[${@egovProperties.getProperty('Url.Geoserver')}]]';
			var APIGW = DeveloperUrl + '/proxy/api';
			/* var WfsAPI = APIGW + '[[${@egovProperties.getProperty('Url.WfsAPI')}]]';
			var WmsAPI = APIGW + '[[${@egovProperties.getProperty('Url.WmsAPI')}]]';
			var WmtsAPI = APIGW + '[[${@egovProperties.getProperty('Url.WmtsAPI')}]]'; */
			var WfsAPI = '[[${@egovProperties.getProperty('Url.Geoserver')}]]';
			var WmsAPI = '[[${@egovProperties.getProperty('Url.Geoserver')}]]';
			var WmtsAPI ='[[${@egovProperties.getProperty('Url.Geoserver')}]]';
			var cssJs = DeveloperUrl + '/js/sample/buttonControl.js';
			// 레이어
			var pointLayer = '[[${@egovProperties.getProperty('Layer.PointLayer')}]]';
			var lineLayer = '[[${@egovProperties.getProperty('Layer.LineLayer')}]]';
			var testLineLayer1 = '[[${@egovProperties.getProperty('Layer.TestLineLayer1')}]]'
			var polygonLayer1 = '[[${@egovProperties.getProperty('Layer.PolygonLayer1')}]]';
			var polygonLayer2 = '[[${@egovProperties.getProperty('Layer.PolygonLayer2')}]]';
			var wmtsLayer = '[[${@egovProperties.getProperty('Layer.WmtsLayer')}]]';
			var hotspotLayer = '[[${@egovProperties.getProperty('Layer.HotspotLayer')}]]';

			// 개발자지원센터 예제 공통 설정
			var crtfckey = '[[${@egovProperties.getProperty('Service.Crtfckey')}]]';
			var sampleBasemap = '[[${@egovProperties.getProperty('Sample.Basemap')}]]';
			var sampleSrid = '[[${@egovProperties.getProperty('Sample.map.Srid')}]]';
			var proxyUseAt = '[[${@egovProperties.getProperty('Sample.Proxy.Use')}]]';
			var customBasemapConfig = '[[${@egovProperties.getProperty('Sample.Basemap.Custom.Config')}]]';

			// 외부 API 관련 설정 (Kakao, 브이월드, 바로e맵, 국가공간정보 포털)
			var kakaoAppKey = '[[${@egovProperties.getProperty('AppKey.Kakao')}]]';
            var naverAppKey = '[[${@egovProperties.getProperty('AppKey.Naver')}]]';
            var googleAppKey = '[[${@egovProperties.getProperty('AppKey.Google')}]]';
			var baroEMapURL = '[[${@egovProperties.getProperty('Url.BaroEMapURL')}]]';
			var baroEMapAirURL = '[[${@egovProperties.getProperty('Url.BaroEMapAirURL')}]]';
			var baroEMapKey  = '[[${@egovProperties.getProperty('Url.BaroEMapKey')}]]';
			var vWorldURL = '[[${@egovProperties.getProperty('Url.VWorldURL')}]]';
			var vWorldApiKey = '[[${@egovProperties.getProperty('VWorld.ApiKey')}]]';
			var vWorldDomain = '[[${@egovProperties.getProperty('VWorld.Domain')}]]';
			var nsdiApiKey = '[[${@egovProperties.getProperty('Nsdi.ApiKey')}]]';

			//샘플에서 사용하는 배경지도id 설정
			var basemapVars = BasemapUtils.getBasemapVariables(sampleBasemap);
			var basemapType = basemapVars.basemapType;
			var basemap_base = basemapVars.basemap_base;
			var basemap_air = basemapVars.basemap_air;
			var basemap_white = basemapVars.basemap_white;
			var basemap_color = basemapVars.basemap_color;
			var basemap_etc = basemapVars.basemap_etc;

			var input = false;

			// viewOption 설정
			if(sampleSrid === "5179") {
				var viewOption = "map.setCenter([948149.5499094268, 1934350.7375426753]);";
			} else {
				// sampleSrid === "5186"
				var viewOption = "map.setCenter([192396.63847319243, 534166.8213405443]);";
			}

			// zoomOption 설정
			var zoomOption = "map.setZoom(14);";
		</script>

	</th:block>

</th:block>

