<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<link href="::OuiUrl::/oui.css" rel="stylesheet">
	<link href="::SmtUrl::/css/widgets/addressSearch.css" rel="stylesheet">

	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
	<script type="text/javascript" src="::OuiUrl::/oui.min.js"></script>
</head>
<style>
	#searchAreaWidget {
		position: absolute;
		right: 10px;
		top: 20px;
	}
</style>
<body>
	<div id ="map" style="height:550px;"></div>
	<div id="searchAreaWidget" class="odf_addressSearch_widget"></div>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::, ::coordy::);
	var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);


	/* oui api 연결 객체 생성  */
    var addressApi = oui.AddressApi(oui.HttpClient({
		//api 요청 실패시(500,503) 최대 재시도 횟수
		//maxRetries : 3
		baseURL: '::geocodingAPI::',
	}), {
        projection: '::srid::',
        crtfckey : '::crtfckey::',
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			basicSearch : 3000,
			bldSearch : 3000,
			coordSearch : 3000,
			intSearch : 3000,
			jibunSearch : 3000,
			pnuSearch : 3000,
			poiSearch : 3000,
			roadSearch : 3000,
			roadApiSearch : 3000,
		}
		 */
    });

	/* 위치검색 위젯 생성  */
    var addressSearchWidget = new oui.AddressSearchWidget({
        odf,
        target: document.getElementById('searchAreaWidget'),
        options: {
			pagingType: 'countable',// countable(일반페이징), unbounded(스크롤페이징)
			/* useSearchResult : 검색 결과 도로명/지번/건물명/poi명 표출 여부
			1.한 검색타입에 모든 명칭을 false를 줄 경우 전부 표출
				ex )  int : {
						roadName : false,
						jibun : false,
						buildName : false,
					  }
				  인 경우 통합검색은 전부 표출
			2.선언하지 않으면 기본값 true
				ex ) int : {
						jibun : false
					}
				인 경우 지번검색 미표출 / 도로명, 건물명 표출
			3.전부다 사용할 경우 옵션 설정 하지 않아도 됨
			   ex )  int : {
						roadName : true,
						jibun : true,
						buildName : true,
					  }
			  위의 예시 처럼 사용하지 않고 선언 안하면 기본값 전부 true (아래 옵션에는 전부 설명하기 위해 전부 표출)
			*/
			useSearchResult:{
				/*basic: {
					roadName: true,
				    jibun: false,
				    buildName: true
				},
				bld: {
					roadName: false,
				    jibun: false,
				    buildName: true
				},
				coord: {
				    roadName: true,
				    jibun: false,
				    buildName: true
				},*/
				int: {
				    roadName: true,	//도로명
				    jibun: true,		//지번
				    buildName: false//건물명
				}//,
				/*jibun: {
				    roadName: true,
				    jibun: false,
				    buildName: true
				},
				pnu: {
				    roadName: true,
				    jibun: true,
				    buildName: true
				},
				poi: {
				    roadName: true,
				    jibun: true,
				    buildName: true,
				    poiName: true
				},
				road: {
				    roadName: true,
				    jibun: true,
				    buildName: true
				},
				roadApi: {
				    roadName: true,
				    jibun: true,
				    buildName: true
				}*/
			},
			useOneClickResultClose : true,//검색 결과 주소 클릭 시 검색 결과창 닫힘 사용 여부 (기본값 false)
			styleObject: {
				image: {
					circle: {
						radius: 10,
						fill: { color: [255, 255, 255, 0.4] },
						stroke: { color: [241, 189, 29, 0.82], width: 2 },
					},
				},
				fill: { color: [255, 255, 255, 0.4] },
				stroke: { color: [241, 189, 29, 0.82], width: 2 },
			},
			//어떤 주소 검색을 사용할지 정의
			addressSearch : {
				basic : false, //기초구역번호 검색
				bld : false,	//건물명 검색
				coord : true, //경위도 좌표 검색
				int : true, //통합검색
				jibun : true, //지번 검색
				pnu : true, //PNU검색
				poi : true, //POI검색
				road : true,	//도로명주소 검색(주소정제 이용)
				roadApi : false, //행안부 도로명 주소검색 (api 이용)
  			},
		},
		api: {
			basicSearch: addressApi.basicSearch,
			bldSearch: addressApi.bldSearch,
			coordSearch: addressApi.coordSearch,
			intSearch: addressApi.intSearch,
			jibunSearch: addressApi.jibunSearch,
			pnuSearch: addressApi.pnuSearch,
			poiSearch: addressApi.poiSearch,
			roadSearch: addressApi.roadSearch,
			roadApiSearch: addressApi.roadApiSearch,
		}
    });
    //주소검색 위젯을 지도객체와 연결 및 렌더링
    addressSearchWidget.addTo(map);
    //지우기 함수
	//addressSearchWidget.remove();
</script>
</html>
