<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<link href="::OuiUrl::/oui.css" rel="stylesheet">

	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
	<script type="text/javascript" src="::OuiUrl::/oui.min.js"></script>
</head>
<style>
  #administrativeDistrictSearchArea {
    position: absolute;
    top: 20px;
    left: 10px;
    border: 1px solid #e9e9e9;
    background: #fff;
    box-shadow: 0.5px 0.9px 4px 0 rgba(0, 0, 0, 0.27);
    border-radius: 3px;
  }
</style>
<body>
	<div id ="map" style="height:550px;"></div>
	<div id="administrativeDistrictSearchArea" class="administrativeDistrictSearch_location"></div>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::, ::coordy::);
	var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);


	/* oui api 연결 객체 생성  */
    var administApi = oui.AdministApi(oui.HttpClient({
        baseURL: '::geocodingAPI::',
    }), {
        projection: '::srid::',
      	crtfckey : '::crtfckey::',
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			geometrySearch : 3000,
			ctpvAdministrativeDistrictSearch : 3000,
			sggAdministrativeDistrictSearch : 3000,
			emdAdministrativeDistrictSearch : 3000,
			liAdministrativeDistrictSearch : 3000,
		}
		 */
    });
    var addressApi = oui.AddressApi(oui.HttpClient({
		baseURL: '::geocodingAPI::',
	}), {
        projection: '::srid::',
      	crtfckey : '::crtfckey::',
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			coordSearch : 3000,
		}
		 */
    });

	/* 행정구역 조회 위젯 생성  */
    var administrativeDistrictSearchWidget = new oui.AdministrativeDistrictSearchWidget({
      odf,
      target: document.querySelector('#administrativeDistrictSearchArea'),
      options: {
        useLi: true, //li 사용 여부(default 값 : false, 사용 여부를 true 로 설정해도 리가 존재하지 않는 읍면동 지역은 li 목록선택 영역 부분을 표출하지 않습니다.)
        useHilight: true,//하이라이트 레이어 사용 여부
        clearHilightLayerFlagMove: true,//지도 이동시 검색결과 날리기 활성화
        // styleObject: {// 하이라이트 레이어 스타일. 없으면 기본 스타일 적용
        //   text: {
        //     fill: {
        //       color: "#858484ff",
        //     },
        //     font: "normal bold 16px 굴림",
        //   },
        //   fill: { color: [255, 255, 255, 0.4] },
        //   stroke: { color: [241, 189, 29, 0.82], width: 2 },
        // },
        //알림옵션
        alertList: {
          //사용자 정의 알림 메세지 정의
          customAlert: (message) => {
            console.log(message);
          },
          //사용자 정의 로딩바 시작 function
          startLoadingBar: (message) => {
            console.log(message);
          },
          //사용자 정의 로딩바 종료 function
          endLoadingBar: (message) => {
            console.log(message);
          },
        }
      },
      api: {
        //단건 행정구역 정보 조회  function
        geometrySearch: administApi.geometrySearch,
        //행정구역 유형별 정의
        //시도 목록 조회 function
        ctpvAdministrativeDistrictSearch: administApi.ctpvAdministrativeDistrictSearch,
        //시군구 목록 조회 function
        sggAdministrativeDistrictSearch: administApi.sggAdministrativeDistrictSearch,
        //읍면동 목록 조회 function
        emdAdministrativeDistrictSearch: administApi.emdAdministrativeDistrictSearch,
        //리 목록 조회 function
        liAdministrativeDistrictSearch: administApi.liAdministrativeDistrictSearch,
        //경위도 좌표 검색 fucntion
        coordSearch: addressApi.coordSearch,
      }
    });
    administrativeDistrictSearchWidget.addTo(map);
  	//지우기 함수
	//administrativeDistrictSearchWidget.remove();

</script>
</html>
