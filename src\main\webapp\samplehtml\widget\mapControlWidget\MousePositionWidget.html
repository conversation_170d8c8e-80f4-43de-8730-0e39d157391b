<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<link href="::OuiUrl::/oui.css" rel="stylesheet">

	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
	<script type="text/javascript" src="::OuiUrl::/oui.min.js"></script>
</head>
<style>
	#coordDiv{
		position: absolute;
		bottom: 20px;
		right: 40px;
		background-color: #fff;
	}
</style>
<body>
	<div class="mapContainer1">
		<div id ="map" style="height:550px;"></div>
		<div id="coordDiv"></div>
	</div>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::, ::coordy::);
	var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);


	/* 마우스 위치 좌표표시 위젯 생성  */
	var mousePositionControlWidget = new oui.MousePositionControlWidget({
		options : {proj : '::srid::'},
		target: document.getElementById('coordDiv'),
	});
	mousePositionControlWidget.addTo(map)
	//지우기함수
	//mousePositionControlWidget.remove();
</script>
</html>
