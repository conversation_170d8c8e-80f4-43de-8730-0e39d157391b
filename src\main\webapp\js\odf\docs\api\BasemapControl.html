<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>GeOnPaas ui widgets: BasemapControl</title>
    
      <link type="text/css" rel="stylesheet" href="styles/vendor/prism-tomorrow-night.css">
    
    <link type="text/css" rel="stylesheet" href="styles/styles.css">
    
    
    <style>
      :root {
      
      
        --nav-width: 370px;
      
      
        --nav-heading-margin-top: 0.5em;
      
      }
    </style>
    
</head>
<body>

<header class="layout-header">
  
  <h1>
    <a href="./index.html">
      GeOnPaas ui widgets
    </a>
  </h1>
  <nav class="layout-nav">
    <ul><li class="nav-heading">Classes</li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="BasemapControl.html">BasemapControl</a></span><span class="nav-desc"><p>배경지도 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="BookmarkControl.html">BookmarkControl</a></span><span class="nav-desc"><p>북마크 컨트롤 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ClearControl.html">ClearControl</a></span><span class="nav-desc"><p>지도 그리기 이벤트 초기화 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ColorFactory.html">ColorFactory</a></span><span class="nav-desc"><p>색 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Control.html">Control</a></span><span class="nav-desc"><p>사용자 정의 컨트롤 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Coordinate.html">Coordinate</a></span><span class="nav-desc"><p>좌표 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DivideMapControl.html">DivideMapControl</a></span><span class="nav-desc"><p>지도 분할 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DownloadControl.html">DownloadControl</a></span><span class="nav-desc"><p>다운로드 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DrawControl.html">DrawControl</a></span><span class="nav-desc"><p>그리기 도구 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Easing.html">Easing</a></span><span class="nav-desc"><p>애니메이션 효과</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="event.html">event</a></span><span class="nav-desc"><p>이벤트 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Extent.html">Extent</a></span><span class="nav-desc"><p>영역 관련 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Feature.html">Feature</a></span><span class="nav-desc"><p>Feature 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FeatureFactory.html">FeatureFactory</a></span><span class="nav-desc"><p>Feature 생성을 위한 FeatureFactory 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FormatFactory.html">FormatFactory</a></span><span class="nav-desc"></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FullScreenControl.html">FullScreenControl</a></span><span class="nav-desc"><p>전체화면 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="HomeControl.html">HomeControl</a></span><span class="nav-desc"><p>홈 이동 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Layer.html">Layer</a></span><span class="nav-desc"><p>레이어 관리 클래스로, 레이어는 odf.LayerFactory를 통해서만 생성가능하다.</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LayerFactory.html">LayerFactory</a></span><span class="nav-desc"><p>레이어 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LayerInfoControl.html">LayerInfoControl</a></span><span class="nav-desc"><p>레이어 정보 조회 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Map.html">Map</a></span><span class="nav-desc"><p>지도 생성, 조작, 컴퍼넌트, 레이어 추가 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Marker.html">Marker</a></span><span class="nav-desc"><p>마커 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MeasureControl.html">MeasureControl</a></span><span class="nav-desc"><p>지도 측정 도구 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MousePositionControl.html">MousePositionControl</a></span><span class="nav-desc"><p>마우스 좌표 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MoveControl.html">MoveControl</a></span><span class="nav-desc"><p>현재 화면 기준으로 이전/다음 화면 이동 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="OverviewMapControl.html">OverviewMapControl</a></span><span class="nav-desc"><p>인덱스맵 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Popup.html">Popup</a></span><span class="nav-desc"><p>팝업 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="PrintControl.html">PrintControl</a></span><span class="nav-desc"><p>프린트 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Projection.html">Projection</a></span><span class="nav-desc"><p>좌표 변환 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="RotationControl.html">RotationControl</a></span><span class="nav-desc"><p>화면을 회전 시키는 기능
alt + shift 드래그로 지도 회전</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ScaleControl.html">ScaleControl</a></span><span class="nav-desc"><p>축척 표시 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="SLD.html">SLD</a></span><span class="nav-desc"><p>WMS 스타일 관리 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Style.html">Style</a></span><span class="nav-desc"><p>스타일 관리 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="StyleFactory.html">StyleFactory</a></span><span class="nav-desc"><p>스타일 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="StyleFunction.html">StyleFunction</a></span><span class="nav-desc"><p>스타일 Function 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="SwiperControl.html">SwiperControl</a></span><span class="nav-desc"><p>지도 스와이퍼 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ZipControl.html">ZipControl</a></span><span class="nav-desc"><p>Server없이 Layer 생성하는 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ZoomControl.html">ZoomControl</a></span><span class="nav-desc"><p>지도 줌 설정클래스</p></span></li></ul><li class="nav-heading"><a href="global.html">Globals</a></li>
  </nav>
</header>


<main class="layout-main ">
  <div class="container">
    <p class="page-kind">Class</p>
    <h1 class="page-title">BasemapControl</h1>
    




<section>


<header class="class">


    
        
        <!-- <h2>BasemapControl</h2> -->

        

        
            <h4 class="method-heading">Summary</h4>
            <div class="class-summary"><p>BasemapControl 생성자</p>
<pre class="prettyprint source lang-javascript"><code>기본지도와 항공지도를 베이스맵으로 선택하여 생성(프록시 사용)
 let basemapControl = new odf.BasemapControl({
   baroEMap:['eMapBasic','eMapAIR']
 },{
proxyURL : '/proxy.jsp',
proxyParam : 'url',});//BasemapControl 생성
</code></pre>
<pre class="prettyprint source lang-javascript"><code> 기본지도와 영문지도를 베이스맵으로 선택하여 생성(프록시 미사용)
 let basemapControl = new odf.BasemapControl({
   baroEMap:['eMapBasic','eMapAIR']
 });//BasemapControl 생성
</code></pre></div>
        

        
            <h4 class="method-heading">Description</h4>
            <div class="class-description"><p>배경지도 설정 클래스</p></div>
        
    
</header>

<article>
    <div class="container-overview">



    
        





    


    
    <h3 class="subtitle">Constructor</h3>
    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="BasemapControl">new BasemapControl<span class="signature">(basemapOption, options)</span><span class="return-type-signature"></span>
    </h4>













    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">basemapOption</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#odf_basemap_option">odf_basemap_option</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>사용자가 어떤 그룹의 어떤 베이스맵을 사용할지 정의한 Object
(ex) {baroEMap :['eMapKorean','eMapAIR'] };</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">options</span>
            

            
                


    <span class="param-type">
        <code>Object</code>
    </span>
    

            

            

            

            <div class="param-description"><p>proxyURL, baroEMapURL,baroEMapAirURL,vWorldURL, 바로e맵 API KEY를 정의</p></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">proxyURL</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>[선택] 프록시를 사용하고자 하는경우 프록시 URL을 정의</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">proxyParam</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>[선택] 프록시를 사용하고자 하는경우 프록시파라미터 정의</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">baroEMapURL</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>일반 바로 eMap url</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">vWorldURL</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>vWord url</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">kakaoURL</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>kakao url</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">kakaoSkyviewURL</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>kakao 스카이뷰 url</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">baroEMapAirURL</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>[선택] 바로e맵 영상지도 url</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">baroEMapKey</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>[선택] 바로e맵 API KEY</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">vWorldKey</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>[선택] vWord API KEY</p></div>
            
        </li>

    
</ul>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    
    </div>

    

    

     

    

    


    

    
        <h3 class="subtitle">Methods</h3>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getBaseLayer">getBaseLayer<span class="signature">(key)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>베이스맵의 key 값으로 baselayer 조회</p>
<pre class="prettyprint source lang-javascript"><code> 프록시 사용하지 않고 eMapBasic 레이어 객체 조회
 let basemapControl = new odf.BasemapControl(['eMapBasic','eMapAIR'],'/proxy.jsp');//BasemapControl 생성
 basemapControl.getBaseLayer('eMapBasic');
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">key</span>
            

            
                


    <span class="param-type">
        <code>odf.baseMapKey</code>
    </span>
    

            

            

            

            <div class="param-description"><p>조회할 베이스맵의 key 값</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getConstructorOptions">getConstructorOptions<span class="signature">()</span><span class="return-type-signature"> &rarr; {Array.&lt;Object>}</span>
    </h4>





<div class="method-description">
    
    <p>컨트롤 생성 옵션 반환</p>
<pre class="prettyprint source lang-javascript"><code> let basemapControl = new odf.BasemapControl({
   baroEMap:['eMapBasic','eMapAIR']
 },{
proxyURL : '/proxy.jsp',
proxyParam : 'url',});//BasemapControl 생성
 basemapControl.getConstructorOptions();
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Array.&lt;Object></code>
            
            
                <p>컨트롤 생성 옵션 반환</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getMap">getMap<span class="signature">()</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>BasemapControl 객체와 연결된 Map 객체 반환</p>
<pre class="prettyprint source lang-javascript"><code> let basemapControl = new odf.BasemapControl([
   'eMapBasic','eMapAIR'
 ],{
proxyURL : '/proxy.jsp',
proxyParam : 'url',});//BasemapControl 생성
 basemapControl.getMap();//undefined
 basemapControl.setMap(map);
 basemapControl.getMap();//map
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getPresentBaseGrpKey">getPresentBaseGrpKey<span class="signature">()</span><span class="return-type-signature"> &rarr; {String}</span>
    </h4>





<div class="method-description">
    
    <p>현재 설정된 베이스맵 그룹 key 조회</p>
<pre class="prettyprint source lang-javascript"><code> let basemapControl = new odf.BasemapControl();//BasemapControl 생성
 basemapControl.getPresentBaseGrpKey();
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>String</code>
            
            
                <p>현재 설정된 베이스맵 그룹 key</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getPresentBaseLayer">getPresentBaseLayer<span class="signature">()</span><span class="return-type-signature"> &rarr; {<a href="Layer.html">Layer</a>}</span>
    </h4>





<div class="method-description">
    
    <p>현재 설정된 베이스맵 레이어 조회</p>
<pre class="prettyprint source lang-javascript"><code> let basemapControl = new odf.BasemapControl();//BasemapControl 생성
 basemapControl.getPresentBaseLayer();
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Layer</code>
            
            
                <p>현재 설정된 베이스맵 레이어</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getPresentBaseLayerKey">getPresentBaseLayerKey<span class="signature">()</span><span class="return-type-signature"> &rarr; {String}</span>
    </h4>





<div class="method-description">
    
    <p>현재 설정된 베이스맵 key 조회</p>
<pre class="prettyprint source lang-javascript"><code> let basemapControl = new odf.BasemapControl();//BasemapControl 생성
 basemapControl.getPresentBaseLayer();
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>String</code>
            
            
                <p>현재 설정된 베이스맵 key</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getSetableBasemapList">getSetableBasemapList<span class="signature">()</span><span class="return-type-signature"> &rarr; {Json}</span>
    </h4>





<div class="method-description">
    
    <p>설정가능한 BasemapList 조회</p>
<pre class="prettyprint source lang-javascript"><code> let basemapControl = new odf.BasemapControl();//BasemapControl 생성
 basemapControl.getSetableBasemapList();
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Json</code>
            
            
                <p>배경지도로 설정가능한 목록 및 상세 명칭</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="hasBaseLayer">hasBaseLayer<span class="signature">(layerKey)</span><span class="return-type-signature"> &rarr; {Boolean}</span>
    </h4>





<div class="method-description">
    
    <p>특정 키를 갖는 베이스 레이어가 존재하는지 확인</p>
<pre class="prettyprint source lang-javascript"><code> //베이스컨트롤 불러오기
 let basemapControl = map.getBasemapControl();
 basemapControl.setMap(map);

 //'myLayer'를 키로 하는 베이스맵이 존재하는지 확인
 basemapControl.hasBaseLayer('myLayer');

</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">layerKey</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>등록할 베이스레이어의 key 값</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Boolean</code>
            
            
                <p>입력한 베이스레이어 key가 존재하는지 여부</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="hasGrp">hasGrp<span class="signature">(grpKey)</span><span class="return-type-signature"> &rarr; {Boolean}</span>
    </h4>





<div class="method-description">
    
    <p>베이스맵 그룹 존재 여부 확인</p>
<pre class="prettyprint source lang-javascript"><code> //레이어 불러오기
 let basemapControl = map.getBasemapControl();
 basemapControl.setMap(map);

 //새로운 베이스맵 존재 여부 확인
 basemapControl.hasGrp('myGrp');
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">grpKey</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>베이스맵 그룹이 존재하는지 확인할 grpKey</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Boolean</code>
            
            
                <p>입력한 베이스맵 그룹 key가 존재하는지 여부</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="rebuildElement">rebuildElement<span class="signature">()</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>정의된 베이스맵 그룹과 베이스맵 레이어 정보에따라 basemap 컨트롤을 지우고 다시 생성</p>
<pre class="prettyprint source lang-javascript"><code> //베이스컨트롤 불러오기
 let basemapControl = map.getBasemapControl();
 basemapControl.setMap(map);

 //새로운 베이스맵 그룹 생성
 basemapControl.setGrp('myGrp');
 //새로운 베이스맵 그룹에 새로운 레이어 추가
 basemapControl.setBaseLayer('myGrp','myLayer','내레이어',odf.LayerFactory.produce(...));

 //생성된 컨트롤 element 재생성
 basemapControl.rebuildElement();
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="removeBaseLayer">removeBaseLayer<span class="signature">(layerKey)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>사용자 정의 베이스레이어 제거</p>
<pre class="prettyprint source lang-javascript"><code> //베이스컨트롤 불러오기
 let basemapControl = map.getBasemapControl();
 basemapControl.setMap(map);

 //베이스맵 기본그룹 'basic'에 새로운 레이어 추가
 basemapControl.setBaseLayer('basic','myLayer','내레이어',odf.LayerFactory.produce(...));

 //새로운 베이스맵 그룹 생성
 //basemapControl.setGrp('myGrp');
 //새로운 베이스맵 그룹에 새로운 레이어 추가
 //basemapControl.setBaseLayer('myGrp','myNewGroupLayer','내새그룹레이어',odf.LayerFactory.produce(...));

 //정의된 베이스맵 그룹과 베이스맵 레이어 정보에따라 basemap 컨트롤을 지우고 다시 생성
 basemapControl.rebuildElement();

 //새로 생성한 베이스맵 레이어 제거
 basemapControl.removeBaseLayer('myNewGroupLayer');

</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">layerKey</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>등록할 베이스레이어의 key 값. 영문 대/소문자와 언더바(_)로 구성</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="removeGrp">removeGrp<span class="signature">(grpKey)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>베이스맵 그룹 제거</p>
<pre class="prettyprint source lang-javascript"><code> //베이스컨트롤 불러오기
 let basemapControl = map.getBasemapControl();
 basemapControl.setMap(map);

 //새로운 베이스맵 그룹 생성
 basemapControl.setGrp('myGrp');
 //새로만든 베이스맵 그룹 제거
 basemapControl.removeGrp('myGrp')
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">grpKey</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>제거할 대상이 되는 베이스맵 그룹으로 등록된 grpKey</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setBaseLayer">setBaseLayer<span class="signature">(grpKey, layerKey, layerName, layer)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>사용자 정의 베이스레이어 추가 및 베이스맵 컨트롤 재구축</p>
<pre class="prettyprint source lang-javascript"><code> //베이스컨트롤 불러오기
 let basemapControl = map.getBasemapControl();
 basemapControl.setMap(map);

 //베이스맵 기본그룹 'basic'에 새로운 레이어 추가
 basemapControl.setBaseLayer('basic','myLayer','내레이어',odf.LayerFactory.produce(...));

 //새로운 베이스맵 그룹 생성
 //basemapControl.setGrp('myGrp');
 //새로운 베이스맵 그룹에 새로운 레이어 추가
 //basemapControl.setBaseLayer('myGrp','myNewGroupLayer','내새그룹레이어',odf.LayerFactory.produce(...));

 //정의된 베이스맵 그룹과 베이스맵 레이어 정보에따라 basemap 컨트롤을 지우고 다시 생성
 basemapControl.rebuildElement();
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">grpKey</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>베이스맵 그룹으로 등록된 grpKey</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">layerKey</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>등록할 베이스레이어의 key 값</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">layerName</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>컨트롤에서 나타날 베이스 레이어 명</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">layer</span>
            

            
                


    <span class="param-type">
        <code>odf.Layer</code>
    </span>
    

            

            

            

            <div class="param-description"><p>베이스맵으로 등록할 레이어 객체</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setGrp">setGrp<span class="signature">(grpKey)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>베이스맵 그룹 생성</p>
<pre class="prettyprint source lang-javascript"><code> //레이어 불러오기
 let basemapControl = map.getBasemapControl();
 basemapControl.setMap(map);

 //새로운 베이스맵 그룹 생성
 basemapControl.setGrp('myGrp');
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">grpKey</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>베이스맵 그룹으로 등록할 grpKey.</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setMap">setMap<span class="signature">(map, createElementFlag<span class="signature-attributes">nullable</span>)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>BasemapControl 객체와 Map 객체를 연결</p>
<pre class="prettyprint source lang-javascript"><code> let basemapControl = new odf.BasemapControl([
   'eMapBasic','eMapAIR'
 ],{
proxyURL : '/proxy.jsp',
proxyParam : 'url',});//BasemapControl 생성
 basemapControl.setMap(map);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">map</span>
            

            
                


    <span class="param-type">
        <code><a href="Map.html">Map</a></code>
    </span>
    

            

            
                <span class="param-attributes">
                

                

                
                </span>
            

            

            <div class="param-description"><p>BasemapControl 객체와 연결할 Map 객체</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">createElementFlag</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                
                    &lt;nullable&gt;<br>
                

                
                </span>
            

            

            <div class="param-description"><p>BasemapControl 버튼 생성 여부</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setSwitchBaseLayerCallback">setSwitchBaseLayerCallback<span class="signature">(callback)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>배경지도 변경 완료 후 호출할 콜백</p>
<pre class="prettyprint source lang-javascript"><code> //베이스컨트롤 불러오기
 let basemapControl = map.getBasemapControl();
 basemapControl.setMap(map);

 //switchBaseLayer 호출 직후 사용될 callback 셋팅
 basemapControl.setSwitchBaseLayerCallback(function(beforeLayer,afterLayer){
   //do something
});
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">callback</span>
            

            
                


    <span class="param-type">
        <code>function</code>
    </span>
    

            

            

            

            <div class="param-description"><p>beforeLayer와 afterLayer를 매개변수로 갖는 사용자 정의 callback function</p>
<ul>
<li>첫번째 매개변수(beforeLayer) : 변경 이전 layer</li>
<li>두번째 매개변수(afterLayer) : 변경될 layer</li>
</ul></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="switchBaseLayer">switchBaseLayer<span class="signature">(layerKey)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>배경지도 변경</p>
<pre class="prettyprint source lang-javascript"><code> //베이스컨트롤 불러오기
 let basemapControl = map.getBasemapControl();
 basemapControl.setMap(map);

 //색각지도로 변경
 basemapControl.switchBaseLayer('eMapColor');
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">layerKey</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#odf_basemap_baroEMapKey">odf_basemap_baroEMapKey</a></code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odf_basemap_vWorldMapKey">odf_basemap_vWorldMapKey</a></code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odf_basemap_kakaoMapKey">odf_basemap_kakaoMapKey</a></code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odf_basemap_OSMMapKey">odf_basemap_OSMMapKey</a></code>
    </span>
    |

    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>변경할 baselayer의 키 값</p>
<ul>
<li>사용자정의 베이스맵의 키 값 허용</li>
<li>undefined 또는 null 입력시 배경지도 삭제</li>
</ul></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
    

    

    
</article>

</section>




  </div>
</main>

<footer class="layout-footer">
  <div class="container">
    Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.11</a> on Tue Jan 21 2025 11:05:51 GMT+0900 (대한민국 표준시)
  </div>
</footer>



<script src="scripts/prism.dev.js"></script>
</body>
</html>