<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
</head>
<body>
	<div id="map" class="odf-view" style="height:550px;"></div>
	<div class="btnDiv">
		<p>버튼을 클릭 후 지도에서 클릭합니다.</p>
		<input type="button" id="addMarker" class="onoffOnlyBtn toggle grp1" value="기본 마커 추가">
		<input type="button" id="addCustomMarker1" class="onoffOnlyBtn toggle grp1" value="사용자 정의 마커 추가1">
		<input type="button" id="addCustomMarker2" class="onoffOnlyBtn toggle grp1" value="사용자 정의 마커 추가2">
	</div>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::,::coordy::);
	var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);

	var addDefaultMarkerFlag = false;
	var addcustomMarkerFlag1 = false;
	var addcustomMarkerFlag2 = false;

	//기본마커나 사용자 정의 마커 버튼 클릭시 해당 마커를 추가 가능한 마커로 지정

	document.getElementById('addMarker').addEventListener('click',
		function(evt) {
			addDefaultMarkerFlag = !addDefaultMarkerFlag;
			if (addDefaultMarkerFlag) {
				addcustomMarkerFlag1 = false;
				addcustomMarkerFlag2 = false;
			}
		}
	);
	document.getElementById('addCustomMarker1').addEventListener('click',
		function(evt) {
			addcustomMarkerFlag1 = !addcustomMarkerFlag1;
			if (addcustomMarkerFlag1) {
				addDefaultMarkerFlag = false;
				addcustomMarkerFlag2 = false;
			}
		}
	);
	document.getElementById('addCustomMarker2').addEventListener('click',
		function(evt) {
			addcustomMarkerFlag2 = !addcustomMarkerFlag2;
			if (addcustomMarkerFlag2) {
				addDefaultMarkerFlag = false;
				addcustomMarkerFlag1 = false;
			}
		}
	);

	//마커가 생성되기를 원하는 지도위치에 버튼 클릭시 추가 설정이 완료된 마커 생성
	odf.event.addListener(map, 'click', function(evt) {

		var clickPosition = new odf.Coordinate(evt.coordinate);
		var m = undefined;
		if (addDefaultMarkerFlag) {
			//기본마커
			m = new odf.Marker({
				position : clickPosition
			//클릭한 좌표로 생성될 마커의 위치 설정
			});
		} else if (addcustomMarkerFlag1) {
			//커스텀마커1
			m = new odf.Marker({
				position : clickPosition, //클릭한 좌표로 생성될 마커의 위치 설정
				style : { //마커의 스타일 설정
					width : '20px', //너비
					height : '40px', //높이
					src : 'images/smileIcon.png' //이미지 경로
				}
			});
		} else if (addcustomMarkerFlag2) {
			//커스텀마커2
			var _element = document.createElement('div');
			_element.style.width = "20px";
			_element.style.height = "30px";
			_element.style['background-color'] = "rgba(132,229,252,0.95)";
			_element.style['font-size'] = "20px";
			_element.innerHTML = "♥";
			m = new odf.Marker({
				position : clickPosition, //클릭한 좌표로 생성될 마커의 위치 설정
				style : {
					element : _element
				//element를 직접 만들어 셋팅
				}
			});
		}

		if (m) {
			m.setMap(map);
		}
	});
</script>
</html>

