<!doctype html>

<title>CodeMirror: Swift mode</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../../doc/docs.css">

<link rel="stylesheet" href="../../lib/codemirror.css">
<script src="../../lib/codemirror.js"></script>
<script src="../../addon/edit/matchbrackets.js"></script>
<script src="./swift.js"></script>
<style>
	.CodeMirror { border: 2px inset #dee; }
    </style>
<div id=nav>
  <a href="https://codemirror.net"><h1>CodeMirror</h1><img id=logo src="../../doc/logo.png" alt=""></a>

  <ul>
    <li><a href="../../index.html">Home</a>
    <li><a href="../../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror">Code</a>
  </ul>
  <ul>
    <li><a href="../index.html">Language modes</a>
    <li><a class=active href="#">Swift</a>
  </ul>
</div>

<article>
<h2>Swift mode</h2>
<form><textarea id="code" name="code">
protocol HeaderViewProtocol {
    func setTitle(_ string: String)
}

struct AnyHeaderView {
    let view: UIView
    let headerView: HeaderViewProtocol
    init<T: UIView>(view: T) where T: HeaderViewProtocol {
        self.view = view
        self.headerView = view
    }
}

let header = AnyHeaderView(view: myView)
header.headerView.setTitle("hi")

struct HeaderView {
    let view: UIView
    let setTitle: (String) -> ()
}

var label = UILabel()
let header = HeaderView(view: label) { str in
    label.text = str
}
header.setTitle("hello")
</textarea></form>

    <script>
      var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
        lineNumbers: true,
        matchBrackets: true,
        mode: "text/x-swift"
      });
    </script>

    <p>A simple mode for Swift</p>

    <p><strong>MIME types defined:</strong> <code>text/x-swift</code> (Swift code)</p>
  </article>
