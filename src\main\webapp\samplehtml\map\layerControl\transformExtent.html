<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
</head>
<body>
	<div id="map" class="odf-view" style="height:550px;"></div>
	<div id="result" style="width: 1000px; height: 120px;">지도를 움직이시면 내용을 확인하실 수 있습니다.</div>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::,::coordy::);
	var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);

	//지도 이동시 extent를 좌표변환하여 출력
	odf.event.addListener(map, 'pointerdrag', function(evt) {
		/* 현재 View의 Extent */
		var div = document.getElementById("result");
		var extent = map.getView().calculateExtent();
		div.innerText = "변경 전(::srid::) extent => " + extent;

		/* 방법 2 */
		var projection = new odf.Projection({
			EPSG : '::srid::'
		});
		var newGeoExtent = projection.unprojectExtent(extent, '4326');
		div.innerText += "\n\n변경 후(4326) extent => " + newGeoExtent;
		var newMapextent = projection.projectExtent(newGeoExtent, '4326');
		div.innerText += "\n\n재 변경 후(::srid::) extent => " + newMapextent;
	});
</script>
</html>
