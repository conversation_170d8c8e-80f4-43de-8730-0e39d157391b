<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<link href="::OuiUrl::/oui.css" rel="stylesheet">
	<link href="::SmtUrl::/css/common_toolbar.css" rel="stylesheet">
	<link href="::SmtUrl::/css/widgets/roadView.css" rel="stylesheet">

	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
	<script type="text/javascript" src="::OuiUrl::/oui.min.js"></script>

	<script type="text/javascript" src="//dapi.kakao.com/v2/maps/sdk.js?appkey=::kakaoAppKey::"></script>
</head>
<body>
	<div id ="map" style="height:550px;"></div>
	<ul class="toolbar">
		<li class="roadViewWidget" id="roadViewWidget"></li>
	</ul>
	<div id="roadview_popup" class="modal_modal modal_open"></div>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::, ::coordy::);
	var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);


	/* oui api 연결 객체 생성  */
	var basemapClient =  oui.HttpClient({
		baseURL: '::smtAPI::',
	});
	var basemapApi = oui.BasemapApi(basemapClient, {
		crtfckey: '::crtfckey::',
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			getBasemap : 3000,
		}
		 */
	});

	/* custom marker 객체 생성 */
	var markerInstance = new odf.Marker({
		/*마커의 위치*/
		position : [ 199312.9996, 551784.6924 ],
		draggable : true,
		positioning : 'bottom-center',
		offset : [0,0],
	})

	/* 로드뷰 위젯 생성 */
	var roadViewWidget = new oui.RoadViewWidget({
        options: {
        	element: document.getElementById('roadview_popup'),
            alertList: {
                customAlert: (message) => {
                    alert(message);
                },
                customErrorAlert: (message) => {
                    alert(message);
                }
            },
            hybridLayer: {
            	use : true,
            	bcrnMapId : 'BM0000000058',
            	proxyObject: {
                  proxyURL:'::DeveloperUrl::/proxyUrl.jsp',
                  proxyParam:'url',
                }
            },
			marker: {
				// default, mapWalker, custom
				type: 'mapWalker',
				// custom type 일 경우 markerInstance 를 생성하여 값에 넣어줍니다.
				// markerInstance: markerInstance
			}
        },
        api: {
			// 배경지도 조회 api
			getBasemap: basemapApi.getBasemap,
		},
        target: document.querySelector('#roadViewWidget')
    });
	roadViewWidget.addTo(map);

</script>
</html>
