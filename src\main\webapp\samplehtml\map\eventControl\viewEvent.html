<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
</head>
<body>
	<div id="map" class="odf-view" style="height:550px;"></div>
	<textarea id="eventLog" style="width: 100%; height: 200px;"></textarea>
	<div class="btnLogArea">
		<div class="innerBox">
			<input type="button" class="onoffBtn" value="로그 초기화" id="clearLog" />
			<div>
				이벤트 로깅 여부 변경 :
				<input type="button" class="onoffBtn toggle" value="change" id="changeFlag" title="일반 변경 이벤트. 개정 카운터가 증가하면 트리거" />
				<input type="button" class="onoffBtn toggle" value="changeCenter" id="changeCenterFlag" title="center 속성 변경시 트리거" />
				<input type="button" class="onoffBtn toggle" value="changeResolution" id="changeResolutionFlag" title="resolution 속성 변경시 트리거 (줌 레벨 변경시)" />
				<input type="button" class="onoffBtn toggle" value="changeRotation" id="changeRotationFlag" title="rotation 속성 변경시 트리거(Alt+shift 지도 드래그 시)" />
				<input type="button" class="onoffBtn toggle" value="propertychange" id="propertychangeFlag" title="속성이 변경되면 트리거" />
			</div>
		</div>
	</div>
	<p>※ 직접해보기에서는 이벤트 버튼 토글이 지원되지 않습니다.</p>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::,::coordy::);
	var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);
	var view = map.getView();

	//일반 변경 이벤트. 개정 카운터가 증가하면 트리거
	odf.event.addListener(view, 'change', function(evt) {
		if (changeFlag) addLog("view change!");
	});
	//center 속성 변경시 트리거
	odf.event.addListener(view, 'change:center', function(evt) {
		if (changeCenterFlag) addLog("map change center!");
	});

	//resolution 속성 변경시 트리거
	odf.event.addListener(view, 'change:resolution', function(evt) {
		if (changeResolutionFlag) addLog("map change resolution!");
	});

	//rotation 속성 변경시 트리거
	odf.event.addListener(view, 'change:rotation', function(evt) {
		if (changeRotationFlag) addLog("map change rotation!");
	});

	//속성이 변경되면 트리거
	odf.event.addListener(view, 'propertychange', function(evt) {
		if (propertychangeFlag) addLog("map property change!");
	});

	// 로그 추가
	var addLog = function(log) {
		document.getElementById("eventLog").innerHTML = log + '\n' + document.getElementById("eventLog").value;
	};
	//로그 초기화
	odf.event.addListener('#clearLog', 'click', function(evt) {
		document.getElementById("eventLog").innerHTML = "";
	});

	//이벤트 로깅 여부변경
	var changeFlag = false;
	var changeCenterFlag = false;
	var changeResolutionFlag = false;
	var changeRotationFlag = false;
	var propertychangeFlag = false;

	odf.event.addListener('#changeFlag', 'click', function(evt) {
		changeFlag = !changeFlag;
	});
	odf.event.addListener('#changeCenterFlag', 'click', function(evt) {
		changeCenterFlag = !changeCenterFlag;
	});
	odf.event.addListener('#changeResolutionFlag', 'click', function(evt) {
		changeResolutionFlag = !changeResolutionFlag;
	});
	odf.event.addListener('#changeRotationFlag', 'click', function(evt) {
		changeRotationFlag = !changeRotationFlag;
	});
	odf.event.addListener('#propertychangeFlag', 'click', function(evt) {
		propertychangeFlag = !propertychangeFlag;
	});
</script>
</html>

