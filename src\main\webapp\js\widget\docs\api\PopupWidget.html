<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>GeOnPaas ui widgets: PopupWidget</title>
    
      <link type="text/css" rel="stylesheet" href="styles/vendor/prism-tomorrow-night.css">
    
    <link type="text/css" rel="stylesheet" href="styles/styles.css">
    
    
    <style>
      :root {
      
      
        --nav-width: 370px;
      
      
        --nav-heading-margin-top: 0.5em;
      
      }
    </style>
    
</head>
<body>

<header class="layout-header">
  
  <h1>
    <a href="./index.html">
      GeOnPaas ui widgets
    </a>
  </h1>
  <nav class="layout-nav">
    <ul><li class="nav-heading">Classes</li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="AddressSearchWidget.html">AddressSearchWidget</a></span><span class="nav-desc"><p>주소 검색 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="AdministrativeDistrictSearchWidget.html">AdministrativeDistrictSearchWidget</a></span><span class="nav-desc"><p>행정구역 조회 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="AttributeEditorWidget.html">AttributeEditorWidget</a></span><span class="nav-desc"><p>속성 설정 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="BasemapWidget.html">BasemapWidget</a></span><span class="nav-desc"><p>배경지도 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="BookMarkControlWidget.html">BookMarkControlWidget</a></span><span class="nav-desc"><p>북마크컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="CCTVControlWidget.html">CCTVControlWidget</a></span><span class="nav-desc"><p>CCTV컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ChartWidget.html">ChartWidget</a></span><span class="nav-desc"><p>차트 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ClearControlWidget.html">ClearControlWidget</a></span><span class="nav-desc"><p>초기화컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ConditionFilterWidget.html">ConditionFilterWidget</a></span><span class="nav-desc"><p>조건식 편집기 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="CreateLayerWidget.html">CreateLayerWidget</a></span><span class="nav-desc"><p>레이어 생성 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DivideMapWidget.html">DivideMapWidget</a></span><span class="nav-desc"><p>분할지도 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DownloadControlWidget.html">DownloadControlWidget</a></span><span class="nav-desc"><p>다운로드컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DrawControlWidget.html">DrawControlWidget</a></span><span class="nav-desc"><p>그리기컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FeatureAttributeFormWidget.html">FeatureAttributeFormWidget</a></span><span class="nav-desc"><p>피쳐 속성 폼 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FullScreenControlWidget.html">FullScreenControlWidget</a></span><span class="nav-desc"><p>전체화면컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="GeocodingGridWidget.html">GeocodingGridWidget</a></span><span class="nav-desc"><p>지오코딩 그리드 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="GridWidget.html">GridWidget</a></span><span class="nav-desc"><p>속성테이블 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="HomeControlWidget.html">HomeControlWidget</a></span><span class="nav-desc"><p>홈이동컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LabelStyleWidget.html">LabelStyleWidget</a></span><span class="nav-desc"><p>레이어 스타일 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LayerSearchWidget.html">LayerSearchWidget</a></span><span class="nav-desc"><p>레이어검색 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LayerUploadWidget.html">LayerUploadWidget</a></span><span class="nav-desc"><p>레이어업로드 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LegendWidget.html">LegendWidget</a></span><span class="nav-desc"><p>범례 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MeasureControlWidget.html">MeasureControlWidget</a></span><span class="nav-desc"><p>측정컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MousePositionControlWidget.html">MousePositionControlWidget</a></span><span class="nav-desc"><p>현재위치컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MoveControlWidget.html">MoveControlWidget</a></span><span class="nav-desc"><p>이동컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="OneParcelWidget.html">OneParcelWidget</a></span><span class="nav-desc"><p>일필지 조회 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="OverViewMapControlWidget.html">OverViewMapControlWidget</a></span><span class="nav-desc"><p>지도오버뷰컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="PopupSettingsWidget.html">PopupSettingsWidget</a></span><span class="nav-desc"><p>팝업 설정 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="PopupWidget.html">PopupWidget</a></span><span class="nav-desc"><p>지도 위 클릭 시 피쳐 정보 표출 팝업 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="PrintControlWidget.html">PrintControlWidget</a></span><span class="nav-desc"><p>출력컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="RoadViewWidget.html">RoadViewWidget</a></span><span class="nav-desc"><p>로드뷰(카카오) 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="RotationControlWidget.html">RotationControlWidget</a></span><span class="nav-desc"><p>회전컨트롤 위젯 (생성 후 지도 객체에서 Alt + Shift + 지도 객체 Drag로 사용)</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ScaleControlWidget.html">ScaleControlWidget</a></span><span class="nav-desc"><p>축척컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="SpatialAnalysisWidget.html">SpatialAnalysisWidget</a></span><span class="nav-desc"><p>공간분석 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="StyleWidget.html">StyleWidget</a></span><span class="nav-desc"><p>레이어 스타일 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="SwiperControlWidget.html">SwiperControlWidget</a></span><span class="nav-desc"><p>스와이퍼컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="SwiperWidget.html">SwiperWidget</a></span><span class="nav-desc"><p>스와이퍼 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="TimeSliderControlWidget.html">TimeSliderControlWidget</a></span><span class="nav-desc"><p>타임슬라이더컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="TOCWidget.html">TOCWidget</a></span><span class="nav-desc"><p>TOC 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ZoomControlWidget.html">ZoomControlWidget</a></span><span class="nav-desc"><p>지도 확대/축소 조작 위젯</p></span></li></ul><li class="nav-heading"><a href="global.html">Globals</a></li>
  </nav>
</header>


<main class="layout-main ">
  <div class="container">
    <p class="page-kind">Class</p>
    <h1 class="page-title">PopupWidget</h1>
    




<section>


<header class="class">


    
        
        <!-- <h2>PopupWidget</h2> -->

        

        
            <h4 class="method-heading">Summary</h4>
            <div class="class-summary"><p>팝업 위젯 생성자 (지도상의 피쳐 클릭하면 피쳐 정보 팝업 표출)</p></div>
        

        
            <h4 class="method-heading">Description</h4>
            <div class="class-description"><p>지도 위 클릭 시 피쳐 정보 표출 팝업 위젯</p></div>
        
    
</header>

<article>
    <div class="container-overview">



    
        





    

        <h3 class="subtitle">Properties</h3>
        

<ul class="method-params">


    <li>
        
            <span class="param-name">target</span>
        

        
            


    <span class="param-type">
        <code>HTMLElement</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>팝업 위젯을 생성할 영역 (없을 경우 객체 클릭한 위치에 팝업으로 표출됨)</p>
        </div>

    </li>
    
</ul>


    


    
    <h3 class="subtitle">Constructor</h3>
    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="PopupWidget">new PopupWidget<span class="signature">(map, odf, api<span class="signature-attributes">opt</span>, options<span class="signature-attributes">opt</span>)</span><span class="return-type-signature"></span>
    </h4>













    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">map</span>
            

            
                


    <span class="param-type">
        <code>map</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                

                
                </span>
            

            

            <div class="param-description"><p>맵객체</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">odf</span>
            

            
                


    <span class="param-type">
        <code>odf</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                

                
                </span>
            

            

            <div class="param-description"><p>odf 모듈</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">api</span>
            

            
                


    <span class="param-type">
        <code>Object</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>팝업 위젯에서 사용할 api 정보</p></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">getLayerList</span>
            

            
                


    <span class="param-type">
        <code>function</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>팝업표출할 레이어 정보 목록 조회 (예 : toc가 있는 경우 toc 레이어 목록 )</p></div>
            
        </li>

    
</ul>
            
        </li>

    

        <li>
            
                <span class="param-name">object.api.columnInfoFunction</span>
            

            
                


    <span class="param-type">
        <code>function</code>
    </span>
    |

    <span class="param-type">
        <code>ColumnInfoApi.columnInfoFunction</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>컬럼정보 조회 api</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">object.api.getAllDetailCode</span>
            

            
                


    <span class="param-type">
        <code>function</code>
    </span>
    |

    <span class="param-type">
        <code>CommonCodeApi.getAllDetailCode</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>공통코드 상세조회 api</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">options</span>
            

            
                


    <span class="param-type">
        <code>Object</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>옵션</p></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">setAddInfoHtml</span>
            

            
                


    <span class="param-type">
        <code>function</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>클릭한 레이어 객체의 팝업에 부가적으로 html을 추가 시킬 수 있는 콜백함수/ 레이어 객체를 클릭할 경우 함수가 실행된다.</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">draggable</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>드래그 사용 여부 , true인 경우 팝업이 드래그 됨, (target이 존재하지 않을때만 사용 가능)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">getIsActivateOption</span>
            

            
                


    <span class="param-type">
        <code>function</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>//팝업 비활성화할 조건 입력</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">callbackOpen</span>
            

            
                


    <span class="param-type">
        <code>function</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>팝업이 열릴때 호출되는 콜백함수</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">useHilight</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>하이라이트 기능 사용 여부(기본값 true)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">styleObject</span>
            

            
                


    <span class="param-type">
        <code>Object</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>하이라이트 레이어 스타일(미정의시 내부에서 정의한 기본 스타일 적용)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">pointBuffer</span>
            

            
                


    <span class="param-type">
        <code>Number</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>클릭 한 위치에 버퍼를 지정하여 팝업 조회 (단위:픽셀) (기본값 20) (getLayerList를 사용하고, geometryType이 점,선인 레이어만 적용됨)</p></div>
            
        </li>

    
</ul>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>






    <h4 class="method-heading">Examples</h4>
    
    <pre><code class="language-js">//[예제1] 간단하게 지도에 있는 레이어의 속성 팝업을 표출할 경우 (해당 예제로는 레이어 필터링 불가, geojson레이어도 팝업 표출가능)
 var popupWidget = new oui.PopupWidget({
	    odf: odf
	    , options: {
	      draggable: true, //팝업 위젯 드래그 사용 여부
	      //팝업에 커스텀 html 추가
	      setAddInfoHtml: (popupInfo)=>{
	    	  let popupInfoStr = JSON.stringify(popupInfo);
	    	  return {
				    location : 'bottom', //table 속성팝업 테이블이랑 같은위치, 스크롤 생길경우 스크롤 아래에 생김 bottom 팝업 맨아래 생김 스크롤 생겨도 맨아래 계속 표줄됨 default table
				    html :
					    `&lt;button id="popupBtn" onclick='onClickPopupBtn1(${popupInfoStr})'>팝업버튼1&lt;/button>
					    &lt;button id="popupBtn2" onclick='onClickPopupBtn2(${popupInfoStr})'>팝업버튼2&lt;/button>`
	    	  }
	      }
	    }
	  });
 popupWidget.addTo(map);
  
 function onClickPopupBtn1(popupInfo) {
   console.dir(popupInfo);
   alert('onClickPopupBtn1 클릭');
 }
 function onClickPopupBtn2(popupInfo) {
   console.dir(popupInfo);
   alert('onClickPopupBtn2 클릭');
 }</code></pre>

    <pre><code class="language-js">//[예제2] 팝업을 표출할 레이어를 필터링하여 속성팝업을 표출할 경우 (geoserver에 발행된 레이어만 팝업 표출 가능)
 var client = oui.HttpClient({
    baseURL: '[api 경로]',
  });
  var popupWidget = new oui.PopupWidget({
    odf: odf
    , options: {
       draggable: true, //팝업 위젯 드래그 사용 여부
       getIsActivateOption: () => { // 팝업의 활성/비활성화 여부
         //popupDisplay : 팝업 표출 활성/비활성화
         //isActivateOption(활성화여부) : true(활성화)/false(비활성화)
         //condition 필터조건을 넣을 배열객체. 조건으로 사용할수 있는 값은 (getLayerList에서 넘겨줬던 레이어 목록의 레이어 객체의 키값들)
         //예시1) condition: [{ cntntsId: '[Geoserver 발행 id(a)]' }, { cntntsId: '[Geoserver 발행 id(b)]' }] -> cntntsId가 (a) 이거나(or) (b)인 경우 해당 레이어 속성팝업 비활성화
         //예시2) condition: [{ cntntsId: '[Geoserver 발행 id(a)]', title: '타이틀명' }] -> cntntsId가 (a) 이면서(and) title이 '타이틀명'인 경우 해당 레이어 속성팝업 비활성화
      
         return {
           popupDisplay: { isActivateOption: false, condition: [{ cntntsId: '[Geoserver 발행 id(a)]' }] }
         }
       },
       //팝업에 커스텀 html 추가
     },
     api: {
       //지도에 표출되고 있는 레이어 목록을 넘기는 곳 (geonserver에 발행된 레이어면서, 레이어 필터링을 해야할 경우 해당 함수에 레이어 리스트를 파라미터로 넘겨야함) 
       //지도에 맨위에 표출되고 있는 레이어 순서대로 layerList를 만들어야함
       getLayerList: (callback) => {
         let layerList = [
         //(필수)linkedLayer [odfLayerObject] : 레이어 객체
         //(필수)odfLayerId [String] : 레이어 마다 고유 id값 (새로고침때마다 ODFId는 계속바뀜) odfLayerObject.getODFId()로 id 값 가져오기 가능
         //(필수)cntntsId [String] : geoserver에 발행된 cntntsId 값
         //(옵션)title [String] : 팝업제목 위치에 표출될 타이틀 값 (명시하지 않을 경우 레이어-1, 레이어-2..의 명칭으로 표출, 팝업 제목을 아예 표출하고 싶지않을 경우 빈값(title : '') 넘기면 됨 )
         //(옵션)visibleRange [Array] : 레이어객체의 줌레벨 최소 최대 값 [a, b] (a min zoomLevel값, b는 maxZoomLevel 값) 가시범위 값을 파라미터로 넘길 경우 부하를 줄일 수 있다.
         //(옵션)layerId [String] : db에 레이어 정보를 insert 하면서, 생성된 레이어 id 값 (코드테이블과 매핑한 데이터를 표출하려면 반드시 필요함)
         {
          linkedLayer: [odf로 만든 레이어 객체(a)], odfLayerId: [odf로 만든 레이어 객체(a)].getODFId(), cntntsId: '[geoserver 발행 id]', title: '타이틀명1'
         },
         {
           linkedLayer: [odf로 만든 레이어 객체(b)], odfLayerId: [odf로 만든 레이어 객체(b)].getODFId(), cntntsId: '[geoserver 발행 id]', title: '타이틀명2'
         },
         {
           linkedLayer: [odf로 만든 레이어 객체(c)], odfLayerId: [odf로 만든 레이어 객체(c)].getODFId(), cntntsId: '[geoserver 발행 id]', title: '타이틀명3'
        },
       ];
       callback(layerList);
     },
   }
 });
 popupWidget.addTo(map);
   
   </code></pre>

    <pre><code class="language-js">//[예제3] 컬럼을 코드테이블과 매핑해서 사용하는 경우 
 var client = oui.HttpClient({
    baseURL: '[api 경로]',
  });
  var commonCodeApi = oui.CommonCodeApi(client, { crtfckey: '[api 키값]' });
  var columnInfoApi = oui.ColumnInfoApi(client, { userId: '[user Id]', crtfckey: '[api 키값]' });
  var popupWidget = new oui.PopupWidget({
    odf: odf
    , options: {
      draggable: true, //팝업 위젯 드래그 사용 여부
    }
   api: {
     //지도에 표출되고 있는 레이어 목록을 넘기는 곳 (geonserver에 발행된 레이어면서, 레이어 필터링을 해야할 경우 해당 함수에 레이어 리스트를 파라미터로 넘겨야함) 
     //지도에 맨위에 표출되고 있는 레이어 순서대로 layerList를 만들어야함
     getLayerList: (callback) => {
       let layerList = [
       //(필수)linkedLayer [odfLayerObject] : 레이어 객체
       //(필수)odfLayerId [String] : 레이어 마다 고유 id값 (새로고침때마다 ODFId는 계속바뀜) odfLayerObject.getODFId()로 id 값 가져오기 가능
       //(필수)cntntsId [String] : geoserver에 발행된 cntntsId 값
       //(옵션)title [String] : 팝업제목 위치에 표출될 타이틀 값 (명시하지 않을 경우 레이어-1, 레이어-2..의 명칭으로 표출, 팝업 제목을 아예 표출하고 싶지않을 경우 빈값(title : '') 넘기면 됨 )
       //(옵션)visibleRange [Array] : 레이어객체의 줌레벨 최소 최대 값 [a, b] (a min zoomLevel값, b는 maxZoomLevel 값) 가시범위 값을 파라미터로 넘길 경우 부하를 줄일 수 있다.
       //(옵션)layerId [String] : db에 레이어 정보를 insert 하면서, 생성된 레이어 id 값 (코드테이블과 매핑한 데이터를 표출하려면 반드시 필요함)        
       {
         linkedLayer: [odf로 만든 레이어 객체(a)], odfLayerId: [odf로 만든 레이어 객체(a)].getODFId(), cntntsId: '[geoserver 발행 id]', title: '타이틀명1', layerId: '[layer id]'
       },
       {
         linkedLayer: [odf로 만든 레이어 객체(b)], odfLayerId: [odf로 만든 레이어 객체(b)].getODFId(), cntntsId: '[geoserver 발행 id]', title: '타이틀명2', layerId: '[layer id]'
       },
       {
         linkedLayer: [odf로 만든 레이어 객체(c)], odfLayerId: [odf로 만든 레이어 객체(c)].getODFId(), cntntsId: '[geoserver 발행 id]', title: '타이틀명3', layerId: '[layer id]'
       },
     ];
     callback(layerList);
   },
   //별칭 및 컬럼 정보 조회 (코드테이블과 매핑한 데이터를 표출할떄만 사용)
   columnInfoFunction: columnInfoApi.columnInfoFunction,
   //상세공통코드 조회 (코드테이블과 매핑한 데이터를 표출할떄만 사용)
   getAllDetailCode: commonCodeApi.getAllDetailCode
   }
 });
 popupWidget.addTo(map);</code></pre>
















    
    </div>

    

    

     

    

    


    

    
        <h3 class="subtitle">Methods</h3>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="addTo">addTo<span class="signature">(flag)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>팝업 위젯 사용</p>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">flag</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            

            

            <div class="param-description"></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>






    <h4 class="method-heading">Example</h4>
    
    <pre><code class="language-js">//레이어 검색 위젯 열기
popupWidget.addTo(map);</code></pre>
















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getEventId">getEventId<span class="signature">()</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>팝업 이벤트 아이디 조회</p>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>






    <h4 class="method-heading">Example</h4>
    
    <pre><code class="language-js">var popupEventId = popupWidget.getEventId();</code></pre>
















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="hilightClear">hilightClear<span class="signature">()</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>하이라이트 제거</p>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>






    <h4 class="method-heading">Example</h4>
    
    <pre><code class="language-js">popupWidget.hilightClear();</code></pre>
















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="remove">remove<span class="signature">()</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>팝업 위젯 제거</p>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>






    <h4 class="method-heading">Example</h4>
    
    <pre><code class="language-js">popupWidget.remove();</code></pre>
















    </article>

        
    

    

    
</article>

</section>




  </div>
</main>

<footer class="layout-footer">
  <div class="container">
    Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.11</a> on Mon Dec 09 2024 10:44:32 GMT+0900 (대한민국 표준시)
  </div>
</footer>



<script src="scripts/prism.dev.js"></script>
</body>
</html>