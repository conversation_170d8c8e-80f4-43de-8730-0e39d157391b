<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
</head>
<body>
	<div id="map" class="odf-view" style="height:550px;"></div>
	<p>SLD 적용 Scale 조회 결과는 개발자지원도구의 console에 나타납니다.</p>
	<p>지도 줌레벨에 따른 스타일 변화를 확인할 수 있습니다.</p>
</body>
<script>


	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::,::coordy::);
	var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);

	/* 줌 컨트롤 생성 */
	var zoomControl = new odf.ZoomControl({
		zoomSlider : true,
	});
	zoomControl.setMap(map);

	/*포인트 레이어 추가*/
	var pointLayer = odf.LayerFactory.produce('geoserver', {
		method : 'get',
		server : '::WmsAPI::',
		layer : '::pointLayer::', // 발행된 레이어 명칭 (ex. 저장소명:레이어명)
		crtfckey : '::crtfckey::',
		service : 'wms',
	});
	pointLayer.setMap(map);
	pointLayer.fit();

	var sldScale = map.getSLDScale();
	console.log(sldScale);

	/*점 스타일 생성*/
	var sld = odf.StyleFactory.produceSLD({
		rules : [ {
			name : 'My Rule', /*룰 이름*/
			/*해당 룰 표현 범위*/
			scaleDenominator : {
				min : sldScale["14"].sldScale,
				max : sldScale["12"].sldScale,
			},
			/*해당 룰 적용 대상 한정
			 ★ 기본 비교
			- filter[0] : 비교연산자 ('==' , '!=' ,  '>' , '<' , '>=', '<=')
			- filter[1] : 칼럼명
			- filter[2] : 기준 값
			★ like 비교
			- filter[0] : '*='
			- filter[1] : 칼럼명
			- filter[2] : 비교 문자열 (wildCard="*" singleChar="." escape="!")
							 (ex 1) *_2  => [somthing] + '_2'
							 (ex 2) *_.   => [somthing] + '_' +[어떤 문자이든 한개의 문자]
				 ★ null 비교
				- filter[0] : 비교연산자 ('==' , '!=')
				- filter[1] : 칼럼명
				- filter[2] : null
				 ★ 두개 이상의 조건
				- filter[0] : 논리연산자('&&','||')
				- filter[1] : 조건1
				- filter[2] : 조건2
				(ex) filter:['&&',['>=','id','3'],['!=',id,null]]
			 */
			//filter: ['<', 'id', '20'], //[기본 비교][ , 칼럼명, 기준값]
			//filter: ['*=', 'type', '*_2'], //[like비교] wildCard="*" singleChar="." escape="!"
			//filter: ['!=', 'id', null], //[isnull비교]
			//filter:['&&',['>=','id','3'],['!=',id,null], ...]//[두개 이상의 조건]
			symbolizers : [ {
				kind : 'Mark',
				/*포인트에 표현될 도형 종류
				- 'circle' : 원
				- 'square' : 사각형
				- 'triangle' : 세모
				- 'star' : 별 모양
				- 'cross' :  + 모양
				- 'x' : x 모양
				 */
				wellKnownName : 'Square',

				/*포인트에 표현될 도형의 반지름*/
				radius : 6,
				/*포인트에 표현될 도형의 채우기색
					rgba 값 입력시 자동변환. 네번째 인수 (투명도) 존재시 fillOpacity보다 우선 적용됨
				 */
				color : '#FF0000',
				/*포인트에 표현될 도형의 채우기색 투명도 0~1*/
				fillOpacity : 0.5,

				/*포인트에 표현될 도형의 윤곽선 색
					rgba 값 입력시 자동변환. 네번째 인수 (투명도) 존재시 strokeOpacity보다 우선 적용됨
				 */
				strokeColor : '#0000FF',
				/*포인트에 표현될 도형의 윤곽선 투명도 0~1*/
				strokeOpacity : 0.7,
				/*포인트에 표현될 도형의 윤곽선 두께*/
				strokeWidth : 3,
			/*offset 적용
			 - [x이동량,y이동량]
			 */
			//offset: [0, -2000], //coordinate 단위로 이동
			/*offset 적용 시킬 geometry 타입 칼럼명*/
			//offsetGeometry: 'the_geom'
			}, ],
		}, ]
	});

	//sld 적용
	pointLayer.setSLD(sld);
</script>
</html>
