<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
</head>
<body>
	<div id="map" class="odf-view" style="height:550px;"></div>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::,::coordy::);
	var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);


	/* 출력 컨트롤 생성 */
	var printControl = new odf.PrintControl(/*{
		//출력 제목
		title : '개발자지원센터 출력 컨트롤 샘플',
		//출력 대상 element 선택자
		targetElementSelector : '#mapContainer',
		//출력 제외 element 선택자
        exceptionElementSelectors : ['#map_odfControl'],
        proxyURL : 'proxyUrl.jsp',
        proxyParam : 'url'
	}*/);
	printControl.setMap(map);

	/* 저장 컨트롤 생성 */
	var downloadControl = new odf.DownloadControl(/*{
		// 출력 대상 element 선택자
		targetElementSelector : '#mapContainer',
		//출력 제외 element 선택자
        exceptionElementSelectors : ['#map_odfControl'],
        proxyURL : 'proxyUrl.jsp',
        proxyParam : 'url'
	}*/);
	downloadControl.setMap(map);
</script>
</html>
