@charset "UTF-8";

/*----------- common start ------------*/
html, body {
	width: 100%;
	height: 100%;
	overflow:auto;
}

html {
	overflow: hidden;
} 

dl, li, menu, ol, ul {
    list-style: none;
}


blockquote, body, button, code, dd, div, dl, dt, fieldset, form, h1, h2, h3, h4, h5, h6, input, legend, li, ol, pre, select, td, textarea, th, ul {
    margin: 0;
    padding: 0;
}

a {
    color: #333;
    text-decoration: none
}

a:active,a:hover {
    text-decoration: underline;
}


em {
	font-style: normal;	
}

.page-link a {
	border-bottom: 1px dashed #1a86ae;
	padding-bottom: 1px;
	color: #1a86ae;
}

.page-link a:hover {
	text-decoration: none;
}

.no-margin {
	margin: 0px !important;
}

.mg-top-10 {
	margin-top: 10px; 
}

/*----------- common end ------------*/

/*----------- gnb start ------------*/
#gnblogo {
    background-color: #292929;
    text-align: center;
    height: 89px;
    width: 100%;
    position: relative;
    z-index: 11
}

#gnblogo a {
    display: block;
    padding: 21px 0 23px;
    color: #7e8788 !important;
    font-size: 15px;
    letter-spacing: -.5px;
    position: relative;
    -webkit-transition: all .5s;
    transition: all .5s;
    word-break: keep-all;
}

#gnblogo a:hover {
    color: #fff !important;
}


#gnb {
    position: fixed;
    width: 86px;
    left: 0;
    top: 0;
    bottom: 0;
    background-color: #292929;
    z-index: 10;
    font-family: Helvetica,Arial,sans-serif;
}

#gnb h1 {
	font-family: Helvetica,Arial,sans-serif;
	font-size: 13px;
	font-weight: bold;
	margin:0;
	letter-spacing: 0;
	line-height: 1.2;
}

#gnbMenu {
    position: relative;
    z-index: 11;
}

#gnbMenu li {
    background-color: #ffcd36;
    border-bottom: 1px solid #302A24;
    transition: background .5s ease;
    -webkit-transition: background .5s ease;
    position: relative;
}

#gnbMenu li:last-child {
    border: none
}

#gnbMenu li:hover {
    background-color: #302A24
}

#gnbMenu .selected:hover,
#gnbMenu li.selected {
    background-color: #f9fafb
}

#gnbMenu li>a {
    display: block;
    width: 100%;
    height: 100%;
    font-size: 13px;
    line-height: 44px;
    text-align: center;
    color: #302A24
}

#gnbMenu li:hover>a {
    color: #f7fbfd
}

#gnbMenu li.selected>a {
    color: #302A24
}

#gnbMenu li.disable>a {
    color: #7e8389
}

#gnbMenu .disable,
#gnbMenu .disable:hover {
    background-color: #363a3e;
    border-color: #302A24
}

#gnbMenu .disable a {
    cursor: default
}
/*----------- gnb end ------------*/

/*----------- search start ------------*/
.panel_search {
    position: fixed;
    width: 280px;
    height: 77px;
    top: 0;
    left: 86px;
    font-size: 12px;
    background-color: #e8eced;
    z-index: 10;
}

#boxSearch {
    position: fixed;
    height: 25px;
    margin: 28px 20px 0 18px;
    padding: 6px 6px 0;
    border: 1px solid #D3D4D6;
    border-radius: 5px;
    width: 227px;
    background-color: #fff
}

#boxSearch .tf_query {
    width: 100%;
    border: 0;
    border-radius: 2px;
    font-size: .93em;
    line-height: 16px;
    background-color: transparent;
    color: #000;
    vertical-align: top;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    height:23px;
}

#boxSearch.write_on .btn_reset {
    display: block;
}

#boxSearch.focus .btn_dummy_search,
#boxSearch.write_on .btn_dummy_search {
    display: none;
}

#boxSearch.write_on .tf_query {
    width: 207px;
}

#boxSearch .btn_reset {
    position: absolute;
    top: -1px;
    right: -2px;
    display: none;
    float: left;
    width: 30px;
    height: 30px;
    border: 0;
    font-size: 0;
    line-height: 0;
    background-color: transparent;
    cursor: pointer;
    text-align: center
}

#boxSearch .btn_reset .ico_del {
    display: block;
    width: 17px;
    height: 17px;
    margin: 0 auto 1px;
    background: url(../images/search_icon.png) 0 -36px no-repeat
}

#boxSearch .btn_dummy_search {
    position: absolute;
    top: 2px;
    right: -2px;
    display: block;
    float: left;
    width: 30px;
    height: 30px;
    border: 0;
    font-size: 0;
    line-height: 0;
    background-color: transparent;
    cursor: default;
    text-align: center
}

#boxSearch .btn_dummy_search .ico_sch {
    display: block;
    width: 17px;
    height: 17px;
    margin: 6px auto 1px;
    background: url(../images/search_icon.png) 0 1px no-repeat
}
/*----------- search end ------------*/

/*----------- submenu start ------------*/
#submenu {
	position: fixed;
    width: 280px;
    left: 86px;
    top: 77px;
    bottom: 0;
    overflow: auto;
    font-size: 14px;
    background-color: #e8eced;
    z-index:10;
}

#submenu ul {
	margin: 4px 25px 25px;
}

#submenu ul li {
	padding-bottom: 10px;
	position: relative;
}

#submenu h2 {
	font-size: 1.3em;
	margin-top: 10px;
}

#submenu .selected {
	text-decoration: underline;
	font-weight: bolder;
}

#submenu .tooltip {
	display: none;
	color:#fff;
	font-size: 11px;
	background-color: #626262;
	border-radius: 2px;
	text-align: left;
	padding: 5px;
	width: 100%;
	position: absolute;
	left:0;
	bottom:35px;
	z-index: 1000;
}

#submenu .tooltip ul {
	font-size: inherit !important;
	color: inherit !important;
}

#submenu li:hover .tail {
	position: absolute;
	border-top: solid #626262 8px;
	border-left: solid transparent 6px;
	border-right: solid transparent 6px;
	margin-left:8px;
	bottom: 28px;
}

#submenu li:hover .tooltip {
	display:inline-block;
}

/*----------- submenu end ------------*/

/*----------- contents start ------------*/
#contents {
    margin-left: 366px;
    padding: 40px;
    width: 1100px;
}

/*----------- contents end ------------*/

/*----------- tab start ------------*/
.tabs {
	list-style: none;
	margin: 0;
	padding: 0;
	overflow: hidden;
}

.tabs li {
	float: left;
	display: inline-block;
	color: #000;
	text-align: center;
	text-decoration: none;
	padding: 14px 16px;
	font-size: 14px;
	transition: 0.3s;
	background-color: #eee;
	margin-left: 5px;
	cursor: pointer;
}

.tabs li.current {
	background-color: #ccc;
	color: #222;
}


.tab-pannel.current {
	display: block;
}

.tab-pannel {
	display: none;
}
/*----------- tab end ------------*/

/*----------- guide start ------------*/
#guide-doc h2 {
	font-size: 1.75em;
	padding-bottom:18px;
	border-bottom: 1px solid #bfbfbf;
	margin-top: 50px;
}

#guide-doc h2+p {
	margin-left: 0;
}

#guide-doc h3 {
	font-size: 1.5em;
	margin: 30px 0 0 10px;
}

#guide-doc p,
#guide-doc table {
	margin-left: 33px;
}

#guide-doc ol {
	margin: 20px 0 20px 45px;
}

#guide-doc ul {
	margin: 20px 0 20px 33px;
}

#guide-doc li {
    margin-bottom:5px;
}

#guide-doc ol li {
	list-style: decimal;
}

#guide-doc table th {
	border-bottom: 2px solid #bbb;
	white-space: nowrap;
	padding: 8px 10px;
}

#guide-doc table td {
	border-bottom: 1px solid #ddd;
	padding: 8px 10px;
}

#guide-doc code {
	padding: 2px 5px;
	background-color: #f3f5f5;
	border: 1px solid #ddd;
	border-radius: 3px;
}

#guide-doc pre code {
	border: none;
	background-color: transparent;
}

#guide-doc .codeblock {
    margin: 0 0 15px 33px;
}
/*----------- guide end ------------*/

/*----------- modal start ------------*/
.modal-wrap {
	position: fixed;
	top: 0;
	left: 0;
	width:100%;
	height:100%;
	z-index: 10000000;
	background-color: rgba(0,0,0,0.5);
}
.modal {
	position: fixed;
	top: 5%;
	left: 50%;
	width:90%;
	height:90%;
	margin-left: -45%;
	background-color: #fff;
}
.modal .close {
	position: absolute;
	top: 8px;
	right: 15px;
	width:23px;
	height: 23px;
	cursor: pointer;
	background: url(../images/live/livecoding.png);
}
.modal iframe {
	width:100%;
	height:100%;
	border:0;
}
/*----------- modal end ------------*/

/*----------- wizard start ------------*/
#wizardmenu {
	position: fixed;
    width: 280px;
    left: 86px;
    top: 0px;
    bottom: 0;
    overflow: auto;
    font-size: 13px;
    background-color: #e8eced;
    padding-top: 10px;
	padding-left: 18px;
}

#wizardmenu .open, 
#wizardmenu .close {
	float: right;
	color: #8d8d8d;
	font-size: 11px;
	padding: 9px 5px 0 0;
	cursor: pointer;
}

#wizardmenu .open {
	display: none;
}

#wizardmenu input,
#wizardmenu select {
	font-size:12px;
}

#wizardmenu .control {
	margin-bottom: 15px;
}

#wizardmenu .wrap {
	margin-bottom: 10px;
}

#wizardmenu h3 {
	padding: 9px 0;
	display: inline-block;
}

#wizardmenu h4 {
	padding: 5px 0;
}

#wizardmenu input[type=text],
#wizardmenu input[type=number] {
	width:60px;
	display: block;
}

#wizardmenu input[type=text],
#wizardmenu input[type=number],
#wizardmenu input[type=checkbox],
#wizardmenu label,
#wizardmenu select {
	vertical-align: middle;
	line-height: 1.2em;
}

#wizardmenu .helpwrap {
	position: relative;
}

#wizardmenu .help {
	display: inline-block;
	text-align: center;
	cursor: pointer;
	color: #7a7a7a;
	background-color: #c9c9c9;
	width:15px;
	height:15px;
	border-radius: 50%;
	font-size:11px;
}

#wizardmenu .desc {
	display:none;
	color:#fff;
	background-color: #626262;
	border-radius: 2px;
	text-align: left;
	padding: 5px;
	width: 250px;
	position: absolute;
	left:0;
	bottom:20px;
	z-index: 1000;
}

#wizardmenu .help:hover .tail {
	position: absolute;
	border-top: solid #626262 8px;
	border-left: solid transparent 6px;
	border-right: solid transparent 6px;
	bottom:13px;
	margin-left:-8px;
}

#wizardmenu .help:hover .desc {
	display:block;
}


#wizardmenu .inline {
	display: inline-block !important;
}

/*----------- wizard end ------------*/
/*----------- swagger start ------------*/
.opblock-body ul {
	list-style-type: disc;
    padding-inline-start: 40px;
    color:#5555ff;
    font-size:1.2em;
}
.opblock-body li {
	list-style-type: disc;
	padding-bottom: 5px;
}

.opblock-body li span {
	text-decoration: underline;
}
/*----------- swagger end ------------*/

/*----------- scroll buttons start ------------*/
#scrollbuttons {
	position: fixed;
	bottom: 50px;
	right: 50px;
	width: 50px;
	height: 105px;
	overflow: hidden;
}
#scrollbuttons a {
	width: 30px;
	height: 30px;
	background-color: #ff7b00;
	font-weight: bold;
	padding: 3px;
	margin: 3px;
	display: block;
	text-align: center;
	font-size: 16px;
	color: #000;
}
#scrollbuttons a:hover {
	text-decoration: none;
	color: #fff;
}
/*----------- scroll buttons end ------------*/