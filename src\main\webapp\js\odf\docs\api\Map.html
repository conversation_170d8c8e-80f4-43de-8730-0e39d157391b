<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>GeOnPaas ui widgets: Map</title>
    
      <link type="text/css" rel="stylesheet" href="styles/vendor/prism-tomorrow-night.css">
    
    <link type="text/css" rel="stylesheet" href="styles/styles.css">
    
    
    <style>
      :root {
      
      
        --nav-width: 370px;
      
      
        --nav-heading-margin-top: 0.5em;
      
      }
    </style>
    
</head>
<body>

<header class="layout-header">
  
  <h1>
    <a href="./index.html">
      GeOnPaas ui widgets
    </a>
  </h1>
  <nav class="layout-nav">
    <ul><li class="nav-heading">Classes</li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="BasemapControl.html">BasemapControl</a></span><span class="nav-desc"><p>배경지도 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="BookmarkControl.html">BookmarkControl</a></span><span class="nav-desc"><p>북마크 컨트롤 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ClearControl.html">ClearControl</a></span><span class="nav-desc"><p>지도 그리기 이벤트 초기화 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ColorFactory.html">ColorFactory</a></span><span class="nav-desc"><p>색 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Control.html">Control</a></span><span class="nav-desc"><p>사용자 정의 컨트롤 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Coordinate.html">Coordinate</a></span><span class="nav-desc"><p>좌표 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DivideMapControl.html">DivideMapControl</a></span><span class="nav-desc"><p>지도 분할 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DownloadControl.html">DownloadControl</a></span><span class="nav-desc"><p>다운로드 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DrawControl.html">DrawControl</a></span><span class="nav-desc"><p>그리기 도구 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Easing.html">Easing</a></span><span class="nav-desc"><p>애니메이션 효과</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="event.html">event</a></span><span class="nav-desc"><p>이벤트 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Extent.html">Extent</a></span><span class="nav-desc"><p>영역 관련 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Feature.html">Feature</a></span><span class="nav-desc"><p>Feature 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FeatureFactory.html">FeatureFactory</a></span><span class="nav-desc"><p>Feature 생성을 위한 FeatureFactory 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FormatFactory.html">FormatFactory</a></span><span class="nav-desc"></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FullScreenControl.html">FullScreenControl</a></span><span class="nav-desc"><p>전체화면 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="HomeControl.html">HomeControl</a></span><span class="nav-desc"><p>홈 이동 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Layer.html">Layer</a></span><span class="nav-desc"><p>레이어 관리 클래스로, 레이어는 odf.LayerFactory를 통해서만 생성가능하다.</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LayerFactory.html">LayerFactory</a></span><span class="nav-desc"><p>레이어 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LayerInfoControl.html">LayerInfoControl</a></span><span class="nav-desc"><p>레이어 정보 조회 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Map.html">Map</a></span><span class="nav-desc"><p>지도 생성, 조작, 컴퍼넌트, 레이어 추가 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Marker.html">Marker</a></span><span class="nav-desc"><p>마커 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MeasureControl.html">MeasureControl</a></span><span class="nav-desc"><p>지도 측정 도구 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MousePositionControl.html">MousePositionControl</a></span><span class="nav-desc"><p>마우스 좌표 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MoveControl.html">MoveControl</a></span><span class="nav-desc"><p>현재 화면 기준으로 이전/다음 화면 이동 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="OverviewMapControl.html">OverviewMapControl</a></span><span class="nav-desc"><p>인덱스맵 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Popup.html">Popup</a></span><span class="nav-desc"><p>팝업 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="PrintControl.html">PrintControl</a></span><span class="nav-desc"><p>프린트 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Projection.html">Projection</a></span><span class="nav-desc"><p>좌표 변환 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="RotationControl.html">RotationControl</a></span><span class="nav-desc"><p>화면을 회전 시키는 기능
alt + shift 드래그로 지도 회전</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ScaleControl.html">ScaleControl</a></span><span class="nav-desc"><p>축척 표시 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="SLD.html">SLD</a></span><span class="nav-desc"><p>WMS 스타일 관리 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Style.html">Style</a></span><span class="nav-desc"><p>스타일 관리 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="StyleFactory.html">StyleFactory</a></span><span class="nav-desc"><p>스타일 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="StyleFunction.html">StyleFunction</a></span><span class="nav-desc"><p>스타일 Function 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="SwiperControl.html">SwiperControl</a></span><span class="nav-desc"><p>지도 스와이퍼 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ZipControl.html">ZipControl</a></span><span class="nav-desc"><p>Server없이 Layer 생성하는 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ZoomControl.html">ZoomControl</a></span><span class="nav-desc"><p>지도 줌 설정클래스</p></span></li></ul><li class="nav-heading"><a href="global.html">Globals</a></li>
  </nav>
</header>


<main class="layout-main ">
  <div class="container">
    <p class="page-kind">Class</p>
    <h1 class="page-title">Map</h1>
    




<section>


<header class="class">


    
        
        <!-- <h2>Map</h2> -->

        

        
            <h4 class="method-heading">Summary</h4>
            <div class="class-summary"><p>Map 생성 클래스</p>
<pre class="prettyprint source lang-javascript"><code>let mapContainer = document.getElementById('map');
const center = new odf.Coordinate(955156.7761, 1951925.0984);
let mapOption = {
  center: center,
  zoom: 15,
  projection: 'EPSG:5179',
  maxZoom: 20,
  minZoom: 8,
};
let map = new odf.Map(mapContainer, mapOption);
</code></pre></div>
        

        
            <h4 class="method-heading">Description</h4>
            <div class="class-description"><p>지도 생성, 조작, 컴퍼넌트, 레이어 추가 설정 클래스</p></div>
        
    
</header>

<article>
    <div class="container-overview">



    
        





    


    
    <h3 class="subtitle">Constructor</h3>
    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="Map">new Map<span class="signature">(mapContainer, mapOption)</span><span class="return-type-signature"></span>
    </h4>













    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">mapContainer</span>
            

            
                


    <span class="param-type">
        <code>Element</code>
    </span>
    

            

            

            

            <div class="param-description"><p>지도에 사용할 Div Element</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">mapOption</span>
            

            
                


    <span class="param-type">
        <code>Object</code>
    </span>
    

            

            

            

            <div class="param-description"><p>지도 생성에 사용할 옵션</p></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">center</span>
            

            
                


    <span class="param-type">
        <code>Odf.Coordinate</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                

                
                </span>
            

            

            <div class="param-description"><p>지도 중심점 좌표</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">zoom</span>
            

            
                


    <span class="param-type">
        <code>Number</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                

                
                </span>
            

            

            <div class="param-description"><p>현재 확대 레벨</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">maxZoom</span>
            

            
                


    <span class="param-type">
        <code>Number</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                

                
                </span>
            

            

            <div class="param-description"><p>최대 확대레벨</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">minZoom</span>
            

            
                


    <span class="param-type">
        <code>Number</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                

                
                </span>
            

            

            <div class="param-description"><p>최소 확대레벨</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">projection</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                

                
                </span>
            

            

            <div class="param-description"><p>좌표계 SRS ID</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">proxyURL</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                

                
                </span>
            

            

            <div class="param-description"><p>프록시 URL</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">proxyParam</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                

                
                </span>
            

            

            <div class="param-description"><p>프록시에서 사용할 PARAM명 없으면 PARAMETER명 없이 넘김</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">apiGateWayKey</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                

                
                </span>
            

            

            <div class="param-description"><p>api GateWay Key</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">basemap</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#odf_basemap_option">odf_basemap_option</a></code>
    </span>
    

            

            
                <span class="param-attributes">
                

                

                
                </span>
            

            

            <div class="param-description"><p>베이스맵 옵션(사용할 베이스맵 선택)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">baroEMapURL</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                

                
                </span>
            

            

            <div class="param-description"><p>바로e맵 경로</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">baroEMapKey</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                

                
                </span>
            

            

            <div class="param-description"><p>바로e맵 API KEY</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">vWorldURL</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                

                
                </span>
            

            

            <div class="param-description"><p>vWord url</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">vWorldKey</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                

                
                </span>
            

            

            <div class="param-description"><p>[선택] vWord API KEY</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">kakaoURL</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                

                
                </span>
            

            

            <div class="param-description"><p>kakao url</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">kakaoSkyviewURL</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                

                
                </span>
            

            

            <div class="param-description"><p>kakao 스카이뷰 url</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">controlGroup</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                

                
                </span>
            

            

            <div class="param-description"><p>컨트롤 그룹 구조</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">optimization</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                

                
                </span>
            

            

            <div class="param-description"><p>배경지도 최적화 기능 사용여부</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">wfsPostPropertyNameEncodeYn</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                

                
                </span>
            

            

            <div class="param-description"><p>wfs 포스트 getFeature요청시 propertyName 파라미터 인코딩 여부</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">wfsPostCQLFilterTwiceEncodeYn</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                

                
                </span>
            

            

            <div class="param-description"><p>wfs 포스트 getFeature요청시 cql_filter 파라미터 인코딩 여부</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">wmsPostCQLFilterTwiceEncodeYn</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                

                
                </span>
            

            

            <div class="param-description"><p>wms 포스트 getFeature요청시 cql_filter 파라미터 인코딩 여부</p>
<p>사용할 컨트롤에 대해서는 모두 정의되있어야함.
(ex)  [
['basemap'],
['move'],
['zoom'],
['initscreen', 'rotate', 'fullscreen'],
['clear', 'draw', 'measure', 'image', 'print', 'download', 'overviewmap'],
['swiper', 'dividemap'],
];</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">crtfckey</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>api 사용시 인증 키</p></div>
            
        </li>

    
</ul>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    
    </div>

    

    

     

    

    


    

    
        <h3 class="subtitle">Methods</h3>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="deleteVertex">deleteVertex<span class="signature">(extentFeature, targetFeature, callback)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>지도에서 클릭한 Feature를 선택</p>
<pre class="prettyprint source lang-javascript"><code> map.deleteVertex(extentFeature, targetFeature);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">extentFeature</span>
            

            
                


    <span class="param-type">
        <code><a href="Feature.html">Feature</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>버텍스를 삭제할 범위를 지정할 feature</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">targetFeature</span>
            

            
                


    <span class="param-type">
        <code><a href="Feature.html">Feature</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>버텍스 삭제 대상 feature</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">callback</span>
            

            
                


    <span class="param-type">
        <code>function</code>
    </span>
    |

    <span class="param-type">
        <code>Object</code>
    </span>
    

            

            

            

            <div class="param-description"><p>결과값 리턴받을 콜백 함수 {original : 원본피쳐, changed : 변경된 피쳐}</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="findLayer">findLayer<span class="signature">(odfId)</span><span class="return-type-signature"> &rarr; {odf.Layer}</span>
    </h4>





<div class="method-description">
    
    <p>지도객체에 추가되있는 레이어를 odfId로 찾아 반환</p>
<pre class="prettyprint source lang-javascript"><code>const layerId = layer.getODFId();
const findLayer = map.findLayer(layerID);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">odfId</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>: 레이어Id</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>odf.Layer</code>
            
            
                <p>레이어 id와 일치하는 레이어</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="forEachFeatureAtPixel">forEachFeatureAtPixel<span class="signature">(pixel, callback, layerFlag, options)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>해당 픽셀 아래 도형이 있으면 각 도형별로 콜백 호출</p>
<pre class="prettyprint source lang-javascript"><code>//마우스 포인터 아래 도형정보가 있다면 커서를 포인터 형태로 변경
odf.event.addListener(map, 'pointermove', function (evt) {
     map.forEachFeatureAtPixel(evt.pixel,(feature, layer)=>{
       // somthing to do
     },true,{

     });
   })
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">pixel</span>
            

            
                


    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    

            

            

            
                
            

            <div class="param-description"><p>도형을 조회할 위치</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">callback</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#forEachFeatureAtPixel_callback">forEachFeatureAtPixel_callback</a></code>
    </span>
    

            

            

            
                
            

            <div class="param-description"><p>해당 위치에 도형이 있다면 호출되는 콜백</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">layerFlag</span>
            

            
                


    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    

            

            

            
                
                    <span class="param-default">
                        true
                    </span>
                
            

            <div class="param-description"><p>레이어 반환 여부</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">options</span>
            

            
                


    <span class="param-type">
        <code>Object</code>
    </span>
    

            

            

            
                
            

            <div class="param-description"><p>도형 조회 옵션</p></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">layerFilter</span>
            

            
                


    <span class="param-type">
        <code>function</code>
    </span>
    

            

            

            

            <div class="param-description"><p>레이어 필터 함수</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">hitTolerance</span>
            

            
                


    <span class="param-type">
        <code>Number</code>
    </span>
    

            

            

            

            <div class="param-description"><p>감지 허용 오차(css 픽셀 단위)</p></div>
            
        </li>

    
</ul>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getCenter">getCenter<span class="signature">()</span><span class="return-type-signature"> &rarr; {odf.Coordinate}</span>
    </h4>





<div class="method-description">
    
    <p>지도 중심점 좌표 정보 조회</p>
<pre class="prettyprint source lang-javascript"><code>let map = new odf.Map(mapContainer, mapOption);
const center = map.getCenter();
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>odf.Coordinate</code>
            
            
                <p>중심 좌표</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getCoordinateFromPixel">getCoordinateFromPixel<span class="signature">(pixel)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>coordinate 값을 화면상의 좌표값(pixel)로 변경</p>
<pre class="prettyprint source lang-javascript"><code>//화면상의 위치(pixel)를 지도상의 좌표값 값으로 변경
map.getCoordinateFromPixel([x,y]);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">pixel</span>
            

            
                


    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    

            

            

            

            <div class="param-description"><p>화면상의 위치(pixel)</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getDragTranslateTarget">getDragTranslateTarget<span class="signature">()</span><span class="return-type-signature"></span>
    </h4>



    <h4 class="method-heading">Summary</h4>
    <p>벡터 레이어의 도형 변형(이동) 대상 도형 조회</p>
<pre class="prettyprint source lang-javascript"><code>//DragTranslate 인터렉션 활성화 - 선택한 도형 변형(확대/축소/회전/이동) 가능
map.setDragTranslate(true,{
      translateType : [  'move', 'rotate','resize'], //사용할 변형 기능
      targetType : 'feature', // 변형 대상 유형
      targetLayer : wfsLayer,// 레이어 제한
      //targetFeature : feature,//변경 대상 도형 지정.</code></pre>



<div class="method-description">
    <h4 class="method-heading">Description</h4>
    <p>벡터 레이어의 도형 변형(이동) 대상 도형 조회</p>
<pre class="prettyprint source lang-javascript"><code>//DragTranslate 인터렉션 활성화 - 선택한 도형 변형(확대/축소/회전/이동) 가능
map.setDragTranslate(true,{
      translateType : [  'move', 'rotate','resize'], //사용할 변형 기능
      targetType : 'feature', // 변형 대상 유형
      targetLayer : wfsLayer,// 레이어 제한
      //targetFeature : feature,//변경 대상 도형 지정. 미정의시 클릭한 도형 편집 활성화
      changeTargetFeature : true,//변형 대상 도형 변경 가능 여부
      //hilightStyle : {
      //  fill : { color: [0, 0, 0, 0.4] },
      //  stroke : { color: [255, 0, 0, 0.7], width: 2 },
      //  image : {
      //    circle: {
      //      fill: { color: [0, 0, 0, 0.4] },
      //      stroke: { color: [255, 0, 0, 0.7], width: 2 },
      //      radius: 10,
      //    },
      //  }
      //}
});
//벡터 레이어의 도형 변형(이동) 대상 도형 조회
map.getDragTranslateTarget();

//DragTranslate 인터렉션 비활성화 - 도형 변형(확대/축소/회전/이동) 기능 비활성화
//map.setDragTranslate(false);
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getInfo">getInfo<span class="signature">()</span><span class="return-type-signature"> &rarr; {<a href="global.html#mapInfo">mapInfo</a>}</span>
    </h4>





<div class="method-description">
    
    <p>지도 정보 조회</p>
<pre class="prettyprint source lang-javascript"><code>let map = new odf.Map(mapContainer, mapOption);
const mapInfo = map.getInfo();
const zoom = mapInfo.zoom;
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>mapInfo</code>
            
            
                <p>지도 정보</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getMarker">getMarker<span class="signature">(markerId)</span><span class="return-type-signature"> &rarr; {<a href="Marker.html">Marker</a>}</span>
    </h4>





<div class="method-description">
    
    <p>지도에 등록된 Marker 중 특정 id를 갖는 마커 조회</p>
<pre class="prettyprint source lang-javascript"><code>map.getMarker('M_012654987');
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">markerId</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>조회할 마커의 고유 id</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Marker</code>
            
            
                <ul>
<li>markerId를 id로 갖는 Marker 반환</li>
</ul>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getMarkers">getMarkers<span class="signature">()</span><span class="return-type-signature"> &rarr; {js_Map.&lt;String, <a href="Marker.html">Marker</a>>}</span>
    </h4>





<div class="method-description">
    
    <p>지도에 등록된 모든 Marker  조회</p>
<pre class="prettyprint source lang-javascript"><code>map.getMarkers();
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>js_Map.&lt;String, Marker></code>
            
            
                <ul>
<li>지도에 등록된 모든 Marker를 보관하는 javascript Map 객체</li>
</ul>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getMaxResolution">getMaxResolution<span class="signature">()</span><span class="return-type-signature"> &rarr; {number}</span>
    </h4>





<div class="method-description">
    
    <p>지도 최대 레졸루션 조회</p>
<pre class="prettyprint source lang-javascript"><code>let map = new odf.Map(mapContainer, mapOption);
map.getMaxResolution();
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>number</code>
            
            
                <p>설정된 지도 최대 레졸루션</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getMaxZoom">getMaxZoom<span class="signature">()</span><span class="return-type-signature"> &rarr; {number}</span>
    </h4>





<div class="method-description">
    
    <p>지도 최대 줌레벨 조회</p>
<pre class="prettyprint source lang-javascript"><code>let map = new odf.Map(mapContainer, mapOption);
map.getMaxZoom();
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>number</code>
            
            
                <p>설정된 지도 최대 줌레벨</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getMinResolution">getMinResolution<span class="signature">()</span><span class="return-type-signature"> &rarr; {number}</span>
    </h4>





<div class="method-description">
    
    <p>지도 최소 레졸루션 조회</p>
<pre class="prettyprint source lang-javascript"><code>let map = new odf.Map(mapContainer, mapOption);
map.getMinResolution();
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>number</code>
            
            
                <p>설정된 지도 최소 레졸루션</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getMinZoom">getMinZoom<span class="signature">()</span><span class="return-type-signature"> &rarr; {number}</span>
    </h4>





<div class="method-description">
    
    <p>지도 최소 줌레벨 조회</p>
<pre class="prettyprint source lang-javascript"><code>let map = new odf.Map(mapContainer, mapOption);
map.getMinZoom();
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>number</code>
            
            
                <p>설정된 지도 최소 줌레벨</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getODFLayer">getODFLayer<span class="signature">(searchString, likeStatus)</span><span class="return-type-signature"> &rarr; {List.&lt;<a href="Layer.html">Layer</a>>}</span>
    </h4>





<div class="method-description">
    
    <p>지도에 등록된 레이어를 odfId로 조회</p>
<pre class="prettyprint source lang-javascript"><code>//odfId가 'odf-jsonLayer-vector1705996383773s97sq0a2l4h'인 레이어 찾기
map.getODFLayer('odf-jsonLayer-vector1705996383773s97sq0a2l4h');

//odfId에 'jsonLayer' 문자열이 포함된 레이어 찾기
map.getODFLayer('odf-jsonLayer-vector1705996383773s97sq0a2l4h',-1);

//odfId가 'odf-jsonLayer-vector1705996383773s97sq0a2l4h'인 레이어 찾기
map.getODFLayer('odf-jsonLayer-vector1705996383773s97sq0a2l4h',0);

//odfId가 'odf-jsonLayer-'로 시작하는 레이어 찾기
map.getODFLayer('odf-jsonLayer-',1);

//odfId가 'unique'로 끝나는는 레이어 찾기
map.getODFLayer('unique',2);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">searchString</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>조회할 레이어의 id</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">likeStatus</span>
            

            
                


    <span class="param-type">
        <code>Number</code>
    </span>
    

            

            

            

            <div class="param-description"><p>검색조건</p>
<ul>
<li>searchString 포함 : -1</li>
<li>searchString과 일치(기본값) : 0,</li>
<li>searchString으로 시작 : 1,</li>
<li>searchString으로 끝남 : 2</li>
</ul></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>List.&lt;Layer></code>
            
            
                <p>지도에 등록된 모든 레이어 목록</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getODFLayerList">getODFLayerList<span class="signature">()</span><span class="return-type-signature"> &rarr; {List.&lt;<a href="Layer.html">Layer</a>>}</span>
    </h4>





<div class="method-description">
    
    <p>지도에 등록된 모든 레이어  조회</p>
<pre class="prettyprint source lang-javascript"><code>map.getODFLayerList();
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>List.&lt;Layer></code>
            
            
                <ul>
<li>지도에 등록된 모든 레이어 목록</li>
</ul>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getODFLayers">getODFLayers<span class="signature">()</span><span class="return-type-signature"> &rarr; {List.&lt;<a href="Layer.html">Layer</a>>}</span>
    </h4>





<div class="method-description">
    
    <p>지도에 등록된 모든 레이어  조회</p>
<pre class="prettyprint source lang-javascript"><code>map.getODFLayers();
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>List.&lt;Layer></code>
            
            
                <ul>
<li>지도에 등록된 모든 레이어 목록</li>
</ul>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getPixelFromCoordinate">getPixelFromCoordinate<span class="signature">(coordinate)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>coordinate 값을 화면상의 좌표값(pixel)로 변경</p>
<pre class="prettyprint source lang-javascript"><code>//지도상의 좌표값을 화면상의 위치(pixel)값으로 변경
map.getPixelFromCoordinate([x,y]);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">coordinate</span>
            

            
                


    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    

            

            

            

            <div class="param-description"><p>좌표(지도 좌표계)</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getPopup">getPopup<span class="signature">(popupId)</span><span class="return-type-signature"> &rarr; {<a href="Popup.html">Popup</a>}</span>
    </h4>





<div class="method-description">
    
    <p>지도에 등록된 Popup 중 특정 id를 갖는 Popup 조회</p>
<pre class="prettyprint source lang-javascript"><code>map.getPopup('P_012654987');
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">popupId</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>조회할 팝업의 고유 id</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Popup</code>
            
            
                <ul>
<li>popupId를 id로 갖는 Popup 반환</li>
</ul>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getPopups">getPopups<span class="signature">()</span><span class="return-type-signature"> &rarr; {js_Map.&lt;String, <a href="Popup.html">Popup</a>>}</span>
    </h4>





<div class="method-description">
    
    <p>지도에 등록된 모든 Popup  조회</p>
<pre class="prettyprint source lang-javascript"><code>map.getPopups();
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>js_Map.&lt;String, Popup></code>
            
            
                <ul>
<li>지도에 등록된 모든 Popup을 보관하는 javascript Map 객체</li>
</ul>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getProjection">getProjection<span class="signature">()</span><span class="return-type-signature"> &rarr; {<a href="Projection.html">Projection</a>}</span>
    </h4>





<div class="method-description">
    
    <p>지도의 좌표계 정보 조회</p>
<pre class="prettyprint source lang-javascript"><code>let map = new odf.Map(mapContainer, mapOption);
map.getProjection();
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Projection</code>
            
            
                <p>지도의 좌표계</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getSLDScale">getSLDScale<span class="signature">()</span><span class="return-type-signature"> &rarr; {<a href="global.html#odf_scaleInfo">odf_scaleInfo</a>}</span>
    </h4>





<div class="method-description">
    
    <p>해당  wms 레이어와 연결된 사용자정의 스타일 조회</p>
<pre class="prettyprint source lang-javascript"><code>let sample = odf.LayerFactory.produce(...);
sample.setMap(map)
sample.getWMSScale();
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>odf_scaleInfo</code>
            
            
                <p>해당 wms 레이어의  zoomLevel별 resolution과 sldScale 정보</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getZIndex">getZIndex<span class="signature">(odfId)</span><span class="return-type-signature"> &rarr; {Number}</span>
    </h4>





<div class="method-description">
    
    <p>해당 layer의 z-index 반환</p>
<pre class="prettyprint source lang-javascript"><code>const layerId = layer.getODFId();
const z = map.getZIndex(layerID);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">odfId</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>: 레이어Id</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Number</code>
            
            
                <p>해당 layer의 z-index.</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getZoom">getZoom<span class="signature">()</span><span class="return-type-signature"> &rarr; {number}</span>
    </h4>





<div class="method-description">
    
    <p>지도 줌레벨 조회</p>
<pre class="prettyprint source lang-javascript"><code>let map = new odf.Map(mapContainer, mapOption);
const zoom = map.getZoom();
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>number</code>
            
            
                <p>zoom level</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="hasFeatureAtPixel">hasFeatureAtPixel<span class="signature">(evt)</span><span class="return-type-signature"> &rarr; {Boolean}</span>
    </h4>





<div class="method-description">
    
    <p>해당 마우스 포인터 아래 도형 정보가 있는지 조회</p>
<pre class="prettyprint source lang-javascript"><code>//마우스 포인터 아래 도형정보가 있다면 커서를 포인터 형태로 변경
odf.event.addListener(map, 'pointermove', function (evt) {
     var hit = map.hasFeatureAtPixel(evt);
     map.getTarget().style.cursor = hit ? 'pointer' : '';
   })
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">evt</span>
            

            
                


    <span class="param-type">
        <code>Event</code>
    </span>
    

            

            

            

            <div class="param-description"><p>map 관련 이벤트 객체(click, pointermove 등)</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Boolean</code>
            
            
                <p>도형이 있으면 true, 도형이 없으면 false 반환</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="modifyComplete">modifyComplete<span class="signature">(type)</span><span class="return-type-signature"> &rarr; {<a href="global.html#modifyObject">modifyObject</a>}</span>
    </h4>





<div class="method-description">
    
    <p>레이어 수정 완료 - 수정된 피쳐값 리턴</p>
<pre class="prettyprint source lang-javascript"><code> map.modifyComplete('update');
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">type</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>: 레이어편집유형 ('update', 'insert', 'delete' , 'moveFeature','transform')</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>modifyObject</code>
            
            
                <p>피쳐 결과 배열</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="modifyLayer">modifyLayer<span class="signature">(type, evt, tooltipflag)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>레이어 편집 - 지도상에서 선택한 레이어 수정</p>
<pre class="prettyprint source lang-javascript"><code>let map = new odf.Map(mapContainer, mapOption);
odf.event.addListener(map, 'click', function (evt) {
      map.modifyLayer(type,evt);
        });
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">type</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>: 레이어편집유형 ('update', 'insert', 'delete')</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">evt</span>
            

            
                


    <span class="param-type">
        <code><a href="event.html">event</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>: 지도객체 마우스 클릭 이벤트</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">tooltipflag</span>
            

            
                


    <span class="param-type">
        <code>boolean</code>
    </span>
    

            

            

            

            <div class="param-description"><p>: 툴팁생성여부 (default : false);</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="removeSelectCluster">removeSelectCluster<span class="signature">()</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>클러스터 표출 인터렉션 제거</p>
<pre class="prettyprint source lang-javascript"><code>let map = new odf.Map(mapContainer, mapOption);

map.removeSelectCluster();
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="removeSnap">removeSnap<span class="signature">()</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>스Snap 인터렉션 비활성화</p>
<pre class="prettyprint source lang-javascript"><code>map.removeSnap();
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="removeTranslate">removeTranslate<span class="signature">()</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>레이어 전체 이동 비활성화 (현재 view영역에 속한 피쳐로 한정)</p>
<pre class="prettyprint source lang-javascript"><code>map.removeTranslate();
레이어 전체 이동 비활성화
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="selectFeature">selectFeature<span class="signature">(parameters)</span><span class="return-type-signature"> &rarr; {Array.&lt;<a href="Feature.html">Feature</a>>}</span>
    </h4>





<div class="method-description">
    
    <p>지도에서 클릭한 Feature를 선택</p>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">parameters</span>
            

            
                


    <span class="param-type">
        <code>Object</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                

                
                </span>
            

            

            <div class="param-description"></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">extractType</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>피쳐 추출 유형</p>
<ul>
<li>draw 직접 그린 도형 내에 속한 피쳐 추출</li>
<li>view 현재 지도영역에서 피쳐 추출</li>
<li>pixel 특정 픽셀에 곂치는 피쳐 추출</li>
<li>feature 특정 도형(compareFeature) 내에 속한 피쳐 추출</li>
<li>cql cql_filter로 피처 추출</li>
</ul></div>
            
        </li>

    

        <li>
            
                <span class="param-name">drawType</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>그리기 유형. extractType의 값이 'draw'일 경우 필수값</p>
<ul>
<li>polygon 다각형</li>
<li>box 사각형</li>
<li>circle 원형</li>
<li>point 점</li>
</ul></div>
            
        </li>

    

        <li>
            
                <span class="param-name">targetLayer</span>
            

            
                


    <span class="param-type">
        <code><a href="Layer.html">Layer</a></code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>특정 Layer 내에서만 feature 추출, 미입력시 모든 레이어에서 추출</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">featureReturn</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>필터링 대상이 되는 도형 또는 영역 정보를 함께 리턴할지 여부(기본값 false)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">pixel</span>
            

            
                


    <span class="param-type">
        <code>Number</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>피쳐 추출을 위한 좌표 기준. extractType의 값이 'pixel'일 경우 필수값.</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">callback</span>
            

            
                


    <span class="param-type">
        <code>function</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>그리기 영역으로 부터 선택된 피쳐 배열을 리턴받을 콜백 함수. 없으면 selectFeature 함수에서 결과 반환됨(extractType의 값이 'draw' 타입일 경우 제외)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">permanentDrawLifeCycle</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>영구적 그리기 interaction 활성화 여부. extractType의 값이 'draw'일 경우 해당(기본값 true)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">compareFeature</span>
            

            
                


    <span class="param-type">
        <code>ODF.Feature</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>extractType이 feature일 경우, 이 도형 내에 속한 피처 추출 - point 로 geometryType 제한</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">includeNoGeometryData</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>geometry가 없는 feature도 조회할지 여부(기본값 false, geometry 없는 feature 조회  x)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">cql</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>cql 문자열</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">maxAreaSize</span>
            

            
                


    <span class="param-type">
        <code>Number</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>그리기 도형 크기 제한(1000000이 1㎢)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">pointBuffer</span>
            

            
                


    <span class="param-type">
        <code>Number</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>대상 좌표에 버퍼를 지정 (단위:픽셀) (기본값 20)
※ 피처 추출 유형이 pixel이거나 draw이면서 drawType이 point일 경우 적용됨</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">message</span>
            

            
                


    <span class="param-type">
        <code>Object</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                
                    &lt;nullable&gt;<br>
                

                
                </span>
            

            

            <div class="param-description"><p>extractType이 'draw'일때, message 값 정의시 툴팁메세지에 사용자 정의 메세지 적용</p></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">DRAWSTART_POINT</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                
                    &lt;nullable&gt;<br>
                

                
                </span>
            

            

            <div class="param-description"><p>점 그리기 시작 안내 메세지
(기본값) '점을 그리기 위해 지도를 클릭해주세요'</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">DRAWSTART_POLYGON</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                
                    &lt;nullable&gt;<br>
                

                
                </span>
            

            

            <div class="param-description"><p>면 그리기 시작 안내 메세지
(기본값) '면을 그리기 위해 지도를 클릭해주세요'</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">DRAWSTART_CIRCLE</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                
                    &lt;nullable&gt;<br>
                

                
                </span>
            

            

            <div class="param-description"><p>원 그리기 시작 안내 메세지
(기본값) '원을 그리기 위해 지도를 클릭해주세요.'</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">DRAWSTART_BOX</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                
                    &lt;nullable&gt;<br>
                

                
                </span>
            

            

            <div class="param-description"><p>사각형 그리기 시작 안내 메세지
(기본값) '사각형을 그리기 위해 지도를 클릭해주세요.'</p></div>
            
        </li>

    
</ul>
            
        </li>

    
</ul>
            
        </li>

    

        <li>
            
                <span class="param-name">options.message.DRAWEND_DBCLICK</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                
                    &lt;nullable&gt;<br>
                

                
                </span>
            

            

            <div class="param-description"><p>그리기 종료 안내 메세지(더블클릭)
(기본값) '더블클릭시 그리기가 종료됩니다.'</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">parameters.useVisible</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>레이어 visible 여부에 따라 selectFeature 대상 변경</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Array.&lt;Feature></code>
            
            
                <p>선택된 영역으로 부터 포함관계인 피쳐 배열</p>
            
        </li>
    
    </ul>




    <h4 class="method-heading">Example</h4>
    
    <pre><code class="language-js">//01. 도형을 직접 그려서 그 영역에서 feature 검색
map.selectFeature({
  extractType : 'draw',
  drawType : 'polygon', //그리기 유형 polygon, box, circle, point 가능. extractType값이 'draw'일때만 유효함
  callback : function(response){//결과 정보를 받을 콜백 함수. 정의하지 않으면 결과 정보 받을 수 없음
    //response.result => 선택된 도형 배열
    //response.feature => 필터링 대상이 되는 도형 또는 영역 정보. featureReturn 값이 true여야 반환됨
  },
 //featureReturn : true, // 필터링 대상이 되는 도형 또는 영역 정보를 함께 리턴할지 여부(기본값 false)
 //targetLayer : tempLayer,//특정 Layer 내에서만 feature 추출, 미입력시 모든 레이어에서 추출
})
   //02. 현재 지도 영역에서 feature 검색
   map.selectFeature({
     extractType : 'view',
     callback : function(response){//결과 정보를 받을 콜백 함수, 없으면 selectFeature 함수에서 결과 반환됨
       //response.result => 선택된 도형 배열
       //response.feature => 필터링 대상이 되는 도형 또는 영역 정보. featureReturn 값이 true여야 반환됨
     },
    //featureReturn : true, // 필터링 대상이 영역 정보를 함께 리턴할지 여부(기본값 false)
    //targetLayer : tempLayer,//특정 Layer 내에서만 feature 추출, 미입력시 모든 레이어에서 추출
   })
   //03. 지도 영역 pixel 값이 위치한 지점 근방에 있는 feature검색
   let features = map.selectFeature({
     extractType : 'pixel',
     pixel : [x좌표,y좌표],//조회할 지도영역 pixel 좌표값
     //callback : function(response){//결과 정보를 받을 콜백 함수, 없으면 selectFeature 함수에서 결과 반환됨
     //  //response.result => 선택된 도형 배열
     //  //response.feature => 필터링 대상이 되는 도형 또는 영역 정보. featureReturn 값이 true여야 반환됨
     //},
    //featureReturn : true, // 필터링 대상이 영역 정보를 함께 리턴할지 여부(기본값 false)
    //targetLayer : tempLayer,//특정 Layer 내에서만 feature 추출, 미입력시 모든 레이어에서 추출
   })</code></pre>
















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="selectFeatureOnArea">selectFeatureOnArea<span class="signature">(drawType, callback, targetLayer, featureReturn)</span><span class="return-type-signature"> &rarr; {Array}</span>
    </h4>





<div class="method-description">
    
    <p>지도에서 클릭한 Feature를 선택</p>
<pre class="prettyprint source lang-javascript"><code> map.selectFeatureOnArea('polygon', function(e){
 console.log(e);
})
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">drawType</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            
                
            

            <div class="param-description"><p>영역으로 선택할 그리기 타입 (polygon, box, circle, point, view : 현재지도영역, event 특정이벤트마다 함수 실행)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">callback</span>
            

            
                


    <span class="param-type">
        <code>splitPolygonCallback</code>
    </span>
    

            

            

            
                
            

            <div class="param-description"><p>그리기 영역으로 부터 선택된 피쳐 배열을 리턴받을 콜백 함수 , drawType event 일시 pixel 값 입력 필요</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">targetLayer</span>
            

            
                


    <span class="param-type">
        <code><a href="Layer.html">Layer</a></code>
    </span>
    

            

            

            
                
            

            <div class="param-description"><p>특정 Layer 내에서만 feature 추출</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">featureReturn</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            

            
                
                    <span class="param-default">
                        false
                    </span>
                
            

            <div class="param-description"><p>필터링 대상이 되는 도형 또는 영역 정보를 함께 리턴할지 여부(기본값 false)</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Array</code>
            
            
                <p>선택된 영역으로 부터 포함관계인 피쳐 배열</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="selectFeatureOnClick">selectFeatureOnClick<span class="signature">(evt)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>지도에서 클릭한 Feature를 선택</p>
<pre class="prettyprint source lang-javascript"><code>let map = new odf.Map(mapContainer, mapOption);
   odf.event.addListener(map, 'click', function (evt) {
   let feature = map.selectFeatureOnClick(evt);
});
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">evt</span>
            

            
                


    <span class="param-type">
        <code><a href="event.html">event</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>마우스 지도 클릭 이벤트</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="sendToServerModified">sendToServerModified<span class="signature">(result, layer, type, flag)</span><span class="return-type-signature"> &rarr; {String|Object}</span>
    </h4>





<div class="method-description">
    
    <p>서버에 피쳐값 전송</p>
<pre class="prettyprint source lang-javascript"><code>map.sendToServerModified(result, _layer, type, flag);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">result</span>
            

            
                


    <span class="param-type">
        <code>Array</code>
    </span>
    |

    <span class="param-type">
        <code><a href="Feature.html">Feature</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>: 피쳐 배열</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">layer</span>
            

            
                


    <span class="param-type">
        <code><a href="Layer.html">Layer</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>타겟 레이어</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">type</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>: 레이어편집유형 ('update', 'insert', 'delete')</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">flag</span>
            

            
                


    <span class="param-type">
        <code>boolean</code>
    </span>
    

            

            

            

            <div class="param-description"><p>: geoserver 통신 여부 - Default : false</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>String</code>
            
                <code>Object</code>
            
            
                <p>Flag === True : 서버와 직접 통신 시 서버 통신 결과 ; Flag === false : 서버 전송할 XML 데이터 문자열</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setCenter">setCenter<span class="signature">(center)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>지도 중심점 좌표 설정</p>
<pre class="prettyprint source lang-javascript"><code>let map = new odf.Map(mapContainer, mapOption);
const center = new odf.Coordinate(955156.7761, 1951925.0984);
map.setCenter(center);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">center</span>
            

            
                


    <span class="param-type">
        <code><a href="Coordinate.html">Coordinate</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>지도 중심점 좌표 설정 {위도, 경도}</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setDoubleClickZoomable">setDoubleClickZoomable<span class="signature">(param)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>지도 더블클릭 줌 설정</p>
<pre class="prettyprint source lang-javascript"><code>let map = new odf.Map(mapContainer, mapOption);

map.setDoubleClickZoomable(true);
map.setDoubleClickZoomable(false);

</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">param</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            

            

            <div class="param-description"><p>사용여부 True : 허용, false : 차단</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setDraggable">setDraggable<span class="signature">(param)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>지도 마우스 드래그 설정</p>
<pre class="prettyprint source lang-javascript"><code>let map = new odf.Map(mapContainer, mapOption);

map.setDraggable(true);
map.setDraggable(false);

</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">param</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            

            

            <div class="param-description"><p>사용여부 True : 허용, false : 차단</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setDraggableODFFeature">setDraggableODFFeature<span class="signature">(flag, options)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>도형 이동 기능 활성화 상태 변경</p>
<pre class="prettyprint source lang-javascript"><code>//drag 인터렉션 활성화 - 도형 드래그시 이동 가능
map.setDraggableODFFeature(true);
//drag 인터렉션 비활성화 - 도형 이동 기능 비활성화
map.setDraggableODFFeature(false);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">flag</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            

            

            <div class="param-description"><p>변경할 활성화 상태</p>
<ul>
<li>true : 활성화</li>
<li>false : 비활성화</li>
</ul></div>
            
        </li>

    

        <li>
            
                <span class="param-name">options</span>
            

            
                


    <span class="param-type">
        <code>Obejct</code>
    </span>
    

            

            

            

            <div class="param-description"><p>변형기능 상세 옵션</p></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">targetFeature</span>
            

            
                


    <span class="param-type">
        <code><a href="Feature.html">Feature</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>변형 대상 도형</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">targetLayer</span>
            

            
                


    <span class="param-type">
        <code><a href="Layer.html">Layer</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>도형 선택을 정의한 레이어로 제한</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">changeTargetFeature</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            

            

            <div class="param-description"><p>변형 대상 도형 변경 가능 여부(기본값 false)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">useHilight</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            

            

            <div class="param-description"><p>선택 도형 하이라이트 스타일 적용 여부(기본값 true)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">hilightStyle</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#odf_styleOption">odf_styleOption</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>선택 도형 적용 hilightStyle</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">modifiedCallback</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#modifiedCallback">modifiedCallback</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>도형 수정시 수정 도형을 받을 콜백함수</p></div>
            
        </li>

    
</ul>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setDraggableODFLayer">setDraggableODFLayer<span class="signature">(layer, options)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>도형 이동 레이어 제한</p>
<pre class="prettyprint source lang-javascript"><code>//drag 인터렉션 활성화 - 도형 드래그시 이동 가능
map.setDraggableODFFeature(true);
//도형이동 레이어 제한 -  선택된 레이어에 속한 도형만 이동 가능
map.setDraggableODFLayer(layer);
//도형이동 레이어 제한 해제 ※도형이동 레이어 제한 해제되어있어도, drag 인터렉션은 활성화된 상태임
map.setDraggableODFLayer();
// drag 인터렉션 비활성화
//map.setDraggableODFFeature(false);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">layer</span>
            

            
                


    <span class="param-type">
        <code><a href="Layer.html">Layer</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>제한할 레이어</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">options</span>
            

            
                


    <span class="param-type">
        <code>Obejct</code>
    </span>
    

            

            

            

            <div class="param-description"><p>변형기능 상세 옵션</p></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">targetFeature</span>
            

            
                


    <span class="param-type">
        <code><a href="Feature.html">Feature</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>변형 대상 도형</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">targetLayer</span>
            

            
                


    <span class="param-type">
        <code><a href="Layer.html">Layer</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>도형 선택을 정의한 레이어로 제한</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">changeTargetFeature</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            

            

            <div class="param-description"><p>변형 대상 도형 변경 가능 여부(기본값 false)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">useHilight</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            

            

            <div class="param-description"><p>선택 도형 하이라이트 스타일 적용 여부(기본값 true)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">hilightStyle</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#odf_styleOption">odf_styleOption</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>선택 도형 적용 hilightStyle</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">modifiedCallback</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#modifiedCallback">modifiedCallback</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>도형 수정시 수정 도형을 받을 콜백함수</p></div>
            
        </li>

    
</ul>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setDraggableSelectFeature">setDraggableSelectFeature<span class="signature">(feature, options)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>도형 이동 기능 특정 도형으로 제한</p>
<pre class="prettyprint source lang-javascript"><code>//drag 인터렉션 활성화 - 도형 드래그시 이동 가능
map.setDraggableODFFeature(true);
//특정 도형 선택하여 그 도형만 이동 가능하게 제한
map.setDraggableSelectFeature(feature);
//특정 도형 선택 해제 ※ 도형 선택이 해제 되었어도, drag 인터렉션은 활성화된 상태임
map.setDraggableSelectFeature();
// drag 인터렉션 비활성화
//map.setDraggableODFFeature(false);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">feature</span>
            

            
                


    <span class="param-type">
        <code><a href="Feature.html">Feature</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>선택된 도형</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">options</span>
            

            
                


    <span class="param-type">
        <code>Obejct</code>
    </span>
    

            

            

            

            <div class="param-description"><p>변형기능 상세 옵션</p></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">targetFeature</span>
            

            
                


    <span class="param-type">
        <code><a href="Feature.html">Feature</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>변형 대상 도형</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">targetLayer</span>
            

            
                


    <span class="param-type">
        <code><a href="Layer.html">Layer</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>도형 선택을 정의한 레이어로 제한</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">changeTargetFeature</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            

            

            <div class="param-description"><p>변형 대상 도형 변경 가능 여부(기본값 false)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">useHilight</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            

            

            <div class="param-description"><p>선택 도형 하이라이트 스타일 적용 여부(기본값 true)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">hilightStyle</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#odf_styleOption">odf_styleOption</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>선택 도형 적용 hilightStyle</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">modifiedCallback</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#modifiedCallback">modifiedCallback</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>도형 수정시 수정 도형을 받을 콜백함수</p></div>
            
        </li>

    
</ul>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setDragRotate">setDragRotate<span class="signature">(param)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>지도 회전 설정</p>
<pre class="prettyprint source lang-javascript"><code>let map = new odf.Map(mapContainer, mapOption);

map.setDragRotate(true);
map.setDragRotate(false);

</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">param</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            

            

            <div class="param-description"><p>사용여부 True : 허용, false : 차단</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setDragTranslate">setDragTranslate<span class="signature">(flag, options)</span><span class="return-type-signature"></span>
    </h4>



    <h4 class="method-heading">Summary</h4>
    <p>벡터 레이어의 도형 변형(이동) 기능 활성화 상태 변경</p>
<pre class="prettyprint source lang-javascript"><code>//DragTranslate 인터렉션 활성화 - 선택한 도형 변형(확대/축소/회전/이동) 가능
map.setDragTranslate(true,{
      translateType : [  'move', 'rotate','resize'], //사용할 변형 기능
      targetType : 'feature', // 변형 대상 유형
      targetLayer : wfsLayer,// 레이어 제한
      //targetFeature : feature,//변경 대상 도형 지정.</code></pre>



<div class="method-description">
    <h4 class="method-heading">Description</h4>
    <p>벡터 레이어의 도형 변형(이동) 기능 활성화 상태 변경</p>
<pre class="prettyprint source lang-javascript"><code>//DragTranslate 인터렉션 활성화 - 선택한 도형 변형(확대/축소/회전/이동) 가능
map.setDragTranslate(true,{
      translateType : [  'move', 'rotate','resize'], //사용할 변형 기능
      targetType : 'feature', // 변형 대상 유형
      targetLayer : wfsLayer,// 레이어 제한
      //targetFeature : feature,//변경 대상 도형 지정. 미정의시 클릭한 도형 편집 활성화
      changeTargetFeature : true,//변형 대상 도형 변경 가능 여부
      //hilightStyle : {
      //  fill : { color: [0, 0, 0, 0.4] },
      //  stroke : { color: [255, 0, 0, 0.7], width: 2 },
      //  image : {
      //    circle: {
      //      fill: { color: [0, 0, 0, 0.4] },
      //      stroke: { color: [255, 0, 0, 0.7], width: 2 },
      //      radius: 10,
      //    },
      //  }
      //}
});
//DragTranslate 인터렉션 비활성화 - 도형 변형(확대/축소/회전/이동) 기능 비활성화
map.setDragTranslate(false);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">flag</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            

            

            <div class="param-description"><p>변경할 활성화 상태</p>
<ul>
<li>true : 활성화</li>
<li>false : 비활성화</li>
</ul></div>
            
        </li>

    

        <li>
            
                <span class="param-name">options</span>
            

            
                


    <span class="param-type">
        <code>Obejct</code>
    </span>
    

            

            

            

            <div class="param-description"><p>변형기능 상세 옵션</p></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">translateType</span>
            

            
                


    <span class="param-type">
        <code>Array.&lt;String></code>
    </span>
    

            

            

            

            <div class="param-description"><p>사용할 변형 기능</p>
<ul>
<li>'move' : 이동 기능</li>
<li>'rotate' : 회전 기능</li>
<li>'resize' : 확대/축소 기능</li>
</ul></div>
            
        </li>

    

        <li>
            
                <span class="param-name">targetType</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>변형 대상 유형</p>
<ul>
<li>'layer' : 레이어</li>
<li>'feature' : 도형</li>
</ul></div>
            
        </li>

    

        <li>
            
                <span class="param-name">targetFeature</span>
            

            
                


    <span class="param-type">
        <code><a href="Feature.html">Feature</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>변형 대상 도형</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">targetLayer</span>
            

            
                


    <span class="param-type">
        <code><a href="Layer.html">Layer</a></code>
    </span>
    

            

            

            

            <div class="param-description"><ul>
<li>targetType 값이 'layer'일때 변형 대상 레이어</li>
<li>targetType 값이 'feature'일때 도형 선택을 정의한 레이어로 제한</li>
</ul></div>
            
        </li>

    

        <li>
            
                <span class="param-name">changeTargetFeature</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            

            

            <div class="param-description"><p>변형 대상 도형 변경 가능 여부(기본값 false,targetType 값이 'feature'일때 사용됨)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">useHilight</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            

            

            <div class="param-description"><p>선택 도형 하이라이트 스타일 적용 여부(기본값 true)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">hilightStyle</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#odf_styleOption">odf_styleOption</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>선택 도형 적용 hilightStyle</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">modifiedCallback</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#modifiedCallback">modifiedCallback</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>도형 수정시 수정 도형을 받을 콜백함수</p></div>
            
        </li>

    
</ul>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setDragTranslateTargetFeature">setDragTranslateTargetFeature<span class="signature">()</span><span class="return-type-signature"></span>
    </h4>



    <h4 class="method-heading">Summary</h4>
    <p>벡터 레이어의 도형 변형(이동) 대상 도형 셋팅</p>
<pre class="prettyprint source lang-javascript"><code>//DragTranslate 인터렉션 활성화 - 선택한 도형 변형(확대/축소/회전/이동) 가능
map.setDragTranslate(true,{
      translateType : [  'move', 'rotate','resize'], //사용할 변형 기능
      targetType : 'feature', // 변형 대상 유형
      //targetLayer : wfsLayer,// 레이어 제한
      //targetFeature : feature,//변경 대상 도형 지정.</code></pre>



<div class="method-description">
    <h4 class="method-heading">Description</h4>
    <p>벡터 레이어의 도형 변형(이동) 대상 도형 셋팅</p>
<pre class="prettyprint source lang-javascript"><code>//DragTranslate 인터렉션 활성화 - 선택한 도형 변형(확대/축소/회전/이동) 가능
map.setDragTranslate(true,{
      translateType : [  'move', 'rotate','resize'], //사용할 변형 기능
      targetType : 'feature', // 변형 대상 유형
      //targetLayer : wfsLayer,// 레이어 제한
      //targetFeature : feature,//변경 대상 도형 지정. 미정의시 클릭한 도형 편집 활성화
      changeTargetFeature : true,//변형 대상 도형 변경 가능 여부
      //hilightStyle : {
      //  fill : { color: [0, 0, 0, 0.4] },
      //  stroke : { color: [255, 0, 0, 0.7], width: 2 },
      //  image : {
      //    circle: {
      //      fill: { color: [0, 0, 0, 0.4] },
      //      stroke: { color: [255, 0, 0, 0.7], width: 2 },
      //      radius: 10,
      //    },
      //  }
      //}
});
//벡터 레이어의 도형 변형(이동) 대상 도형 셋팅
map.setDragTranslateTargetFeature(feature);

//DragTranslate 인터렉션 비활성화 - 도형 변형(확대/축소/회전/이동) 기능 비활성화
//map.setDragTranslate(false);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">options.targetFeature</span>
            

            
                


    <span class="param-type">
        <code><a href="Feature.html">Feature</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>변형 대상 도형</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setDragTranslateTargetLayer">setDragTranslateTargetLayer<span class="signature">()</span><span class="return-type-signature"></span>
    </h4>



    <h4 class="method-heading">Summary</h4>
    <p>벡터 레이어의 도형 변형(이동) 대상 레이어 셋팅</p>
<pre class="prettyprint source lang-javascript"><code>//DragTranslate 인터렉션 활성화 - 선택한 도형 변형(확대/축소/회전/이동) 가능
map.setDragTranslate(true,{
      translateType : [  'move', 'rotate','resize'], //사용할 변형 기능
      targetType : 'feature', // 변형 대상 유형
      //targetLayer : wfsLayer,// 레이어 제한
      //targetFeature : feature,//변경 대상 도형 지정.</code></pre>



<div class="method-description">
    <h4 class="method-heading">Description</h4>
    <p>벡터 레이어의 도형 변형(이동) 대상 레이어 셋팅</p>
<pre class="prettyprint source lang-javascript"><code>//DragTranslate 인터렉션 활성화 - 선택한 도형 변형(확대/축소/회전/이동) 가능
map.setDragTranslate(true,{
      translateType : [  'move', 'rotate','resize'], //사용할 변형 기능
      targetType : 'feature', // 변형 대상 유형
      //targetLayer : wfsLayer,// 레이어 제한
      //targetFeature : feature,//변경 대상 도형 지정. 미정의시 클릭한 도형 편집 활성화
      changeTargetFeature : true,//변형 대상 도형 변경 가능 여부
      //hilightStyle : {
      //  fill : { color: [0, 0, 0, 0.4] },
      //  stroke : { color: [255, 0, 0, 0.7], width: 2 },
      //  image : {
      //    circle: {
      //      fill: { color: [0, 0, 0, 0.4] },
      //      stroke: { color: [255, 0, 0, 0.7], width: 2 },
      //      radius: 10,
      //    },
      //  }
      //}
});
//벡터 레이어의 도형 변형(이동) 대상 레이어 셋팅
map.setDragTranslateTargetLayer(wfsLayer);

//DragTranslate 인터렉션 비활성화 - 도형 변형(확대/축소/회전/이동) 기능 비활성화
//map.setDragTranslate(false);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">options.targetFeature</span>
            

            
                


    <span class="param-type">
        <code><a href="Feature.html">Feature</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>변형 대상 도형</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setMaxResolution">setMaxResolution<span class="signature">(resolution)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>지도 최대 레졸루션 설정</p>
<pre class="prettyprint source lang-javascript"><code>let map = new odf.Map(mapContainer, mapOption);
map.setMaxResolution(100000);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">resolution</span>
            

            
                


    <span class="param-type">
        <code>number</code>
    </span>
    

            

            

            

            <div class="param-description"><p>설정할 최대 레졸루션</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setMaxZoom">setMaxZoom<span class="signature">(zoom)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>지도 최대 줌레벨 설정</p>
<pre class="prettyprint source lang-javascript"><code>let map = new odf.Map(mapContainer, mapOption);
map.setMaxZoom(19);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">zoom</span>
            

            
                


    <span class="param-type">
        <code>number</code>
    </span>
    

            

            

            

            <div class="param-description"><p>설정할 최대 줌레벨</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setMinResolution">setMinResolution<span class="signature">(resolution)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>지도 최소 레졸루션 설정</p>
<pre class="prettyprint source lang-javascript"><code>let map = new odf.Map(mapContainer, mapOption);
map.setMinResolution(0.125);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">resolution</span>
            

            
                


    <span class="param-type">
        <code>number</code>
    </span>
    

            

            

            

            <div class="param-description"><p>설정할 최소 레졸루션</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setMinZoom">setMinZoom<span class="signature">(zoom)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>지도 최소 줌레벨 설정</p>
<pre class="prettyprint source lang-javascript"><code>let map = new odf.Map(mapContainer, mapOption);
map.setMinZoom(1);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">zoom</span>
            

            
                


    <span class="param-type">
        <code>number</code>
    </span>
    

            

            

            

            <div class="param-description"><p>설정할 최소 줌레벨</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setModifiable">setModifiable<span class="signature">(flag)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>도형 편집 기능 활성화 상태 변경</p>
<pre class="prettyprint source lang-javascript"><code>//modify 인터렉션 활성화
map.setModifiable(true);
//modify 인터렉션 비활성화
map.setModifiable(false);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">flag</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            

            

            <div class="param-description"><p>변경할 활성화 상태</p>
<ul>
<li>true : 활성화</li>
<li>false : 비활성화</li>
</ul></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setModifyFeature">setModifyFeature<span class="signature">(feature)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>도형 편집 기능 특정 도형으로 제한</p>
<pre class="prettyprint source lang-javascript"><code>//modify 인터렉션 활성화
map.setModifiable(true);
//특정 도형 선택하여 그 도형만 편집 가능하게 제한
map.setModifyFeature(feature);
//특정 도형 선택 해제 ※ 도형 선택이 해제되면 modify 인터렉션도 비활성화됨
map.setModifyFeature();
//modify 인터렉션 비활성화
//map.setModifiable(false);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">feature</span>
            

            
                


    <span class="param-type">
        <code><a href="Feature.html">Feature</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>선택된 도형</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setModifyLayer">setModifyLayer<span class="signature">(type, evt, layer, tooltipflag)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>레이어 편집 - 특정 레이어에 대한 수정</p>
<pre class="prettyprint source lang-javascript"><code>let map = new odf.Map(mapContainer, mapOption);
odf.event.addListener(map, 'click', function (evt) {
      map.modifyLayer(type,evt, layer);
        });
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">type</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>: 레이어편집유형 ('update', 'insert', 'delete','moveFeature','transform')</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">evt</span>
            

            
                


    <span class="param-type">
        <code><a href="event.html">event</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>: 지도객체 마우스 클릭 이벤트</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">layer</span>
            

            
                


    <span class="param-type">
        <code><a href="Layer.html">Layer</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>: layer 객체</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">tooltipflag</span>
            

            
                


    <span class="param-type">
        <code>boolean</code>
    </span>
    

            

            

            

            <div class="param-description"><p>: 툴팁생성여부 (default : false);</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setResizable">setResizable<span class="signature">(resizable)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>지도 리사이징</p>
<pre class="prettyprint source lang-javascript"><code>      let map = new odf.Map(mapContainer, mapOption);

      map.setResizable(true);
      map.setResizable(false);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">resizable</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            

            

            <div class="param-description"><p>사용여부 True : 허용, false : 차단</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setSelectCluster">setSelectCluster<span class="signature">(options)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>클러스터 표출 인터렉션 추가</p>
<pre class="prettyprint source lang-javascript"><code>let map = new odf.Map(mapContainer, mapOption);

map.setSelectCluster(
  layers : [pointLayer], //클러스터 클릭 인터렉션 설정 할 레이어 배열 목록
 addCallback : function(e){ //클러스터 클릭 시 지도에 피쳐 추가 콜백 함수
   console.log(e) ;
   },
 removeCallback : function(e){ //지도에서 피쳐 사라질 시 콜백 함수
   console.log(e);
     }
)
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">options</span>
            

            
                


    <span class="param-type">
        <code>object</code>
    </span>
    

            

            

            

            <div class="param-description"><p>생성옵션</p></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">layers</span>
            

            
                


    <span class="param-type">
        <code>Array</code>
    </span>
    

            

            

            

            <div class="param-description"><p>적용할 대상 레이어 목록</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">addCallback</span>
            

            
                


    <span class="param-type">
        <code>function</code>
    </span>
    

            

            

            

            <div class="param-description"><p>객체 선택 시 콜백함수</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">removeCallback</span>
            

            
                


    <span class="param-type">
        <code>function</code>
    </span>
    

            

            

            

            <div class="param-description"><p>객체 선택 해제 시 콜백함수</p></div>
            
        </li>

    
</ul>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setSnap">setSnap<span class="signature">(options, layerArray)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>편집/그리기 시 Snap 인터렉션 설정</p>
<pre class="prettyprint source lang-javascript"><code>//snap 인터렉션 활성화
map.setSnap({pixelTolerance : 10, edge : true, vertex : true}, [layer1, layer2]);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">options</span>
            

            
                


    <span class="param-type">
        <code>Object</code>
    </span>
    

            

            

            

            <div class="param-description"><p>스냅 인터렉션 옵션</p></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">pixelTolerance</span>
            

            
                


    <span class="param-type">
        <code>Number</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>픽셀 민감도 default 10</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">edge</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>선으로 Snap 활성화 여부</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">vertex</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>꼭지점으로 Snap 활성화 여부</p></div>
            
        </li>

    
</ul>
            
        </li>

    

        <li>
            
                <span class="param-name">layerArray</span>
            

            
                


    <span class="param-type">
        <code>Array</code>
    </span>
    |

    <span class="param-type">
        <code><a href="Layer.html">Layer</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>스냅 인터렉션을 활성화할 ODF 레이어 배열</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setTransformable">setTransformable<span class="signature">(flag, options)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>도형 변형(확대/축소/회전) 기능 활성화 상태 변경</p>
<pre class="prettyprint source lang-javascript"><code>//transform 인터렉션 활성화 - 선택한 도형 변형(확대/축소/회전) 가능
map.setTransformable(true);
//transform 인터렉션 비활성화 - 도형 변형 기능 비활성화
map.setTransformable(false);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">flag</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            

            

            <div class="param-description"><p>변경할 활성화 상태</p>
<ul>
<li>true : 활성화</li>
<li>false : 비활성화</li>
</ul></div>
            
        </li>

    

        <li>
            
                <span class="param-name">options</span>
            

            
                


    <span class="param-type">
        <code>Obejct</code>
    </span>
    

            

            

            

            <div class="param-description"><p>변형기능 상세 옵션</p></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">targetFeature</span>
            

            
                


    <span class="param-type">
        <code><a href="Feature.html">Feature</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>변형 대상 도형</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">targetLayer</span>
            

            
                


    <span class="param-type">
        <code><a href="Layer.html">Layer</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>도형 선택을 정의한 레이어로 제한</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">changeTargetFeature</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            

            

            <div class="param-description"><p>변형 대상 도형 변경 가능 여부(기본값 false)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">useHilight</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            

            

            <div class="param-description"><p>선택 도형 하이라이트 스타일 적용 여부(기본값 true)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">hilightStyle</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#odf_styleOption">odf_styleOption</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>선택 도형 적용 hilightStyle</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">modifiedCallback</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#modifiedCallback">modifiedCallback</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>도형 수정시 수정 도형을 받을 콜백함수</p></div>
            
        </li>

    
</ul>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setTransformFeature">setTransformFeature<span class="signature">(feature, options)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>도형 변형(확대/축소/회전) 기능 특정 도형으로 제한</p>
<pre class="prettyprint source lang-javascript"><code>// transform 인터렉션 활성화 - 선택한 도형 변형(확대/축소/회전) 가능
map.setTransformable(true);
//특정 도형 선택하여 그 도형만 변형 가능하게 제한
map.setTransformFeature(feature);
//특정 도형 선택 해제 ※ 도형 선택이 해제 되었어도, transform 인터렉션은 활성화된 상태임
map.setTransformFeature();
// transform 인터렉션 비활성화
//map.setTransformable(false);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">feature</span>
            

            
                


    <span class="param-type">
        <code><a href="Feature.html">Feature</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>선택된 도형</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">options</span>
            

            
                


    <span class="param-type">
        <code>Obejct</code>
    </span>
    

            

            

            

            <div class="param-description"><p>변형기능 상세 옵션</p></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">targetFeature</span>
            

            
                


    <span class="param-type">
        <code><a href="Feature.html">Feature</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>변형 대상 도형</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">targetLayer</span>
            

            
                


    <span class="param-type">
        <code><a href="Layer.html">Layer</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>도형 선택을 정의한 레이어로 제한</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">changeTargetFeature</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            

            

            <div class="param-description"><p>변형 대상 도형 변경 가능 여부(기본값 false)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">useHilight</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            

            

            <div class="param-description"><p>선택 도형 하이라이트 스타일 적용 여부(기본값 true)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">hilightStyle</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#odf_styleOption">odf_styleOption</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>선택 도형 적용 hilightStyle</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">modifiedCallback</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#modifiedCallback">modifiedCallback</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>도형 수정시 수정 도형을 받을 콜백함수</p></div>
            
        </li>

    
</ul>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setTransformLayer">setTransformLayer<span class="signature">(layer, options)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>도형 변형(확대/축소/회전) 레이어 제한</p>
<pre class="prettyprint source lang-javascript"><code>// transform 인터렉션 활성화 - 선택한 도형 변형(확대/축소/회전) 가능
map.setTransformable(true);
//도형 변형 레이어 제한 -  선택된 레이어에 속한 도형만 변형 가능
map.setTransformLayer(layer);
//도형 변형 레이어 제한 해제 ※ 레이어 제한이 해제되었어도, transform 인터렉션은 활성화된 상태임
map.setTransformLayer();
// transform 인터렉션 비활성화
//map.setTransformable(false);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">layer</span>
            

            
                


    <span class="param-type">
        <code><a href="Layer.html">Layer</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>제한할 레이어</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">options</span>
            

            
                


    <span class="param-type">
        <code>Obejct</code>
    </span>
    

            

            

            

            <div class="param-description"><p>변형기능 상세 옵션</p></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">targetFeature</span>
            

            
                


    <span class="param-type">
        <code><a href="Feature.html">Feature</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>변형 대상 도형</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">targetLayer</span>
            

            
                


    <span class="param-type">
        <code><a href="Layer.html">Layer</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>도형 선택을 정의한 레이어로 제한</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">changeTargetFeature</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            

            

            <div class="param-description"><p>변형 대상 도형 변경 가능 여부(기본값 false)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">useHilight</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            

            

            <div class="param-description"><p>선택 도형 하이라이트 스타일 적용 여부(기본값 true)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">hilightStyle</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#odf_styleOption">odf_styleOption</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>선택 도형 적용 hilightStyle</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">modifiedCallback</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#modifiedCallback">modifiedCallback</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>도형 수정시 수정 도형을 받을 콜백함수</p></div>
            
        </li>

    
</ul>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setTranslate">setTranslate<span class="signature">(layer, options)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>레이어 전체 이동 활성화 (현재 view영역에 속한 피쳐로 한정)</p>
<pre class="prettyprint source lang-javascript"><code>map.setTranslate(layer);
레이어 전체 이동 활성화
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">layer</span>
            

            
                


    <span class="param-type">
        <code><a href="Layer.html">Layer</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>타겟 레이어</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">options</span>
            

            
                


    <span class="param-type">
        <code>Obejct</code>
    </span>
    

            

            

            

            <div class="param-description"><p>변형기능 상세 옵션</p></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">targetFeature</span>
            

            
                


    <span class="param-type">
        <code><a href="Feature.html">Feature</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>변형 대상 도형</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">targetLayer</span>
            

            
                


    <span class="param-type">
        <code><a href="Layer.html">Layer</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>도형 선택을 정의한 레이어로 제한</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">changeTargetFeature</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            

            

            <div class="param-description"><p>변형 대상 도형 변경 가능 여부(기본값 false)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">useHilight</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            

            

            <div class="param-description"><p>선택 도형 하이라이트 스타일 적용 여부(기본값 true)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">hilightStyle</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#odf_styleOption">odf_styleOption</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>선택 도형 적용 hilightStyle</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">modifiedCallback</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#modifiedCallback">modifiedCallback</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>도형 수정시 수정 도형을 받을 콜백함수</p></div>
            
        </li>

    
</ul>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setZIndex">setZIndex<span class="signature">(odfId, zIdx)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>해당 layer의 z-index 설정</p>
<pre class="prettyprint source lang-javascript"><code>const layerId = layer.getODFId();
const z = map.getZIndex(layerID, 100);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">odfId</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>: 레이어Id</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">zIdx</span>
            

            
                


    <span class="param-type">
        <code>Number</code>
    </span>
    

            

            

            

            <div class="param-description"><p>: z index 값</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setZoom">setZoom<span class="signature">(zoom)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>지도 줌레벨 설정</p>
<pre class="prettyprint source lang-javascript"><code>let map = new odf.Map(mapContainer, mapOption);
map.setZoom(17);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">zoom</span>
            

            
                


    <span class="param-type">
        <code>number</code>
    </span>
    

            

            

            

            <div class="param-description"><p>설정할 줌레벨</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setZoomable">setZoomable<span class="signature">(param)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>지도 마우스 휠 확대 축소 설정</p>
<pre class="prettyprint source lang-javascript"><code>let map = new odf.Map(mapContainer, mapOption);

map.setZoomable(true);
map.setZoomable(false);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">param</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            

            

            <div class="param-description"><p>사용여부 True : 허용, false : 차단</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="splitPolygonByLine">splitPolygonByLine<span class="signature">(polygonFeature, lineFeature, callback)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>라인으로 다각형 자르기 (라인이 다각형을 2분할 시에만 가능 2분할 이상으로 분할 시 원본 유지)</p>
<pre class="prettyprint source lang-javascript"><code>let map = new odf.Map(mapContainer, mapOption);
map.splitPolygonByLine(polygon, line, callback);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">polygonFeature</span>
            

            
                


    <span class="param-type">
        <code>odf.Feature</code>
    </span>
    

            

            

            

            <div class="param-description"><p>대상 피쳐 (polygon, multipolygon 타입만 가능)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">lineFeature</span>
            

            
                


    <span class="param-type">
        <code>odf.Feature</code>
    </span>
    

            

            

            

            <div class="param-description"><p>cutting 할 피쳐(line | lineString 타입)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">callback</span>
            

            
                


    <span class="param-type">
        <code>function</code>
    </span>
    

            

            

            

            <div class="param-description"><p>그리기 완료후 분할된 피쳐, 원본 피쳐 리턴받을 콜백 함수 return {originalFeature : 원본피쳐, splitFeatures : [분할된피쳐1, ....]}</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="switchLayer">switchLayer<span class="signature">(layerId, condition)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>지도 레이어 on/off</p>
<pre class="prettyprint source lang-javascript"><code>let map = new odf.Map(mapContainer, mapOption);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">layerId</span>
            

            
                


    <span class="param-type">
        <code>string</code>
    </span>
    

            

            

            

            <div class="param-description"><p>: odf-layer-생성시간</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">condition</span>
            

            
                


    <span class="param-type">
        <code>boolean</code>
    </span>
    

            

            

            

            <div class="param-description"><p>: on/off 여부</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="switchLayerList">switchLayerList<span class="signature">(layers)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>지도에 등록된 레이어 목록을 layers로 교체</p>
<pre class="prettyprint source lang-javascript"><code>map.switchLayerList(layers);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">layers</span>
            

            
                


    <span class="param-type">
        <code>Array.&lt;<a href="Layer.html">Layer</a>></code>
    </span>
    

            

            

            

            <div class="param-description"><p>: 바꿔치기 할 레이어 목록</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
    

    

    
</article>

</section>




  </div>
</main>

<footer class="layout-footer">
  <div class="container">
    Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.11</a> on Tue Jan 21 2025 11:05:52 GMT+0900 (대한민국 표준시)
  </div>
</footer>



<script src="scripts/prism.dev.js"></script>
</body>
</html>