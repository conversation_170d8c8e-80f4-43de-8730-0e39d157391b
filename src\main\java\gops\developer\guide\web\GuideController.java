package gops.developer.guide.web;

import egovframework.com.cmm.service.EgovProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import jakarta.servlet.ServletContext;
import java.io.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;


@Controller
public class GuideController {

	@Autowired
	ServletContext servletContext;
	private static final Logger LOGGER = LoggerFactory.getLogger(EgovProperties.class);

	@RequestMapping(value = "/guide")
	public String guide() {

		return "guide/index.html";
	}


	@RequestMapping(value="/settingFileDownload", method = RequestMethod.GET)
	@ResponseBody
	public ResponseEntity<Resource> settingFileDownload(Model model) throws IOException {

		String filePath = servletContext.getRealPath("/");
		String zipFileName = "settingFile.zip";

		try{
			ByteArrayOutputStream baos = new ByteArrayOutputStream();
			try (ZipOutputStream zipOut = new ZipOutputStream(baos)) {
				addFileToZip(filePath, zipOut, "odf-config-wavus.json");
				addFileToZip(filePath, zipOut, "proxyUrl.jsp");
			}
			byte[] zipBytes = baos.toByteArray();
			ByteArrayResource resource = new ByteArrayResource(baos.toByteArray());

			// 다운로드할 파일명 설정
			HttpHeaders headers = new HttpHeaders();
			headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + zipFileName);
			headers.add(HttpHeaders.CONTENT_LENGTH, String.valueOf(zipBytes.length));
			headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);


			// ResponseEntity를 사용하여 HTTP 응답 생성
			return ResponseEntity
					.status(HttpStatus.OK)
					.headers(headers)
					.contentType(MediaType.APPLICATION_OCTET_STREAM)
					.body(resource);

		}catch (IOException ioe) {
			LOGGER.debug("api file write io exception",ioe);
			return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
		}
	}

	@RequestMapping(value="/apiDownload", method = RequestMethod.GET)
	@ResponseBody
	public ResponseEntity<Resource> apiDownload(String apiName, Model model) throws IOException {

		String filePath = servletContext.getRealPath("/js/"+apiName+"/");
		String zipFileName = apiName+".zip";

		try{
			ByteArrayOutputStream baos = new ByteArrayOutputStream();
			try (ZipOutputStream zipOut = new ZipOutputStream(baos)) {
				addFileToZip(filePath, zipOut, apiName+".min.js");
				addFileToZip(filePath, zipOut, apiName+".css");
				addFolderToZip(filePath+"images","images", zipOut);
			}
			byte[] zipBytes = baos.toByteArray();
			ByteArrayResource resource = new ByteArrayResource(baos.toByteArray());

			// 다운로드할 파일명 설정
			HttpHeaders headers = new HttpHeaders();
			headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + zipFileName);
			headers.add(HttpHeaders.CONTENT_LENGTH, String.valueOf(zipBytes.length));
			headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);


			// ResponseEntity를 사용하여 HTTP 응답 생성
			return ResponseEntity
					.status(HttpStatus.OK)
					.headers(headers)
					.contentType(MediaType.APPLICATION_OCTET_STREAM)
					.body(resource);

		}catch (IOException ioe) {
			LOGGER.debug("api file write io exception",ioe);
			return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
		}
	}

	// 파일을 zip 파일에 추가하는 메서드
	private void addFileToZip(String filePath, ZipOutputStream zipOut, String entryName) throws IOException {
		File file = new File(filePath,entryName);
		if (file.exists() && file.isFile()) {
			try (FileInputStream in = new FileInputStream(file)) {
				zipOut.putNextEntry(new ZipEntry(entryName));
				int len = 0;
				byte[] buf = new byte[1024];
				while ((len = in.read(buf)) > 0) {
					zipOut.write(buf, 0, len);
				}
				zipOut.closeEntry();
			}
		}
		else{
			throw new FileNotFoundException("File not found: " + file.getAbsolutePath());
		}
	}

	// 폴더 내 파일을 zip 파일에 추가하는 메서드
	private void addFolderToZip(String folderPath, String folderName, ZipOutputStream zipOut) throws IOException {
		File folder = new File(folderPath);
		if (folder.exists() && folder.isDirectory() && folder.listFiles() != null) {
			for (File file : folder.listFiles()) {
				if (file.isFile()) {
					try(FileInputStream in  = new FileInputStream(file)){
						zipOut.putNextEntry(new ZipEntry(folderName+"/"+file.getName()));
						int len = 0;
						byte[] buf = new byte[4096];
						while((len = in.read(buf)) > 0) {
							zipOut.write(buf, 0, len);
						}
						zipOut.closeEntry();
					}
				}
				else if (file.isDirectory()){
					String childFolderName = folderName + "/" + file.getName();
					addFolderToZip(file.getAbsolutePath() , childFolderName, zipOut);
				}
			}
		}
		else{
			throw new FileNotFoundException("Folder not found: " + folder.getAbsolutePath());
		}
	}
}
