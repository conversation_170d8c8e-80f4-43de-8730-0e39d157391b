<%@page import="org.springframework.http.HttpStatus"%>
<%@page language="java" contentType="" pageEncoding="UTF-8"%>
<%@page trimDirectiveWhitespaces="true"%>
<%@page import="java.net.*,java.io.*"%>
<%@page import="java.util.*"%>
<%@page import="jakarta.servlet.http.*"%>
<%@page import="org.springframework.util.StringUtils"%>
<%@page import="org.springframework.web.util.HtmlUtils"%>


<%!private static final org.slf4j.Logger logger = org.slf4j.LoggerFactory.getLogger("proxyUrl.jsp");

	public int copyStream(InputStream in, OutputStream out) throws IOException, NullPointerException, Exception {
		int length;
		byte[] buff = new byte[8192];
		while ((length = in.read(buff)) != -1) {
			out.write(buff, 0, length);
		}
		out.flush();
		return length;
	}
	%>
<%
	HttpURLConnection connection = null;
	try {

		String method = request.getMethod();
		String queryString = request.getQueryString();
		if (StringUtils.isEmpty(queryString)) {
			throw new Exception("no parameters.");
		}
		String url = request.getParameter("url");
		if (url == null) {
			logger.info("URL이 존재하지 않습니다");
			return;
		}
		int questionIndex = url.indexOf("?");
		if (questionIndex >= 0) {
			url = url.substring(0, questionIndex);
			url = url + queryString.replace("url=" + url, "");
		} else {
			url = url + "?" + queryString.replace("url=" + url, "");
		}

		if(url.endsWith("?")){
			url = url.substring(0, url.length()-1);
		}

		out.clear(); // 제거시 spring 으로 구성된 프로젝트에서 getOutputStream() 에러가 발생합니다.
		URL resolved = new URL(url);
		connection = (HttpURLConnection) resolved.openConnection();
		int httpStatus =-1;
		if(url.endsWith(".tif")){
			// 요청 헤더에서 Range 읽기
			String rangeHeader = request.getHeader("Range");
			if (rangeHeader != null) {

				// Range 요청 처리
				String[] ranges = rangeHeader.replace("bytes=", "").split("-");
				long start = Long.parseLong(ranges[0]);
				long end = (ranges.length > 1) ? Long.parseLong(ranges[1]) : -1; // -1로 설정하여 전체 끝까지

				connection.setRequestProperty("Connection","keep-alive");
				connection.setRequestProperty("Range", "bytes=" + start + "-");

				httpStatus = connection.getResponseCode();
				//connection.connect();
				// 응답 코드 확인
				if (httpStatus == HttpURLConnection.HTTP_PARTIAL) {
					long contentLength = connection.getContentLengthLong();
					if(start!=0){
						contentLength += start;
					}
					if (end == -1 || end >= contentLength) {
						end = contentLength - 1;
					}

					// 응답 헤더 설정
					response.setStatus(HttpServletResponse.SC_PARTIAL_CONTENT); // 206
					response.setHeader("Content-Range", "bytes " + start + "-" + end + "/" + contentLength);
					response.setHeader("Content-Length", String.valueOf(end - start + 1));
				}
				else{
					response.sendError(HttpServletResponse.SC_NOT_FOUND); // 404 Not Found
				}
			} else {
				//connection.connect();
				// 응답 코드 확인
				httpStatus = connection.getResponseCode();
				if (httpStatus == HttpURLConnection.HTTP_OK) {
					long contentLength = connection.getContentLengthLong();

					// 응답 헤더 설정
					response.setContentLength((int) contentLength);
				} else {
					response.sendError(HttpServletResponse.SC_NOT_FOUND); // 404 Not Found
				}
			}
		}


		if (url.contains("stream")) {
			connection.setConnectTimeout(1000 * 600);
			connection.setReadTimeout(1000 * 600);
		} else {
			connection.setConnectTimeout(1000 * 60);
			connection.setReadTimeout(1000 * 60);
		}
		connection.connect();

		if ("POST".equalsIgnoreCase(method)) {
			connection.setDoOutput(true);
			connection.setRequestMethod(request.getMethod().toString());
			connection.setRequestProperty("Content-type", request.getContentType());

			if ((request.getContentType() != null
					&& request.getContentType().startsWith("application/x-www-form-urlencoded"))) {

				StringBuffer parameters = new StringBuffer();
				Enumeration<String> parameterNames = request.getParameterNames();
				if (parameterNames.hasMoreElements()) {
					while (parameterNames.hasMoreElements()) {
						String parameterName = parameterNames.nextElement();
						String[] parameterValues = request.getParameterValues(parameterName);
						if (parameterValues != null) {
							String parameterValue = Arrays.toString(parameterValues);
							parameterValue = parameterValue.substring(1, parameterValue.length() - 1);
							parameterName = parameterName.replaceAll("%", "%25");
							parameterName = HtmlUtils.htmlUnescape(parameterName);
							parameterName = parameterName.replaceAll("&apos;", "'");
							parameterValue = parameterValue.replaceAll("%", "%25");
							parameterValue = parameterValue.replaceAll("\\+", "%2B");
							parameterValue = HtmlUtils.htmlUnescape(parameterValue);
							parameterValue = parameterValue.replaceAll("&apos;", "'");
							parameters.append(parameterName + "=" + parameterValue);
							parameters.append("&");
						}
					}
				}
				ByteArrayInputStream parameterStream = new ByteArrayInputStream(
						parameters.toString().getBytes());
				copyStream(parameterStream, connection.getOutputStream());
			} else {
				copyStream(request.getInputStream(), connection.getOutputStream());
			}
		}
		String contentType = connection.getContentType();
		if(httpStatus==-1){
			httpStatus = connection.getResponseCode();
		}

		if (!contentType.isEmpty()) {
			contentType = contentType.replaceAll("[\\r\\n]", "");
			response.setContentType(contentType);
		}

		if (!HttpStatus.valueOf(httpStatus).is2xxSuccessful()) {
			response.setStatus(httpStatus);
			InputStream errorStream = connection.getErrorStream();
			BufferedReader errorReader = new BufferedReader(new InputStreamReader(errorStream, "UTF-8"));
			String line;
			StringBuilder errorResponse = new StringBuilder();
			while ((line = errorReader.readLine()) != null) {
				errorResponse.append(line);
			}
			errorReader.close();
			// 에러 응답을 처리하는 로직을 추가하세요
			logger.error("api error: " + errorResponse.toString());

			if (errorStream != null && connection.getOutputStream() != null) {
				copyStream(errorStream, connection.getOutputStream());
			}
		}

		copyStream(connection.getInputStream(), response.getOutputStream());
	} catch (IOException e) {
		logger.error("IOException ", e);
	} catch (Exception e) {
		logger.error("Exception  ", e);
	} finally {
		if (connection != null)
			connection.disconnect();
	}
%>
