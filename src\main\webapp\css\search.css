.searchBody {
    flex-direction: row;
    background-color: #fff;
    z-index: 999;
    right: 20px;
    width: 300px;
    box-shadow: 0px 0px 5px rgba(0,0,0,0.5);
}
.searchBox {
    width: 40%;
    border: 1px solid #eee;
    padding: 3px 3px 3px 3px;
    margin: 5px 3px 5px 5px;
}
.nextBtn, .prevBtn, .clearBtn {
    width: 10%;
    padding: 0px 3px 0px 3px;
    margin: 5px 3px 5px 3px;
    border: 2px solid white;
}
.countBody {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 30%;
    padding: 0px 3px 0px 3px;
    margin: 5px 5px 5px 3px;
    border: 2px solid white;
}
mark {
    background: yellow;
}
mark.current {
    background: orange;
}
.position_absolute {
 position: absolute;
}
.position_fixed {
    position: fixed;
    top: 80px;
}
.hide {
    display: none;
}
.show {
    display: flex;
}