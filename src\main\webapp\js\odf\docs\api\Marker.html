<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>GeOnPaas ui widgets: Marker</title>
    
      <link type="text/css" rel="stylesheet" href="styles/vendor/prism-tomorrow-night.css">
    
    <link type="text/css" rel="stylesheet" href="styles/styles.css">
    
    
    <style>
      :root {
      
      
        --nav-width: 370px;
      
      
        --nav-heading-margin-top: 0.5em;
      
      }
    </style>
    
</head>
<body>

<header class="layout-header">
  
  <h1>
    <a href="./index.html">
      GeOnPaas ui widgets
    </a>
  </h1>
  <nav class="layout-nav">
    <ul><li class="nav-heading">Classes</li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="BasemapControl.html">BasemapControl</a></span><span class="nav-desc"><p>배경지도 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="BookmarkControl.html">BookmarkControl</a></span><span class="nav-desc"><p>북마크 컨트롤 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ClearControl.html">ClearControl</a></span><span class="nav-desc"><p>지도 그리기 이벤트 초기화 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ColorFactory.html">ColorFactory</a></span><span class="nav-desc"><p>색 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Control.html">Control</a></span><span class="nav-desc"><p>사용자 정의 컨트롤 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Coordinate.html">Coordinate</a></span><span class="nav-desc"><p>좌표 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DivideMapControl.html">DivideMapControl</a></span><span class="nav-desc"><p>지도 분할 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DownloadControl.html">DownloadControl</a></span><span class="nav-desc"><p>다운로드 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DrawControl.html">DrawControl</a></span><span class="nav-desc"><p>그리기 도구 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Easing.html">Easing</a></span><span class="nav-desc"><p>애니메이션 효과</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="event.html">event</a></span><span class="nav-desc"><p>이벤트 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Extent.html">Extent</a></span><span class="nav-desc"><p>영역 관련 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Feature.html">Feature</a></span><span class="nav-desc"><p>Feature 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FeatureFactory.html">FeatureFactory</a></span><span class="nav-desc"><p>Feature 생성을 위한 FeatureFactory 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FormatFactory.html">FormatFactory</a></span><span class="nav-desc"></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FullScreenControl.html">FullScreenControl</a></span><span class="nav-desc"><p>전체화면 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="HomeControl.html">HomeControl</a></span><span class="nav-desc"><p>홈 이동 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Layer.html">Layer</a></span><span class="nav-desc"><p>레이어 관리 클래스로, 레이어는 odf.LayerFactory를 통해서만 생성가능하다.</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LayerFactory.html">LayerFactory</a></span><span class="nav-desc"><p>레이어 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LayerInfoControl.html">LayerInfoControl</a></span><span class="nav-desc"><p>레이어 정보 조회 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Map.html">Map</a></span><span class="nav-desc"><p>지도 생성, 조작, 컴퍼넌트, 레이어 추가 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Marker.html">Marker</a></span><span class="nav-desc"><p>마커 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MeasureControl.html">MeasureControl</a></span><span class="nav-desc"><p>지도 측정 도구 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MousePositionControl.html">MousePositionControl</a></span><span class="nav-desc"><p>마우스 좌표 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MoveControl.html">MoveControl</a></span><span class="nav-desc"><p>현재 화면 기준으로 이전/다음 화면 이동 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="OverviewMapControl.html">OverviewMapControl</a></span><span class="nav-desc"><p>인덱스맵 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Popup.html">Popup</a></span><span class="nav-desc"><p>팝업 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="PrintControl.html">PrintControl</a></span><span class="nav-desc"><p>프린트 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Projection.html">Projection</a></span><span class="nav-desc"><p>좌표 변환 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="RotationControl.html">RotationControl</a></span><span class="nav-desc"><p>화면을 회전 시키는 기능
alt + shift 드래그로 지도 회전</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ScaleControl.html">ScaleControl</a></span><span class="nav-desc"><p>축척 표시 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="SLD.html">SLD</a></span><span class="nav-desc"><p>WMS 스타일 관리 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Style.html">Style</a></span><span class="nav-desc"><p>스타일 관리 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="StyleFactory.html">StyleFactory</a></span><span class="nav-desc"><p>스타일 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="StyleFunction.html">StyleFunction</a></span><span class="nav-desc"><p>스타일 Function 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="SwiperControl.html">SwiperControl</a></span><span class="nav-desc"><p>지도 스와이퍼 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ZipControl.html">ZipControl</a></span><span class="nav-desc"><p>Server없이 Layer 생성하는 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ZoomControl.html">ZoomControl</a></span><span class="nav-desc"><p>지도 줌 설정클래스</p></span></li></ul><li class="nav-heading"><a href="global.html">Globals</a></li>
  </nav>
</header>


<main class="layout-main ">
  <div class="container">
    <p class="page-kind">Class</p>
    <h1 class="page-title">Marker</h1>
    




<section>


<header class="class">


    
        
        <!-- <h2>Marker</h2> -->

        

        
            <h4 class="method-heading">Summary</h4>
            <div class="class-summary"><p>마커 생성자</p>
<pre class="prettyprint source lang-javascript"><code>//기본 마커 생성
let marker = new odf.Marker({
 position : new odf.Coordinate(955156.7761, 1951925.0984),
 //position : [955156.7761, 1951925.0984],
});
marker.setMap(map);
</code></pre>
<pre class="prettyprint source lang-javascript"><code>//마커가 지도 영역 벗어났을 경우 지도가 이동되는 마커 생성
let marker = new odf.Marker({
  position : new odf.Coordinate(955156.7761, 1951925.0984),
  autoPan: true,
  autoPanAnimation: {
     duration: 250
  }
 });
 marker.setMap(map);
</code></pre>
<pre class="prettyprint source lang-javascript"><code>//드래그 가능한 마커 생성
  let marker = new odf.Marker({
    position : new odf.Coordinate(955156.7761, 1951925.0984),
    draggable : true
  });
  marker.setMap(map);
</code></pre>
<pre class="prettyprint source lang-javascript"><code>//커스터마이징 마커 1
let marker = new odf.Marker({
  position : new odf.Coordinate(955156.7761, 1951925.0984),
  style : {
    width : '20px',
    height : '20px',
    src : 'images/smileIcon'
  }
});
marker.setMap(map);
</code></pre>
<pre class="prettyprint source lang-javascript"><code>//커스터마이징 마커 2
  let marker = new odf.Marker({
    position : new odf.Coordinate(955156.7761, 1951925.0984),
    style : {
      element : document.getElementById('customMarker')
    }
  });
  marker.setMap(map);
</code></pre></div>
        

        
            <h4 class="method-heading">Description</h4>
            <div class="class-description"><p>마커 생성 클래스</p></div>
        
    
</header>

<article>
    <div class="container-overview">



    
        





    


    
    <h3 class="subtitle">Constructor</h3>
    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="Marker">new Marker<span class="signature">(options)</span><span class="return-type-signature"></span>
    </h4>













    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">options</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#markerOption">markerOption</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>마커 생성을 위한 option</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    
    </div>

    

    

     

    

    


    

    
        <h3 class="subtitle">Methods</h3>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getDraggable">getDraggable<span class="signature">()</span><span class="return-type-signature"> &rarr; {Boolean}</span>
    </h4>





<div class="method-description">
    
    <p>마커의 드래그 가능 여부 반환</p>
<pre class="prettyprint source lang-javascript"><code>         //마커의 드래그 가능 여부 조회
         let marker = new odf.Marker({
         position : new odf.Coordinate(955156.7761, 1951925.0984),
        });
         marker.setMap(map);

         marker.getDraggable();//드래그 가능여부 기본 false
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Boolean</code>
            
            
                <p>마커의 드래그 가능 여부</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getElement">getElement<span class="signature">()</span><span class="return-type-signature"> &rarr; {Element}</span>
    </h4>





<div class="method-description">
    
    <p>마커와 연결된 element 조회</p>
<pre class="prettyprint source lang-javascript"><code>       //마커와 연결된 element 반환
       let marker = new odf.Marker({
       position : new odf.Coordinate(955156.7761, 1951925.0984),
      });
       marker.setMap(map);
       let mElement  = marker.getElement();
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Element</code>
            
            
                <p>마커와 연결된 Html element</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getMap">getMap<span class="signature">()</span><span class="return-type-signature"> &rarr; {<a href="Map.html">Map</a>}</span>
    </h4>





<div class="method-description">
    
    <p>마커에 연결된 map객체 반환</p>
<pre class="prettyprint source lang-javascript"><code>       //마커에 연결된 map객체 조회
       let marker = new odf.Marker({
         position : new odf.Coordinate(955156.7761, 1951925.0984),
       });
       marker.setMap(map);
       let _map = marker.getMap();
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Map</code>
            
            
                <p>마커에 연결된 map객체, 없으면 undefined</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getPosition">getPosition<span class="signature">()</span><span class="return-type-signature"> &rarr; {<a href="Coordinate.html">Coordinate</a>}</span>
    </h4>





<div class="method-description">
    
    <p>마커의 위치정보 반환</p>
<pre class="prettyprint source lang-javascript"><code>       //마커의 위치정보 조회
       let marker = new odf.Marker({
       position : new odf.Coordinate(955156.7761, 1951925.0984),
      });
       marker.setMap(map);
       let mCoord  = marker.getPosition();
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Coordinate</code>
            
            
                <p>마커의 위치정보</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="removeMap">removeMap<span class="signature">()</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>마커와 연결된 map 객체 제거</p>
<pre class="prettyprint source lang-javascript"><code>       //마커와 연결된 map 객체 제거
       let marker = new odf.Marker({
        position : new odf.Coordinate(955156.7761, 1951925.0984),
      });
       marker.setMap(map);
       marker.removeMap();
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setDraggable">setDraggable<span class="signature">(draggable)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>마커의 드래그 가능 여부 변경</p>
<pre class="prettyprint source lang-javascript"><code>         //마커의 드래그 가능 여부 변경
         let marker = new odf.Marker({
           position : new odf.Coordinate(955156.7761, 1951925.0984),
         });
         marker.setMap(map);

         marker.setDraggable(true);//드래그 가능한 마커로 변경
         //marker.setDraggable(false));//드래그 불가능한 마커로 변경
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">draggable</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            

            

            <div class="param-description"><p>마커의 드래그 가능 여부</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setMap">setMap<span class="signature">(Map)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>마커에 map객체 연결</p>
<pre class="prettyprint source lang-javascript"><code>       //마커에 map객체 연결
       let marker = new odf.Marker({
         position : new odf.Coordinate(955156.7761, 1951925.0984),
       });
       marker.setMap(map);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">Map</span>
            

            
                


    <span class="param-type">
        <code><a href="Map.html">Map</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>마커에 연결할 map객체</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setOffset">setOffset<span class="signature">(position)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>배치할 때 사용되는 오프셋(픽셀)입니다. 배열의 첫 번째 요소는 수평 오프셋(양수 값은 오버레이를 오른쪽으로 이동) 배열의 두 번째 요소는 수직 오프셋(양수 값은 오버레이를 아래로 이동합니다.)</p>
<pre class="prettyprint source lang-javascript"><code>       let marker = new odf.Marker({
     position : new odf.Coordinate(955156.7761, 1951925.0984),
    });
       marker.setMap(map);
       marker.setPosition(odf.Coordinate(955909.5218701352, 1954955.496334219));
       marker.setOffset([0, -10]);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">position</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>지도의 해당 지점을 기준으로 배치되는 방식</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setPosition">setPosition<span class="signature">(coord)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>마커의 위치정보 변경</p>
<pre class="prettyprint source lang-javascript"><code>       //마커의 위치정보 변경
       let marker = new odf.Marker({
     position : new odf.Coordinate(955156.7761, 1951925.0984),
    });
       marker.setMap(map);
       marker.setPosition(odf.Coordinate(955909.5218701352, 1954955.496334219));
       //marker.setPosition([955909.5218701352, 1954955.496334219]);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">coord</span>
            

            
                


    <span class="param-type">
        <code><a href="Coordinate.html">Coordinate</a></code>
    </span>
    |

    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    

            

            

            

            <div class="param-description"><p>마커의 위치정보</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setPositioning">setPositioning<span class="signature">(position)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>마커의 속성과 관련하여 실제로 배치되는 방식을 정의</p>
<pre class="prettyprint source lang-javascript"><code>       let marker = new odf.Marker({
   position : new odf.Coordinate(955156.7761, 1951925.0984),
  });
       marker.setMap(map);
       marker.setPosition(odf.Coordinate(955909.5218701352, 1954955.496334219));
       marker.setPositioning('bottom-left');
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">position</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>지도의 해당 지점을 기준으로 배치되는 방식</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
    

    

    
</article>

</section>




  </div>
</main>

<footer class="layout-footer">
  <div class="container">
    Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.11</a> on Tue Jan 21 2025 11:05:52 GMT+0900 (대한민국 표준시)
  </div>
</footer>



<script src="scripts/prism.dev.js"></script>
</body>
</html>