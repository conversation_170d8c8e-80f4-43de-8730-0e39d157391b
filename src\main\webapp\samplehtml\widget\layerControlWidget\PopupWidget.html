<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<link href="::OuiUrl::/oui.css" rel="stylesheet">

	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
	<script type="text/javascript" src="::OuiUrl::/oui.min.js"></script>
</head>
<body>
	<div id ="map" style="height:550px;"></div>
</body>

<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::, ::coordy::);
    var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);

	/* 테스트 레이어 생성 */
	var wfsLayer = odf.LayerFactory.produce('geoserver', {
		method : 'get',
		server : '::WfsAPI::',
		layer : '::testPointLayer1::',
		service : 'wfs',
	});
	wfsLayer.setMap(map);
	wfsLayer.fit();


    /* 팝업 위젯(기본) 생성  */
	var popupWidget = new oui.PopupWidget({
		odf: odf,
		options: {
			draggable: true, //팝업 위젯 드래그 사용 여부
			//팝업에 커스텀 html 추가
			setAddInfoHtml: (popupInfo)=>{
				console.dir(popupInfo);//레이어 피쳐 정보
				let popupInfoStr = JSON.stringify(popupInfo);
				return {
					location : 'bottom', //table 속성팝업 테이블이랑 같은위치, 스크롤 생길경우 스크롤 아래에 생김 bottom 팝업 맨아래 생김 스크롤 생겨도 맨아래 계속 표줄됨 default table
					html :
						`<button id="popupBtn" onclick='onClickPopupBtn1(${popupInfoStr})'>팝업버튼1</button>
						<button id="popupBtn2" onclick='onClickPopupBtn2(${popupInfoStr})'>팝업버튼2</button>`
 	  			}
			},
			/*
			//팝업이 열릴때 호출되는 콜백함수
			callbackOpen :(popupInfo)=>{

			},
			*/
			/*
			// 팝업의 활성/비활성화 여부
			getIsActivateOption: () => {
				//popupDisplay : 팝업 표출 활성/비활성화
				//isActivateOption(활성화여부) : true(활성화)/false(비활성화)
				//condition 필터조건을 넣을 배열객체. 조건으로 사용할수 있는 값은 (getLayerList에서 넘겨줬던 레이어 목록의 레이어 객체의 키값들)
				//예시1) condition: [{ cntntsId: '[Geoserver 발행 id(a)]' }, { cntntsId: '[Geoserver 발행 id(b)]' }] -> cntntsId가 (a) 이거나(or) (b)인 경우 해당 레이어 속성팝업 비활성화
				//예시2) condition: [{ cntntsId: '[Geoserver 발행 id(a)]', title: '타이틀명' }] -> cntntsId가 (a) 이면서(and) title이 '타이틀명'인 경우 해당 레이어 속성팝업 비활성화

				return {
					popupDisplay: { isActivateOption: false, condition: [{ cntntsId: '[Geoserver 발행 id(a)]' }] }
				}
			},*/
			//하이라이트 기능 사용 여부(기본값 true)
			//useHilight :true,
			//하이라이트 레이어 스타일(미정의시 내부에서 정의한 기본 스타일 적용)
			styleObject : {
				image: {
					circle: {
						radius: 10,
						fill: { color: [255, 255, 255, 0] },
						stroke: { color: [237, 116, 116, 0.82], width: 2 },
					},
				},
				fill: { color: [255, 255, 255, 0] },
				stroke: { color: [237, 116, 116, 0.9], width: 4 },
			},
			//클릭 한 위치에 버퍼를 지정하여 팝업 조회 (단위:픽셀) (기본값 20) (getLayerList를 사용하고, geometryType이 점,선인 레이어만 적용됨)
			//pointBuffer : 20
		}
	});
	popupWidget.addTo(map);

	/* 팝업 위젯 옵션중 커스텀 html을 추가(setAddInfoHtml)하는 옵션에 사용되는 예제 함수 */
	function onClickPopupBtn1(popupInfo){
		console.dir(popupInfo);
		alert('onClickPopupBtn1 클릭');
	}
	function onClickPopupBtn2(popupInfo){
		console.dir(popupInfo);
		alert('onClickPopupBtn2 클릭');
	}
</script>
</html>
