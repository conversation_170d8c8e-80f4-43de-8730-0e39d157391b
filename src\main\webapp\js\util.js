/**
 * 
 */
var util = {

	nullCheck : function(val) {
		if (val == undefined || val == null) {
			return "";
		} else {
			return val;
		}
	},
	
	isNullOrEmpty : function(val) {
		return val == undefined || val == null || val == "";
	},

	isJsonString : function(str) {
		
		try {
			var json = JSON.parse(str);
			return (typeof json === 'object');
		} catch (e) {
			return false;
		}
	},
	
	getParamJson: function() {
		var url = document.location.href;
		var qs = url.substring(url.indexOf('?') + 1).split('&');
		var result = {};
		for(var i=0; i < qs.length; i++) {
			qs[i] = qs[i].split('=');
			result[qs[i][0]] = decodeURIComponent(qs[i][1]);
		}
		
		return result; 
	},
	
	getParam: function(name) {
		var  results = new RegExp('[\?&]' + name + '=([^&#]*)').exec(window.location.href);
		return results ? decodeURIComponent(results[1].replace(/\+/g, '%20')) : null;
	},
	
	jq: function(id) {
		return id.replace(/(:|\.|\[|\]|\(|\)|,|=|@|\/)/g, "\\\\$1");
	},
	
	removeTags: function(str) {
		if(util.isNullOrEmpty(str)) {
			return false;
		} else {
			str = str.toString();
			return str.replace(/<([^>]+)>/gi, '');
		}
	},
	
	removeStyle: function(str) {
		
		if(util.isNullOrEmpty(str)) {
			return false;
		} else {
			str = str.toString();
			return str.replace(/style="([^"]*)"/gi, '');
		}
		
	},
	
	replaceNewLine: function(str) {
		if(util.isNullOrEmpty(str)) {
			return false;
		} else {
			str = str.toString();
			return str.replace(/\r\n/gi, '<br>');
		}
	}
}