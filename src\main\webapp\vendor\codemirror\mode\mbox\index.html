<!doctype html>

<title>CodeMirror: mbox mode</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../../doc/docs.css">

<link rel="stylesheet" href="../../lib/codemirror.css">
<script src="../../lib/codemirror.js"></script>
<script src="mbox.js"></script>
<style>.CodeMirror { border-top: 1px solid #ddd; border-bottom: 1px solid #ddd; }</style>
<div id=nav>
  <a href="https://codemirror.net"><h1>CodeMirror</h1><img id=logo src="../../doc/logo.png" alt=""></a>

  <ul>
    <li><a href="../../index.html">Home</a>
    <li><a href="../../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror">Code</a>
  </ul>
  <ul>
    <li><a href="../index.html">Language modes</a>
    <li><a class=active href="#">mbox</a>
  </ul>
</div>

<article>
<h2>mbox mode</h2>
<form><textarea id="code" name="code">
From <EMAIL> Sun Apr 17 01:40:43 2016
From: Timothy Gu &lt;<EMAIL>&gt;
Date: Sat, 16 Apr 2016 18:40:43 -0700
Subject: mbox mode
Message-ID: &lt;Z8d+bTT50U/az94FZnyPkDjZmW0=@gmail.com&gt;

mbox mode is working!

Timothy
</textarea></form>
    <script>
      var editor = CodeMirror.fromTextArea(document.getElementById("code"), {});
    </script>

    <p><strong>MIME types defined:</strong> <code>application/mbox</code>.</p>

  </article>
