# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 프로젝트 개요

이 프로젝트는 전자정부 프레임워크 기반의 Spring Boot 웹 애플리케이션입니다. Java 8에서 17로, Spring 4.x에서 6.x로 업그레이드된 개발자 센터 플랫폼입니다.

## 기술 스택

- **Java**: 17
- **Spring Framework**: 6.1.13
- **전자정부 프레임워크**: 4.2.0
- **View Template**: JSP + Thymeleaf (하이브리드)
- **Build Tool**: Maven 3
- **웹 서버**: Tomcat 10.1.23

## 개발 명령어

### 빌드 및 패키징
```bash
# 테스트 스킵하고 WAR 파일 생성
mvn clean package -DskipTests

# 의존성 포함하여 빌드 (GitLab CI와 동일)
mvn --batch-mode --errors --fail-at-end --show-version -DinstallAtEnd=true -DdeployAtEnd=true clean package -DskipTests
```

### 로컬 개발
```bash
# 개발 모드로 실행 (Tomcat 필요)
mvn clean compile

# 의존성 설치
mvn dependency:resolve
```

## 아키텍처 구조

### 패키지 구조
- `gops.developer.core`: 핵심 설정 및 공통 기능
  - `config/WebMvcConfig.java`: Spring MVC 설정 (Java Configuration)
  - `interceptor/`: 공통 인터셉터
  - `viewResolver/`: 커스텀 뷰 리졸버
- `gops.developer.*`: 각 도메인별 컨트롤러
  - `home/web/`: 홈페이지 관련
  - `docs/web/`: 문서 관련
  - `guide/web/`: 가이드 관련
  - `map/web/`: 지도 관련
  - `widget/web/`: 위젯 관련
  - `sample/web/`: 샘플 관련
  - `proxy/web/`: 프록시 관련
- `egovframework.com`: 전자정부 프레임워크 공통 컴포넌트

### 설정 방식
- **Java Configuration**: XML 설정에서 Java Configuration으로 전환 완료
- **View Resolver**: JSP와 Thymeleaf 하이브리드 방식
  - Thymeleaf: 우선순위 1, `/WEB-INF/views/`
  - JSP: 우선순위 2, `/WEB-INF/jsp/`

### 정적 리소스
- **위치**: `src/main/webapp/`
- **구조**:
  - `asset/`: API 관련 에셋
  - `css/`: 스타일시트 (폰트 포함)
  - `js/`: JavaScript 파일
  - `images/`: 이미지 파일
  - `lib/`: 라이브러리
  - `vendor/`: 외부 벤더 파일
  - `data/`: 샘플 데이터

## 주요 컴포넌트

### WebMvcConfig.java
- Spring MVC 설정의 중심
- Thymeleaf와 JSP 뷰 리졸버 동시 설정
- 정적 리소스 핸들링 (`/**` 패턴으로 모든 리소스 매핑)
- DefaultInterceptor 등록

### 프록시 기능
- `proxy.jsp`, `proxyUrl.jsp`: 외부 API 호출용 프록시
- CORS 이슈 해결을 위한 서버사이드 프록시

### 전자정부 프레임워크 연동
- `EgovProperties`: 전자정부 프레임워크 설정 관리
- 공통 유틸리티: `EgovStringUtil`, `EgovWebUtil` 등

## 배포 설정

### GitLab CI
- **이미지**: `maven:*********************alpine`
- **빌드**: `mvn clean package -DskipTests`
- **배포**: SSH를 통한 원격 서버 배포
- **아티팩트**: `target/*.war` (1일 보관)

### Docker
- `Dockerfile` 포함
- Tomcat 기반 컨테이너화

## 개발 시 주의사항

1. **Java Configuration 사용**: XML 설정 대신 Java Configuration 사용
2. **하이브리드 뷰**: JSP와 Thymeleaf 동시 지원
3. **정적 리소스**: `/**` 패턴으로 모든 리소스가 매핑되므로 충돌 주의
4. **전자정부 프레임워크**: 기존 컴포넌트와의 호환성 유지 필요
5. **프록시 기능**: 외부 API 연동 시 기존 프록시 JSP 활용

## 테스트

현재 테스트는 비활성화되어 있음 (`.gitlab-ci.yml`에서 주석 처리)
테스트 추가 시 Maven Surefire Plugin 설정 필요