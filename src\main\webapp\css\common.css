@charset "UTF-8";
@font-face {
	font-family: 'Noto Sans Light';
	font-style: normal;
	font-weight: 400;
	src: url(./font/notoSans/eot/NotoSansKR-Light-Hestia.eot);
	src: url(./font/notoSans/eot/NotoSansKR-Light-Hestia.eot?#iefix) format("embedded-opentype"), url(./font/notoSans/woff/NotoSansKR-Light-Hestia.woff) format("woff");
}

@font-face {
	font-family: 'Noto Sans DemLight';
	font-style: normal;
	font-weight: 400;
	src: url(./font/notoSans/eot/NotoSansKR-DemiLight-Hestia.eot);
	src: url(./font/notoSans/eot/NotoSansKR-DemiLight-Hestia.eot?#iefix) format("embedded-opentype"), url(./font/notoSans/woff/NotoSansKR-DemiLight-Hestia.woff) format("woff");
}

@font-face {
	font-family: 'Noto Sans';
	font-style: normal;
	font-weight: 400;
	src: url(./font/notoSans/eot/NotoSansKR-Regular-Hestia.eot);
	src: url(./font/notoSans/eot/NotoSansKR-Regular-Hestia.eot?#iefix) format("embedded-opentype"), url(./font/notoSans/woff/NotoSansKR-Regular-Hestia.woff) format("woff");
}

@font-face {
	font-family: 'Noto Sans Medium';
	font-style: normal;
	font-weight: 400;
	src: url(./font/notoSans/eot/NotoSansKR-Medium-Hestia.eot);
	src: url(./font/notoSans/eot/NotoSansKR-Medium-Hestia.eot?#iefix) format("embedded-opentype"), url(./font/notoSans/woff/NotoSansKR-Medium-Hestia.woff) format("woff");
}

/* reset */
/* line 36, scss/common.scss */
html, body, div, span, object, h1, h2, h3, h4, h5, h6, p, blockquote, a, button, abbr, address, img, q, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td, article, aside, footer, header, section, summary {
	margin: 0;
	padding: 0;
	border: 0;
	font: inherit;
}

/* line 37, scss/common.scss */
article, aside, details, figcaption, figure, footer, header, hgroup, menu, nav, section {
	display: block;
}

/* HTML5 display-role reset for older browsers */
/* line 38, scss/common.scss */
body, html {
	width: 100%;
	height: 100%;
	font-family: 'Noto Sans';
	color: #393939;
}

/* line 39, scss/common.scss */
ol, ul {
	list-style: none;
}

/* line 40, scss/common.scss */
table {
	border-collapse: collapse;
	border-spacing: 0;
}

/* line 41, scss/common.scss */
img {
	border: none;
}

/* line 42, scss/common.scss */
a {
	text-decoration: none;
	color: inherit;
}

/* line 43, scss/common.scss */
address {
	font-style: normal;
}

/* line 44, scss/common.scss */
button {
	display: inline-block;
	border: none;
	background-color: transparent;
	cursor: pointer;
}

/* line 45, scss/common.scss */
input, button, a, select, option {
	font-family: inherit;
	font-size: inherit;
}

/* line 46, scss/common.scss */
input[type="checkbox"] {
	border: none !important;
}

/* for lte IE10 */
/* line 47, scss/common.scss */
input[type="radio"] {
	border: none !important;
}

/* for lte IE10 */
/* line 48, scss/common.scss */
input[type="text"]::-ms-clear {
	display: none;
}

/* line 49, scss/common.scss */
input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
	margin: 0;
}

/* line 51, scss/common.scss */
button:focus {
	outline: none;
}

/* line 52, scss/common.scss */
caption, hr {
	display: none;
}

/* line 53, scss/common.scss */
strong {
	font-weight: normal;
}

/* line 54, scss/common.scss */
pre {
	word-break: break-all;
	white-space: pre-line;
}

/* default */
/* line 57, scss/common.scss */
body, html {
	font-family: 'Noto Sans DemLight', "맑은 고딕", "Malgun Gothic", Dotum, sans-serif;
	font-size: 13px;
	color: #333;
	letter-spacing: -0.045em !important;
}

/* line 58, scss/common.scss */
.clearFix:after {
	content: "";
	display: block;
	clear: both;
}

/* line 59, scss/common.scss */
.hidden {
	display: block;
	margin: 0;
	padding: 0;
	width: 0;
	height: 0;
	overflow: hidden;
	font-size: 0;
	line-height: 0;
	visibility: hidden;
}

/* line 60, scss/common.scss */
.txt-center {
	text-align: center;
}

/* line 61, scss/common.scss */
.pos-r {
	position: relative;
	z-index: 15;
}

/* line 62, scss/common.scss */
.pos-a {
	position: absolute;
}

/* line 63, scss/common.scss */
.va-t {
	vertical-align: top;
}

/* line 64, scss/common.scss */
.inner {
	margin-left: 90px;
}

/* line 65, scss/common.scss */
.left {
	float: left;
}

/* line 66, scss/common.scss */
.right {
	float: right;
}

/* line 67, scss/common.scss */
.txtRight {
	text-align: right;
}

/* line 68, scss/common.scss */
.txtBlue {
	color: #2853d5;
}

/* line 69, scss/common.scss */
.txtRed {
	color: #f24c4c;
}

/* line 70, scss/common.scss */
.bold {
	font-weight: bold;
}

/* line 71, scss/common.scss */
#wrap {
	/* min-width: 1280px; */
	min-width: 1580px;
	min-height: 100%;
	position : relative;
}

/* line 72, scss/common.scss */
#wrap.moduMain {
	position: relative;
	height: auto;
	background: #fff;
}

/* line 73, scss/common.scss */
#wrap.moduMain:after {
	position: absolute;
	left: 0;
	top: 656px;
	z-index: 10;
	display: block;
	content: '';
	width: 100%;
	height: 500px;
	background: #eff1f5;
}

/* line 74, scss/common.scss */
#wrap.moduMain #header {
	border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* line 75, scss/common.scss */
#wrap.moduMain #container {
	padding-left: 0;
	padding-right: 0;
	background: none;
}

/* line 76, scss/common.scss */
#wrap.moduMain #content {
	z-index: 20;
	width: 1280px;
	background: none;
	padding: 0 0 100px 15px;
}

/* line 77, scss/common.scss */
#wrap.moduMain #content .cont {
	margin-bottom: -20px;
}

/* 로딩바 */
@keyframes showing {
	0% {
		opacity: 0;
	}
	100% {
		opacity: 1;
	}
}

/* line 87, scss/common.scss */
#dimmed {
	position: fixed;
	z-index: 1800;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: #000;
	opacity: 0.6;
}

/* line 88, scss/common.scss */
.loadingBg {
	position: fixed;
	z-index: 1800;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, 0.6);
}

/* line 89, scss/common.scss */
.loadingBg .loading {
	position: absolute;
	left: calc(50% - 150px);
	top: calc(50% - 64px);
	width: 300px;
	padding-top: 164px;
	padding-bottom: 20px;
	color: #333333;
	font-size: 16px;
	font-family: 'Noto Sans Medium';
	background: #fdfcff url("../images/common/loading.gif");
	background-repeat: no-repeat;
	background-position: center 15px;
	text-align: center;
	border-radius: 10px;
}

/* line 91, scss/common.scss */
.loadingBg .loading .textWrap {
	padding: 15px 20px;
}

/* line 92, scss/common.scss */
.loadingBg .loading .textWrap strong {
	color: #222;
	font-size: 18px;
	font-weight: normal;
	font-family: 'Noto Sans Medium';
	animation: showing 2s;
}

/* line 93, scss/common.scss */
.loadingBg .loading .textWrap p {
	color: #787878;
	font-size: 14px;
	font-weight: bold;
	font-family: '맑은 고딕';
	animation: showing 4s;
}

/* 인풋 텍스트 */
/* line 100, scss/common.scss */
input[type="text"] {
	border: 1px solid #dbdfe5;
	padding: 0 10px;
}

/* line 101, scss/common.scss */
input[type="number"] {
	border: 1px solid #dbdfe5;
	padding: 0 0 0 10px;
}

/* 체크박스 */
/* line 105, scss/common.scss */
.checkbox input[type="checkbox"] {
	display: none;
}

/* line 106, scss/common.scss */
.checkbox input[type="checkbox"]:checked + label {
	background: url("../images/common/bg-checkbox-check.png") no-repeat left center;
}

/* line 108, scss/common.scss */
.checkbox label {
	display: inline-block;
	min-height: 18px;
	padding-left: 23px;
	background: url("../images/common/bg-checkbox.png") no-repeat left center;
}

/* 라디오 */
/* line 112, scss/common.scss */
.radio input[type="radio"] {
	display: none;
}

/* line 113, scss/common.scss */
.radio input[type="radio"]:checked + label {
	background: url("../images/common/bg-radio-check.png") no-repeat left center;
}

/* line 115, scss/common.scss */
.radio label {
	display: inline-block;
	min-height: 10px;
	padding-left: 15px;
	background: url("../images/common/bg-radio.png") no-repeat left center;
}

/* 인풋 셀렉트박스 input select */
/* line 118, scss/common.scss */
select {
	font-size: 14px;
	font-family: "맑은 고딕";
	color: #585858;
	-webkit-appearance: none;
	-moz-appearance: none;
	appearance: none;
	padding: 0 10px;
}

/* line 125, scss/common.scss */
select {
	display: inline-block;
	width: 100%;
	height: 38px;
	border: 1px solid #cdd2d9;
	box-sizing: border-box;
	font-size: 14px;
	font-family: "맑은 고딕";
	background: #fff url("../images/common/ico-down.png") no-repeat right 13px center;
	appearance: none;
	/* 기본 스타일 없애기 */
	-ms-appearance: none;
	-webkit-appearance: none;
	-moz-appearance: none;
	-o-appearance: none;
}

/* line 134, scss/common.scss */
select::-ms-expand {
	display: none;
}

/* Accessibility Navigation */
/* line 137, scss/common.scss */
.accNav {
	position: absolute;
	top: 0;
	left: 0;
	z-index: 500;
	width: 100%;
	height: 0;
}

/* line 138, scss/common.scss */
.accNav a {
	display: block;
	position: absolute;
	left: 0;
	top: 0;
	overflow: hidden;
	width: 1px;
	height: 1px;
	margin-left: -1px;
	margin-bottom: -1px;
	text-align: center;
	color: #fff;
	white-space: nowrap;
	font-size: 0.75em;
}

/* line 139, scss/common.scss */
.accNav a:focus,
.accNav a:hover,
.accNav a:active {
	z-index: 1000;
	width: 100%;
	height: auto;
	padding: 5px 0;
	background: #ffc000;
	color: #4A2713;
	font-weight: 700;
}

/* line 144, scss/common.scss */
#wrap::before {
	content: '';
	display: inline-block;
	clear: both;
}

/* gnb */
/* line 147, scss/common.scss */
#header {
	position: fixed;
	left: 0;
	top: 0;
	width: 90px;
	height:calc(100% - 67px);
	box-shadow: 1px 1px 4px rgb(0 0 0 / 15%);
	z-index: 1;
	background: #fff;
}

/* line 148, scss/common.scss */
#header h1 {
	text-align: center;
	margin-bottom:22px
}

/* line 149, scss/common.scss */
#header h1 > span{
	position: relative;
	display: inline-block;
	padding-top: 55px;
	margin-bottom: 15px;
	color: #333;
	letter-spacing:-1px;
	font-size:14px;
	font-weight:bold;
	font-family: 'Noto Sans';
}
#header h1 span>span{
font-size:12px;
color:#707070;
letter-spacing:0px;
font-family:'Noto Sans Light';
}
/* line 150, scss/common.scss */
#header h1>span:before {
	position: absolute;
    top: 32px;
    left: 50%;
    display: block;
    content: '';
    width: 56px;
    height: 31px;
    background: url(../images/common/ico-main-logo.png);
    transform: translate(-50%, -50%);
}

/* line 153, scss/common.scss */
#header .gnb {
	padding: 0;
}

/* line 154, scss/common.scss */
#header .gnb li {
	border-bottom:1px solid #dedede;
}

/* line 155, scss/common.scss */
#header .gnb li a {
	display: block;
/* 	width: 90px;
	height: 75px;*/
	line-height: 75px;
	text-align: center;
	font-size: 16px;
	font-family: 'Noto Sans';
	background:#fff;
	color:#949494;
}

/* line 156, scss/common.scss */
#header .gnb li a:hover, #header .gnb li a.active {
	background:#f4f4f4;
	box-shadow:inset 3px 3px 4px rgb(0 0 0 / 25%);
	color:#333;
	font-weight: bold;
}

@media screen and (max-height: 573px) {
	#header .gnb li a {
		line-height: 40px;
	}
}

@media screen and (min-height: 574px) and (max-height: 643px) {
	#header .gnb li a {
		line-height: 50px;
	}
}

@media screen and (min-height: 644px) and (max-height: 750px) {
	#header .gnb li a {
		line-height: 60px;
	}
}

/* sideMenu */
/* line 163, scss/common.scss */
.sideMenu {
	position: absolute;
	left: 90px;
	top: 0;
	width: 310px;
	height:calc(100% - 67px);
	background: #f4f4f4;
}

/* line 164, scss/common.scss */
.sideMenu.mScroll .mCSB_outside + .mCSB_scrollTools {
	right: 0;
}

/* line 165, scss/common.scss */
.sideMenu .innerBox {
	position: relative;
	padding: 14px 12px 20px;
}

/* line 166, scss/common.scss */
.sideMenu .innerBox input[type="text"], .sideMenu .innerBox input[type="number"] {
	width: 100%;
	height: 48px;
	border: 0;
	box-sizing: border-box;
	border-radius:5px;
	box-shadow:0px 3px 6px rgb(0 0 0 / 15%);
	font-size: 16px;
	font-family: 'Noto Sans';
	text-indent: 44px;
	background: #fff url("../images/common/ico-search.png") no-repeat center left 20px;
}

/* line 168, scss/common.scss */
.sideMenu .innerBox input[type="text"]::placeholder, .sideMenu .innerBox input[type="number"]::placeholder {
	color: #a8a8a8;
}

/* line 170, scss/common.scss */
.sideMenu .innerBox .btnSearchRemove {
position: absolute;
    display: none;
    right: 26px;
    top: 30px;
    width: 15px;
    height: 15px;
    background: url(../images/common/ico-search-reset.png) no-repeat;
    background-size: cover;
}

/* line 172, scss/common.scss */
.sideMenu .innerBox .btnSearchRemove:hover {
	background-image: url("../images/common/ico-search-reset-hover.png");
}

/* line 175, scss/common.scss */
.sideMenu .listArea {
	text-indent: 20px;
	height: calc(100% - 82px);
}

/* line 176, scss/common.scss */
.sideMenu .listArea .listGroup {
	position: relative;
	margin-top: 13px;
}

/* line 178, scss/common.scss */
.sideMenu .listArea .listGroup .helpWrap {
	position: relative;
}

/* line 179, scss/common.scss */
.sideMenu .listArea .listGroup .helpWrap::after {
	content: "";
	display: block;
	clear: both;
}

/* line 180, scss/common.scss */
.sideMenu .listArea .listGroup .helpWrap .row {
	width: 100%;
	margin-top: 0;
	display: block;
}

/* line 181, scss/common.scss */
.sideMenu .listArea .listGroup .helpWrap .row:first-child {
	margin-bottom: 5px;
}

/* line 185, scss/common.scss */
.sideMenu .listArea .listGroup .helpWrap span.tooltip {
	display: inline-block;
	width: 20px;
	height: 20px;
	background: url("../images/common/ico-tooltip.png");
	border: 0;
	vertical-align: bottom;
	font-size: 13px;
	font-family: 'Noto Sans';
}

/* line 187, scss/common.scss */
.sideMenu .listArea .listGroup .helpWrap span.tooltip.strong {
	margin-bottom: 10px;
	padding: 0;
}

/* line 188, scss/common.scss */
.sideMenu .listArea .listGroup .helpWrap span.tooltip.strong .desc {
	bottom: 39px;
	line-height: 20px;
}

/* line 190, scss/common.scss */
.sideMenu .listArea .listGroup .helpWrap span.tooltip:hover {
	width: 20px;
	height: 20px;
	background: url("../images/common/ico-tooltip.png");
	border: 0;
}

/* line 191, scss/common.scss */
.sideMenu .listArea .listGroup .helpWrap span.tooltip:hover .desc {
	display: block;
}

/* line 192, scss/common.scss */
.sideMenu .listArea .listGroup .helpWrap span.tooltip:hover:after {
	display: block;
	margin-left: 5px;
	margin-top: -16px;
	content: '';
	width: 0;
	height: 0;
	transform: rotate(45deg);
	border-top: 10px solid transparent;
	border-right: 10px solid #626262;
}

/* line 195, scss/common.scss */
.sideMenu .listArea .listGroup .helpWrap span.tooltip .desc {
	display: none;
	width: 278px;
	left: 5px;
	bottom: 29px;
	top: auto;
	padding: 5px;
	text-indent: 0;
}

/* line 196, scss/common.scss */
.sideMenu .listArea .listGroup .helpWrap span.tooltip .desc:after {
	display: none;
}

/* line 200, scss/common.scss */
.sideMenu .listArea .listGroup .helpWrap a {
	display: block;
	line-height: 38px;
	font-size: 12px;
	color: #888888;
	font-family: 'Noto Sans';
	border-left: 2px solid transparent;
}

/* line 202, scss/common.scss */
.sideMenu .listArea .listGroup .helpWrap a:hover, .sideMenu .listArea .listGroup .helpWrap a.active {
	color: #393939;
	background: #ffffff;
	border-left: 2px solid #393939;
}

/* line 203, scss/common.scss */
.sideMenu .listArea .listGroup .helpWrap a:hover:before, .sideMenu .listArea .listGroup .helpWrap a.active:before {
	background: #393939;
}

/* line 205, scss/common.scss */
.sideMenu .listArea .listGroup .helpWrap a:before {
	display: inline-block;
	content: '';
	width: 4px;
	height: 4px;
	margin-right: 10px;
	margin-bottom: 3px;
	border-radius: 50%;
	background: #888888;
}

/* line 206, scss/common.scss */
.sideMenu .listArea .listGroup .helpWrap a:hover + .desc {
	display: block;
}

/* line 208, scss/common.scss */
.sideMenu .listArea .listGroup .helpWrap .desc {
	display: none;
	position: absolute;
	left: 10px;
	bottom: 40px;
	z-index: 100;
	width: 279px;
	color: #fff;
	background: #626262;
	border-radius: 5px;
}

/* line 209, scss/common.scss */
.sideMenu .listArea .listGroup .helpWrap .desc p {
	padding: 15px;
	font-size: 12px;
	text-indent: 0;
}

/* line 210, scss/common.scss */
.sideMenu .listArea .listGroup .helpWrap .desc:after {
	position: absolute;
	bottom: -5px;
	left: 20px;
	display: block;
	content: '';
	width: 0;
	height: 0;
	transform: rotate(45deg);
	border-top: 10px solid transparent;
	border-right: 10px solid #626262;
}

/* line 214, scss/common.scss */
.sideMenu .listArea .listGroup .btnToggle {
	position: absolute;
	right: 10px;
	top: 18px;
	width: 14px;
	height: 8px;
	background: url("../images/common/ico-down.png");
}

/* line 215, scss/common.scss */
.sideMenu .listArea .listGroup:first-child {
	margin-top: 0;
}

/* line 216, scss/common.scss */
.sideMenu .listArea .listGroup .titList {
	position: relative;
	display: inline-block;
	height: 42px;
	line-height: 42px;
	font-family: 'Noto Sans Medium';
	color: #121619;
	font-size: 16px;
	text-indent: 0;
}

/* line 217, scss/common.scss */
.sideMenu .listArea .listGroup .menuList {
	padding: 0;
}

/* line 218, scss/common.scss */
.sideMenu .listArea .listGroup .menuList li {
	position: relative;
}

/* line 219, scss/common.scss */
.sideMenu .listArea .listGroup .menuList li a {
	display: block;
	line-height: 38px;
	font-size: 12px;
	color: #888888;
	font-family: 'Noto Sans';
	border-left: 2px solid transparent;
}

/* line 221, scss/common.scss */
.sideMenu .listArea .listGroup .menuList li a:hover, .sideMenu .listArea .listGroup .menuList li a.active {
	color: #393939;
	background: #ffffff;
	box-shadow:0px 3px 4px rgb(0 0 0 / 15%);
}

/* line 222, scss/common.scss */
.sideMenu .listArea .listGroup .menuList li a:hover:before, .sideMenu .listArea .listGroup .menuList li a.active:before {
	background: #393939;
}

/* line 224, scss/common.scss */
.sideMenu .listArea .listGroup .menuList li a:before {
	display: inline-block;
	content: '';
	width: 4px;
	height: 4px;
	margin-right: 10px;
	margin-bottom: 3px;
	border-radius: 50%;
	background: #888888;
}

/* line 225, scss/common.scss */
.sideMenu .listArea .listGroup .menuList li a:hover + .desc {
	display: block;
}

/* line 227, scss/common.scss */
.sideMenu .listArea .listGroup .menuList li .desc {
	display: none;
	position: absolute;
	left: 10px;
	top: 40px;
	z-index: 100;
	width: 279px;
	color: #fff;
	background: #626262;
	border-radius: 5px;
}

/* line 228, scss/common.scss */
.sideMenu .listArea .listGroup .menuList li .desc p {
	padding: 15px;
	font-size: 12px;
	text-indent: 0;
}

/* line 229, scss/common.scss */
.sideMenu .listArea .listGroup .menuList li .desc:after {
	position: absolute;
	top: -3px;
	left: 20px;
	display: block;
	content: '';
	width: 0;
	height: 0;
	transform: rotate(-135deg);
	border-top: 10px solid transparent;
	border-right: 10px solid #626262;
}

/* line 231, scss/common.scss */
.sideMenu .listArea .listGroup .menuList li .desc.upper {
	display: none;
	position: absolute;
	left: 10px;
	bottom: 40px;
	top: auto;
	z-index: 100;
	width: 279px;
	color: #fff;
	background: #626262;
	border-radius: 5px;
}

/* line 232, scss/common.scss */
.sideMenu .listArea .listGroup .menuList li .desc.upper p {
	padding: 15px;
	font-size: 12px;
	text-indent: 0;
}

/* line 233, scss/common.scss */
.sideMenu .listArea .listGroup .menuList li .desc.upper:after {
	position: absolute;
	bottom: -3px;
	top: auto;
	left: 20px;
	display: block;
	content: '';
	width: 0;
	height: 0;
	transform: rotate(45deg);
	border-top: 10px solid transparent;
	border-right: 10px solid #626262;
}

/* line 240, scss/common.scss */
.sideMenu .listArea.type2 {
	text-indent: 16px;
}

/* line 241, scss/common.scss */
.sideMenu .listArea.type2 .greyType {
	padding: 5px 8px;
}

/* line 242, scss/common.scss */
.sideMenu .listArea.type2 .greyType:hover {
	background: #376fee;
}

/* line 244, scss/common.scss */
.sideMenu .listArea.type2 .listGroup {
	margin: 13px 4px 0;
	padding-bottom: 19px;
	padding-right: 8px;
	border-bottom: 1px solid #888888;
}

/* line 245, scss/common.scss */
.sideMenu .listArea.type2 .listGroup:last-child {
	border-bottom: 0;
}

/* line 246, scss/common.scss */
.sideMenu .listArea.type2 .listGroup .inputArea {
	padding: 0 20px;
}

/* line 247, scss/common.scss */
.sideMenu .listArea.type2 .listGroup .inputArea .col {
	float: left;
	width: 30%;
	margin-right: 3%;
	text-indent: 0;
}

/* line 249, scss/common.scss */
.sideMenu .listArea.type2 .listGroup .inputArea .col input[type="text"], .sideMenu .listArea.type2 .listGroup .inputArea .col input[type="number"] {
	width: 100% !important;
}

/* line 252, scss/common.scss */
.sideMenu .listArea.type2 .listGroup .titList {
	display: block;
	text-indent: 15px;
	cursor: pointer;
	background: url("../images/common/ico-down.png") no-repeat right 10px center;
}

/* line 253, scss/common.scss */
.sideMenu .listArea.type2 .listGroup .titList.active {
	background: url("../images/common/ico-up.png") no-repeat right 10px center;
}

/* line 255, scss/common.scss */
.sideMenu .listArea.type2 .listGroup .whiteType {
	width: 106px;
	height: 21px;
	font-size: 12px;
	color: #888888;
	letter-spacing: -1px;
	border-radius: 0;
}

/* line 256, scss/common.scss */
.sideMenu .listArea.type2 .listGroup .whiteType:hover {
	color: #376fee;
}

/* line 258, scss/common.scss */
.sideMenu .listArea.type2 .listGroup .checkbox {
	display: inline-block;
	text-indent: 0;
}

/* line 259, scss/common.scss */
.sideMenu .listArea.type2 .listGroup label {
	display: inline-block;
	text-indent: 0;
	color: #888888;
	font-size: 12px;
	font-family: 'Noto Sans';
}

/* line 260, scss/common.scss */
.sideMenu .listArea.type2 .listGroup .menuList.type2 {
	display: none;
}

/* line 262, scss/common.scss */
.sideMenu .listArea.type2 .listGroup .menuList.type2.styleChangeOption li {
	margin-bottom: 15px;
}

/* line 264, scss/common.scss */
.sideMenu .listArea.type2 .listGroup .menuList.type2 li {
	margin-bottom: 5px;
}

/* line 265, scss/common.scss */
.sideMenu .listArea.type2 .listGroup .menuList.type2 li .auto {
	width: 86px;
}

/* line 266, scss/common.scss */
.sideMenu .listArea.type2 .listGroup .menuList.type2 li .auto.type2 {
	width: 30px;
}

/* line 267, scss/common.scss */
.sideMenu .listArea.type2 .listGroup .menuList.type2 li .auto.type3 {
	width: 125px;
}

/* line 269, scss/common.scss */
.sideMenu .listArea.type2 .listGroup .menuList.type2 li .colGroup {
	display: inline-block;
	width: 210px;
	vertical-align: middle;
	text-indent: 0;
}

/* line 270, scss/common.scss */
.sideMenu .listArea.type2 .listGroup .menuList.type2 li .colGroup:after {
	content: "";
	display: block;
	clear: both;
}

/* line 271, scss/common.scss */
.sideMenu .listArea.type2 .listGroup .menuList.type2 li .colGroup .col {
	float: left;
	width: 30%;
	margin-right: 3.3%;
}

/* line 272, scss/common.scss */
.sideMenu .listArea.type2 .listGroup .menuList.type2 li .colGroup .col:last-child {
	margin-right: 0;
}

/* line 273, scss/common.scss */
.sideMenu .listArea.type2 .listGroup .menuList.type2 li .colGroup .col input[type="text"], .sideMenu .listArea.type2 .listGroup .menuList.type2 li .colGroup .col input[type="number"], .sideMenu .listArea.type2 .listGroup .menuList.type2 li .colGroup .col select {
	width: 100%;
}

/* line 276, scss/common.scss */
.sideMenu .listArea.type2 .listGroup .menuList.type2 li dl {
	margin-top: 10px;
}

/* line 277, scss/common.scss */
.sideMenu .listArea.type2 .listGroup .menuList.type2 li:last-child {
	margin-bottom: 0;
}

/* line 278, scss/common.scss */
.sideMenu .listArea.type2 .listGroup .menuList.type2 li select {
	display: inline-block;
	width: 165px;
	height: 21px;
	font-size: 12px;
	color: #a8a8a8;
	background-image: url("../images/common/ico-down-type2.png");
	background-position: right 7px center;
}

/* line 280, scss/common.scss */
.sideMenu .listArea.type2 .listGroup .menuList.type2 li select#layerType {
	display: none;
	width: 80px;
}

/* line 281, scss/common.scss */
.sideMenu .listArea.type2 .listGroup .menuList.type2 li select#serverType {
	width: 165px;
}

/* line 282, scss/common.scss */
.sideMenu .listArea.type2 .listGroup .menuList.type2 li select#serverType.active {
	width: 83px;
}

/* line 285, scss/common.scss */
.sideMenu .listArea.type2 .listGroup .menuList.type2 li .option {
	display: block;
	font-size: 12px;
	color: #a8a8a8;
	font-family: 'Noto Sans';
}

/* line 286, scss/common.scss */
.sideMenu .listArea.type2 .listGroup .menuList.type2 li input[type="text"], .sideMenu .listArea.type2 .listGroup .menuList.type2 li input[type="number"] {
	display: inline-block;
	width: 165px;
	height: 21px;
	font-size: 12px;
	color: #a8a8a8;
	box-sizing: border-box;
}

/* line 288, scss/common.scss */
.sideMenu .listArea.type2 .listGroup .menuList.type2 li input[type="text"]#textPlacement, .sideMenu .listArea.type2 .listGroup .menuList.type2 li input[type="number"]#textPlacement {
	width: 125px;
}

/* line 292, scss/common.scss */
.sideMenu .listArea.type2 .listGroup .radioGroup {
	display: inline-block;
	width: 120px;
}

/* line 293, scss/common.scss */
.sideMenu .listArea.type2 .listGroup .radioGroup .radio {
	display: inline-block;
	text-indent: 0;
	margin-right: 10px;
}

/* line 294, scss/common.scss */
.sideMenu .listArea.type2 .listGroup .radioGroup .radio:last-child {
	margin-right: 0;
}

/* content */
/* line 302, scss/common.scss */
#content {
	position: absolute;
	left: 400px;
	top: 0;
	width: calc(100% - 400px);
	height: calc(100% - 50px);
	background: #fff;
}

/* line 303, scss/common.scss */
#content .btnGroup {
	position: fixed;
	right: 100px;
	/* left: 1560px; */
	bottom: 200px;
	z-index: 100;
	
	/* 맨위, 맨아래로 */
}

/* line 305, scss/common.scss */
#content .btnGroup button {
	display: block;
	width: 21px;
	height: 21px;
	margin: 3px 0;
}

/* line 306, scss/common.scss */
#content .btnGroup .btnBottom {
	background: url("../images/common/btn-bottom.png");
}

/* line 307, scss/common.scss */
#content .btnGroup .btnTop {
	background: url("../images/common/btn-top.png");
}

/* line 310, scss/common.scss */
#content .innerContent {
	width: 1100px;
	padding: 60px 30px 100px;
}

/* line 311, scss/common.scss */
#content .innerContent section {
	position: relative;
	margin-bottom: 50px;
}

/* line 312, scss/common.scss */
#content .innerContent section .posBtn {
	position: absolute;
	right: 0;
	top: 0;
}

/* line 313, scss/common.scss */
#content .innerContent section .posBtn button {
	width: 112px;
	height: 38px;
}

/* line 319, scss/common.scss */
h3.titSec {
	padding-left: 30px;
	color: #333333;
	font-size: 24px;
	text-indent : 14px;
	font-family: 'Noto Sans Medium';
	font-weight: normal;
	background: url("../images/common/ico-main.png") no-repeat left 10px;
}

/* line 321, scss/common.scss */
h4.titSub {
	margin-left: 30px;
	padding-left: 14px;
	color: #393939;
	font-size:16px;
	text-indent : 14px;
	font-family: 'Noto Sans';
	font-weight: normal;
	background: url("../images/common/ico-sub.png") no-repeat left 8px;
}

/* line 323, scss/common.scss */
.detailSec {
	padding: 15px;
	color: #393939;
	font-size: 18px;
	border-radius:5px;
	font-family:'Noto Sans Medium';
	border:1px solid #ccc;
	background:#f4f4f4;
}

/* line 325, scss/common.scss */
.row {
	margin-top: 30px;
}

/* line 327, scss/common.scss */
.textArea strong {
	display: block;
	padding-left: 45px;
	color: #393939;
	font-weight: normal;
	font-size: 16px;
	font-family: 'Noto Sans Medium';
}

/* line 328, scss/common.scss */
.textArea p {
	padding: 10px 0 10px 60px;
	font-weight: normal;
	color:#444;
	font-size:14px;
	font-family:'Malgun Gothic'
}

/* line 329, scss/common.scss */
.textArea p.desc {
	padding-left: 45px;
}

.textArea p>a{
font-family:'Noto Sans Medium';
}

/* line 333, scss/common.scss */
.codeArea {
	padding-left: 45px;
}

/* line 334, scss/common.scss */
.codeArea.large {
	padding-left: 0;
}

/* line 335, scss/common.scss */
.codeArea .codeBox {
	width: 100%;
	height: 100%;
	background: #444;
	color: #fff;
}

/* line 336, scss/common.scss */
.codeArea .codeBox p {
	color: #fff;
	padding: 10px;
}

/* line 337, scss/common.scss */
.codeArea .codeBox.small {
	height: 25px;
}

/* line 340, scss/common.scss */
.logChk {
	width: 100%;
	resize: none;
	height: 155px;
	border: 1px solid #393939;
	border-radius: 3px;
}

/* line 341, scss/common.scss */
.logChk pre {
	margin: 0;
	padding: 10px;
}

/* line 343, scss/common.scss */
.btnLogArea {
	margin-top: 5px;
	border: 1px solid #376fee;
	background: #ffffff;
	border-radius: 3px;
}

/* line 344, scss/common.scss */
.btnLogArea .innerBox {
	padding: 10px;
}

/* line 345, scss/common.scss */
.btnLogArea .innerBox .btnLogReset {
	padding: 9px 18px;
	background: #fff;
	border: 1px solid #cbced2;
	color: #585858;
	border-radius: 3px;
	font-family: '맑은 고딕';
	font-weight: bold;
	font-size: 14px;
}

/* line 346, scss/common.scss */
.btnLogArea .innerBox .btnLogReset:hover {
	color: #fff;
	background: #376fee;
	border: 1px solid transparent;
}

/* line 349, scss/common.scss */
.btnLogArea .innerBox .btnList strong {
	color: #121619;
	font-size: 14px;
	font-family: '맑은 고딕';
	font-weight: bold;
	margin-right: 10px;
}

/* line 350, scss/common.scss */
.btnLogArea .innerBox .btnList .btnLog {
	margin: 0 5px 5px 0;
	padding: 3px 23px;
	background: #fff;
	border: 1px solid #cbced2;
	color: #585858;
	border-radius: 3px;
	font-family: '맑은 고딕';
	font-weight: bold;
	font-size: 14px;
}

/* line 351, scss/common.scss */
.btnLogArea .innerBox .btnList .btnLog:hover, .btnLogArea .innerBox .btnList .btnLog.active {
	color: #fff;
	background: #376fee;
	border: 1px solid transparent;
}

/* line 356, scss/common.scss */
.slideBox {
	margin-bottom: 4px;
}

/* line 358, scss/common.scss */
.slideBox.get .boxWrap {
	background-color: #eff7ff;
}

/* line 359, scss/common.scss */
.slideBox.get > a span.type {
	background: #61affe;
}

/* line 362, scss/common.scss */
.slideBox.delete .boxWrap {
	background-color: #feebeb;
}

/* line 363, scss/common.scss */
.slideBox.delete > a span.type {
	background: #f93e3e;
}

/* line 366, scss/common.scss */
.slideBox.put .boxWrap {
	background-color: #fff5ea;
}

/* line 367, scss/common.scss */
.slideBox.put > a span.type {
	background: #fca130;
}

/* line 370, scss/common.scss */
.slideBox.post .boxWrap {
	background-color: #d7f4e7;
}

/* line 371, scss/common.scss */
.slideBox.post > a span.type {
	background: #005590;
}

/* line 374, scss/common.scss */
.slideBox .boxWrap {
	padding: 3px;
	border: 1px solid #dee1e7;
	background: #eef3fa url("../images/common/ico-down.png") no-repeat right 24px center;
}

/* line 375, scss/common.scss */
.slideBox > a {
	display: block;
	line-height: 42px;
	color: #393939;
	font-size: 14px;
	font-family: 'Noto Sans';
}

/* line 376, scss/common.scss */
.slideBox > a.active .boxWrap {
	background-image: url("../images/common/ico-up.png");
}

/* line 377, scss/common.scss */
.slideBox > a span.type {
	display: inline-block;
	width: 108px;
	height: 42px;
	background: #376fee;
	border: 3px;
	color: #fff;
	text-align: center;
}

/* line 378, scss/common.scss */
.slideBox > a .local {
	display: inline-block;
	padding-left: 14px;
}

/* line 380, scss/common.scss */
.slideBox .boxCont {
	width: 100%;
	border: 1px solid #376fee;
	box-shadow: 0 0 4px #376fee;
	box-sizing: border-box;
}

/* line 381, scss/common.scss */
.slideBox .boxCont .row {
	position: relative;
	margin: 0;
	padding: 15px 0 15px 20px;
	border-bottom: 1px solid #e5e7ec;
}

/* line 382, scss/common.scss */
.slideBox .boxCont .row .btnReset {
	position: absolute;
	right: 20px;
	top: 15px;
	padding-left: 23px;
	color: #393939;
	font-family: 'Noto Sans';
	font-size: 14px;
	font-weight: normal;
	background: url("../images/common/ico-reset-type2.png") no-repeat left center;
}

/* line 384, scss/common.scss */
.slideBox .boxCont .row .btnReset:hover {
	color: #376fee;
	background: url("../images/common/ico-reset-type2-hover.png") no-repeat left center;
}

/* line 386, scss/common.scss */
.slideBox .boxCont .row.greyType {
	background: #f6f6f7;
}

/* line 387, scss/common.scss */
.slideBox .boxCont .row strong {
	color: #393939;
	font-family: 'Noto Sans';
	font-size: 14px;
}

/* line 388, scss/common.scss */
.slideBox .boxCont .row i {
	display: inline-block;
	margin: 10px 0;
}

/* line 389, scss/common.scss */
.slideBox .boxCont .row .whiteType {
	position: absolute;
	right: 13px;
	top: 6px;
	width: 113px;
	height: 38px;
}

/* line 390, scss/common.scss */
.slideBox .boxCont .row .table {
	padding-left: 30px;
	margin-bottom: 20px;
}

/* line 392, scss/common.scss */
.slideBox .boxCont .row .table td .codeArea {
	padding-right: 0;
}

/* line 395, scss/common.scss */
.slideBox .boxCont .row input[type="text"], .slideBox .boxCont .row input[type="number"] {
	width: 277px;
	height: 36px;
	padding: 0 10px;
	border: 1px solid #dbdfe5;
	color: #393939;
}

/* line 396, scss/common.scss */
.slideBox .boxCont .row .btnArea {
	width: 826px;
	margin: 130px 0 7px;
}

/* line 397, scss/common.scss */
.slideBox .boxCont .row .btnArea .whiteType2 {
	float: left;
	width: 50%;
	height: 42px;
	font-size: 17px;
	font-family: 'Noto Sans Medium';
	font-weight: normal;
	color: #686868;
}

/* line 398, scss/common.scss */
.slideBox .boxCont .row .btnArea .whiteType2:hover, .slideBox .boxCont .row .btnArea .whiteType2.active {
	color: #fff;
	background: #376fee;
	border: 0;
	box-shadow: 0 1px 4px #888;
}

/* line 400, scss/common.scss */
.slideBox .boxCont .row .btnArea.java {
	margin: 19px 0 12px 0;
}

/* line 401, scss/common.scss */
.slideBox .boxCont .row .btnArea.java button {
	width: 88px;
	height: 32px;
	font-weight: normal;
}

/* line 404, scss/common.scss */
.slideBox .boxCont .row .inputArea {
	position: absolute;
	right: 13px;
	top: 6px;
}

/* line 405, scss/common.scss */
.slideBox .boxCont .row .inputArea label {
	font-size: 12px;
	font-family: 'Noto Sans';
	color: #393939;
	margin-right: 5px;
}

/* line 406, scss/common.scss */
.slideBox .boxCont .row .inputArea select {
	display: inline-block;
	width: 222px;
}

/* line 408, scss/common.scss */
.slideBox .boxCont .row .obj {
	margin-bottom: 15px;
}

/* line 409, scss/common.scss */
.slideBox .boxCont .row .status {
	font-size: 14px;
}

/* line 410, scss/common.scss */
.slideBox .boxCont .row .titSm {
	display: block;
	margin: 3px 0 5px;
	font-size: 12px;
	font-family: "Noto Sans";
	color: #393939;
}

/* line 411, scss/common.scss */
.slideBox .boxCont .row .titSm .blue {
	color: #3673e2;
}

/* line 412, scss/common.scss */
.slideBox .boxCont .row .titSm .grey {
	color: #cecece;
	padding: 0 3px;
}

/* line 414, scss/common.scss */
.slideBox .boxCont .row .codeArea {
	padding: 0 13px 0 0;
}

/* line 415, scss/common.scss */
.slideBox .boxCont .row .codeArea .codeBox {
	border-radius: 3px;
}

/* line 416, scss/common.scss */
.slideBox .boxCont .row .codeArea .codeBox.grey {
	background: #f6f6f7;
}

/* line 417, scss/common.scss */
.slideBox .boxCont .row .codeArea .codeBox.small p {
	padding: 2px 15px;
}

/* line 418, scss/common.scss */
.slideBox .boxCont .row .codeArea .codeBox p {
	padding: 15px;
	font-size: 12px;
	font-family: 'Noto Sans';
}

/* line 423, scss/common.scss */
.slideBox .boxCont.lang .btnArea {
	margin: 20px 0 4px;
	text-align: center;
}

/* line 424, scss/common.scss */
.slideBox .boxCont.lang .btnArea button {
	width: 87px;
	height: 30px;
}

/* line 425, scss/common.scss */
.slideBox .boxCont.lang .btnArea button:hover, .slideBox .boxCont.lang .btnArea button.active {
	background: #393939;
}

/* line 428, scss/common.scss */
.slideBox .boxCont.lang .colGroup {
	height: 387px;
}

/* line 429, scss/common.scss */
.slideBox .boxCont.lang .col {
	display: none;
	float: left;
	height: 100%;
}

/* line 430, scss/common.scss */
.slideBox .boxCont.lang .col.col3 {
	width: 100%;
}

/* line 431, scss/common.scss */
.slideBox .boxCont.lang .col.col2 {
	width: 50%;
}

/* line 432, scss/common.scss */
.slideBox .boxCont.lang .col.col1 {
	width: 33.3%;
}

/* line 433, scss/common.scss */
.slideBox .boxCont.lang .col strong {
	display: block;
	text-align: center;
	background: #393939;
	color: #fff;
}

/* line 434, scss/common.scss */
.slideBox .boxCont.lang .col .box {
	width: 100%;
	height: calc(100% - 19px);
	border-right: 1px solid #393939;
	overflow: auto;
}

/* line 435, scss/common.scss */
.slideBox .boxCont.lang .col .box p {
	padding: 10px;
}

/* line 438, scss/common.scss */
.slideBox .boxCont.lang .col:last-child .box {
	border-right: 0;
}

/* line 444, scss/common.scss */
.large {
	font-size: 14px;
}

/* line 445, scss/common.scss */
.btnArea {
	width: 100%;
}

/* line 447, scss/common.scss */
.table table {
	width: 100%;
	font-family: "Noto Sans";
	color: #393939;
	font-size: 12px;
}

/* line 448, scss/common.scss */
.table table tr {
	border-bottom: 1px solid #e5e7ec;
}

/* line 449, scss/common.scss */
.table table tr:last-child {
	border-bottom: 0;
}

/* line 451, scss/common.scss */
.table table th, .table table td {
	vertical-align: top;
	text-align: left;
	padding-top: 12px;
	padding-right: 13px;
}

/* line 452, scss/common.scss */
.table table th.pdTop, .table table td.pdTop {
	padding-top: 37px;
}

/* line 456, scss/common.scss */
.indent {
	margin-left: 45px;
	margin-bottom: 8px;
	font-size: 12px;
	font-family: 'Noto Sans';
	color: #393939;
}

/* line 457, scss/common.scss */
button {
	color: #585858;
	font-family: '맑은 고딕';
	font-weight: bold;
	border-radius: 3px;
}

/* line 458, scss/common.scss */
button.whiteType {
	background: #fff;
	border: 1px solid #cbced2;
}

/* line 459, scss/common.scss */
button.whiteType:hover {
	color: #376fee;
	border: 1px solid #3673e2;
	box-shadow: 0 0 3px #376fee;
}

/* line 461, scss/common.scss */
button.whiteType2 {
	background: #fff;
	border: 1px solid #cbced2;
}

/* line 462, scss/common.scss */
button.whiteType2:hover, button.whiteType2.active {
	color: #fff;
	background: #376fee;
	border: 0;
}

/* line 464, scss/common.scss */
button.blueType {
	color: #fff;
	background: #3673e2;
}

/* line 465, scss/common.scss */
button.greyType {
	color: #fff;
	background: #bdc0c5;
}

/* line 466, scss/common.scss */
button.btnSample {
	width: 109px;
	height: 36px;
	color: #4067de;
	border: 1px solid #4067de;
	border-radius: 0;
}

/* line 467, scss/common.scss */
button.btnParam {
	height: 30px;
	padding: 0 8px;
	margin-right: 5px;
	color: #393939;
	font-size: 14px;
	font-family: 'Noto Sans';
	background: #e8e8e8;
	border: 1px solid #bdc0c5;
}

/* line 471, scss/common.scss */
.mapArea {
	width: 100%;
	margin-top: 5px;
}

/* line 472, scss/common.scss */
.mapArea img {
	width: 100%;
	height: 100%;
}

/* line 474, scss/common.scss */
.strong {
	display: inline-block;
	border-radius: 3px;
	font-weight: normal;
	padding:2px 6px;
	color:#000;
	border:0;
	background:#DCDCDC;
	font-family:'Noto Sans';
}

/* line 475, scss/common.scss */
.link {
	color: #007fff;
	text-decoration: underline;
}

/* 스크롤 */
/* line 478, scss/common.scss */
.mScroll .mCSB_scrollTools {
	width: 8px;
}

/* line 479, scss/common.scss */
.mScroll .mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar {
	width: 8px;
	background: #99a0a6;
}

/* line 480, scss/common.scss */
.mScroll .mCSB_scrollTools .mCSB_draggerRail {
	background: none;
}

/* line 482, scss/common.scss */
.mScroll .mCSB_inside > .mCSB_container {
	margin-right: 0;
}

/* line 483, scss/common.scss */
.mScroll .mCSB_outside + .mCSB_scrollTools {
	right: 0;
}

/* line 486, scss/common.scss */
.popup {
	position: fixed;
	left: 100px;
	display: none;
	top: 100px;
	z-index: 100;
	width: 860px;
	background: #fff;
}

/* line 487, scss/common.scss */
.popup .btnPopupClose {
	position: absolute;
	right: 10px;
	top: 10px;
	width: 20px;
	height: 20px;
	background: url("../images/common/btn-popup-close.png");
	background-size: cover;
}

/* line 488, scss/common.scss */
.popup .controlArea {
	padding: 5px 10px;
}

/* line 489, scss/common.scss */
.popup .controlArea button {
	padding: 5px 5px 5px 25px;
	color: #585858;
	font-size: 14px;
	font-family: '맑은 고딕';
	font-weight: bold;
}

/* line 490, scss/common.scss */
.popup .controlArea .btnReset {
	background: url("../images/common/ico-reset-type2.png") no-repeat left center;
}

/* line 491, scss/common.scss */
.popup .controlArea .btnReset:hover {
	color: #376fee;
	background: url("../images/common/ico-reset-type2-hover.png") no-repeat left center;
}

/* line 493, scss/common.scss */
.popup .controlArea .btnAutoPlay {
	padding-left: 20px;
	border: 1px solid #dee1e7;
	background: #fff url("../images/common/btn-autoplay.png") no-repeat 5px center;
}

/* line 494, scss/common.scss */
.popup .controlArea .btnAutoPlay.active {
	color: #fff;
	background: #376fee url("../images/common/btn-autoplay-hover.png") no-repeat 5px center;
}

/* line 496, scss/common.scss */
.popup .controlArea .btnPlay {
	padding-left: 5px;
	margin-right: 4px;
	border: 1px solid #dee1e7;
}

/* line 497, scss/common.scss */
.popup .controlArea .btnPlay:hover {
	color: #fff;
	background: #376fee;
}

/* Footer */
/* line 503, scss/common.scss */
#footer {
	position: fixed;
	left: 0;
	bottom: 0;
	z-index: 10;
	width: 100%;
	height: 50px;
	min-width: 1782px;
	padding-top: 17px;
	background: #4b535f;
}

/* line 504, scss/common.scss */
#footer:after {
	content: "";
	display: block;
	clear: both;
}

/* line 505, scss/common.scss */
#footer address {
	display: inline-block;
	margin-right: 20px;
}

/* line 506, scss/common.scss */
#footer a {
	margin-right: 20px;
}

/* line 507, scss/common.scss */
#footer .logo {
	float: left;
	margin-right: 57px;
}

/* line 508, scss/common.scss */
#footer .logo + div {
	float: left;
	padding: 6px 0 0;
	color: #7f8389;
}

/* line 509, scss/common.scss */
#footer .copyright {
	margin-top: 5px;
	text-align : center;
	color: white;
}
#content .btnSample{
	position: fixed;
    /* left: 1560px; */
    right : 100px;
    top: 120px;
    z-index : 100;
    width: 150px;
}
#content .btnSample .btnLinkSample{
	background: #5f5f5f url(../images/common/ico-source.png)no-repeat center left 5px;
    width: unset;
    height: 23px;
    padding: 0 10px 0 30px;
    color: #fff;
    font-size: 12px;
    font-family: 'Noto Sans Light';
    font-weight: 100;
}
#content .btnSample .btnLinkSample:hover{
background:#3C87FA url(../images/common/ico-source-hover.png)no-repeat center left 5px;
}
ul.sampleListUl>li::after{
	content:'';
	display:block;
	position:absolute;
	width:3px;height:3px;
	background:#fff;
	top:50%;
	transform:translateY(-50%);left:10px;
	}
ul.sampleListUl>li:hover{
	background:#3C87FA
	}
ul.sampleListUl>li {
	background: #5f5f5f;
	padding: 4px 10px 4px 18px;
	font-size: 12px;
	border-radius: 5px;
	font-family: 'Noto Sans Light';
	color: #fff;
	letter-spacing: 0px;
	position: relative;
	cursor : pointer;
	margin-top: 10px;
	}
#helpMenu {
	width: 100%;
	position: absolute;
    bottom: 0;
    margin-bottom:30px;
}	
#helpMenu li{
	display: list-item;
	margin-top: 20px;
}
#helpMenu li span{
	width:90px;
	font-size:15px;
	text-align:center;
	letter-spacing: 0px;
	position:absolute;
	left:50%;
	transform:translateX(-50%);
	bottom:0px;
}	
#helpMenu li button{
	width: 100%;
    height: 70px;
    color: #333;
    background-repeat: no-repeat;
    position: relative;
    background-position: center top 10px;
}
#helpDocsBtn {
	background-image: url(../images/common/ico-header-help.png);

}
#versionInfoBtn{
	background-image: url(../images/common/ico-header-version.png);
}
#helpDocsBtn:hover {
	background-image: url(../images/common/ico-header-help_on.png);

}
#versionInfoBtn:hover{
	background-image: url(../images/common/ico-header-version_on.png);
}
/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
