<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
</head>
<body>
	<div id="map" class="odf-view" style="height:550px;"></div>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::,::coordy::);
	var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);


	/*포인트 레이어 추가*/
	var pointLayer = odf.LayerFactory.produce('geoserver', {
		method: 'get',
		method: 'get',
		server: '::WfsAPI::',
		layer: '::pointLayer::',
		service: 'wfs',
	});
	pointLayer.setMap(map);
	map.setZoom(16);

	/*막대 차트 스타일 스타일 생성*/
	var chartStyle = odf.StyleFactory.produceFunction([
		{
			seperatorFunc: 'default',
			style: {
				//차트 위치 조정
				geometry: (feature) => {
					var geom = feature.getGeometry().clone();
					var coordinates = geom.getCoordinates();
					coordinates[1] += (map.getView().getResolution() * 100) / 2;
					geom.setCoordinates(coordinates);
					return geom;
				},
				image: {
					chart: {
						type: 'bar',
						datas: ['X', 'Y', 'id'],//칼럼 속성명
						stroke: {
							color: '#000000',
							width: 2,
						},
						//막대차트에서 사용할 색상
						//colors : 'pale' , // 'classic', 'dark', 'pale', 'pastel', 'neon'
						//아래와같이 직접 정의하여 사용할 수도 있음
						colors: ['#FF4B4B', '#FF7272', '#FF9999', '#FFC0C0', '#FFE7E7'],
						barWidth: 20,//막대 개별 너비. 기본값 10
						barBufferSize: 5, //막대 간의 간격. 기본값 0
						barMaxHegiht: 100,//최대 막대 높이 . 기본값 50
						barMinHegiht: 1,//최소 막대 높이. 기본값 1
						rotation: Math.PI * 90 / 180//기울기
					}
				},
			},
			callbackFunc: function (style, feature, resolution) {
				var datas = style.getImage().getDatas();
				var data = [];
				var max = 0;
				datas.forEach((dKey) => {
					var d = Number(feature.get(dKey));
					if (Number.isNaN(d)) {
						d = 0;
					}
					data.push(d);
					if (max < d) max = d;
				});
				style.getImage().setData(data);

				var s = 0; //누적 합계

				//복합 스타일 형태로 생성
				var styles = [style];

				var barWidth = style.getImage().getBarWidth(); //막대 너비
				var barBufferSize = style.getImage().getBarBufferSize(); //막대 간격
				var strokeWidth = style.getImage().getStroke().getWidth(); //막데 테두리 너비
				var maxHegiht = style.getImage().getBarMaxHegiht(); //최대막대 높이
				var coord = feature.getGeometry().getCoordinates();

				var dLen = datas.length;
				var totWidth = (barWidth + strokeWidth) * dLen + barBufferSize * (dLen - 1);
				var x = -totWidth / 2;
				var y = -totWidth - Math.abs((maxHegiht - totWidth) / 2);


				//값 종류 레이블
				datas.forEach(function (dKey) {
					var d = feature.get(dKey);
					y += barWidth / 2;
					styles.push(
						odf.StyleFactory.produce({
							text: {
								font: 'bold 10px Courier New', //폰트 크기(필수) 및 글씨체(필수), 두께(옵션)
								fill: {
									color: [0, 0, 0, 0.95],
								},
								offsetX: x - Math.abs((maxHegiht - totWidth)),
								offsetY: y,
								textAlign: 'right',
								textBaseline: 'middle',
								text: dKey,
							},
						})
					);

					//데이터 레이블
					styles.push(
						odf.StyleFactory.produce({
							text: {
								font: 'bold 10px Courier New', //폰트 크기(필수) 및 글씨체(필수), 두께(옵션)
								fill: {
									color: [0, 0, 0, 0.95],
								},
								offsetX: x + (d / max) * maxHegiht,
								offsetY: y,
								textAlign: 'left',
								textBaseline: 'middle',
								text: d + '',
							},
						})
					);

					y += barWidth / 2 + barBufferSize;
				});
				feature.setStyle(styles);
			},
		}
	]);
	pointLayer.setStyle(chartStyle);
</script>

</html>
