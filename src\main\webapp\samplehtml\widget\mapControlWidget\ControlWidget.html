<!DOCTYPE HTML>
<html>
<head>
<meta charset="utf-8">
</head>
<link href="::OdfUrl::/odf.css" rel="stylesheet">
<link href="::OuiUrl::/oui.css" rel="stylesheet">
<link href="::SmtUrl::/css/widget.css" rel="stylesheet">
<link href="::SmtUrl::/css/common.css" rel="stylesheet">
<link href="::SmtUrl::/css/widget_custom.css" rel="stylesheet">
<link href="::SmtUrl::/css/common_custom.css" rel="stylesheet">
<link href="::SmtUrl::/css/widgets/basemap.css" rel="stylesheet">


<link href="::SmtUrl::/css/widgets/addressSearch.css" rel="stylesheet">
<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
<script type="text/javascript" src="::OuiUrl::/oui.min.js"></script>

<!--<script type="text/javascript" src="http://dapi.kakao.com/v2/maps/sdk.js?appkey=877e5c7fe4d7dc48bd8a7ca05e85ef68"></script>-->

<body>
<div class="mapContainer1">
<div id ="map" style="height:550px;"></div>
	<div class="location odf_administrativeDistrictSearch_widget" id="location">
	<div id="administrativeDistrictSearchArea" class="administrativeDistrictSearch_location"></div>
	</div>
	<div id="userMenu">
		<!--searchArea-->
		<div id="searchAreaWidget" class="odf_addressSearch_widget"></div>
	</div>
	<!--widget-->
  	<div id="widget">

  	<div class="flex">
		<div class="group active" id="webmapToolGroup1">
			<ul class="dep1">
				<li class="homeWidget" id="homeControlWidget"></li>
				<li class="basemapWidget" id="basemapWidget"></li>
				<li class="drawControlWidget" id="drawControlWidget"></li>
				<li class="printControlWidget" id="printControlWidget"></li>
				<li class="cctvControlWidget" id="cctvControlWidget"></li>
				<li class="downloadControlWidget" id="downloadControlWidget"></li>
				<li class="measureControlWidget" id="measureControlWidget"></li>
				<!-- <li class="overViewMapWidget" id="overViewMapWidget"></li>-->
				<!-- <li class="roadViewWidget" id="roadViewWidget"></li> -->
				<li class="resetWidget" id="clearControlWidget"></li>
			</ul>
		</div>
	</div>
	<div id="overview" style="position:fixed;top:64%;left:214px;"></div>
	<div id="roadview_popup" class="modal_modal modal_open" style="top: 52px; left: 380px; width: 1000px; height: auto; z-index: 99999999;"></div>
</div>
</div>

</body>

<script>
	if (navigator.onLine == false){
		alert("현재 네트워크에 연결되어 있지 않습니다.");
	}
	/* 맵 타겟 */
	var mapContainer = document.getElementById('map');

	/* 맵 중심점 */
	var coord = new odf.Coordinate(::coordx::, ::coordy::);

	/* 맵객체 옵션 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapOption = "::mapOpt::";
	/*
		* 배경지도 종류
		eMapBasic - 바로e맵 일반 지도
		eMapColor - 바로e맵 색각 지도
		eMapLowV - 바로e맵 큰글씨 지도
		eMapWhite - 바로e맵 백지도
		eMapEnglish - 바로e맵 영어 지도
		eMapChinese - 바로e맵 중어 지도
		eMapJapanese - 바로e맵 일어 지도
		eMapWhiteEdu - 바로e맵 교육용 백지도
		eMapAIR - 바로e맵  항공지도

		* 프록시 사용
		proxyURL: 'proxy.jsp' 프록시 설정
	*/

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var map = new odf.Map(mapContainer, mapOption);

	// wfsLayer;
	var wfsLayer = odf.LayerFactory.produce('geoserver', { // 레이어 호출 방법 (ex. geoserver, geojson)
		method : 'get',
		server : '::WfsAPI::', // 레이어가 발행된 서버 주소
		layer : 'smtuser:L100008843', // 발행된 레이어 명칭 (ex. 저장소명:레이어명)
		service : 'wfs', // 호출하고자 하는 레이여 형태(wms, wfs, wmts)
		crtfckey : '::crtfckey::',
	});
	wfsLayer.setMap(map);
	map.setCenter([ 195567.109, 531610.616 ]);
	map.setZoom(13);


	//00. 주소검색 위젯 api 생성
	var addressApi = oui.AddressApi(oui.HttpClient({
		baseURL: '::geocodingAPI::',
	}), {
		projection: '::srid::',
		crtfckey : '::crtfckey::',
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			basicSearch : 3000,
			bldSearch : 3000,
			coordSearch : 3000,
			intSearch : 3000,
			jibunSearch : 3000,
			pnuSearch : 3000,
			poiSearch : 3000,
			roadSearch : 3000,
			roadApiSearch : 3000,
		}
		 */
	});

	//02. 주소검색 위젯 생성
	var addressSearchWidget = new oui.AddressSearchWidget({
		odf,
		target: document.getElementById('searchAreaWidget'),
		options: {
			pagineType: 'countable' // countable(일반페이징), unbounded(스크롤페이징)
			styleObject: {
				image: {
					circle: {
						radius: 10,
						fill: { color: [255, 255, 255, 0.4] },
						stroke: { color: [241, 189, 29, 0.82], width: 2 },
					},
				},
				fill: { color: [255, 255, 255, 0.4] },
				stroke: { color: [241, 189, 29, 0.82], width: 2 },
			},
		},
		api: {
			//01. type01
			//기초구역번호 검색 function
			basicSearch: addressApi.basicSearch,
			//건물명 검색
			bldSearch: addressApi.bldSearch,
			//경위도 좌표 검색 function
			coordSearch: addressApi.coordSearch,
			//통합검색 function
			intSearch: addressApi.intSearch,
			//지번 검색 function
			jibunSearch: addressApi.jibunSearch,
			//PNU 검색 function
			pnuSearch: addressApi.pnuSearch,
			//POI 검색 function
			poiSearch: addressApi.poiSearch,
			//도로명주소 검색 (주소정제 이용) function
			roadSearch: addressApi.roadSearch,
			//행안부 도로명 주소 검색 api 이용 function
			roadApiSearch: addressApi.roadApiSearch,
		}
	});

	//03. 주소검색 위젯을 지도객체와 연결 및 렌더링
	addressSearchWidget.addTo(map);


	//00. 행정구역 검색 api 객체 생성
    var administApi = oui.AdministApi(oui.HttpClient({
		baseURL: '::geocodingAPI::',
	}), {
		projection: '::srid::',
		crtfckey : '::crtfckey::',
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			geometrySearch : 3000,
			ctpvAdministrativeDistrictSearch : 3000,
			sggAdministrativeDistrictSearch : 3000,
			emdAdministrativeDistrictSearch : 3000,
			liAdministrativeDistrictSearch : 3000,
			pnuSearch : 3000,
			poiSearch : 3000,
			roadSearch : 3000,
			roadApiSearch : 3000,
		}
		 */
	});


	//01. 행정구역 검색 위젯 생성
	var administrativeDistrictSearchWidget = new oui.AdministrativeDistrictSearchWidget({
		odf,
		target: document.querySelector('#administrativeDistrictSearchArea'),
		options: {
			useLi: true, //li 사용 여부(default 값 : false, 사용 여부를 true 로 설정해도 리가 존재하지 않는 읍면동 지역은 li 목록선택 영역 부분을 표출하지 않습니다.)
			useHilight: true,//하이라이트 레이어 사용 여부
			clearHilightLayerFlagMove: true,//지도 이동시 검색결과 날리기 활성화
			// styleObject: {// 하이라이트 레이어 스타일. 없으면 기본 스타일 적용
			//   text: {
			//     fill: {
			//       color: "#858484ff",
			//     },
			//     font: "normal bold 16px 굴림",
			//   },
			//   fill: { color: [255, 255, 255, 0.4] },
			//   stroke: { color: [241, 189, 29, 0.82], width: 2 },
			// },
			//알림옵션
			alertList: {
				//사용자 정의 알림 메세지 정의
				customAlert: (message) => {
					console.log(message);
				},
				//사용자 정의 로딩바 시작 function
				startLoadingBar: (message) => {
					console.log(message);
				},
				//사용자 정의 로딩바 종료 function
				endLoadingBar: (message) => {
					console.log(message);
				},
			}
		},
		api: {
			//단건 행정구역 정보 조회  function
			geometrySearch: administApi.geometrySearch,
			//행정구역 유형별 정의
			//시도 목록 조회 function
			ctpvAdministrativeDistrictSearch: administApi.ctpvAdministrativeDistrictSearch,
			//시군구 목록 조회 function
			sggAdministrativeDistrictSearch: administApi.sggAdministrativeDistrictSearch,
			//읍면동 목록 조회 function
			emdAdministrativeDistrictSearch: administApi.emdAdministrativeDistrictSearch,
			//리 목록 조회 function
			liAdministrativeDistrictSearch: administApi.liAdministrativeDistrictSearch,
			//경위도 좌표 검색 fucntion
			coordSearch: addressApi.coordSearch,
		}
	});

	administrativeDistrictSearchWidget.addTo(map);


	var drawControlWidget = new oui.DrawControlWidget({
		target: document.getElementById('drawControlWidget'),
	});

	drawControlWidget.addTo(map);
	var cctvClient = oui.HttpClient({
		baseURL: '::cctvApiUrl::',
	});
	var cctvApi = oui.CCTVApi(cctvClient, {
		apiKey : '::cctvApiKey::',
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			getCCTVData : 3000,
		}
		 */
	});
	var cctvControlWidget = new oui.CCTVControlWidget({
		options : {
			alertList : {
				customAlert: (message) => {
					console.dir(message);
				},
			},
			callback: (cctvLayer) => { //cctvLayer 객체 받는 callback 함수 ();
				let cctvStyle = odf.StyleFactory.produce({
					image: {
						icon: {
							opacity: 1,
							scale: 1,
							src: '/smt/images/widget/CCTV.png',
						}
					}
				});
				cctvLayer.setStyle(cctvStyle);
			},
			//width : '360',
			//height : '180',
		},
		api : {
			getCCTVData : cctvApi.getCCTVData,
		},
		target : document.getElementById('cctvControlWidget'),
	})
	cctvControlWidget.addTo(map);

	var clearControlWidget = new oui.ClearControlWidget({
		target: document.getElementById('clearControlWidget'),
	});
	clearControlWidget.addTo(map)
	var downloadControlWidget = new oui.DownloadControlWidget({
		target: document.getElementById('downloadControlWidget'),
	});
	downloadControlWidget.addTo(map);

	var homeControlWidget = new oui.HomeControlWidget({
		options : {zoom : 14, center : [::coordx::, ::coordy::]},
		target: document.getElementById('homeControlWidget'),
	});
	homeControlWidget.addTo(map);

  	var measureControlWidget = new oui.MeasureControlWidget({
		api: {
			getAddressFromPoi: addressApi.getAddressFromPoi //좌표로 주소값 조회하는 api
		},
		target: document.getElementById('measureControlWidget'),
	});

	measureControlWidget.addTo(map);
	/*
	var overViewMapControlWidget = new oui.OverViewMapControlWidget({
		options: { element: document.getElementById('overview') },
		target: document.getElementById('overViewMapWidget'),
	});

	overViewMapControlWidget.addTo(map)

	var roadViewWidget = new oui.RoadViewWidget({
		options: {
			element: document.getElementById('roadview_popup'),
			alertList: {
				customAlert: (message) => {
					alert(message);
				},
				customErrorAlert: (message) => {
					alert(message);
				}
			},
		},
		target:  document.getElementById('roadViewWidget')
	})
  	roadViewWidget.addTo(map)
	*/

	var basemapApi = oui.BasemapApi(oui.HttpClient({
		baseURL: '::smtAPI::',
	}), {
		crtfckey : '::crtfckey::',
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			getBasemapList : 3000,
		}
		 */
	});

	var basemapWidget = new oui.BasemapWidget({
		odf: odf,
		target: document.querySelector('#basemapWidget'),
		api: {
			// 배경지도 조회 api
			getBasemapList: basemapApi.getBasemapList,
		},
		options: {
			useImage: true,// 이미지 사용여부
			toolboxPosition: 'left',// toolbox 표현 방향
			thema: 'gallary',
			alertList: {
				customAlert: (message) => {
					callAlertMessage(message);
				},
				customErrorAlert: (message) => {
					callAlert('error', message);
				}
			},
			directClose : true,
		},
		// 사용할 배경지도만 필터링
		// filter: (bcrnMapId) => {
		// let tailNumber = Number(bcrnMapId.substring(10));
		// if (tailNumber >= 2 && tailNumber <= 18) {
		// return true;
		// }
		// return false;
		// }
	});

  	basemapWidget.addTo(map)



	/* var columnInfoApi = oui.columnInfoApi(oui.HttpClient({
        baseURL: '::smtAPI::',
    }), {
      	crtfckey : '::crtfckey::',
    });

  	var commonCodeApi = oui.commonCodeApi(oui.HttpClient({
        baseURL: '::smtAPI::',
    }), {
      	crtfckey : '::crtfckey::',
    });

  	var popupWidget = new oui.PopupWidget({
		odf: odf
		, api: {
			// 컬럼정보조회 옵션값 변경
			columnInfoOptionChange: columnInfoApi.changeOption,
			// 별칭 및 컬럼 정보 조회
			columnInfoFunction: columnInfoApi.columnInfoFunction,
			// 공통코드조회
			getCommonCode: commonCodeApi.commonCodeFunction,
			// 상세공통코드 조회 aixos.all
			getAllDetailCode: commonCodeApi.getAllDetailCode,
			getLayerList: (callback) => {
				callback(tocWidget.getLayerList());
			}
		}, options: {
			draggable: true,
			setAddInfoHtml: async (popupInfo) => {
				let dipImgInfo = popupInfo.find(item => item.columnNm == 'image_file');
				if (dipImgInfo != undefined && dipImgInfo.attributeValue != '') {
					// q드라이브에서 조회
					// app.cmm.server.file.getDipImgBase64({fileNm :
					// dipImgInfo.attributeValue}).then(res=>{
					// return `<img src=${res}>`;
					// });
					let dipImgBase64 = await app.cmm.server.file.getDipImgBase64({ fileNm: dipImgInfo.attributeValue });
					return `
					<table>
						<colgroup>
							<col style="width:50%;">
							<col style="width:auto;">
						</colgroup>
						<tbody>
							<tr>
								<th>이미지</th>
								<td><img class="dipImg" src=data:image/gif;base64,${dipImgBase64}></td>
							</tr>
						</body>
					</table>`;
				}
			}
		}
		, map: map
	}); */

</script>
</html>
