<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>GeOnPaas ui widgets: SLD</title>
    
      <link type="text/css" rel="stylesheet" href="styles/vendor/prism-tomorrow-night.css">
    
    <link type="text/css" rel="stylesheet" href="styles/styles.css">
    
    
    <style>
      :root {
      
      
        --nav-width: 370px;
      
      
        --nav-heading-margin-top: 0.5em;
      
      }
    </style>
    
</head>
<body>

<header class="layout-header">
  
  <h1>
    <a href="./index.html">
      GeOnPaas ui widgets
    </a>
  </h1>
  <nav class="layout-nav">
    <ul><li class="nav-heading">Classes</li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="BasemapControl.html">BasemapControl</a></span><span class="nav-desc"><p>배경지도 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="BookmarkControl.html">BookmarkControl</a></span><span class="nav-desc"><p>북마크 컨트롤 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ClearControl.html">ClearControl</a></span><span class="nav-desc"><p>지도 그리기 이벤트 초기화 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ColorFactory.html">ColorFactory</a></span><span class="nav-desc"><p>색 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Control.html">Control</a></span><span class="nav-desc"><p>사용자 정의 컨트롤 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Coordinate.html">Coordinate</a></span><span class="nav-desc"><p>좌표 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DivideMapControl.html">DivideMapControl</a></span><span class="nav-desc"><p>지도 분할 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DownloadControl.html">DownloadControl</a></span><span class="nav-desc"><p>다운로드 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DrawControl.html">DrawControl</a></span><span class="nav-desc"><p>그리기 도구 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Easing.html">Easing</a></span><span class="nav-desc"><p>애니메이션 효과</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="event.html">event</a></span><span class="nav-desc"><p>이벤트 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Extent.html">Extent</a></span><span class="nav-desc"><p>영역 관련 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Feature.html">Feature</a></span><span class="nav-desc"><p>Feature 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FeatureFactory.html">FeatureFactory</a></span><span class="nav-desc"><p>Feature 생성을 위한 FeatureFactory 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FormatFactory.html">FormatFactory</a></span><span class="nav-desc"></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FullScreenControl.html">FullScreenControl</a></span><span class="nav-desc"><p>전체화면 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="HomeControl.html">HomeControl</a></span><span class="nav-desc"><p>홈 이동 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Layer.html">Layer</a></span><span class="nav-desc"><p>레이어 관리 클래스로, 레이어는 odf.LayerFactory를 통해서만 생성가능하다.</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LayerFactory.html">LayerFactory</a></span><span class="nav-desc"><p>레이어 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LayerInfoControl.html">LayerInfoControl</a></span><span class="nav-desc"><p>레이어 정보 조회 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Map.html">Map</a></span><span class="nav-desc"><p>지도 생성, 조작, 컴퍼넌트, 레이어 추가 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Marker.html">Marker</a></span><span class="nav-desc"><p>마커 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MeasureControl.html">MeasureControl</a></span><span class="nav-desc"><p>지도 측정 도구 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MousePositionControl.html">MousePositionControl</a></span><span class="nav-desc"><p>마우스 좌표 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MoveControl.html">MoveControl</a></span><span class="nav-desc"><p>현재 화면 기준으로 이전/다음 화면 이동 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="OverviewMapControl.html">OverviewMapControl</a></span><span class="nav-desc"><p>인덱스맵 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Popup.html">Popup</a></span><span class="nav-desc"><p>팝업 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="PrintControl.html">PrintControl</a></span><span class="nav-desc"><p>프린트 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Projection.html">Projection</a></span><span class="nav-desc"><p>좌표 변환 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="RotationControl.html">RotationControl</a></span><span class="nav-desc"><p>화면을 회전 시키는 기능
alt + shift 드래그로 지도 회전</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ScaleControl.html">ScaleControl</a></span><span class="nav-desc"><p>축척 표시 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="SLD.html">SLD</a></span><span class="nav-desc"><p>WMS 스타일 관리 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Style.html">Style</a></span><span class="nav-desc"><p>스타일 관리 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="StyleFactory.html">StyleFactory</a></span><span class="nav-desc"><p>스타일 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="StyleFunction.html">StyleFunction</a></span><span class="nav-desc"><p>스타일 Function 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="SwiperControl.html">SwiperControl</a></span><span class="nav-desc"><p>지도 스와이퍼 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ZipControl.html">ZipControl</a></span><span class="nav-desc"><p>Server없이 Layer 생성하는 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ZoomControl.html">ZoomControl</a></span><span class="nav-desc"><p>지도 줌 설정클래스</p></span></li></ul><li class="nav-heading"><a href="global.html">Globals</a></li>
  </nav>
</header>


<main class="layout-main ">
  <div class="container">
    <p class="page-kind">Class</p>
    <h1 class="page-title">SLD</h1>
    




<section>


<header class="class">


    
        
        <!-- <h2>SLD</h2> -->

        

        
            <h4 class="method-heading">Summary</h4>
            <div class="class-summary"><p>SLD 생성자</p></div>
        

        
            <h4 class="method-heading">Description</h4>
            <div class="class-description"><p>WMS 스타일 관리 클래스</p></div>
        
    
</header>

<article>
    <div class="container-overview">



    
        





    


    
    <h3 class="subtitle">Constructor</h3>
    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="SLD">new SLD<span class="signature">(options)</span><span class="return-type-signature"></span>
    </h4>













    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">options</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#odf_sld_option">odf_sld_option</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>SLD 생성 옵션</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>






    <h4 class="method-heading">Example</h4>
    
    <pre><code class="language-js">```javascript
   let sld = odf.StyleFactory.produceSLD({
	   rules: [
		   {
			   name: 'My Rule', //룰 이름
         symbolizers: [
           {
             kind: 'Mark',
             // 포인트에 표현될 도형 종류
             // - 'circle' : 원
             // - 'square' : 사각형
             // - 'triangle' : 세모
             // - 'star' : 별 모양
             // - 'cross' :  + 모양
             // - 'x' : x 모양
             wellKnownName: 'Square',
  
             //포인트에 표현될 도형의 반지름
             radius: 6,
             //포인트에 표현될 도형의 채우기색
             color: '#FF0000',
             //포인트에 표현될 도형의 채우기색 투명도 0~1
             fillOpacity: 0.5,
             //포인트에 표현될 도형의 윤곽선 색
             strokeColor: '#0000FF',
             //포인트에 표현될 도형의 윤곽선 투명도 0~1
             strokeOpacity: 0.7,
             //포인트에 표현될 도형의 윤곽선 두께
             strokeWidth: 3,
           },
         ],
       },
     ]
   });
```</code></pre>
















    
    </div>

    

    
        <h3 class="subsection-title">Classes</h3>

        <dl>
            <dt><a href="SLD.html">SLD</a></dt>
            <dd><p>SLD 생성자</p></dd>
        </dl>
    

     

    

    


    

    
        <h3 class="subtitle">Methods</h3>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="download">download<span class="signature">()</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>SLD 파일 다운로드</p>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getJSON">getJSON<span class="signature">()</span><span class="return-type-signature"> &rarr; {String}</span>
    </h4>





<div class="method-description">
    
    <p>SLD 생성 옵션을 JSON 타입으로 변환</p>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>String</code>
            
            
                <p>JSON 타입으로 변환된 SLD 생성 옵션</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getObject">getObject<span class="signature">()</span><span class="return-type-signature"> &rarr; {<a href="global.html#odf_sld_option">odf_sld_option</a>}</span>
    </h4>





<div class="method-description">
    
    <p>SLD 생성 옵션 조회</p>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>odf_sld_option</code>
            
            
                <p>SLD 생성 옵션</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getSLD">getSLD<span class="signature">()</span><span class="return-type-signature"> &rarr; {Promise}</span>
    </h4>





<div class="method-description">
    
    <p>sld 형식의 문자열 조회</p>
<pre class="prettyprint source lang-javascript"><code>   let sld = odf.StyleFactory.produceSLD({
	  rules: [
		  {
			  name: 'My Rule', //룰 이름
        symbolizers: [
          {
            kind: 'Mark',
            // 포인트에 표현될 도형 종류
            // - 'circle' : 원
            // - 'square' : 사각형
            // - 'triangle' : 세모
            // - 'star' : 별 모양
            // - 'cross' :  + 모양
            // - 'x' : x 모양
            wellKnownName: 'Square',
  
            //포인트에 표현될 도형의 반지름
            radius: 6,
            //포인트에 표현될 도형의 채우기색
            color: '#FF0000',
            //포인트에 표현될 도형의 채우기색 투명도 0~1
            fillOpacity: 0.5,
            //포인트에 표현될 도형의 윤곽선 색
            strokeColor: '#0000FF',
            //포인트에 표현될 도형의 윤곽선 투명도 0~1
            strokeOpacity: 0.7,
            //포인트에 표현될 도형의 윤곽선 두께
            strokeWidth: 3,
          },
        ],
      },
    ]
  });
  sld.getSLD().then(sldString => {
    console.log(sldString);
  });
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Promise</code>
            
            
                <p>생성된 sld 형식의 문자열이 promise value로 담긴 promis 객체</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="produceElement">produceElement<span class="signature">(size, labelFlag)</span><span class="return-type-signature"> &rarr; {HTMLElement}</span>
    </h4>





<div class="method-description">
    
    <p>SLD 스타일에 맞는 HTMLElement 생성</p>
<pre class="prettyprint source lang-javascript"><code>  let sld = odf.StyleFactory.produceSLD({
	  rules: [
		  {
			  name: 'My Rule', //룰 이름
        symbolizers: [
          {
            kind: 'Mark',
            // 포인트에 표현될 도형 종류
            // - 'circle' : 원
            // - 'square' : 사각형
            // - 'triangle' : 세모
            // - 'star' : 별 모양
            // - 'cross' :  + 모양
            // - 'x' : x 모양
            wellKnownName: 'Square',
  
            //포인트에 표현될 도형의 반지름
            radius: 6,
            //포인트에 표현될 도형의 채우기색
            color: '#FF0000',
            //포인트에 표현될 도형의 채우기색 투명도 0~1
            fillOpacity: 0.5,
            //포인트에 표현될 도형의 윤곽선 색
            strokeColor: '#0000FF',
            //포인트에 표현될 도형의 윤곽선 투명도 0~1
            strokeOpacity: 0.7,
            //포인트에 표현될 도형의 윤곽선 두께
            strokeWidth: 3,
          },
        ],
      },
    ]
  });

  sld.produceElement(30, true);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">size</span>
            

            
                


    <span class="param-type">
        <code>Numbe</code>
    </span>
    

            

            

            

            <div class="param-description"><p>HTMLElement 크기 최소 0 ~ 최대 2000</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">labelFlag</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            

            

            <div class="param-description"><p>라벨 표현 여부</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>HTMLElement</code>
            
            
                <p>SLD 스타일에 맞게 생성된 HTMLElement</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setLayer">setLayer<span class="signature">(wmslayer)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>해당 SLD를 WMS에 적용</p>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">wmslayer</span>
            

            
                


    <span class="param-type">
        <code><a href="Layer.html">Layer</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>해당 SLD를 적용할 WMS 레이어</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
    

    

    
</article>

</section>




  </div>
</main>

<footer class="layout-footer">
  <div class="container">
    Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.11</a> on Tue Jan 21 2025 11:05:52 GMT+0900 (대한민국 표준시)
  </div>
</footer>



<script src="scripts/prism.dev.js"></script>
</body>
</html>