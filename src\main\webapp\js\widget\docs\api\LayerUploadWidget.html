<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>GeOnPaas ui widgets: LayerUploadWidget</title>
    
      <link type="text/css" rel="stylesheet" href="styles/vendor/prism-tomorrow-night.css">
    
    <link type="text/css" rel="stylesheet" href="styles/styles.css">
    
    
    <style>
      :root {
      
      
        --nav-width: 370px;
      
      
        --nav-heading-margin-top: 0.5em;
      
      }
    </style>
    
</head>
<body>

<header class="layout-header">
  
  <h1>
    <a href="./index.html">
      GeOnPaas ui widgets
    </a>
  </h1>
  <nav class="layout-nav">
    <ul><li class="nav-heading">Classes</li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="AddressSearchWidget.html">AddressSearchWidget</a></span><span class="nav-desc"><p>주소 검색 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="AdministrativeDistrictSearchWidget.html">AdministrativeDistrictSearchWidget</a></span><span class="nav-desc"><p>행정구역 조회 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="AttributeEditorWidget.html">AttributeEditorWidget</a></span><span class="nav-desc"><p>속성 설정 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="BasemapWidget.html">BasemapWidget</a></span><span class="nav-desc"><p>배경지도 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="BookMarkControlWidget.html">BookMarkControlWidget</a></span><span class="nav-desc"><p>북마크컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="CCTVControlWidget.html">CCTVControlWidget</a></span><span class="nav-desc"><p>CCTV컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ChartWidget.html">ChartWidget</a></span><span class="nav-desc"><p>차트 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ClearControlWidget.html">ClearControlWidget</a></span><span class="nav-desc"><p>초기화컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ConditionFilterWidget.html">ConditionFilterWidget</a></span><span class="nav-desc"><p>조건식 편집기 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="CreateLayerWidget.html">CreateLayerWidget</a></span><span class="nav-desc"><p>레이어 생성 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DivideMapWidget.html">DivideMapWidget</a></span><span class="nav-desc"><p>분할지도 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DownloadControlWidget.html">DownloadControlWidget</a></span><span class="nav-desc"><p>다운로드컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DrawControlWidget.html">DrawControlWidget</a></span><span class="nav-desc"><p>그리기컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FeatureAttributeFormWidget.html">FeatureAttributeFormWidget</a></span><span class="nav-desc"><p>피쳐 속성 폼 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FullScreenControlWidget.html">FullScreenControlWidget</a></span><span class="nav-desc"><p>전체화면컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="GeocodingGridWidget.html">GeocodingGridWidget</a></span><span class="nav-desc"><p>지오코딩 그리드 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="GridWidget.html">GridWidget</a></span><span class="nav-desc"><p>속성테이블 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="HomeControlWidget.html">HomeControlWidget</a></span><span class="nav-desc"><p>홈이동컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LabelStyleWidget.html">LabelStyleWidget</a></span><span class="nav-desc"><p>레이어 스타일 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LayerSearchWidget.html">LayerSearchWidget</a></span><span class="nav-desc"><p>레이어검색 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LayerUploadWidget.html">LayerUploadWidget</a></span><span class="nav-desc"><p>레이어업로드 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LegendWidget.html">LegendWidget</a></span><span class="nav-desc"><p>범례 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MeasureControlWidget.html">MeasureControlWidget</a></span><span class="nav-desc"><p>측정컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MousePositionControlWidget.html">MousePositionControlWidget</a></span><span class="nav-desc"><p>현재위치컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MoveControlWidget.html">MoveControlWidget</a></span><span class="nav-desc"><p>이동컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="OneParcelWidget.html">OneParcelWidget</a></span><span class="nav-desc"><p>일필지 조회 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="OverViewMapControlWidget.html">OverViewMapControlWidget</a></span><span class="nav-desc"><p>지도오버뷰컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="PopupSettingsWidget.html">PopupSettingsWidget</a></span><span class="nav-desc"><p>팝업 설정 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="PopupWidget.html">PopupWidget</a></span><span class="nav-desc"><p>지도 위 클릭 시 피쳐 정보 표출 팝업 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="PrintControlWidget.html">PrintControlWidget</a></span><span class="nav-desc"><p>출력컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="RoadViewWidget.html">RoadViewWidget</a></span><span class="nav-desc"><p>로드뷰(카카오) 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="RotationControlWidget.html">RotationControlWidget</a></span><span class="nav-desc"><p>회전컨트롤 위젯 (생성 후 지도 객체에서 Alt + Shift + 지도 객체 Drag로 사용)</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ScaleControlWidget.html">ScaleControlWidget</a></span><span class="nav-desc"><p>축척컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="SpatialAnalysisWidget.html">SpatialAnalysisWidget</a></span><span class="nav-desc"><p>공간분석 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="StyleWidget.html">StyleWidget</a></span><span class="nav-desc"><p>레이어 스타일 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="SwiperControlWidget.html">SwiperControlWidget</a></span><span class="nav-desc"><p>스와이퍼컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="SwiperWidget.html">SwiperWidget</a></span><span class="nav-desc"><p>스와이퍼 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="TimeSliderControlWidget.html">TimeSliderControlWidget</a></span><span class="nav-desc"><p>타임슬라이더컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="TOCWidget.html">TOCWidget</a></span><span class="nav-desc"><p>TOC 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ZoomControlWidget.html">ZoomControlWidget</a></span><span class="nav-desc"><p>지도 확대/축소 조작 위젯</p></span></li></ul><li class="nav-heading"><a href="global.html">Globals</a></li>
  </nav>
</header>


<main class="layout-main ">
  <div class="container">
    <p class="page-kind">Class</p>
    <h1 class="page-title">LayerUploadWidget</h1>
    




<section>


<header class="class">


    
        
        <!-- <h2>LayerUploadWidget</h2> -->

        

        
            <h4 class="method-heading">Summary</h4>
            <div class="class-summary"><p>레이어검색 위젯 생성자</p></div>
        

        
            <h4 class="method-heading">Description</h4>
            <div class="class-description"><p>레이어업로드 위젯</p></div>
        
    
</header>

<article>
    <div class="container-overview">



    
        





    


    
    <h3 class="subtitle">Constructor</h3>
    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="LayerUploadWidget">new LayerUploadWidget<span class="signature">(object)</span><span class="return-type-signature"></span>
    </h4>













    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">object</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#oui_LayerUpload_object">oui_LayerUpload_object</a></code>
    </span>
    

            

            

            

            <div class="param-description"></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>






    <h4 class="method-heading">Example</h4>
    
    <pre><code class="language-js">//api 셋팅
    let uploadApi = oui.UploadApi(oui.HttpClient({
        baseURL: 'http://**********:10040',
        }), {
        sendOpertNtcnInfo: sendOpertNtcnInfo,
        sendUploadImageInfo : sendUploadImageInfo,
        crtfckey: 'crtfckey',
        userId: "user"
    });
    let geocodingApi = oui.GeocodingApi(oui.HttpClient({
        baseURL: 'http://**********:10060',
        }), {
            sendOpertNtcnInfo: sendOpertNtcnInfo,
            crtfckey: 'crtfckey'
        });
    //레이어 api
    let layerApi = oui.LayerApi(oui.HttpClient({
        baseURL: '[api 경로]',
        }), {
        userId: 'user',
        userMapId: 'UM0000000429',
        //웹레이어 업로드 후 웹 레이어 정보 콜백 
        sendLayerInfo: (layerInfo)=>{
            console.dir(layerInfo)     
        },
        crtfckey: 'crtfckey'
    });
    let layerUploadCodeOption = {
        paramList: [
           { params: { groupCode: 'MPD003' }, type: 'detail' },
           { params: { groupCode: 'MPD007' }, type: 'detail' },
           { params: { groupCode: 'MPD025' }, type: 'detail' },
        ],
        categoryId: 'layerUpload'
    };
    let layerUploadCodeApi = oui.CommonCodeApi(oui.HttpClient({
        baseURL: '[api 경로]',
    }), layerUploadCodeOption);

    let mapApi = oui.MapApi(oui.HttpClient({
        baseURL: '[api 경로]'}),{
        crtfcKey: 'crtfcKey'
    })
    let noticeApi = oui.NoticeApi(oui.HttpClient({
        baseURL: '[api 경로]'}),{
        userId: 'user',
        crtfckey: 'crtfckey'
    }

    let layerUploadWidget = new oui.LayerUploadWidget({
        target: document.querySelector('#test'),
        options: {
            // [uploadTypeList] web : 웹 레이어 업로드  / shp : shp레이어 업로드 / geocoding : 지오코딩 업로드 / dxf : dxf 업로드 / custom : 사용자가 직접 생성 / web : 웹레이어 업로드 / image : 이미지 레이어 업로드 / geoTiff : geoTiff 레이어 업로드 / netCdf : netCdf업로드  [기본값 : 전부 사용가능]
            // 배열 순서에따라 업로드 탭의 순서도 변경
            uploadTypeList : ["web","shp","geocoding","dxf","custom","image","geoTiff","netCdf"],
            defaultUploadType : "shp", //처음 표출할 업로드 화면 Type ['shp','geocoding','web','dxf','custom','image','geoTiff','netCdf'] 중 선택
            errorCallback: function (message) { //에러 발생시 호출
                callLoadingBar({ status: false });
            },
            alertList: {
                startLoadingBar: (message) => {
                    //로딩바 시작
                },
                endLoadingBar: (message) => {
                    //로딩바 종료
                },
                customAlert: (message) => {
                    //알림창
                },
                customErrorAlert: (message, subMessage) => {
                    //에러 알림창
                }
            },
            shpUploadOptions: {
                //[size] 단위: mb , 기본값 100mb
                //size : 50,
                //발행 클릭 시 타는 함수
                onClickPublish : function(){
                    callLoadingBar({ status: true, message: "레이어를 업로드중입니다" });
                },
                serviceType: 'wms' // ['all','wms','wfs'] 중 선택 (all 선택시 wms/wfs 선택할 수있는 selectBox 표출) [기본값 : all]
            },
            dxfFileUploadOptions : {
                //[size] 단위: mb , 기본값 100mb
                //size : 50,
                serviceType: 'wms' // ['all','wms','wfs'] 중 선택 (all 선택시 wms/wfs 선택할 수있는 selectBox 표출) [기본값 : all]
                //발행 클릭 시 타는 함수
                onClickPublish : function(){
                    //업로드버튼 클릭시 타는 함수
                }
            }    
            geocodingOptions: {
                //[size] 단위: mb , 기본값  xlsxAndXls : 30mb, csvAndTxt: 50mb
                //size : {xlsxAndXls : 10 , csvAndTxt : 30},
                //[limitNumber] 기본값  xlsx/xls : 20000건, csv/txt : 20000건
                //limitNumber :  {xlsxAndXls : 500 , csvAndTxt : 1000}
                targetSrid: 5186
                //[matchingField] 지오코딩 데이터 업로드 시, 이곳에 정의한 값이 필드명으로 존재할 경우 해당 필드로 '기준필드' 셋팅
                //기본값  x : ['경도','X','x'] , y : ['위도','Y','y'] , address : ['주소'] , pnu : ['필지고유번호']
                matchingField: {
                    coord: {
                        x: ['좌표정보(X)', '좌표정보(x)', '좌표정보_X', '좌표정보_x'],
                        y: ['좌표정보(Y)', '좌표정보(y)', '좌표정보_Y', '좌표정보_y'],
                    },
                    address : ['정제주소'],
                    pnu : ['필지고유번호']
                }
            },
            customUploadOptions: {
                serviceType: 'wms', // ['all','wms','wfs'] 중 선택 (all 선택시 wms/wfs 선택할 수있는 selectBox 표출) [기본값 : all]
                targetSrid: '5186', //5186, 5174 등 targetSrid 선언 안하면 셀렉트박스 생김
                mapApiClient: `${API_MAP}/api/map/wfs`,
                crtfckey: 'crtfckey', //api 사용시 인증키 필수값
                onClickPublish: function () {
                    //업로드버튼 클릭시 타는 함수
                },
                onClickPublish: function () {
                    //업로드버튼 클릭시 타는 함수
                }
            },
            imgUploadOptions: {
                //[size] 단위: mb , 기본값 3mb
                //size : 1,
                //발행 클릭 시 타는 함수
                onClickPublish : function(){
                    //업로드버튼 클릭시 타는 함수
                },
            },
            geoTiffUploadOptions:{
                //[size] 단위: mb , 기본값 200mb
                //size : 50,
                //발행 클릭 시 타는 함수
                onClickPublish : function(){
                    //업로드버튼 클릭시 타는 함수
                },
            },
            netCdfUploadOptions: {
                //[size] 단위: mb , 기본값 100mb
                //size : 50,
                //발행 클릭 시 타는 함수
                onClickPublish : function(){
                    //업로드버튼 클릭시 타는 함수
                },
            },

        },
        api: {
            publishFileLayer: uploadApi.publishFileLayer,
            geocodingLayer: geocodingApi.geocodingLayer,
            uploadWebLayer: layerApi.uploadWebLayer,
            publishDXFFile: uploadApi.publishDXFFile,
            getCommonCode: layerUploadCodeApi.getAllDetailCode,
            publishGeojsonLayerText: uploadApi.publishGeojsonLayerText,
            updateData: mapApi.updateData,
            getData: mapApi.getData,
            selectNotice: noticeApi.selectNotice,
            insertLayer: layerApi.insertLayer,
            uploadImage: uploadApi.uploadImage
        }
    });</code></pre>
















    
    </div>

    

    

     

    

    


    

    
        <h3 class="subtitle">Methods</h3>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="addTo">addTo<span class="signature">(map)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>레이어 업로드 위젯 열기</p>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">map</span>
            

            
                


    <span class="param-type">
        <code>Map</code>
    </span>
    

            

            

            

            <div class="param-description"></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>






    <h4 class="method-heading">Example</h4>
    
    <pre><code class="language-js">//레이어 검색 위젯 열기
layerUploadWidget.addTo(map);</code></pre>
















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="remove">remove<span class="signature">()</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>레이어 업로드 위젯 제거</p>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>






    <h4 class="method-heading">Example</h4>
    
    <pre><code class="language-js">layerUploadWidget.remove();</code></pre>
















    </article>

        
    

    

    
</article>

</section>




  </div>
</main>

<footer class="layout-footer">
  <div class="container">
    Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.11</a> on Mon Dec 09 2024 10:44:32 GMT+0900 (대한민국 표준시)
  </div>
</footer>



<script src="scripts/prism.dev.js"></script>
</body>
</html>