<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<link href="::OuiUrl::/oui.css" rel="stylesheet">

	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
	<script type="text/javascript" src="::OuiUrl::/oui.min.js"></script>
</head>
<body>
	<div id="map" style="height:550px;"></div>
	<div class="featureAttributeFormWidget" id="featureAttributeFormWidget" style="background-color: white;"></div>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::, ::coordy::);
    var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);

	/* 테스트 레이어 생성  */
	var wfsLayer = odf.LayerFactory.produce('geoserver', {
		method : 'get',
		server : '::WfsAPI::',
		layer : '::testEditPolygonLayer1::',
		service : 'wfs',
	});
	wfsLayer.setMap(map);
	wfsLayer.fit();


	/* oui api 연결 객체 생성  */
	var smtApiClient = oui.HttpClient({
		//api 요청 실패시(500,503) 최대 재시도 횟수
		//maxRetries : 3,
		baseURL: '::smtAPI::', //api 주소
	});
	var mapApiClient =  oui.HttpClient({
		//api 요청 실패시(500,503) 최대 재시도 횟수
		//maxRetries : 3,
		baseURL: '::mapAPI::', //api 주소
	});
	var columnInfoApi = oui.ColumnInfoApi(smtApiClient, {
		crtfckey: '::crtfckey::',
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			columnInfoFunction : 3000,
		}
		 */
	});
	var mapApi = oui.MapApi(mapApiClient, {
		crtfckey: '::crtfckey::',
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			getData : 3000,
			updateData : 3000
		}
		 */
	});

	/* 피쳐속성폼 위젯 생성 */
	var featureAttributeFormWidget = new oui.FeatureAttributeFormWidget({
		odf: odf,
		target: document.getElementById('featureAttributeFormWidget'),
		options: {
			//사용자 정의 알림 메세지 정의
	        	alertList: {
				//사용자 정의 알림
				customAlert: (message) => {
					alert(message);
				},
				//사용자 정의 확인창
				customConfirm: (message, callback) => {
					let isConfirmed = confirm(message);
					if (isConfirmed) {
	           			callback();
	            		}
				},
			},
			//피쳐 삭제 시 호출 되는 callback function
			callbackRemoveFeature: (feature) => {
				featureAttributeFormWidget.remove();
			},
			//피쳐 저장시 시 호출 되는 callback function
			callbackSaveFeature: (feature) => {
				featureAttributeFormWidget.remove();
			},
			header : true,
		},
		api: {
			getData: mapApi.getData,
			getLayerList: (callback) => {
				//피쳐 폼으로 선택할 수 있는 지도상의 레이어 정보 목록
				//callback으로 넘길 값은 {linkedLayer : 레이어 객체 , layerNm : 레이어명, layerId : 디비에 저장된 레이어 id} 의 배열
				callback([{
           			linkedLayer: wfsLayer,
					layerNm: '샘플레이어',
					layerId: '::testEditPolygonLayer1Id::',
					cntntsId: '::testEditPolygonLayer1::'.split(':')[1]
				}]);
			},
			//피쳐 업데이트
			updateData: mapApi.updateData,
			//별칭 및 컬럼 정보 조회
			columnInfoFunction: columnInfoApi.columnInfoFunction
		}
	});
	featureAttributeFormWidget.addTo(map);
</script>
</html>
