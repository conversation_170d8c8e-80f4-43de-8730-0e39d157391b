/**
 *
 */
(function() {
	// 부모창에서 넘어온 data
	var data = decodeURI(window.parent.document.getElementById('data') ? window.parent.document.getElementById('data').value : "");

	var htmlEditor = CodeMirror.fromTextArea(document.getElementById('text-html'), {
		mode: 'text/html',
		lineNumbers: true,
		autoRefresh: true
	});
	htmlEditor.setValue(data);

	var cssEditor = CodeMirror.fromTextArea(document.getElementById('text-css'), {
		mode: 'css',
		lineNumbers: true,
		autoRefresh: true
	});

	// toggle 버튼 클릭 이벤트
	var toggleButtons = document.getElementsByClassName("toggle-button");
	for(var i=0; i < toggleButtons.length; i++) {

		toggleButtons[i].onclick = function() {
			this.classList.toggle('on');
			togglePannel();
		}
	}

	togglePannel();
	function togglePannel() {
		var isOnHtml = document.getElementById('btn-html').classList.contains('on');
		var isOnCss = document.getElementById('btn-css').classList.contains('on');
		var isOnResult = document.getElementById('btn-result').classList.contains('on');

		var pannelHtml = document.getElementById('pannel-html');
		var pannelCss = document.getElementById('pannel-css');
		var pannelResult = document.getElementById('pannel-result');

		if( isOnHtml && isOnCss && isOnResult ) {
			// 모두 on
			pannelHtml.style = "display:block;left:0;right:67%;";
			pannelCss.style = "display:block;left:33%;right:34%;";
			pannelResult.style = "display:block;left:66%;right:0;";

		} else if( !isOnHtml && !isOnCss && !isOnResult ){
			// 모두 off
			pannelHtml.style = "display:none;";
			pannelCss.style = "display:none;";
			pannelResult.style = "display:none;";

		} else if( isOnHtml && !isOnCss && !isOnResult ){
			// Html on
			pannelHtml.style = "display:block;left:0;right:0;";
			pannelCss.style = "display:none;";
			pannelResult.style = "display:none;";

		} else if( !isOnHtml && isOnCss && !isOnResult ){
			// Css on
			pannelHtml.style = "display:none;";
			pannelCss.style = "display:block;left:0;right:0;";
			pannelResult.style = "display:none;";

		} else if( !isOnHtml && !isOnCss && isOnResult ){
			// 결과 on
			pannelHtml.style = "display:none;";
			pannelCss.style = "display:none;";
			pannelResult.style = "display:block;left:0;right:0;";

		} else if( isOnHtml && isOnCss && !isOnResult ){
			// Html Css on
			pannelHtml.style = "display:block;left:0;right:50%;";
			pannelCss.style = "display:block;left:50%;right:0;";
			pannelResult.style = "display:none;";

		} else if( isOnHtml && !isOnCss && isOnResult ){
			// Html 결과 on
			pannelHtml.style = "display:block;left:0;right:50%;";
			pannelCss.style = "display:none;";
			pannelResult.style = "display:block;left:50%;right:0;";

		} else if( !isOnHtml && isOnCss && isOnResult ){
			// Css 결과 on
			pannelHtml.style = "display:none;";
			pannelCss.style = "display:block;left:0;right:50%;";
			pannelResult.style = "display:block;left:50%;right:0;";
		}
	}

	// 결과 실행
	function updatePreview() {
		var previewFrame = document.getElementById("preview-frame");
		var preview = previewFrame.contentDocument || previewFrame.contentWindow.document;

		preview.open();
		preview.write("<style>" + cssEditor.getValue() + "</style>");
		preview.write(htmlEditor.getValue());
		preview.close();

		var iDoc = document.querySelector('iframe:not(#sample-container)').contentWindow.document;

		for(var i=  0; i < parent.document.styleSheets.length; i++){
			var sS = parent.document.styleSheets[i];
			if(sS.href && sS.href.includes('sample.css')){
				let _styleSheet = iDoc.createElement('link');
				_styleSheet.rel ='stylesheet';
				_styleSheet.href =sS.href;
				iDoc.head.appendChild(_styleSheet);
			}
		}

		let _styleSheet = iDoc.createElement('style');
		_styleSheet.innerHTML = `
.mapContent {
	height: 676px !important;
    width: 845px !important;
}
#map {
	height: 676px !important;
}
		`
		iDoc.head.appendChild(_styleSheet);
	}

	// 실행 버튼
	document.getElementById('btn-run').onclick = updatePreview;

	// 초기화 버튼
	document.getElementById('btn-reset').onclick = function() {

		htmlEditor.setValue(data);
		cssEditor.setValue('');
		updatePreview();
	}

	window.onload = function(){
		var autorunon = document.getElementById('btn-auto-run');
		autorunon.classList.toggle('on');
		if(autorunon.classList.contains('on')) {
			updatePreview();
			//setTimeout(updatePreview, 300);
			cssEditor.on("change", setUpdateDelay);
			htmlEditor.on("change", setUpdateDelay);
		}
	}
	// 자동실행 버튼
	var delay;
	document.getElementById('btn-auto-run').onclick = function() {

		this.classList.toggle('on');

		if(this.classList.contains('on')) {
			updatePreview();
			//setTimeout(updatePreview, 300);
			cssEditor.on("change", setUpdateDelay);
			htmlEditor.on("change", setUpdateDelay);
		} else {

			clearTimeout(delay);
			cssEditor.off("change", setUpdateDelay);
			htmlEditor.off("change", setUpdateDelay);
		}
	}

	// change event on/off 시에 codemirror에서 동일한 함수인지 검사하므로 off 시 반드시 함수를 넘겨줘야 함.
	function setUpdateDelay() {
		clearTimeout(delay);
		delay = updatePreview();//setTimeout(updatePreview, 300);
	}

})();

