@charset "UTF-8";

/*----------- 불필요한 부분 숨기기 ------------*/
pre.base-url {
	display: none;
}

div.information-container {
	display: none;
}


/*----------- overide start ------------*/


.swagger-ui .wrapper {
	padding: 0;
}

.swagger-ui .opblock-tag {
	padding-top: 0;
	padding-left: 0;
}

h4.opblock-tag a span {
	padding-left: 30px;
    color: #333333;
    font-size: 24px;
    font-family: 'Noto Sans Medium';
    font-weight: normal;
    background: url(../images/common/bullet-titSec.png) no-repeat left 10px;
}

.opblock-body ul {
	list-style-type: disc;
    padding-inline-start: 40px;
    color:#5555ff;
    font-size:1.2em;
}
.opblock-body li {
	list-style-type: disc;
	padding-bottom: 5px;
}

.opblock-body li span {
	text-decoration: underline;
}
.swagger-ui .opblock.opblock-get .opblock-summary-method{
	background-color :  #3D87FB;
}
.swagger-ui .opblock.opblock-get{
	border-color : #3D87FB;
	background-color :  #EEEEEE;
	box-shadow:0px 0px 3px rgb(0 0 0 / 15%);
}
.swagger-ui .opblock.opblock-post .opblock-summary-method{
	background-color : #9D9D9D;
}
.swagger-ui .opblock.opblock-post{
	border-color : #CCCCCC ;
	background-color :  #E5F6FD;
	box-shadow:0px 0px 3px rgb(0 0 0 / 15%);
}

.swagger-ui .opblock-summary-control:focus {
	outline: none;
}

/* openapi server container margin-bottom 제거  */
.swagger-ui .scheme-container section {
	margin-bottom: 0 !important;
}

/* noscript 요소를 숨김 */
.swagger-ui .opblock {
	display: flex;
	flex-direction: column;
}

.swagger-ui .no-margin, .swagger-ui .opblock-summary, .swagger-ui .opblock-tag {
	order: -1;
}
