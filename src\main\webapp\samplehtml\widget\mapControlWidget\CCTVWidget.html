<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<link href="::OuiUrl::/oui.css" rel="stylesheet">
	<link href="::SmtUrl::/css/common_toolbar.css" rel="stylesheet">
	<link href="::SmtUrl::/css/widgets/cctvControl.css" rel="stylesheet">

	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
	<script type="text/javascript" src="::OuiUrl::/oui.min.js"></script>
</head>
<body>
	<div id ="map" style="height:550px;"></div>
    <ul class="toolbar">
        <li class="cctvControlWidget" id="cctvControlWidget"></li>
    </ul>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::, ::coordy::);
	var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);


	/* oui api 연결 객체 생성  */
	var cctvClient = oui.HttpClient({
		baseURL: '::cctvApiURL::',
	});
	var cctvApi = oui.CCTVApi(cctvClient, {
		apiKey : '::cctvApiKey::',
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			getCCTVData : 3000,
		}
		 */
	});

	/* CCTV 뷰 위젯 생성 */
	var cctvControlWidget = new oui.CCTVControlWidget({
		options : {
			alertList : {
				customAlert: (message) => {
					console.dir(message);
				},
			},
			callback: (cctvLayer) => { //cctvLayer 객체 받는 callback 함수 ();
				let cctvStyle = odf.StyleFactory.produce({
					image: {
						icon: {
							opacity: 1,
							scale: 1,
							//src: 'smt/images/widget/CCTV.png',
							src: '::DeveloperUrl::/smt/images/widget/CCTV.png',
						}
					}
				});
				cctvLayer.setStyle(cctvStyle);
			},
			//width : '360',
			//height : '180',
		},
		api : {
			getCCTVData : cctvApi.getCCTVData,
		},
		target : document.getElementById('cctvControlWidget'),
	})
	cctvControlWidget.addTo(map);
</script>
</html>
