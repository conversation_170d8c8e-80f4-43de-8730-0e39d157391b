<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
</head>
<body>
	<div id="map" class="odf-view" style="height:550px;"></div>
	<div class="btnDiv">
		<input type="button" class="onoffOnlyBtn toggle grp1" value="이벤트 등록" onclick="addListener();" />
		<input type="button" class="onoffOnlyBtn toggle grp1" value="이벤트 삭제" onclick="removeListener();" />
		등록된 이벤트 개수 : <span id="eventCount">0</span>
	</div>
	<p>※ 직접해보기에서는 이벤트 버튼 토글이 지원되지 않습니다.</p>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::,::coordy::);
	var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);


	//클릭이벤트 등록
	var event = odf.event;
	var eventArr = [];

	//이벤트 추가
	function addListener() {
		eventArr.push(event.addListener(map, 'click', function(evt) {
			var marker = new odf.Marker({
				position : evt.coordinate
			});
			marker.setMap(map);
		}));
		hasEvent();
	}

	//이벤트 삭제

	function removeListener() {
		eventArr.slice().forEach(function(eventId) {
			event.removeListener(eventId);
		});
		eventArr = [];
		hasEvent();
	}

	//이벤트 등록 여부 확인
	function hasEvent() {
		document.querySelector('#eventCount').innerHTML = event.hasEvent(map,'click');//특정 타겟에 특정 타입의 이벤트가 등록된 개수 조회
		//event.hasEventId('click_000001'); //이벤트 id로 확인
	}
</script>
</html>

