<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<link href="::OuiUrl::/oui.css" rel="stylesheet">
	<link href="::SmtUrl::/css/common_toolbar.css" rel="stylesheet">
	<link href="::SmtUrl::/css/widgets/downloadControl.css" rel="stylesheet">
	<link href="::SmtUrl::/css/widgets/printControl.css" rel="stylesheet">

	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
	<script type="text/javascript" src="::OuiUrl::/oui.min.js"></script>
</head>
<body>
<div id ="map" style="height:550px;"></div>
<ul class="toolbar">
	<li class="downloadControlWidget" id="downloadControlWidget"></li>
	<li class="printControlWidget" id="printControlWidget"></li>
</ul>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::, ::coordy::);
	var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);


	/* 다운로드 위젯 생성  */
	var downloadControlWidget = new oui.DownloadControlWidget({
		target: document.getElementById('downloadControlWidget'),
		options : {
			//출력 대상 element 선택자
			//targetElementSelector : 'body',
			//출력 제외 element 선택자
			//exceptionElementSelectors : ['.toolbar'],
			//지도에 외부 이미지 포함시, proxy 추가해야 출력됨
			//proxyURL : 'proxyUrl.jsp',
			//proxyParam : 'url'
		}
	});
	downloadControlWidget.addTo(map);
	//지우기함수
	//downloadControlWidget.remove();

	/* 지도 출력 위젯 생성 */
	var printControlWidget = new oui.PrintControlWidget({
		target: document.getElementById('printControlWidget'),
		options : {
			//출력 제목
			//title : '개발자지원센터 출력 위젯 샘플',
			//출력 대상 element 선택자
			//targetElementSelector : 'body',
			//출력 제외 element 선택자
			//exceptionElementSelectors : ['.toolbar'],
			//지도에 외부 이미지 포함시, proxy 추가해야 출력 됨
			//proxyURL : 'proxyUrl.jsp',
			//proxyParam : 'url'
		}
	});
	printControlWidget.addTo(map);
	//지우기함수
	//printControlWidget.remove();

</script>
</html>
