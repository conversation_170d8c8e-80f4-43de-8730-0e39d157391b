<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
</head>
<body>
	<div id="map" class="odf-view" style="height:550px;"></div>
	<div>
		※ vWorld 오픈 API 사용 방법<br>
		① <a href="https://www.vworld.kr/dev/v4api.do">vWorld 오픈 API</a> 회원가입 및 로그인<br>
		② <a href="https://www.vworld.kr/dev/v4dv_wmsguide2_s001.do">vWorld WMS/WFS API 2.0 레퍼런스</a>을 확인하여 필요한 데이터 찾기<br>
		③ API 신청 및 승인 대기<br>
		④ 승인된 API 키 입력하여 사용하기<br>
		&nbsp;&nbsp;&nbsp;※ 사용 서비스의 요청변수 목록을 잘 확인하고 레이어 생성 옵션에 적절히 추가하여 사용
  	</div>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::,::coordy::);
	var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);


  //vWorld 오픈 api > wms/wfs api 2.0 레퍼런스
  var apiLayer = odf.LayerFactory.produce('api', {
      server:{
        url:'http://api.vworld.kr/req/wms',
		  proxyURL : 'proxyUrl.jsp',
		  proxyParam : 'url',
	    } , // API 주소
      //server : 'http://api.vworld.kr/req/wms',// API 주소
      service: 'wms', // wms/wfs
      layers: 'lt_c_adsigg_info,lt_c_ademd_info', //하나 또는 쉼표(,)로 분리된 지도레이어 목록, 최대 4개  ※https://www.vworld.kr/dev/v4dv_wmsguide2_s001.do 에서 사용가능한 레이어 목록 확인 가능
      styles: 'lt_c_adsigg,lt_c_ademd_3d', //LAYERS와 1대1 관계의 하나 또는 쉼표(,)로 분리된 스타일 목록
      //version : '1.3.0',//요청 서비스 버전
      crs : 'EPSG:::srid::',//응답결과 좌표계와 bbox 파라미터의 좌표계
      //transparent : 'true',//지도 배경의 투명도 여부
      //bgcolor:'0xFFFFFF',//배경색
      //exceptions:'text/xml',
      originalOption : {//odf에서 제공해주는 기본 파라미터 적용 여부
		  //SERVICE : true,//(기본값 true)
          //REQUEST : true, //(기본값 true)
          //WIDTH : true,//(기본값 true)
          //HEIGHT : true,//(기본값 true)
      		BBOX : '::bbox::',
	/* ★BBOX★
	  odf에서 기본 제공하는 bbox 배열은 minx,miny,maxx,maxy 순인 반면에
	  vworld에서는 EPSG:4326/EPSG:5186/EPSG:5187일 경우 bbox 배열을 miny,minx, maxy,maxx 순으로 입력받음
              해당 경우에는 BBOX 값을 '{{miny}},{{minx}},{{maxy}},{{maxx}}' 와같이 입력하면 x와 y의 순서가 바뀌어 적용됨.
	*/
          //FORMAT : true,//(기본값 false)
          //TRANSPARENT : true,//(기본값 false)
          //STYLES : true,//(기본값 false)
          //CRS : false,//(기본값 false)
          //VERSION : false,//(기본값 false)
      },
      /* 직접해보기에서 api 를 실행할 때는 아래 domain 값 부분을 주석처리해야 api 가 정상 동작합니다. */
      domain:'::vWorldDomain::',//API KEY를 발급받을때 입력했던 URL
      key : '::vWorldApiKey::',//발급받은 api key
  });
  apiLayer.setMap(map);
</script>
</html>
