<th:block data-layout-decorate="~{_layout/default.html}">

	<th:block layout:fragment="css">
		<link rel="stylesheet" th:href="@{/vendor/prism/prism.css}">
		<link rel="stylesheet" th:href="@{/css/search.css}">
    </th:block>
	
	<th:block layout:fragment="content">
		<h2 class="hidden">본문 영역</h2>
		<!-- ctrl + f 입력시 출력되는 검색창 -->
		<div class="searchBody">
	        <input type="search" class="searchBox" placeholder="">
	        <span class="countBody"></span>
	        <button functionName="next" class="nextBtn">∨</button>
	        <button functionName="prev" class="prevBtn">∧</button>
	        <button functionName="clear" class="clearBtn">Ⅹ</button>
	    </div>
	    
        <div class="sideMenu">
            <!-- 검색창 -->
            <div class="innerBox">
                <input id="txt-search" type="text" placeholder="| 검색" />
                <button type="button" class="btnSearchRemove"><span class="hidden">지우기</span></button>
            </div>
            
            <!-- 서브메뉴 -->
            <div class="listArea mScroll">
                <div class="tab tab1" id="submenu">
	            </div>
	            <div id="menu-loader" style="display:none;"></div>
	        </div>
        </div>
        
        <div id="content" class="mScroll">
						<div class="btnGroup">
                <button type="button" class="btnTop"><span class="hidden">맨 위로</span></button>
                <button type="button" class="btnBottom"><span class="hidden">맨 아래로</span></button>
            </div>
			<div class="btnSample">
				<button class="btnLinkSample"><span>샘플소스보기</span></button>
				<div class="sampleList">
					<ul class="sampleListUl">
					
					</ul>
				</div>
			</div>
			<div id="map-doc" class="innerContent">
				<main class="layout-main">
				</main>
			</div>
		</div>
	</th:block>
	
	<!-- index.html 고유 스크립트 추가 -->
    <th:block layout:fragment="script-bottom">
    	<script>
    		var odfBaseUrl = 'js/widget[[${@egovProperties.getProperty("Url.DOCS")}]]';
    		$('.btnSample').mouseenter(function(){
    			let target = $('.menuList a.active')[0].innerHTML;
    			$.getJSON("js/widget/docs/oui-docs.json", function(data){ 
    				let filteredList = data.filter((e) => e.name == target);
    				filteredList.forEach((v)=>{
    					$('.btnSample .sampleList .sampleListUl').append(`<li onClick="linkToSample('${v.path}')">${v.path}</li>`);
    				})
    			})
    		})
    		$('.btnSample').mouseleave(function(){
    			$('.btnSample .sampleList .sampleListUl').children().length > 0 ? $('.btnSample .sampleList .sampleListUl').children().remove() : '';
    		})
/*     		function makeSampleList(target){
    			$('.btnLinkSample').attr('class').includes('on') ? $('.btnLinkSample').removeClass('on') : $('.btnLinkSample').addClass('on');
    			if($('.btnLinkSample').attr('class').includes('on')){
    			$.getJSON("/js/widget/docs/oui-docs.json", function(data){ 
    				let filteredList = data.filter((e) => e.name == target);
    				filteredList.forEach((v)=>{
    					$('.btnSample .sampleList .sampleListUl').append(`<li onClick="linkToSample('${v.path}')">${v.path}</li>`);
    				})
    			})
    			}else{
    				$('.btnSample .sampleList .sampleListUl').children().length > 0 ? $('.btnSample .sampleList .sampleListUl').children().remove() : '';
    			}
    			
    		} */
    		function linkToSample(sampleName){
    				menu.goPage('/widgetsample', sampleName);
    		}
    	</script>
    	<!-- <script th:src="@{/vendor/prism/prism.js}"></script> -->
		<script th:src="@{/js/widget/docs/index.js(ms=${ms})}"></script>
		<script th:src="@{/js/mark.min.js}"></script>
		<script th:src="@{/js/search.js}"></script>
		<link rel="stylesheet" th:href="@{/css/mapdocs.css(ms=${ms})}">
    </th:block>
	
</th:block>