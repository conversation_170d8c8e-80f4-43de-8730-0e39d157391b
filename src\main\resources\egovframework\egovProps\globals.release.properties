#-----------------------------------------------------------------------
#
#   globals.release.properties : \uC2DC\uC2A4\uD15C
#
#-----------------------------------------------------------------------
#   1.  key = value \uAD6C\uC870\uC785\uB2C8\uB2E4.
#   2.  key\uAC12\uC740 \uACF5\uBC31\uBB38\uC790\uB97C \uD3EC\uD568\uBD88\uAC00, value\uAC12\uC740 \uACF5\uBC31\uBB38\uC790\uB97C \uAC00\uB2A5
#   3.  key\uAC12\uC73C\uB85C \uD55C\uAE00\uC744 \uC0AC\uC6A9\uBD88\uAC00,   value\uAC12\uC740 \uD55C\uAE00\uC0AC\uC6A9\uC774 \uAC00\uB2A5
#   4.  \uC904\uC744 \uBC14\uAFC0 \uD544\uC694\uAC00 \uC788\uC73C\uBA74 '\'\uB97C \uB77C\uC778\uC758 \uB05D\uC5D0 \uCD94\uAC00(\uB9CC\uC57D  '\'\uBB38\uC790\uB97C \uC0AC\uC6A9\uD574\uC57C \uD558\uB294 \uACBD\uC6B0\uB294 '\\'\uB97C \uC0AC\uC6A9)
#   5.  Windows\uC5D0\uC11C\uC758 \uB514\uB809\uD1A0\uB9AC \uD45C\uC2DC : '\\' or '/'  ('\' \uC0AC\uC6A9\uD558\uBA74 \uC548\uB428)
#   6.  Unix\uC5D0\uC11C\uC758 \uB514\uB809\uD1A0\uB9AC \uD45C\uC2DC : '/'
#   7.  \uC8FC\uC11D\uBB38 \uCC98\uB9AC\uB294  #\uC0AC\uC6A9
#   8.  value\uAC12 \uB4A4\uC5D0 \uC2A4\uD398\uC774\uC2A4\uAC00 \uC874\uC7AC\uD558\uB294 \uACBD\uC6B0 \uC11C\uBE14\uB9BF\uC5D0\uC11C \uCC38\uC870\uD560\uB54C\uB294 \uC5D0\uB7EC\uBC1C\uC0DD\uD560 \uC218 \uC788\uC73C\uBBC0\uB85C trim()\uD558\uAC70\uB098 \uB9C8\uC9C0\uB9C9 \uACF5\uBC31\uC5C6\uC774 properties \uAC12\uC744 \uC124\uC815\uD560\uAC83
#-----------------------------------------------------------------------

Url.WfsAPI =  /bag/api/map/wfs
Url.WmsAPI = /bag/api/map/wms
Url.WmtsAPI = /bag/api/map/wmts
Url.APIGW = http://*************:8000
Url.ODF = http://localhost:8080
Url.DOCS = /docs/api/

Service.ODF = /dev
Service.Layer = /bag
Service.API = ana,pub,cdr,bag,est,ctt,adg
Service.API.Order = \uB3C4\uD615\uBD84\uC11D,\uB3C4\uD615\uC815\uBCF4 \uD30C\uC77C \uC5C5\uB85C\uB4DC,\uC791\uC5C5\uC54C\uB9BC,\uB808\uC774\uC5B4 \uAD00\uB9AC,\uCEE8\uD150\uCE20 \uC815\uBCF4,\uC9C0\uB3C4 \uC694\uCCAD,\
				\uC8FC\uC18C\uAC80\uC0C9,\uC8FC\uC18C\uC815\uC81C,\uC9C0\uC624\uCF54\uB529,\uC88C\uD45C\uBCC0\uD658,\uACF5\uAC04\uC815\uBCF4 \uD30C\uC77C \uB2E4\uC6B4\uB85C\uB4DC,\
				\uB370\uC774\uD130\uCD94\uCD9C,\uC77C\uD544\uC9C0 \uC885\uD569 \uC815\uBCF4,\uD1A0\uC9C0 \uC870\uD68C(\uC77C\uD544\uC9C0),\uAC74\uBB3C \uC870\uD68C(\uC77C\uD544\uC9C0),\uAC00\uACA9 \uC815\uBCF4 \uC870\uD68C(\uC77C\uD544\uC9C0),\uD1A0\uC9C0\uC774\uC6A9\uACC4\uD68D \uC870\uD68C(\uC77C\uD544\uC9C0),\
				\uB3C4\uD615\uC601\uC5ED\uC73C\uB85C \uAC80\uC0C9,\uD589\uC815\uACBD\uACC4\uB85C \uAC80\uC0C9,\uAD6D\uAC00\uBC95\uB839\uC815\uBCF4,\uC815\uBD80\uB514\uB809\uD130\uB9AC,SMS \uBA54\uC2DC\uC9C0
sysSeCode = 05
APIGW.Apikey = tmiKPqf1niMu5rq1VcG49XKIYmhwDJEh

#\uC0D8\uD50C\uC5D0 \uC0AC\uC6A9\uD560 \uBC30\uACBD\uC9C0\uB3C4 (VWORLD, BAROEMAP)
Sample.Basemap = VWORLD
#Sample.Basemap = BAROEMAP

#\uC0D8\uD50C \uC608\uC81C SRID (\uC804\uCCB4 \uC608\uC81C \uB3D9\uC77C\uD558\uAC8C \uC801\uC6A9\uB428 -> 5179, 5186)
#Sample.Srid = 5179
Sample.Srid = 5186

#Kakao Key
AppKey.Kakao = 1292bf8d492b48c32219cd6cb549c276

#\uBE0C\uC774\uC6D4\uB4DC Key
VWorld.ApiKey = 998FA064-9D48-32C2-8DC8-4DBA90793E9F
VWorld.Domain = https://developer.geon.kr

#\uAD6D\uAC00\uACF5\uAC04\uC815\uBCF4 \uD3EC\uD138 Key
Nsdi.ApiKey = 132d391c091bf7d7e0659e

Url.Tif = https://developer.geon.kr
Url.Pbf = https://developer.geon.kr
