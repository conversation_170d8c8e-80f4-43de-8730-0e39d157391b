<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<link href="::OuiUrl::/oui.css" rel="stylesheet">
	<link href="::SmtUrl::/css/common_toolbar.css" rel="stylesheet">
	<link href="::SmtUrl::/css/widgets/moveControl.css" rel="stylesheet">
	<link href="::SmtUrl::/css/widgets/rotationControl.css" rel="stylesheet">

	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
	<script type="text/javascript" src="::OuiUrl::/oui.min.js"></script>
</head>

<body>
	<div id ="map" style="height:550px;"></div>
	<p>Alt+Shift 키를 누른채로 지도를 드래그 해보세요</p>
	<ul class="toolbar">
	    <li class="moveControlWidget" id="moveControlWidget"></li>
	    <li class="rotationControlWidget" id="rotationControlWidget"></li>
	</ul>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::, ::coordy::);
	var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);


	/* 지도 이동 위젯 생성 */
	var moveControlWidget = new oui.MoveControlWidget({
		target: document.getElementById('moveControlWidget'),
	});
	moveControlWidget.addTo(map);
	//지우기함수
	//moveControlWidget.remove();

	/* 지도 회전 위젯 생성  */
	var rotationControlWidget = new oui.RotationControlWidget({
		target: document.getElementById('rotationControlWidget'),
	});
	rotationControlWidget.addTo(map);
	//지우기함수
	//rotationControlWidget.remove();
</script>
</html>
