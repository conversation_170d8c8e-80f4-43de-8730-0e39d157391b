<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<link href="::OuiUrl::/oui.css" rel="stylesheet">

	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
	<script type="text/javascript" src="::OuiUrl::/oui.min.js"></script>
</head>
<body>
	<div id ="map" style="height:550px;"></div>
	<div class="attributeEditorWidget" id="attributeEditorWidget"></div>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::, ::coordy::);
    var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);


	/* oui api 연결 객체 생성  */
	var smtApiClient = oui.HttpClient({
		//api 요청 실패시(500,503) 최대 재시도 횟수
		//maxRetries : 3,
			baseURL: '::smtAPI::', //api 주소
		});
	var commonCodeApi = oui.CommonCodeApi(smtApiClient, {
		crtfckey: '::crtfckey::',
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			commonCodeFunction : 3000,
			getAllDetailCode : 3000,
		}
		 */
		});
	var columnInfoApi = oui.ColumnInfoApi(smtApiClient, {
		lyrId: '::testEditPolygonLayer1Id::',
		userId: '::userId::',
		crtfckey: '::crtfckey::',
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			columnInfoFunction : 3000,
		}
		 */
	});
	var cqlInfoApi = oui.CqlInfoApi(smtApiClient, {
		crtfckey: '::crtfckey::',
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			cqlInfoFunction : 3000,
		}
		 */
	});

	/* 속성설정 위젯 생성 */
	var attributeEditorWidget = new oui.AttributeEditorWidget({
		data : [],
		loadData : '',
		options: {
			createOption : {
				createColumn : true,
			},
			width : 1075,
			height : 350,
			header : false,
		},
      	api: {//데이터 조회 (mode에 따라 layer(feature 정보), geocoding(지오코딩발행조건식편집기) ,object(일반 json 정보))
	        //공통코드조회
	        getCommonCode: commonCodeApi.commonCodeFunction,
	        //상세공통코드 조회 aixos.all
	        getAllDetailCode: commonCodeApi.getAllDetailCode,
	        //별칭 및 컬럼 정보 조회
	        columnInfoFunction: columnInfoApi.columnInfoFunction,
	        //컬럼정보조회 옵션값 변경
	        columnInfoOptionChange: columnInfoApi.changeOption,
	        //cql 정보 조회
	        cqlInfoFunction: cqlInfoApi.cqlInfoFunction,
      	},
		target: document.getElementById('attributeEditorWidget'),
	});
	attributeEditorWidget.addTo(map);

</script>
</html>
