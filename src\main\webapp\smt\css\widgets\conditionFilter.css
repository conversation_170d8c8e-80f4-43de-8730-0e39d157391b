@charset "UTF-8";
/* 속성테이블 위젯 */
.conditionFilter_columnSelectBox, .conditionFilter_operationSelecBox, .conditionFilter_logicSelectBox {
	width : 186px;
	font-size:20px;
}
.conditionFilter_columnNmSpan, .conditionFilter_cqlSpan, .conditionFilter_valueSpan, .conditionFilter_logicSpan  {
	padding-right : 10px;
	font-size : 20px;
}
.conditionFilter_conditionDiv > input{
	margin-left: 5px;
}

.conditionFilter_applyBtn,.conditionFilter_resetBtn, .conditionFilter_addBtn, .conditionFilter_saveBtn
{
    margin-right: 5px;
    word-break: keep-all;
    background: #333;
    align-items: center;
    height: 40px;
    padding: 0 15px;
    border-radius: 4px;
    box-sizing: border-box;
    font-family: '맑은 고딕';
    font-weight: normal;
    font-size: 16px;
    color: #fff;
    vertical-align: top;
    letter-spacing: -1px;
    transition: .4s;
    margin-top : 15px;
    }
 .conditionFilter_queryDiv{
 	margin-top : 15px;
 }
 .conditionFilter_queryListSpan{
 	font-size : 15px;
 }
