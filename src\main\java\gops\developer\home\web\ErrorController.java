package gops.developer.home.web;

import jakarta.servlet.http.HttpServletRequest;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;

@Controller
public class ErrorController {

	@RequestMapping(value = "/error")
	public String error(HttpServletRequest request, Model model) {

		model.addAttribute("erroCode", "Error " + request.getAttribute("jakarta.servlet.error.status_code"));
		Throwable throwable = (Throwable)request.getAttribute("jakarta.servlet.error.exception");
		StringBuilder errorMessage = new StringBuilder();
		errorMessage.append("<ul>");
		while(throwable != null) {
			errorMessage.append("<li>").append(escapeTags(throwable.getMessage())).append("</li>");
			throwable = throwable.getCause();
		}

		errorMessage.append("</ul>");
		model.addAttribute("errorMessage", errorMessage.toString());

		return "error/index.html";
	}

	private String escapeTags(String text) {
		if(text == null) {
			return null;
		}

		return text.replaceAll("<", "&lt;").replaceAll(">", "&gt;");
	}

	@RequestMapping(value = "/error/resourceNotFound")
	public String resourceNotFound() {

		return "error/resourceNotFound.html";
	}

}
