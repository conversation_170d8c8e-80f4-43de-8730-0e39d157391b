<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<link href="::OuiUrl::/oui.css" rel="stylesheet">
	<link href="::SmtUrl::/css/widgets/scaleControl.css" rel="stylesheet">

	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
	<script type="text/javascript" src="::OuiUrl::/oui.min.js"></script>
</head>
<body>
	<div id ="map" style="height:550px;"></div>
	<p>축척 기본단위는 cm 입니다.</p>
	<div id="scaleControl" class="scaleControl"></div>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::, ::coordy::);
	var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);


	/* 축척 위젯 생성 */
	var scaleControlWidget = new oui.ScaleControlWidget({
		options: {
			size: 150,
			scaleInput: true,
		},
		target: document.getElementById('scaleControl'),
	});
	scaleControlWidget.addTo(map);
	//지우기함수
	//scaleControlWidget.remove();
</script>
</html>
