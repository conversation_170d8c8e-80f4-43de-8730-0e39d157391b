<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
</head>
<body>
	<div id="map" class="odf-view" style="height:550px;"></div>
	<input type="button" id="setBasemapDefault" class="onoffBtn" onClick="basemapControl.switchBaseLayer('::basemap_base::')" value="배경지도 원래대로 변경"/>
	<p id="positionStr">자세한 정보는 Console창을 확인하세요</p>
</body>
<script>
	/* 맵 타겟 */
	var mapContainer = document.getElementById('map');

	/* 맵 중심점 */
	var coord = new odf.Coordinate(::coordx::,::coordy::);

	/* 맵객체 옵션 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapOption = "::mapOpt::";
	/*
		* 배경지도 종류
		eMapBasic - 바로e맵 일반 지도
		eMapColor - 바로e맵 색각 지도
		eMapLowV - 바로e맵 큰글씨 지도
		eMapWhite - 바로e맵 백지도
		eMapEnglish - 바로e맵 영어 지도
		eMapChinese - 바로e맵 중어 지도
		eMapJapanese - 바로e맵 일어 지도
		eMapWhiteEdu - 바로e맵 교육용 백지도
		eMapAIR - 바로e맵  항공지도

		* 프록시 사용
		proxyURL: 'proxy.jsp' 프록시 설정
	 */

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var map = new odf.Map(mapContainer, mapOption);

	/* 베이스맵 컨트롤 생성 */
	var basemapControl = new odf.BasemapControl();
	basemapControl.setMap(map);

	//베이스맵에 새로운 그룹 추가
	basemapControl.setGrp('myGrp');
	//베이스맵에 등록된 그룹 제거// 해당 그룹에 속한 베이스레이어가 있다면 같이 제거
	//basemapControl.removeGrp('myGrp');

	//베이스레이어로 추가할 레이어 생성 //베이스그룹이 없으면 추가하여 베이스레이어 생성
	var _wmtsLayer = odf.LayerFactory.produce('geoserver', {
		method : 'get',
		service: 'wmts',
		server: '::WmtsAPI::',
		layer: '::wmtsLayer::',
		crtfckey : '::crtfckey::',
	});
	basemapControl.setBaseLayer('myGrp','customBaseLayer','사용자정의지도',_wmtsLayer);
	map.setCenter(::wmtsCenterCoord::);
	map.setZoom(15);
	//사용자 정의 베이스레이어 제거 //해당 그룹에 베이스레이어가 하나뿐이면 그룹도 함께 삭제
	//basemapControl.removeBaseLayer('temp');

	//베이스맵 컨트롤 리빌드
	basemapControl.rebuildElement();

	console.log("현재배경지도레이어");
	console.log(basemapControl.getPresentBaseLayer());
	console.log("현재배경지도레이어키:"+basemapControl.getPresentBaseLayerKey());
	console.log("배경지도설정가능목록");
	console.log(basemapControl.getSetableBasemapList());
	console.log("배경지도레이어 유무 확인:"+basemapControl.hasBaseLayer(basemapControl.getPresentBaseLayerKey()));
	console.log("배경지도레이어그룹  유무 확인:"+basemapControl.hasGrp('myGrp'));

</script>
</html>
