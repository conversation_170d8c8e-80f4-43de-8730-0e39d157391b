<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<link href="::OuiUrl::/oui.css" rel="stylesheet">

	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
	<script type="text/javascript" src="::OuiUrl::/oui.min.js"></script>
</head>
<body>
	<div id ="map" style="height:550px;"></div>
	<div class="gridWidget" id="gridWidget">
</div>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::, ::coordy::);
    var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);

	/* 테스트 레이어 생성  */
	var wfsLayer = odf.LayerFactory.produce('geoserver', {
		method : 'get',
		server : '::WfsAPI::',
		layer : '::testEditPolygonLayer1::',
		service : 'wfs',
	});
	wfsLayer.setMap(map);
	wfsLayer.fit();


	/* oui api 연결 객체 생성  */
	var smtApiClient = oui.HttpClient({
		//api 요청 실패시(500,503) 최대 재시도 횟수
		//maxRetries : 3,
		baseURL: '::smtAPI::', //api 주소
	});
	var mapApiClient =  oui.HttpClient({
		//api 요청 실패시(500,503) 최대 재시도 횟수
		//maxRetries : 3,
		baseURL: '::mapAPI::', //api 주소
	});
	var commonCodeApi = oui.CommonCodeApi(smtApiClient, {
		crtfckey: '::crtfckey::',
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			commonCodeFunction : 3000,
			getAllDetailCode : 3000,
		}
		 */
	});
	var columnInfoApi = oui.ColumnInfoApi(smtApiClient, {
		lyrId: '::testEditPolygonLayer1Id::',
		userId: '::userId::',
		crtfckey: '::crtfckey::',
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			columnInfoFunction : 3000,
		}
		 */
	});
	var cqlInfoApi = oui.CqlInfoApi(smtApiClient, {
		crtfckey: '::crtfckey::',
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			cqlInfoFunction : 3000,
		}
		 */
	});
	var mapApi = oui.MapApi(mapApiClient, {
		crtfckey: '::crtfckey::',
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			getData : 3000,
			updateData : 3000,
		}
		 */
	});

	/* 속성테이블 위젯 생성  */
	var gridWidget = new oui.GridWidget({
		layer: wfsLayer,
		options: {
        	gridCallback: function (e) {

			},
			alertList: {

			},
	        pagination: true,
	        pageSize: 100,
	        rowSelection: 'multiple',
	        sortable: true,
	        filter: true,
	        mode: 'layer',
	        gridHeight: '400px',
	        gridWidth: '1078px',
	        cellWidth: '',
	        createOption: {
				chart: true,
				geomSearch: true,
				attributeEditor: true,
				modify: false,
				filter: true,
				export: false, //다운로드 옵션 (layerDownloadApi 있을 시 사용가능)
				delete: false,
				insert: false,
				clear: true,
				editMode: false, //편집모드 사용 여부
	        },
	        conditionFilterOption : {
				thema : 'table',
			},
			attributeEditorOption : {
				width : '1050',
				height : '350',
			},
			chartOption : {

			},
		},
		api: {//데이터 조회 (mode에 따라 layer(feature 정보), geocoding(지오코딩발행속성테이블) ,object(일반 json 정보))
			//지오서버 데이터 조회
			getData: mapApi.getData,
			//지오서버 업로드
			updateData: mapApi.updateData,
			//공통코드조회
			getCommonCode: commonCodeApi.commonCodeFunction,
			//상세공통코드 조회 aixos.all
			getAllDetailCode: commonCodeApi.getAllDetailCode,
			//별칭 및 컬럼 정보 조회
			columnInfoFunction: columnInfoApi.columnInfoFunction,
			//컬럼정보조회 옵션값 변경
			columnInfoOptionChange: columnInfoApi.changeOption,
			//레이어다운로드
			//downloadLayer: layerDownloadApi.downloadLayer,
			// cql 정보 조회
			cqlInfoFunction: cqlInfoApi.cqlInfoFunction,
			// cql 옵션 변경
			cqlInfoOptionChange: cqlInfoApi.changeOption,
		},
		target: document.getElementById('gridWidget'),
	});
	gridWidget.addTo(map);

</script>
</html>
