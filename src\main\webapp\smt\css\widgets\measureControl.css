@charset "UTF-8";
.measureControl_toolBtn{
background-image: url(../../images/widget/widget-measure-gray.png);
    min-height: 67px;
    background-repeat: no-repeat;
    background-position: center 10px;
}
.measureControl_toolBtn:hover{
background-image: url(../../images/widget/widget-measure-white.png);
}

.measureControl_measureGrpDiv {
	position: absolute;
    top: 0;
    right: 0;
    padding: 0 10px;
    word-break: keep-all;
    display: flex;
    flex-direction: row;
    border-radius: 3px;
    transition: .2s;
    background: #fff;
    box-shadow: 0.6px 0.8px 4px 0 rgb(0 0 0 / 35%);
    visibility: visible;
    opacity: 1;
    right: 68px;
}
.measureControl_measureGrpDiv > button > span {
	display: block;
    padding-top: 36px;
    letter-spacing: -1px;
    line-height: 15px;
}    
.measureControl_measureGrpDiv > button {
	min-height: 67px;
    background-repeat: no-repeat;
    padding: 0 7px;
    color: #555;
}    

.measureControl_areaBtn{
	background-image: url(../../images/toolbar/ico-measure-02.png);
	    background-position: center 10px;
}

.measureControl_distanceBtn{
	background-image: url(../../images/toolbar/ico-measure-01.png);
	    background-position: center 10px;
}
.measureControl_circleBtn{
	    background-position: center 10px;
		background-image: url(../../images/toolbar/ico-measure-04.png);
}

.measureControl_spotBtn{
	background-image: url(../../images/toolbar/ico-measure-03.png);
	background-position: center 20px;
}

.measureControl_areaBtn:hover{
	background-image: url(../../images/toolbar/ico-measure-02-active.png);
}

.measureControl_distanceBtn:hover{
	background-image: url(../../images/toolbar/ico-measure-01-active.png);
}
.measureControl_circleBtn:hover{
	background-image: url(../../images/toolbar/ico-measure-04-active.png);
}

.measureControl_spotBtn:hover{
	background-image: url(../../images/toolbar/ico-measure-03-active.png);
}