<header class="main-header" th:fragment="headerFragment">
	<!-- header -->
    <header id="header">
        <h1><span>GeOnPaaS<br/>
        <span>개발자센터</span>
        </span>
        	
        </h1>
        <!--gnb-->
        <ul id="gnbMenu" class="gnb">
            <li id="menuGuide"><a th:href="@{/guide}">가이드</a></li>
			<li id="menuDocs"><a th:href="@{/apidocs}">API문서</a></li>
			<li id="menuOdf"><a th:href="@{/mapdocs}">지도문서</a></li>
			<li id="menuSample"><a th:href="@{/sample}">지도예제</a></li>
			<li id="menuWizard"><a th:href="@{/wizard}">지도마법사</a></li>
			<li id="menuWidgetDocs"><a th:href="@{/widgetdocs}">위젯문서</a></li>
			<li id="menuWidgetSample"><a th:href="@{/widgetsample}">위젯예제</a></li>
			<!-- <li id="menuWidgetWizard"><a th:href="@{/widgetwizard}">위젯마법사</a></li> -->
        </ul>
        <ul id="helpMenu" class="helpMenu">
        	<!-- <li id="helpDocs">
        		<button id="helpDocsBtn" onClick="window.open('/menual/GeOnPaaS-MAN-01-사용자매뉴얼-v1.0.pdf', '_blank')">
        		<span>도움말</span>
        		</button>
        		</a>
        	</li> -->
        	<!-- <li id="versionInfo">
	        	<button id="versionInfoBtn" onClick="alert('국문명: 지온파스 v1.0 영문명: GeOnPaaS v1.0')">
	        	<span>버전정보</span>
	        </button>
        	</li> -->
        </ul>
        <!--//gnb-->
    </header>
    <!-- //header -->
</header>