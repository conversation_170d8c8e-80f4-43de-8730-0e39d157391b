<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
</head>
<body>
	<div id="map" class="odf-view" style="height:550px;"></div>
	<div id="evtChk"></div>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::,::coordy::);
	var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);


	/* 측정 도구 컨트롤 생성 */
	var measureControl = new odf.MeasureControl({
		/*
		연속 측정 여부.
		 - true : 연속 측정 기능 활성화. ※ 측정 종료는 clean 함수를 통해서 실행
		 - false : (기본값) 연속 측정 기능 비활성화.
		 */
		continuity : false,

		/*
		거리 측정 표시 옵션
		*/
		displayOption : {
			area : {//면적 측정 표시 옵션
				//면적 측정 소수점 표기 자리수 (소수몇번째자리까지 표출) default : 2
				decimalPoint : 2,
				//면적 측정 단위 변경 제곱미터기준(㎡ -> ㎢ 단위로 변경) ex) 1000000 -> 1000000㎡ 부터 1 ㎢로 표출 default : 1000000
				transformUnit: 1000000,
			},
			distance : {//거리 측정 표시 옵션
				//거리 측정 소수점 표기 자리수 (소수몇번째자리까지 표출) default : 2
				decimalPoint : 2,
				//거리 측정 단위 변경 미터기준(m -> km 단위로 변경) ex) 1000 -> 1000m 부터 1 km로 표출 default : 1000
				transformUnit: 1000,
			},
			round : {// 반경 측정 표시 옵션
				//반경 측정 소수점 표기 자리수 (소수몇번째자리까지 표출) default : 2
				decimalPoint : 2,
				//반경 측정 단위 변경 미터기준(m -> km 단위로 변경) ex) 1000 -> 1000m 부터 1 km로 표출 default : 1000
				transformUnit: 1000,
			}
		},

		// 생성할 툴 배열
		// 설정하지 않으면 모든 툴 생성
		tools : [
			'distance',// 거리 측정 툴
			'area',// 면적측정 툴
			'round',// 원의 면적측정 툴
			'spot',// 좌표 측정 툴
		],

		// 좌표 측정시 사용할 좌표계 (기본값=> 지도의 좌표계)
		// EPSG:4326 => GPS가 사용하는 좌표계
		spotProjection:'EPSG:4326',

		// 툴팁 메세지
		message : {
			// DRAWSTART: '클릭하여 측정을 시작하세요',
			DRAWEND_POLYGON : '[수정한 메세지]클릭하여 폴리곤을 그리거나, 더블클릭하여 그리기를 종료하세요',
			// DRAWEND_LINE: '클릭하여 라인을 그리거나, 더블클릭하여 그리기를 종료하세요',
		},

		// 측정 도형 스타일
		style : {
			fill : {
				color : [ 254, 243, 255, 0.2 ]
			},
			stroke : {
				color : [ 103, 87, 197, 0.7 ],
				width : 2
			},
			image : {
				circle : {
					fill : {
						color : [ 254, 243, 255, 0.2 ]
					},
					stroke : {
						color : [ 103, 87, 197, 0.7 ],
						width : 2
					},
					radius : 5,
				},
			},
			text : {
				textAlign : 'left',
				font : '30px sans-serif',
				fill : {
					color : [ 103, 87, 197, 1 ]
				},
				stroke : {
					color : [ 255, 255, 255, 1 ]
				},
			},
		}
	});
	measureControl.setMap(map);
	/*그리기/측정 초기화 컨트롤 생성*/
	var clearControl = new odf.ClearControl();
	clearControl.setMap(map);
</script>
</html>
