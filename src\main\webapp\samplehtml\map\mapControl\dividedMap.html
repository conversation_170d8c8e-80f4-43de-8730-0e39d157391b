<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
</head>
<body>
	<div id="map" style="height:550px; position:absolute"></div>
	<div style="margin-top: 15px">
		<input type="button" class="onoffOnlyBtn toggle grp1" onclick="setConnect(true)" value="동기화">
		<input type="button" class="onoffOnlyBtn toggle grp1" onclick="setConnect(false)" value="비동기화">
		<input type="button" class="onoffOnlyBtn toggle grp2" onclick="setOn('dualMap',true)" value="2분할 열기">
		<input type="button" class="onoffOnlyBtn toggle grp2" onclick="setOn('quadMap',true)" value="4분할 열기">
		<input type="button" class="onoffOnlyBtn toggle grp2" onclick="setOn('dualMap',false)" value="분할 닫기">
	</div>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::,::coordy::);
	var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);


	/* 배경지도 컨트롤 생성 */
	var basemapControl = new odf.BasemapControl();
	basemapControl.setMap(map);

	/* 줌 컨트롤 생성 */
	var zoomControl = new odf.ZoomControl();
	zoomControl.setMap(map);

	var dmc = new odf.DivideMapControl({
		dualMap : [
			{
				position : 1,
				mapOption : {
					// 해당 분할지도의 basemap 옵션
					// 정의하지 않는 경우, map 객체 생성시 사용한 basemap option 사용
					basemap : {
						::basemapType:: : [ '::basemap_white::' ]
					},
				},
				// 사용할 컨트롤 지정
				// 정의하지 않는 경우, 기본 값 적용(배경지도 컨트롤만 이용)
				controlOption : {
		             basemap: true,// 기본값 true
		             zoom: false,// 기본값 false
		             clear: false,// 기본값 false
		             download: false,// 기본값 false
		             print: false,// 기본값 false
		             overviewmap: false,// 기본값 false
		             draw: false,// 기본값 false
		             measure: false,// 기본값 false
		             move: false,// 기본값 false
				}
		 	}
		 ],
		 threepleMap: [
			{
				// position: 1, //지정안하면 기본 1
				mapOption: {
					basemap: {
						::basemapType:: :['::basemap_white::']
					},
				},
			},
			{
				// position: 2, //지정안하면 기본 3
				mapOption: {
					basemap:{
						::basemapType:: : ['::basemap_color::']
					},
				},
		    }
		],
		quadMap : [
			{
				// position: 1, //지정안하면 기본 1
				mapOption : {
					basemap : {
						::basemapType::  : [ '::basemap_white::' ]
					},
				},
			},
			{
				//position: 2, //지정안하면 기본 3
				mapOption : {
					basemap : {
						::basemapType::  : [ '::basemap_color::' ]
					},
				},
			},
			{
				//position: 4,//지정안하면 기본 4
				mapOption : {
					basemap : {
						::basemapType::  : [ '::basemap_air::' ]
					},
				},
				controlOption : {//사용할 컨트롤 지정
					download : true,
				},
			}
		],
		// 분할지도 상세 생성 옵션
		// 정의하지 않으면 기본 값 적용
		config : {
			//분할지도 내 컨트롤 ui 생성 여부, 기본값 true
			createElementFlag : true,
			//분할지도 영역 조절 기능 사용 여부, 기본값 false
			useAreaControl : true,
			//지도 영역의 최소 크기(단위 px), 분할지도 영역 조절 기능 사용 여부가 true일때 적용. 기본값 100
			minAreaSize : 100,
			// 2분할지도 상세 생성 옵션
			dualMap : {
			 /** 2분할지도 분할 유형.
			   * - 'vertical' : 수직 분할 (기본값)
			   * ┌─┬─┐
			   * │1│2│
			   * └─┴─┘
			   * - 'horizonal' : 수평 분할
			   * ┌───┐
			   * │ 1 │
			   * ├───┤
			   * │ 2 │
			   * └───┘
			   */
				divType : 'vertical'//수직 분할 (기본값)
			},
			//3분할지도 상세 생성 옵션
			threepleMap : {
			 /** 3분할지도 분할 유형
			   * - 'vertical' : 수직 분할 (기본값)
			   * ┌─┬─┬─┐
			   * │1│2│3│
			   * └─┴─┴─┘
			   * - 'horizonal' : 수평 분할
			   * ┌───┐
			   * │ 1 │
			   * ├───┤
			   * │ 2 │
			   * ├───┤
			   * │ 3 │
			   * └───┘
			   * - 'complex-01' : 복합형 1
			   * ┌─┬───┐
			   * │ │ 2 │
			   * │1├───┤
			   * │ │ 3 │
			   * └─┴───┘
			   * - 'complex-02' : 복합형 2
			   * ┌─────┐
			   * │  1  │
			   * ├──┬──┤
			   * │2 │ 3│
			   * └──┴──┘
			   * - 'complex-03' : 복합형 3
			   * ┌──┬─┐
			   * │2 │ │
			   * ├──┤1│
			   * │3 │ │
			   * └──┴─┘
			   * - 'complex-04' : 복합형 4
			   * ┌──┬──┐
			   * │ 1│2 │
			   * ├──┴──┤
			   * │  3  │
			   * └─────┘
			   */
				divType : 'vertical'//수직 분할 (기본값)
			},
			//3분할지도 상세 생성 옵션
			quadMap : {
			 /**
			   * - 'complex' : 수직,수평 분할 (기본값)
			   * ┌───┬───┐
			   * │ 1 │ 2 │
			   * ├───┼───┤
			   * │ 3 │ 4 │
			   * └───┴───┘
			   * - 'vertical' : 수직 분할
			   * ┌─┬─┬─┬─┐
			   * │1│2│3│4│
			   * └─┴─┴─┴─┘
			   * - 'horizonal' : 수평 분할
			   * ┌─────┐
			   * │  1  │
			   * ├─────┤
			   * │  2  │
			   * ├─────┤
			   * │  3  │
			   * ├─────┤
			   * │  4  │
			   * └─────┘
			   */
				divType : 'complex'//수직,수평 분할 (기본값)
			}
		}
	});
	dmc.setMap(map);
	map.setResizable(true);
	function setConnect(flag) {
		dmc.setConnect(flag);
	}

	function setOn(key, flag) {
		dmc.setOn(key, flag);
	}
</script>
</html>
