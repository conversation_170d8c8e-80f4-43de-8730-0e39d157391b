/**
 * index.js
 */
$(document).ready(function () {
    const urlParams = new URLSearchParams(window.location.search);
    const sampleName = urlParams.get('sampleName');

    if (sampleName) {
        menu.init(sampleName, makeMenuJson, loadSample, true);
    }
    else {
        menu.init("그리기/측정도구 위젯", makeMenuJson, loadSample, false);
    }
});

function makeMenuJson() {

    var keys = Object.keys(menuJson);

    for (var i = 0; i < keys.length; i++) {

        var gKey = keys[i];
        var sList = menuJson[gKey].submenu;

        for (var j = 0; j < sList.length; j++) {
            sList[j].grpId = gKey;
            sList[j].target = "";
        }
    }
}

var menuJson = {

    0: {
        name: "제어도구",
        submenu: [
            /*            {
                            "name": "제어도구 전체",
                            "desc": "홈, 그리기, 측정, 저장 등 제어도구 전체 기능 제공",
                            "path": "/samplehtml/widget/mapControlWidget/ControlWidget.html"
                        },*/
            {
                "name": "그리기/측정도구 위젯",
                "desc": "점, 폴리곤, 사각형, 선, 텍스트, 곡선, 원, 버퍼 등 그리기 기능 제공",
                "path": "/samplehtml/widget/mapControlWidget/DrawWidget.html"
            },
            {
                "name": "지도 출력/다운로드 위젯",
                "desc": "현재 지도를 출력, 다운로드(PDF,JPEG 등)를 제공",
                "path": "/samplehtml/widget/mapControlWidget/DownloadWidget.html"
            },
            {
                "name": "배경지도 위젯",
                "desc": "배경지도 변경 기능 제공",
                "path": "/samplehtml/widget/mapControlWidget/BasemapWidget.html"
            },
            {
                "name": "배경지도 최적화",
                "desc": "배경지도 변경시 지도 좌표계 변경하여 조금 더 선명한 배경지도를 이용",
                "path": "/samplehtml/widget/mapControlWidget/BasemapWidget_optimization.html"
            },
            {
                "name": "북마크 관리 위젯",
                "desc": "사용자가 지도 위의 북마크 설정 기능 제공",
                "path": "/samplehtml/widget/mapControlWidget/BookmarkWidget.html"
            },
            {
                "name": "마우스 위치 좌표표시 위젯",
                "desc": "사용자의 마우스 좌표 표출 기능 제공",
                "path": "/samplehtml/widget/mapControlWidget/MousePositionWidget.html"
            },
            {
                "name": "오버뷰 위젯",
                "desc": "지도를 한 눈에 볼 수 있는 오버뷰맵 제공",
                "path": "/samplehtml/widget/mapControlWidget/OverviewWidget.html"
            },
            {
                "name": "지도 이동/회전 위젯",
                "desc": "지도 네비게이션(이전/다음 화면) 및 지도 회전 기능 제공",
                "path": "/samplehtml/widget/mapControlWidget/MoveWidget.html"
            },
            {
                "name": "축척 위젯",
                "desc": "지도의 축척정보 조회 및 축척 설정 기능 제공",
                "path": "/samplehtml/widget/mapControlWidget/ScaleWidget.html"
            },
            {
                "name": "확대/축소 위젯",
                "desc": "지도의 확대/축소 기능 제공",
                "path": "/samplehtml/widget/mapControlWidget/ZoomControlWidget.html"
            },
            {
                "name": "CCTV 뷰  위젯",
                "desc": "국토교통부에서 제공하는 고속도로, 국도 CCTV 영상 제공 위젯",
                "path": "/samplehtml/widget/mapControlWidget/CCTVWidget.html"
            },
            {
                "name": "로드뷰(카카오) 위젯",
                "desc": "카카오에서 제공하는 로드뷰 위젯",
                "path": "/samplehtml/widget/mapControlWidget/RoadViewWidget.html"
            },
            {
                "name": "범례 위젯",
                "desc": "범례 위젯 생성",
                "path": "/samplehtml/widget/mapControlWidget/LegendWidget.html"
            },
        ]
    },
    1: {
        name: "화면제어 위젯",
        submenu: [
            {
                "name": "스와이퍼 위젯",
                "desc": "지도스와이퍼 기능 제공",
                "path": "/samplehtml/widget/viewControlWidget/SwiperControlWidget.html"
            },
            {
                "name": "스와이퍼 위젯(toc,배경지도 기능 有)",
                "desc": "지도스와이퍼 기능 제공",
                "path": "/samplehtml/widget/viewControlWidget/SwiperWidget.html"
            },
            {
                "name": "분할지도 위젯",
                "desc": "지도 분할 생성 기능 제공",
                "path": "/samplehtml/widget/viewControlWidget/DivideMapWidget.html"
            },
            {
                "name": "전체화면 위젯",
                "desc": "지도를 전체화면 모드로 확대 기능 제공",
                "path": "/samplehtml/widget/viewControlWidget/FullScreenWidget.html"
            },

        ]

    },
    2: {
        name: "위치검색 및 지오코딩",
        submenu: [
            {
                "name": "위치검색 위젯",
                "desc": "통합검색, 지번검색, 도로명주소 검색 등의 위치 검색 기능 제공",
                "path": "/samplehtml/widget/addressWidget/AddressSearchWidget.html"

            },
            {
                "name": "행정구역 조회 위젯",
                "desc": "행정구역정보를 조회 하는 기능 제공",
                "path": "/samplehtml/widget/addressWidget/AdministrativeDistrictSearchWidget.html"

            },
        ]
    },
    3: {
        name: "레이어 제어 위젯",
        submenu: [
            {
                "name": "속성설정 위젯",
                "desc": "레이어의 컬럼정보 속성을 설정",
                "path": "/samplehtml/widget/layerControlWidget/AttributeWidget.html"

            },
            {
                "name": "조건식 편집기 위젯",
                "desc": "레이어에 사용자가 설정한 조건으로 레이어를 필터링",
                "path": "/samplehtml/widget/layerControlWidget/ConditionFilterWidget.html"

            },
            {
                "name": "차트 위젯(사용자 정의 데이터)",
                "desc": "사용자가 입력한 데이터로 차트를 제공",
                "path": "/samplehtml/widget/layerControlWidget/ChartWidget.html"
            },
            {
                "name": "차트 위젯(레이어 데이터)",
                "desc": "사용자가 입력한 데이터로 차트를 제공",
                "path": "/samplehtml/widget/layerControlWidget/ChartWidget_layerData.html"

            },
            {
                "name": "타임 슬라이더 위젯",
                "desc": "레이어를 시간 단위로 설정하여 표출",
                "path": "/samplehtml/widget/layerControlWidget/TimeSliderWidget.html"

            },
            {
                "name": "스타일 위젯(WMS 레이어)",
                "desc": "WMS 레이어의 스타일을 조회하고 수정할 수 있는 기능을 제공",
                "path": "/samplehtml/widget/layerControlWidget/StyleWidget_WMS.html"

            },
            {
                "name": "스타일 위젯(WFS 레이어)",
                "desc": "WFS 레이어의 스타일을 조회하고 수정할 수 있는 기능을 제공",
                "path": "/samplehtml/widget/layerControlWidget/StyleWidget_WFS.html"

            },
            {
                "name": "속성테이블 위젯",
                "desc": "레이어 속성정보를 테이블로 표출",
                "path": "/samplehtml/widget/layerControlWidget/GridWidget.html"

            },
            {
                "name": "피쳐속성폼 위젯",
                "desc": "피쳐를 클릭하여 피쳐 속성정보 및 공간정보를 수정하거나 피쳐를 삭제할 수 있는 기능",
                "path": "/samplehtml/widget/layerControlWidget/FeatureAttributeFormWidget.html"

            },
            {
                "name": "레이어 검색 위젯",
                "desc": "레이어를 조회/삭제하고 지도에 추가",
                "path": "/samplehtml/widget/layerControlWidget/LayerSearchWidget.html"

            },
            {
                "name": "레이어 업로드 위젯",
                "desc": "사용자레이어를 업로드",
                "path": "/samplehtml/widget/layerControlWidget/LayerUploadWidget.html"

            },
            {
                "name": "팝업 위젯(기본)",
                "desc": "피쳐정보를 팝업으로 표출",
                "path": "/samplehtml/widget/layerControlWidget/PopupWidget.html"

            },
            {
                "name": "팝업 위젯(레이어 필터링)",
                "desc": "피쳐정보를 팝업으로 표출",
                "path": "/samplehtml/widget/layerControlWidget/PopupWidget_layerFilter.html"

            },
            {
                "name": "TOC 위젯(기본)",
                "desc": "레이어 검색/업로드기능 및 레이어조작 등 다양하게 레이어를 관리",
                "path": "/samplehtml/widget/layerControlWidget/TocWidget.html"

            },
            {
                "name": "TOC 위젯(컨텐츠 셋팅)",
                "desc": "레이어 검색/업로드기능 및 레이어조작 등 다양하게 레이어를 관리 (레이어 컨텐츠를 미리 셋팅)",
                "path": "/samplehtml/widget/layerControlWidget/TocWidgetWithAPI.html"

            },
            {
                "name": "분석 위젯",
                "desc": "다양한 공간정보 분석",
                "path": "/samplehtml/widget/layerControlWidget/SpatialAnalysisWidget.html"

            },
            {
                "name": "LX맵 위젯",
                "desc": "브이월드에서 제공하는 lx맵을 지도위에 on/off 할 수 있는 위젯",
                "path": "/samplehtml/widget/layerControlWidget/LimsControlWidget.html"

            },
            {
                "name": "연속지적도형정보 위젯",
                "desc": "브이월드에서 제공하는 연속지적도형정보를 지도위에 on/off 할 수 있는 위젯",
                "path": "/samplehtml/widget/layerControlWidget/LsmdControlWidget.html"

            },
        ]

    },

};
window.srcScriptCnt = 0;

function isLoadEnd(loadEndCnt, sampleType) {
    if (window.srcScriptCnt === loadEndCnt) {
        var iDoc = $("#sample-container")[0].contentWindow.document;
        var iScripts = iDoc.scripts;
        var isLen = iScripts.length;

        for (var i = isLen - 1; i > -1; i--) {
            var _javascript = iDoc.createElement('script');
            if (!iScripts[i].src) {
                _javascript.innerHTML = iScripts[i].innerHTML;
                iScripts[i].remove();
                iDoc.head.appendChild(_javascript);


                window.setTimeout(function () {
                    var divMainMaps = iDoc.querySelectorAll('.mainMap .odf-map');
                    var maps = iDoc.getElementsByClassName("odf-map");

                    var target = (divMainMaps.length === 1 ? divMainMaps[0] : maps);

                    if (target.length) {
                        for (var j = 0; j < target.length; j++) {
                            target[j].style['max-height'] = sampleType == 'TOC' ? "650px" : sampleType == 'ANALYSIS' ? "650px" : "550px"
                            target[j].querySelector(".ol-viewport").style['max-height'] = sampleType == 'TOC' ? "650px" : sampleType == 'ANALYSIS' ? "650px" : "550px"
                        }
                    } else {
                        target.style['max-height'] = sampleType == 'TOC' ? "650px" : sampleType == 'ANALYSIS' ? "650px" : "550px"
                        target.querySelector(".ol-viewport").style['max-height'] = sampleType == 'TOC' ? "650px" : sampleType == 'ANALYSIS' ? "650px" : "550px"
                    }
                    window.setTimeout(function () {
                        iDoc.querySelector('body').style.overflow = 'hidden';
                        $("#sample-container")[0].style.height = iDoc.body.scrollHeight + 'px';
                        if (iDoc.getElementsByClassName("odf-dividemap-container").length > 0) {
                            iDoc.getElementsByClassName("odf-dividemap-container")[0].style.height = sampleType == 'TOC' ? "650px" : sampleType == 'ANALYSIS' ? "650px" : "550px"
                        }
                    }, 100);
                }, 100);


            }
        }
    }
}

function containerResize() {
    var iDoc = $("#sample-container")[0].contentWindow.document;
    iDoc.body.style.overflow = 'auto';
    $("#sample-container")[0].style.height = iDoc.body.scrollHeight + 'px';
    iDoc.body.style.overflow = 'hidden';
}

var sampleHtml;

function loadSample(menuName, target) {

    const nowMenu = menu.getMenu(menuName);

    $.ajax({
        url: rootpath + nowMenu.path,
        type: 'GET',
        cache: false,
        async: false, // $.get(async: true) 로 부르는 경우 clipboard.js 동작 안함
        success: function (html) {

            const sampleHtml = processWidgetHtml(html, nowMenu);
            const styleLinks = appendStylesheets();
            const iDoc = $("#sample-container")[0].contentWindow.document;

            // 기본 height 지정 '550px'
            $("#sample-container")[0].style.height = '550px';

            // Iframe 크기 조정
            const observer = new MutationObserver(async (mutations, obs) => {
                if (iDoc.body && iDoc.body.children.length > 0) {
                    obs.disconnect();
                    await initializeMapContainer(iDoc);
                }
            });

            observer.observe(iDoc, {
                childList: true,
                subtree: true
            });

            iDoc.open();
            iDoc.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>${nowMenu.name}</title>
                    ${styleLinks}
                </head>
                <body>
                    ${sampleHtml}
                </body>
                </html>
            `);
            iDoc.close();
        },
        error: function (xhr, status, err) {

            console.log(xhr);
            console.log(status);
            console.log(err);
        }
    });
    updateUI(nowMenu, sampleHtml, target)
}

function resetClipboard() {

    var copyButton = $(".code-toolbar button:last")[0];
    var clip = new ClipboardJS(copyButton, {
        text: function () {
            return sampleHtml;
        }
    });
}

function eventShowLive() {

    var html = '<iframe src="' + rootpath + '/live"></iframe>';

    $("#showLive").off("click");
    $("#showLive").click(function () {
        var popWindow = modal.showModal("modal-live", html, sampleHtml);
    });
}

function processWidgetHtml(html, nowMenu) {
    // 아래 들여쓰기는 개발자지원센터에서 볼 때 깔끔하게 보이도록 설정한 것입니다. 수정하지 말아주세요.
    let mapOption =
        `{
		center : coord,
		zoom : 11,
		projection : 'EPSG:${sampleSrid}',
		`;
    let proxyOption = proxyUseAt === 'Y' ?
        `proxyURL: 'proxyUrl.jsp',
		proxyParam: 'url',
		`
        :
        `//proxyURL: 'proxyUrl.jsp',
		//proxyParam: 'url',
		`;
    let basemapOption = BasemapUtils.generateBasemapOption(sampleBasemap, {
        baroEMapURL: baroEMapURL,
        baroEMapAirURL: baroEMapAirURL,
        baroEMapKey: baroEMapKey,
        vWorldURL: vWorldURL,
        customConfig: customBasemapConfig
    });

    mapOption += proxyOption + basemapOption;
    html = html.replace(/::mapOpt::/gi, mapOption);
    html = html.replace('var mapOption = "{', 'var mapOption = {');
    html = BasemapUtils.replaceBasemapInHtml(html, sampleBasemap);

    if (nowMenu.path.includes('SpatialAnalysisWidget')) {
        // 분석 위젯 예제 전용 중심점
        if (sampleSrid === '5179') {
            html = html.replace(/::coordx::/gi, 964390.7575764904);
            html = html.replace(/::coordy::/gi, 1755204.828463073);
        } else {
            // sampleSrid === '5186'
            html = html.replace(/::coordx::/gi, 209576.17356159375);
            html = html.replace(/::coordy::/gi, 355040.40034509933);
        }
    } else {
        if (sampleSrid === '5179') {
            html = html.replace(/::coordx::/gi, 955156.7761);
            html = html.replace(/::coordy::/gi, 1951925.0984);
        } else {
            // sampleSrid === '5186'
            html = html.replace(/::coordx::/gi, 199312.9996);
            html = html.replace(/::coordy::/gi, 551784.6924);
        }
    }
    // vWorld WMS 예제에서 사용하는 bbox 옵션
    if(sampleSrid === '5179') {
        html = html.replace(/::bbox::/gi, '{{minx}},{{miny}},{{maxx}},{{maxy}}');
    } else {
        // 5186
        html = html.replace(/::bbox::/gi, '{{miny}},{{minx}},{{maxy}},{{maxx}}');
    }

    html = html.replace(/"::viewOption::"/, viewOption);
    html = html.replace(/"::zoomOption::"/, zoomOption);
    html = html.replace(/::polygonLayer1::/gi, polygonLayer1);
    html = html.replace(/::polygonLayer2::/gi, polygonLayer2);
    html = html.replace(/::pointLayer::/gi, pointLayer);
    html = html.replace(/::lineLayer::/gi, lineLayer);
    html = html.replace(/::wmtsLayer::/gi, wmtsLayer);
    html = html.replace(/::hotspotLayer::/gi, hotspotLayer);

    html = html.replace(/::testPointLayer1::/gi, testPointLayer1);
    html = html.replace(/::testPointLayer1Id::/gi, testPointLayer1Id);
    html = html.replace(/::testPointLayer1Nm::/gi, testPointLayer1Nm);
    html = html.replace(/::testLineLayer1::/gi, testLineLayer1);
    html = html.replace(/::testLineLayer1Id::/gi, testLineLayer1Id);
    html = html.replace(/::testLineLayer1Nm::/gi, testLineLayer1Nm);
    html = html.replace(/::testPolygonLayer1::/gi, testPolygonLayer1);
    html = html.replace(/::testPolygonLayer1Id::/gi, testPolygonLayer1Id);
    html = html.replace(/::testPolygonLayer1Nm::/gi, testPolygonLayer1Nm);
    html = html.replace(/::testEditPolygonLayer1::/gi, testEditPolygonLayer1);
    html = html.replace(/::testEditPolygonLayer1Id::/gi, testEditPolygonLayer1Id);
    html = html.replace(/::userMapId::/gi, userMapId);

    html = html.replace(/::vWorldApiKey::/gi, vWorldApiKey);
    html = html.replace(/::vWorldDomain::/gi, vWorldDomain);

    html = html.replace(/::srid::/gi, sampleSrid);
    html = html.replace(/::basemapType::/gi, basemapType);
    html = html.replace(/::basemap_base::/gi, basemap_base);
    html = html.replace(/::basemap_air::/gi, basemap_air);
    html = html.replace(/::basemap_white::/gi, basemap_white);
    html = html.replace(/::basemap_color::/gi, basemap_color);
    html = html.replace(/::basemap_etc::/gi, basemap_etc);
    html = html.replace(/::userId::/gi, userId);

    if (input) {
        html = html.replace("}\";", "	, baroEMapURL: 'http://map.ngii.go.kr/openapi/Gettile.do' \n		, baroEMapKey: '3C0B5534B557FF993B74EE0E86CA22DD' \n	};");
    } else {
        html = html.replace('}";', '};');
    }

    sampleHtml = html.replace(/::OdfUrl::/gi, OdfUrl)
        .replace(/::OuiUrl::/gi, OuiUrl)
        .replace(/::SmtUrl::/gi, SmtUrl)
        .replace(/::APIGW::/gi, APIGW)
        .replace(/::DeveloperUrl::/gi, DeveloperUrl)
        .replace(/::WfsAPI::/gi, WfsAPI)
        .replace(/::WmsAPI::/gi, WmsAPI)
        .replace(/::WmtsAPI::/gi, WmtsAPI)
        .replace(/::smtAPI::/gi, smtAPI)
        .replace(/::mapAPI::/gi, mapAPI)
        .replace(/::analysisAPI::/gi, analysisAPI)
        .replace(/::publishAPI::/gi, publishAPI)
        .replace(/::coordAPI::/gi, coordAPI)
        .replace(/::geocodingAPI::/gi, addrgeoAPI)
        .replace(/::crtfckey::/gi, crtfckey)
        .replace(/::kakaoAppKey::/gi, kakaoAppKey)
        .replace(/::naverAppKey::/gi, naverAppKey)
        .replace(/::googleAppKey::/gi, googleAppKey)
        .replace(/::cctvApiKey::/gi, cctvApiKey)
        .replace(/::cctvApiURL::/gi, cctvApiURL);

    return sampleHtml;
}

function appendStylesheets() {
    const styleSheets = Array.from(document.styleSheets);

    return styleSheets
        .filter(ss => ss.href)
        .map(ss => `<link type="text/css" rel="stylesheet" href="${ss.href}">`)
        .join('\n');
}

function updateUI(nowMenu, sampleHtml, target) {
    $("#sample-code").text(sampleHtml);
    Prism.highlightAll();
    resetClipboard();
    menu.scrollToId(target, true);
    eventShowLive();
    $("#sample-title").text(nowMenu.name);
    $("#sample-desc").text(nowMenu.desc);
    menu.menuSelected(nowMenu.name);
}

function initializeMapContainer(iDoc) {
    return new Promise((resolve) => {
        window.setTimeout(() => {
            // 메인 맵과 일반 맵 요소 선택
            const divMainMaps = iDoc.querySelectorAll('.mainMap .odf-map');
            const maps = iDoc.getElementsByClassName("odf-map");

            // 타겟 결정 (메인맵이 하나면 메인맵, 아니면 일반 맵)
            const target = (divMainMaps.length === 1 ? divMainMaps[0] : maps);

            // 추가 스타일 설정을 위한 지연 실행
            window.setTimeout(() => {
                // body overflow 설정
                iDoc.querySelector('body').style.overflow = 'hidden';

                // iframe 컨테이너 높이 설정
                $("#sample-container")[0].style.height = iDoc.body.scrollHeight + 'px';

                // divide map 컨테이너 높이 설정
                const divideMapContainer = iDoc.getElementsByClassName("odf-dividemap-container")[0];
                if (divideMapContainer) {
                    divideMapContainer.style.height = '550px';
                }

                resolve();
            }, 100);
        }, 100);
    });
}
