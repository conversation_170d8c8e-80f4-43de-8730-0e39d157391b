<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>GeOnPaas ui widgets: ColorFactory</title>
    
      <link type="text/css" rel="stylesheet" href="styles/vendor/prism-tomorrow-night.css">
    
    <link type="text/css" rel="stylesheet" href="styles/styles.css">
    
    
    <style>
      :root {
      
      
        --nav-width: 370px;
      
      
        --nav-heading-margin-top: 0.5em;
      
      }
    </style>
    
</head>
<body>

<header class="layout-header">
  
  <h1>
    <a href="./index.html">
      GeOnPaas ui widgets
    </a>
  </h1>
  <nav class="layout-nav">
    <ul><li class="nav-heading">Classes</li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="BasemapControl.html">BasemapControl</a></span><span class="nav-desc"><p>배경지도 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="BookmarkControl.html">BookmarkControl</a></span><span class="nav-desc"><p>북마크 컨트롤 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ClearControl.html">ClearControl</a></span><span class="nav-desc"><p>지도 그리기 이벤트 초기화 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ColorFactory.html">ColorFactory</a></span><span class="nav-desc"><p>색 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Control.html">Control</a></span><span class="nav-desc"><p>사용자 정의 컨트롤 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Coordinate.html">Coordinate</a></span><span class="nav-desc"><p>좌표 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DivideMapControl.html">DivideMapControl</a></span><span class="nav-desc"><p>지도 분할 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DownloadControl.html">DownloadControl</a></span><span class="nav-desc"><p>다운로드 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DrawControl.html">DrawControl</a></span><span class="nav-desc"><p>그리기 도구 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Easing.html">Easing</a></span><span class="nav-desc"><p>애니메이션 효과</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="event.html">event</a></span><span class="nav-desc"><p>이벤트 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Extent.html">Extent</a></span><span class="nav-desc"><p>영역 관련 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Feature.html">Feature</a></span><span class="nav-desc"><p>Feature 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FeatureFactory.html">FeatureFactory</a></span><span class="nav-desc"><p>Feature 생성을 위한 FeatureFactory 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FormatFactory.html">FormatFactory</a></span><span class="nav-desc"></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FullScreenControl.html">FullScreenControl</a></span><span class="nav-desc"><p>전체화면 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="HomeControl.html">HomeControl</a></span><span class="nav-desc"><p>홈 이동 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Layer.html">Layer</a></span><span class="nav-desc"><p>레이어 관리 클래스로, 레이어는 odf.LayerFactory를 통해서만 생성가능하다.</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LayerFactory.html">LayerFactory</a></span><span class="nav-desc"><p>레이어 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LayerInfoControl.html">LayerInfoControl</a></span><span class="nav-desc"><p>레이어 정보 조회 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Map.html">Map</a></span><span class="nav-desc"><p>지도 생성, 조작, 컴퍼넌트, 레이어 추가 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Marker.html">Marker</a></span><span class="nav-desc"><p>마커 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MeasureControl.html">MeasureControl</a></span><span class="nav-desc"><p>지도 측정 도구 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MousePositionControl.html">MousePositionControl</a></span><span class="nav-desc"><p>마우스 좌표 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MoveControl.html">MoveControl</a></span><span class="nav-desc"><p>현재 화면 기준으로 이전/다음 화면 이동 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="OverviewMapControl.html">OverviewMapControl</a></span><span class="nav-desc"><p>인덱스맵 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Popup.html">Popup</a></span><span class="nav-desc"><p>팝업 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="PrintControl.html">PrintControl</a></span><span class="nav-desc"><p>프린트 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Projection.html">Projection</a></span><span class="nav-desc"><p>좌표 변환 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="RotationControl.html">RotationControl</a></span><span class="nav-desc"><p>화면을 회전 시키는 기능
alt + shift 드래그로 지도 회전</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ScaleControl.html">ScaleControl</a></span><span class="nav-desc"><p>축척 표시 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="SLD.html">SLD</a></span><span class="nav-desc"><p>WMS 스타일 관리 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Style.html">Style</a></span><span class="nav-desc"><p>스타일 관리 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="StyleFactory.html">StyleFactory</a></span><span class="nav-desc"><p>스타일 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="StyleFunction.html">StyleFunction</a></span><span class="nav-desc"><p>스타일 Function 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="SwiperControl.html">SwiperControl</a></span><span class="nav-desc"><p>지도 스와이퍼 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ZipControl.html">ZipControl</a></span><span class="nav-desc"><p>Server없이 Layer 생성하는 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ZoomControl.html">ZoomControl</a></span><span class="nav-desc"><p>지도 줌 설정클래스</p></span></li></ul><li class="nav-heading"><a href="global.html">Globals</a></li>
  </nav>
</header>


<main class="layout-main ">
  <div class="container">
    <p class="page-kind">Class</p>
    <h1 class="page-title">ColorFactory</h1>
    




<section>


<header class="class">


    
        
        <!-- <h2>ColorFactory</h2> -->

        

        

        
            
            <div class="class-description"><p>색 생성 클래스</p></div>
        
    
</header>

<article>
    <div class="container-overview">



    
        





    


    
    <h3 class="subtitle">Constructor</h3>
    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="ColorFactory">new ColorFactory<span class="signature">()</span><span class="return-type-signature"></span>
    </h4>

















<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    
    </div>

    

    

     

    

    


    

    
        <h3 class="subtitle">Methods</h3>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".packColor">packColor<span class="signature">(color)</span><span class="return-type-signature"> &rarr; {Array.&lt;number>}</span>
    </h4>





<div class="method-description">
    
    <p>모든 유형의 색상 값을 두개의 부동소수점 배열로 묶는다(압축)</p>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">color</span>
            

            
                


    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    |

    <span class="param-type">
        <code>string</code>
    </span>
    

            

            

            

            <div class="param-description"><p>rgb/rgba, hex 색상 값</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Array.&lt;number></code>
            
            
                <p>두개의 부동소수점 배열로 압축한 값</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".produce">produce<span class="signature">(type, size, Alpha, order)</span><span class="return-type-signature"> &rarr; {Array.&lt;<a href="global.html#odf_rgba_color">odf_rgba_color</a>>}</span>
    </h4>





<div class="method-description">
    
    <p>colorArray 생성</p>
<pre class="prettyprint source lang-javascript"><code>odf.ColorFactory.produce('red',10);//빨간색 계열로 10개의 색상 값 추출
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">type</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#odf_color_type">odf_color_type</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>생성할 색의 계열</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">size</span>
            

            
                


    <span class="param-type">
        <code>number</code>
    </span>
    

            

            

            

            <div class="param-description"><p>생성할 색의 개수 1~</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">Alpha</span>
            

            
                


    <span class="param-type">
        <code>number</code>
    </span>
    

            

            

            

            <div class="param-description"><p>투명도 0~1</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">order</span>
            

            
                


    <span class="param-type">
        <code>boolean</code>
    </span>
    

            

            

            

            <div class="param-description"><p>색 표현 순서(true =&gt; 옅은색에서 진한색 순, false=&gt;진한색 색에서 옅은색 순)</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Array.&lt;odf_rgba_color></code>
            
            
                <p>생성된 색의 배열</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".produceGradientColor">produceGradientColor<span class="signature">(fromColor, toColor, size, alpha)</span><span class="return-type-signature"> &rarr; {Array.&lt;<a href="global.html#odf_rgba_color">odf_rgba_color</a>>}</span>
    </h4>





<div class="method-description">
    
    <p>fromColor에서 toColor까지의 색상 추출</p>
<pre class="prettyprint source lang-javascript"><code>odf.ColorFactory.getGradientColor([255,100,0],[0,0,0],20,1);//alpha값이 1(불투명)이면서 [255,100,0]에서 [0,0,0]까지의 색상 값을 추출
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">fromColor</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#odf_rgba_color">odf_rgba_color</a></code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odf_hex_color">odf_hex_color</a></code>
    </span>
    

            

            

            
                
            

            <div class="param-description"><p>시작 색상(rgb 또는 hex 색상)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">toColor</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#odf_rgba_color">odf_rgba_color</a></code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odf_hex_color">odf_hex_color</a></code>
    </span>
    

            

            

            
                
            

            <div class="param-description"><p>종료 색상(rgb 또는 hex 색상)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">size</span>
            

            
                


    <span class="param-type">
        <code>number</code>
    </span>
    

            

            

            
                
            

            <div class="param-description"><p>생성할 색의 개수</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">alpha</span>
            

            
                


    <span class="param-type">
        <code>number</code>
    </span>
    

            

            

            
                
                    <span class="param-default">
                        1
                    </span>
                
            

            <div class="param-description"><p>rgba의 alpha값. 0이면 투명, 1이면 불투명, 0~1 사이의 값</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Array.&lt;odf_rgba_color></code>
            
            
                <p>생성된 색의 배열</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".produceGradientColorWitPropotion">produceGradientColorWitPropotion<span class="signature">(colors, size, alpha)</span><span class="return-type-signature"> &rarr; {Array.&lt;<a href="global.html#odf_rgba_color">odf_rgba_color</a>>}</span>
    </h4>



    <h4 class="method-heading">Summary</h4>
    <p>비율을 이용한 색상 추출(2개 이상 추출)</p>
<pre class="prettyprint source lang-javascript"><code>odf.ColorFactory.getGradientColorWithWeight([
 {color :[0,0,0]   ,propotion:0}, //첫번째 색상 정보
 {color :[255,0,0] ,propotion:0.35489},//두번째 색상 정보
 {color :[0,255,0] ,propotion:1},//세번째 색상 정보
],//색상정보
50,//생성할 색의 개수
1//rgba의 alpha값.</code></pre>



<div class="method-description">
    <h4 class="method-heading">Description</h4>
    <p>비율을 이용한 색상 추출(2개 이상 추출)</p>
<pre class="prettyprint source lang-javascript"><code>odf.ColorFactory.getGradientColorWithWeight([
 {color :[0,0,0]   ,propotion:0}, //첫번째 색상 정보
 {color :[255,0,0] ,propotion:0.35489},//두번째 색상 정보
 {color :[0,255,0] ,propotion:1},//세번째 색상 정보
],//색상정보
50,//생성할 색의 개수
1//rgba의 alpha값. 0이면 투명, 1이면 불투명, 0~1 사이의 값
);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">colors</span>
            

            
                


    <span class="param-type">
        <code>Array.&lt;<a href="global.html#odf_graiden_color_info">odf_graiden_color_info</a>></code>
    </span>
    

            

            

            
                
            

            <div class="param-description"><p>비율 정보를 포함한 색상정보</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">size</span>
            

            
                


    <span class="param-type">
        <code>number</code>
    </span>
    

            

            

            
                
            

            <div class="param-description"><p>생성할 색의 개수 (2 이상)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">alpha</span>
            

            
                


    <span class="param-type">
        <code>number</code>
    </span>
    

            

            

            
                
                    <span class="param-default">
                        1
                    </span>
                
            

            <div class="param-description"><p>rgba의 alpha값. 0이면 투명, 1이면 불투명, 0~1 사이의 값</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Array.&lt;odf_rgba_color></code>
            
            
                <p>생성된 색의 배열</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".produceRandomColor">produceRandomColor<span class="signature">(size, alpha)</span><span class="return-type-signature"> &rarr; {Array.&lt;<a href="global.html#odf_rgba_color">odf_rgba_color</a>>}</span>
    </h4>





<div class="method-description">
    
    <p>랜덤 색상 조회</p>
<pre class="prettyprint source lang-javascript"><code>odf.ColorFactory.produceRandomColor(10,1);//alpha값이 1(불투명)인 랜덤색상 10개 추출
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">size</span>
            

            
                


    <span class="param-type">
        <code>number</code>
    </span>
    

            

            

            
                
                    <span class="param-default">
                        1
                    </span>
                
            

            <div class="param-description"><p>생성할 색의 개수</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">alpha</span>
            

            
                


    <span class="param-type">
        <code>number</code>
    </span>
    

            

            

            
                
            

            <div class="param-description"><p>rgba의 alpha값. 0이면 투명, 1이면 불투명, 0~1 사이의 값</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Array.&lt;odf_rgba_color></code>
            
            
                <p>생성된 색의 배열</p>
            
        </li>
    
    </ul>


















    </article>

        
    

    

    
</article>

</section>




  </div>
</main>

<footer class="layout-footer">
  <div class="container">
    Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.11</a> on Tue Jan 21 2025 11:05:51 GMT+0900 (대한민국 표준시)
  </div>
</footer>



<script src="scripts/prism.dev.js"></script>
</body>
</html>