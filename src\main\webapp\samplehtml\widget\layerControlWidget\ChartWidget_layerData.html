<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<link href="::OuiUrl::/oui.css" rel="stylesheet">

	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
	<script type="text/javascript" src="::OuiUrl::/oui.min.js"></script>
	<script type="text/javascript" src="::DeveloperUrl::/vendor/amcharts4/core.js"></script>
	<script type="text/javascript" src="::DeveloperUrl::/vendor/amcharts4/charts.js"></script>
	<script type="text/javascript" src="::DeveloperUrl::/vendor/amcharts4/animated.js"></script>
	<script type="text/javascript" src="::DeveloperUrl::/vendor/amcharts4/forceDirected.js"></script>
	<script type="text/javascript" src="::DeveloperUrl::/vendor/amcharts4/frozen.js"></script>
	<script type="text/javascript" src="::DeveloperUrl::/vendor/amcharts4/wordCloud.js"></script>
</head>
<body>
	<div id ="map" style="height:550px;"></div>
	<p>amcharts4 라이브러리 필요</p>
	<div class="chartWidget" id="chartWidget" style="background-color:white;"></div>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::,::coordy::);
	var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);


	/* oui api 연결 객체 생성  */
	var smtApiClient = oui.HttpClient({
		//api 요청 실패시(500,503) 최대 재시도 횟수
		//maxRetries : 3,
		baseURL: '::smtAPI::', //api 주소
	});
	var attributeApi = oui.AttributeApi(smtApiClient, {
		crtfckey: '::crtfckey::',
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			getAggregate : 3000,
		}
		 */
	});
	var columnInfoApi = oui.ColumnInfoApi(smtApiClient, {
		crtfckey: '::crtfckey::',
		userId : 'geonuser',
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			selectUniqueColumn : 3000,
			selectColumn : 3000,
		}
		 */
	});

	/*차트 위젯(레이어 데이터) 생성 [레이어 데이터 조회하여 집계]*/
	var chartWidget = new oui.ChartWidget({
		// 차트 생성 옵션
		options : {
			// 사용 가능한 집계함수 목록 정의
			// 정의하지 않으면 모두 사용하는 것으로 간주
			// useAggregateUnit : ['SUM'/*합계*/,'COUNT'/*개수*/,'AVG'/*평균*/,'MIN'/*최소값*/,'MAX'/*최대값*/],
			/** 차트 생성 방식
		    * - 'show' ui 제공 없이 바로 뷰잉
		    * - 'ui' 차트 설정 ui 표출 (기본값)
			*/
			chartCreateMode : 'ui',
			// 집계 결과 chart popup 설정값
			createObject : {
				/*
				//[★★★★'chartCreateMode' 값이 'show'일때 사용되는 옵션 start ★★★★
				//차트 제목(필수값 x)
				title : '차트 제목',
				// 차트 타입
				// * - 'piechart' 파이차트
				// * - 'xychart' 막대차트
				// * - 'xychartHorizon' 막대차트(가로)
				chartType : 'piechart',
				//차트 카테고리 필드 (x aixs)
				targetCol : 'country',
				//차트 value 필드 (y axis)
				value : 'litres',
				// * - 'SUM' : 합계
			    // * - 'COUNT' : 개수
			    // * - 'AVG' : 평균
			    // * - 'MIN' : 최소값
			    // * - 'MAX' : 최대값

				aggregateUnitType : 'SUM',
				//[★★★★'chartCreateMode' 값이 'show'일때 사용되는 옵션 end ★★★★
					*/
				// 차트  width  * default : 880
				width : 800,
				// 차트  height * default : 500
				height : 400,

			},
			//모달 헤더창 생성 여부 (default : false)
			header : false,
			//범례 표시 여부 true, false (default : false)
			legend : true,
			/** 범례 위치 설정 (default : 'right')
		    * - 'bottom' : 범례를 하단에 생성
		    * - 'right : 범례를 우측에 생성
		    * - 'top'  : 범례를 상단에 생성
		    * - 'left' : 범례를 좌측에 생성
			*/
			legendPosition : 'right',
			// 라벨 표시 여부 (default : true)
			label : false,
			// 차트 생성 시 객체정보(타이틀, 차트 타입, 차트 카테고리, 차트 필드)를 받을 callback 함수
			/*applyCallback : function(parameter){
				console.log(parameter);
			},*/
			// api를 통해 집계할 경우, 사용
			 dataSourceType : 'api',

			/*
			//사용자 지정 색상 범위 및 데이터 범위 지정
			customColor : {
				// 사용자 지정 색상 배열 (colorLengend 와 배열 길이 같아야함)
				colorList : [[255,219,219],[255,191,191],[255,159,159],[255,128,128],[255,96,96],[255,64,64],[255,32,32],[255,0,0]],
				// 사용자 지정 색상의 값이 분류될 범위 배열 (colorList 와 배열 길이 같아야함)
				colorLegend :  [3,24.714285714285715,46.42857142857143,68.14285714285714,89.85714285714286,111.57142857142858,133.2857142857143,155],
			},*/
			apiParam : {
				//발행된 레이어 명칭 (ex. 저장소명:레이어명)
				typeName : '::testEditPolygonLayer1::',
				//레이어 ID(DB에서 관리하는 레이어 ID)
				lyrId : '::testEditPolygonLayer1Id::',
			}
		},
		 api : {
			//레이어 속성 데이터 집계
			getAggregate: attributeApi.getAggregate,
			// 레이어 컬럼 유일값 조회
			selectUniqueColumn: columnInfoApi.selectUniqueColumn,
			// 레이어 컬럼 정보 조회 api
			selectColumn : columnInfoApi.selectColumn,
		},
		target: document.getElementById('chartWidget'),
	});
	chartWidget.addTo(map);

</script>
</html>
