<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
</head>
<body>
	<div id="map" class="odf-view" style="height:550px;"></div>
	<div class="infoArea" style="margin-top: 15px;">
		<div id="featureInfo"></div>
	</div>
</body>
<style>
	/* 피처 속성 정보 조회 css */
	#featureInfo>div,#featureInfo>div>div{
		padding : 10px;
	}
	#featureInfo>div>div{
		overflow-x : auto;
	}
	#featureInfo h5.title,featureInfo h6.title{
		font-weight : bold;
		margin-bottom: 5px;
	}
	#featureInfo table{
		border-collapse: collapse;
	}
	#featureInfo td {
		border: 1px solid #949494 !important;
	  padding: 3px !important;
	  text-align: center;
	}
</style>
<script>

	// 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.)
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::,::coordy::);
	var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);


	// 빈 벡터 레이어 생성
	// LayerFactory의 produce 함수는 option이 다양하니 개발자지원센터 '지도문서'를 확인하세요
	var emptyLayer = odf.LayerFactory.produce('empty'/*레이어를 생성하기위 한 테이터 호출 방법*/, {}/*레이어 생성을 위한 옵션*/);
	emptyLayer.setMap(map);


	// 빈 벡터 레이어에 피처 추가
	// geojson 형식으로 피처 생성하여 레이어에 추가
	var geojsonFeature = odf.FeatureFactory.fromGeoJson({
		geometry : {
			type : 'Point',
			coordinates : [ ::coordx::,::coordy:: ]
		},
		properties : {
			name : 'Point1',
		},
	});
	emptyLayer.addFeature(geojsonFeature);
	// console.log(geojsonFeature.getProperties());
	// 피처를 geojson 형태로 변환
	// console.log(geojsonFeature.toGeoJson());

	// WKT 형식으로 피처 생성하여 레이어에 추가
	var wktFeature = odf.FeatureFactory.fromWKT('Point(::coordx:: ::coordy::)');
	wktFeature.setProperties({
		name : 'Point2'
	})
	emptyLayer.addFeature(wktFeature);
	// 피처를 wkt 형태로 변환
	// console.log(wktFeature.featureToWKT());

	// 일반 피처 생성하여 레이어에 추가
	var feature = odf.FeatureFactory.produce({
		geometryType: 'point',
		coordinates: [::coordx::,::coordy::],
		properties : {
			name : 'Point3',
		}
	});
	emptyLayer.addFeature(feature);
	// console.log(feature.getProperties());



	// 해당 layer가 한눈에 보이는 보여주는 extent로 화면 위치 이동 및 줌 레벨 변경
	emptyLayer.fit(true);
	map.setZoom(18);

	// 레이어 삭제
	// map.removeLayer(emptyLayer.getODFId());

	// 레이어 on/off
	// map.switchLayer(emptyLayer.getODFId()/*odf id*/, false/*on/off여부*/);

	// 레이어 z-index 조절
	// map.setZIndex(emptyLayer.getODFId(), 0);

	// 레이어 가시범위 설정
	// emptyLayer.setMinZoom(10);
	// emptyLayer.setMaxResolution(152.70292183870401);
	// emptyLayer.setMaxZoom(18);
	// emptyLayer.setMinResolution(0.5964957884324376);

	// 레이어 투명도 조절
	// emptyLayer.setOpacity(0.5);




















	// geojson 피처 속성 조회
	odf.event.addListener(map, 'click', function (evt) {
		//selectFeatureOnClick 서버가 없는 레이어의 경우, 이 메서트로 피처 정보 조회
		var result = map.selectFeatureOnClick(evt);
		var info = {};

		result.forEach(({feature,layer})=>{
			var layerId = layer.getODFId();
			if(!info[layerId]){
				info[layerId] = {
						features : []
				}
			}
			info[layerId].features.push(feature);
		})
		info = Object.entries(info);

		constructInfoDiv(info, 'featureInfo');

		//iframe 크기 조절
		if (parent.window.containerResize)
			parent.window.containerResize();
	}.bind(this));

	function constructInfoDiv(list, selector) {
		selector = document.getElementById(selector)
		selector.innerHTML = '';

		for (var i = 0; i < list.length; i++) {
			var [layerId, layerItem] = list[i];
			var layerDiv = document.createElement('div');
			var layerTitle = document.createElement('h5');
			layerTitle.classList.add('title')
			layerTitle.innerHTML = layerId;
			layerDiv.append(layerTitle);

			var featureLen = layerItem.features.length;

			for (var j = 0; j < featureLen; j++) {
				var featureDiv = document.createElement('div');
				var featureTitle = document.createElement('h6');
				featureTitle.classList.add('title')
				featureTitle.innerHTML = `도형-${j + 1}`;
				featureDiv.append(featureTitle);

				constructFeatureInfoTable(layerItem.features[j],featureDiv)

				layerDiv.append(featureDiv);
			}

			selector.append(layerDiv);
		}

	}

	function constructFeatureInfoTable(feature, target) {

		var featureTable = document.createElement('table');
		var properties = Object.entries(feature.getProperties());
		var thead = document.createElement('thead');
		var tbody = document.createElement('tbody');

		var headerRow = document.createElement('tr');
		var bodyRow = document.createElement('tr');

		for (var i = 0; i < properties.length; i++) {
			if(properties[i][0]!=='geometry'){
				var headerTd =  document.createElement('td');
				headerTd.innerText = properties[i][0];
				headerRow.append(headerTd);

				var bodyTd = document.createElement('td');
				bodyTd.innerText = properties[i][1]?properties[i][1]:'-';
				bodyRow.append(bodyTd);
			}
		}

		thead.append(headerRow);
		tbody.append(bodyRow);
		featureTable.append(thead);
		featureTable.append(tbody);
		target.append(featureTable);

	}
</script>
</html>
