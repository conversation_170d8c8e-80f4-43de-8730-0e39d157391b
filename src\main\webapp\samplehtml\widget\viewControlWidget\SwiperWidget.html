<!DOCTYPE HTML>
<html>
<head>
    <meta charset="utf-8">
    <link href="::OdfUrl::/odf.css" rel="stylesheet">
    <link href="::OuiUrl::/oui.css" rel="stylesheet">
    <link href="::SmtUrl::/css/common_toolbar.css" rel="stylesheet">
    <link href="::SmtUrl::/css/widgets/swiper.css" rel="stylesheet">
    <link href="::SmtUrl::/css/widgets/basemap.css" rel="stylesheet">

    <script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
    <script type="text/javascript" src="::OuiUrl::/oui.min.js"></script>
</head>
<body>
<div id ="map" style="height:550px;"></div>
<!-- 위젯의 target파라미터에 target 태그를 넘기면, 툴바 button 형식으로 만들어지는 위젯이있고(그리기도구/배경지도위젯 등 ),
        위젯의 기능이 타겟에 만들어지는 위젯(북마크/타임슬라이더/스와이퍼 위젯)이 있습니다.
        이때, 툴바 button 형식으로 만들어지지 않는 위젯을 툴바형식으로 만들기 위한 html 과 script가 추가되어있습니다. -->
<ul class="toolbar">
    <li class="swiperWidget" id="swiperWidget"></li>
</ul>
</body>

<script>

    /* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
    var mapContainer = document.getElementById('map');
    var coord = new odf.Coordinate(::coordx::, ::coordy::);
    var mapOption = "::mapOpt::";
    var map = new odf.Map(mapContainer, mapOption);


    /* oui api 연결 객체 생성  */
    var basemapApiClient = oui.HttpClient({
        baseURL: '::smtAPI::', //api 주소
    });
    var basemapApi = oui.BasemapApi(basemapApiClient, {
        crtfckey: '::crtfckey::',
        /*
        //timeout 설정
        //timeout : 3000,
        timeout  :{
            getBasemapList : 3000,
        }
         */
    });


    /* wms 레이어 생성 */
    var wmsLayer = odf.LayerFactory.produce('geoserver', {
        method : 'get',
        server : '::WmsAPI::',
        layer : '::polygonLayer1::',
        service : 'wms',
    });
    wmsLayer.setMap(map);
    wmsLayer.fit();

    /* wfs 레이어 생성 */
    var wfsLayer = odf.LayerFactory.produce('geoserver', {
        method : 'get',
        server : '::WfsAPI::',
        layer : '::polygonLayer2::',
        service : 'wfs',
    });
    wfsLayer.setMap(map);



    /* 스와이퍼 위젯 생성  */
    var swiperControlWidget = new oui.SwiperWidget({
        odf,
        options: {
            widgetTarget: document.querySelector('.mapArea'),
            createCallback: function () {
                document.querySelector('.toolbar').style.display = 'none'
            },
            removeCallback: function () {
                document.querySelector('.toolbar').style.display = 'block'
            },
            widgets: {
                basemap: {
                    options: {
                        useImage: true,// 이미지 사용여부
                        toolboxPosition: 'left',// toolbox 표현 방향
                        thema: 'gallary',
                        useNoDisplay: true,//빈지도 사용여부
                        useHybrid: true,
                        optimization: true,
                    },
                    api: {
                        getBasemapList: basemapApi.getBasemapList,
                    }
                },
                toc: {
                    options: {
                        groupHeight: 38,
                        layerHeight: 38,
                        getContentList: () => {
                            //기존 toc에서 컨텐츠 목록 받는 함수를 리턴합니다.
                            return [
                                {
                                    linkedLayer : wmsLayer,
                                    lyrGroupSeCode : '02'

                                },
                                {
                                    linkedLayer : wfsLayer,
                                    lyrGroupSeCode : '02'
                                }
                            ];
                        }
                    },
                    api: {
                    }
                }
            }
        },
        target: document.querySelector("#swiperWidget")
    });

    swiperControlWidget.addTo(map);
    //지우기함수
    //swiperControlWidget.remove();


</script>
</html>
