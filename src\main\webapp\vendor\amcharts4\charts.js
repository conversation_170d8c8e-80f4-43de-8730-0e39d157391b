/**
 * @license
 * Copyright (c) 2018 amCharts (<PERSON><PERSON><PERSON>, <PERSON><PERSON>)
 *
 * This sofware is provided under multiple licenses. Please see below for
 * links to appropriate usage.
 *
 * Free amCharts linkware license. Details and conditions:
 * https://github.com/amcharts/amcharts4/blob/master/LICENSE
 *
 * One of the amCharts commercial licenses. Details and pricing:
 * https://www.amcharts.com/online-store/
 * https://www.amcharts.com/online-store/licenses-explained/
 *
 * If in doubt, contact <NAME_EMAIL>
 *
 * PLEASE DO NOT REMOVE THIS COPYRIGHT NOTICE.
 * @hidden
 */
am4internal_webpackJsonp(["689e"],{XFs4:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n={};i.d(n,"GaugeChartDataItem",function(){return F}),i.d(n,"GaugeChart",function(){return Y}),i.d(n,"RadarChartDataItem",function(){return L}),i.d(n,"RadarChart",function(){return S}),i.d(n,"XYChartDataItem",function(){return r.b}),i.d(n,"XYChart",function(){return r.a}),i.d(n,"SerialChartDataItem",function(){return W.b}),i.d(n,"SerialChart",function(){return W.a}),i.d(n,"PieChart3DDataItem",function(){return E}),i.d(n,"PieChart3D",function(){return G}),i.d(n,"PieChartDataItem",function(){return X.b}),i.d(n,"PieChart",function(){return X.a}),i.d(n,"SlicedChart",function(){return U}),i.d(n,"SlicedChartDataItem",function(){return K}),i.d(n,"FlowDiagramDataItem",function(){return ge}),i.d(n,"FlowDiagram",function(){return ye}),i.d(n,"SankeyDiagramDataItem",function(){return Ae}),i.d(n,"SankeyDiagram",function(){return Ie}),i.d(n,"ChordDiagramDataItem",function(){return _e}),i.d(n,"ChordDiagram",function(){return ke}),i.d(n,"TreeMapDataItem",function(){return Fe}),i.d(n,"TreeMap",function(){return Ye}),i.d(n,"XYChart3DDataItem",function(){return qe}),i.d(n,"XYChart3D",function(){return Ke}),i.d(n,"ChartDataItem",function(){return Z.b}),i.d(n,"Chart",function(){return Z.a}),i.d(n,"LegendDataItem",function(){return ae.b}),i.d(n,"Legend",function(){return ae.a}),i.d(n,"LegendSettings",function(){return ae.c}),i.d(n,"HeatLegend",function(){return Ue.a}),i.d(n,"SeriesDataItem",function(){return Ze.b}),i.d(n,"Series",function(){return Ze.a}),i.d(n,"XYSeriesDataItem",function(){return Qe.b}),i.d(n,"XYSeries",function(){return Qe.a}),i.d(n,"LineSeriesDataItem",function(){return s.b}),i.d(n,"LineSeries",function(){return s.a}),i.d(n,"LineSeriesSegment",function(){return Je.a}),i.d(n,"CandlestickSeriesDataItem",function(){return et}),i.d(n,"CandlestickSeries",function(){return tt}),i.d(n,"OHLCSeriesDataItem",function(){return nt}),i.d(n,"OHLCSeries",function(){return at}),i.d(n,"ColumnSeriesDataItem",function(){return Se.b}),i.d(n,"ColumnSeries",function(){return Se.a}),i.d(n,"StepLineSeriesDataItem",function(){return ot}),i.d(n,"StepLineSeries",function(){return st}),i.d(n,"RadarSeriesDataItem",function(){return c}),i.d(n,"RadarSeries",function(){return p}),i.d(n,"RadarColumnSeriesDataItem",function(){return ut}),i.d(n,"RadarColumnSeries",function(){return ht}),i.d(n,"PieSeriesDataItem",function(){return M.b}),i.d(n,"PieSeries",function(){return M.a}),i.d(n,"FunnelSeries",function(){return mt}),i.d(n,"FunnelSeriesDataItem",function(){return ft}),i.d(n,"PyramidSeries",function(){return xt}),i.d(n,"PyramidSeriesDataItem",function(){return vt}),i.d(n,"PictorialStackedSeries",function(){return Pt}),i.d(n,"PictorialStackedSeriesDataItem",function(){return bt}),i.d(n,"PieTick",function(){return Ct.a}),i.d(n,"FunnelSlice",function(){return pt}),i.d(n,"PieSeries3DDataItem",function(){return B}),i.d(n,"PieSeries3D",function(){return z}),i.d(n,"TreeMapSeriesDataItem",function(){return Re}),i.d(n,"TreeMapSeries",function(){return je}),i.d(n,"ColumnSeries3DDataItem",function(){return Ee}),i.d(n,"ColumnSeries3D",function(){return Ge}),i.d(n,"ConeSeriesDataItem",function(){return Dt}),i.d(n,"ConeSeries",function(){return Tt}),i.d(n,"CurvedColumnSeries",function(){return _t}),i.d(n,"CurvedColumnSeriesDataItem",function(){return Vt}),i.d(n,"AxisDataItem",function(){return kt.b}),i.d(n,"Axis",function(){return kt.a}),i.d(n,"Grid",function(){return x.a}),i.d(n,"AxisTick",function(){return Lt.a}),i.d(n,"AxisLabel",function(){return St.a}),i.d(n,"AxisLine",function(){return Rt.a}),i.d(n,"AxisFill",function(){return f.a}),i.d(n,"AxisRenderer",function(){return y.a}),i.d(n,"AxisBreak",function(){return jt.a}),i.d(n,"AxisBullet",function(){return A.a}),i.d(n,"ValueAxisDataItem",function(){return Le.b}),i.d(n,"ValueAxis",function(){return Le.a}),i.d(n,"CategoryAxisDataItem",function(){return T.b}),i.d(n,"CategoryAxis",function(){return T.a}),i.d(n,"CategoryAxisBreak",function(){return wt.a}),i.d(n,"DateAxisDataItem",function(){return Nt.b}),i.d(n,"DateAxis",function(){return Nt.a}),i.d(n,"DurationAxisDataItem",function(){return Ft.b}),i.d(n,"DurationAxis",function(){return Ft.a}),i.d(n,"DateAxisBreak",function(){return Yt.a}),i.d(n,"ValueAxisBreak",function(){return Wt.a}),i.d(n,"AxisRendererX",function(){return We.a}),i.d(n,"AxisRendererY",function(){return D.a}),i.d(n,"AxisRendererRadial",function(){return _}),i.d(n,"AxisLabelCircular",function(){return P.a}),i.d(n,"AxisRendererCircular",function(){return I}),i.d(n,"AxisFillCircular",function(){return v}),i.d(n,"GridCircular",function(){return b}),i.d(n,"AxisRendererX3D",function(){return Xe}),i.d(n,"AxisRendererY3D",function(){return Me}),i.d(n,"Tick",function(){return dt.a}),i.d(n,"Bullet",function(){return se.a}),i.d(n,"LabelBullet",function(){return me}),i.d(n,"CircleBullet",function(){return Xt}),i.d(n,"ErrorBullet",function(){return Mt}),i.d(n,"XYChartScrollbar",function(){return Ht.a}),i.d(n,"ClockHand",function(){return N}),i.d(n,"FlowDiagramNode",function(){return re}),i.d(n,"FlowDiagramLink",function(){return ce}),i.d(n,"SankeyNode",function(){return ve}),i.d(n,"SankeyLink",function(){return Pe}),i.d(n,"ChordNode",function(){return Te}),i.d(n,"ChordLink",function(){return Ve}),i.d(n,"NavigationBarDataItem",function(){return qt}),i.d(n,"NavigationBar",function(){return Kt}),i.d(n,"Column",function(){return He.a}),i.d(n,"Candlestick",function(){return $e}),i.d(n,"OHLC",function(){return it}),i.d(n,"RadarColumn",function(){return lt}),i.d(n,"Column3D",function(){return ze}),i.d(n,"ConeColumn",function(){return It}),i.d(n,"CurvedColumn",function(){return Ot}),i.d(n,"XYCursor",function(){return Ut.a}),i.d(n,"Cursor",function(){return Zt.a}),i.d(n,"RadarCursor",function(){return Qt});var a=i("m4/l"),r=i("0Mwj"),o=i("tjMS"),s=i("v36H"),l=i("aCit"),u=i("Gg2j"),h=i("hGwe"),c=function(e){function t(){var t=e.call(this)||this;return t.className="RadarSeriesDataItem",t.setLocation("dateX",0,0),t.setLocation("dateY",0,0),t.setLocation("categoryX",0,0),t.setLocation("categoryY",0,0),t.applyTheme(),t}return Object(a.c)(t,e),t}(s.b),p=function(e){function t(){var t=e.call(this)||this;return t.className="RadarSeries",t.connectEnds=!0,t.applyTheme(),t}return Object(a.c)(t,e),t.prototype.validate=function(){this.chart.invalid&&this.chart.validate(),e.prototype.validate.call(this)},t.prototype.createDataItem=function(){return new c},t.prototype.getPoint=function(e,t,i,n,a,r,o){r||(r="valueX"),o||(o="valueY");var s=this.yAxis.getX(e,i,a,o),l=this.yAxis.getY(e,i,a,o),h=u.getDistance({x:s,y:l});0==h&&(h=1e-5);var c=this.xAxis.getAngle(e,t,n,r),p=this.chart.startAngle,d=this.chart.endAngle;return c<p||c>d?void 0:{x:h*u.cos(c),y:h*u.sin(c)}},t.prototype.addPoints=function(e,t,i,n,a){var r=this.getPoint(t,i,n,t.locations[i],t.locations[n]);r&&e.push(r)},t.prototype.getMaskPath=function(){var e=this.yAxis.renderer;return h.arc(e.startAngle,e.endAngle-e.startAngle,e.pixelRadius,e.pixelInnerRadius)},t.prototype.drawSegment=function(t,i,n){var a=this.yAxis.renderer;this.connectEnds&&360==Math.abs(a.endAngle-a.startAngle)&&(this.dataFields[this._xOpenField]||this.dataFields[this._yOpenField]||this.stacked)&&(i.push(i[0]),n.length>0&&n.unshift(n[n.length-1])),e.prototype.drawSegment.call(this,t,i,n)},Object.defineProperty(t.prototype,"connectEnds",{get:function(){return this.getPropertyValue("connectEnds")},set:function(e){this.setPropertyValue("connectEnds",e,!0)},enumerable:!0,configurable:!0}),t.prototype.positionBulletReal=function(e,t,i){var n=this.xAxis,a=this.yAxis;(t<n.start||t>n.end||i<a.start||i>a.end)&&(e.visible=!1),e.moveTo(this.xAxis.renderer.positionToPoint(t,i))},t.prototype.setXAxis=function(t){e.prototype.setXAxis.call(this,t),this.updateRendererRefs()},t.prototype.setYAxis=function(t){e.prototype.setYAxis.call(this,t),this.updateRendererRefs()},t.prototype.updateRendererRefs=function(){var e=this.xAxis.renderer,t=this.yAxis.renderer;e.axisRendererY=t},t}(s.a);l.c.registeredClasses.RadarSeries=p,l.c.registeredClasses.RadarSeriesDataItem=c;var d=i("C6dT"),g=i("FzPm"),y=i("Meme"),f=i("8EhG"),m=i("Mtpk"),v=function(e){function t(t){var i=e.call(this,t)||this;return i.className="AxisFillCircular",i.element=i.paper.add("path"),i.radius=Object(o.c)(100),i.applyTheme(),i}return Object(a.c)(t,e),t.prototype.draw=function(){if(e.prototype.draw.call(this),!this.__disabled&&!this.disabled&&this.axis){var t=this.axis.renderer;this.fillPath=t.getPositionRangePath(this.startPosition,this.endPosition,this.radius,m.hasValue(this.innerRadius)?this.innerRadius:t.innerRadius,this.cornerRadius),this.path=this.fillPath}},Object.defineProperty(t.prototype,"innerRadius",{get:function(){return this.getPropertyValue("innerRadius")},set:function(e){this.setPercentProperty("innerRadius",e,!0,!1,10,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"radius",{get:function(){return this.getPropertyValue("radius")},set:function(e){this.setPercentProperty("radius",e,!0,!1,10,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"cornerRadius",{get:function(){return this.getPropertyValue("cornerRadius")},set:function(e){this.setPropertyValue("cornerRadius",e,!0)},enumerable:!0,configurable:!0}),t}(f.a);l.c.registeredClasses.AxisFillCircular=v;var x=i("AaJ4"),b=function(e){function t(){var t=e.call(this)||this;return t.className="GridCircular",t.pixelPerfect=!1,t.applyTheme(),t}return Object(a.c)(t,e),Object.defineProperty(t.prototype,"innerRadius",{get:function(){return this.getPropertyValue("innerRadius")},set:function(e){this.setPercentProperty("innerRadius",e,!0,!1,10,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"radius",{get:function(){return this.getPropertyValue("radius")},set:function(e){this.setPercentProperty("radius",e,!0,!1,10,!1)},enumerable:!0,configurable:!0}),t}(x.a);l.c.registeredClasses.GridCircular=b;var P=i("IbTV"),C=i("v9UT"),A=i("5xph"),I=function(e){function t(){var t=e.call(this)||this;return t.pixelRadiusReal=0,t.layout="none",t.className="AxisRendererCircular",t.isMeasured=!1,t.startAngle=-90,t.endAngle=270,t.useChartAngles=!0,t.radius=Object(o.c)(100),t.isMeasured=!1,t.grid.template.location=0,t.labels.template.location=0,t.labels.template.radius=15,t.ticks.template.location=0,t.ticks.template.pixelPerfect=!1,t.tooltipLocation=0,t.line.strokeOpacity=0,t.applyTheme(),t}return Object(a.c)(t,e),t.prototype.setAxis=function(t){var i=this;e.prototype.setAxis.call(this,t),t.isMeasured=!1;var n=t.tooltip;n.adapter.add("dx",function(e,t){var n=C.svgPointToSprite({x:t.pixelX,y:t.pixelY},i);return i.pixelRadius*Math.cos(Math.atan2(n.y,n.x))-n.x}),n.adapter.add("dy",function(e,t){var n=C.svgPointToSprite({x:t.pixelX,y:t.pixelY},i);return i.pixelRadius*Math.sin(Math.atan2(n.y,n.x))-n.y})},t.prototype.validate=function(){this.chart&&this.chart.invalid&&this.chart.validate(),e.prototype.validate.call(this)},Object.defineProperty(t.prototype,"axisLength",{get:function(){return 2*Math.PI*this.pixelRadius},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"radius",{get:function(){return this.getPropertyValue("radius")},set:function(e){this.setPercentProperty("radius",e,!1,!1,10,!1)&&this.axis&&this.axis.invalidate()},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"pixelRadius",{get:function(){return C.relativeRadiusToValue(this.radius,this.pixelRadiusReal)||0},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"innerRadius",{get:function(){var e=this.chart,t=this.getPropertyValue("innerRadius");if(e){if(m.hasValue(t)){if(t instanceof o.a&&e){var i=e.mr,n=t.value;n=Math.max(i*n,i-Math.min(e.plotContainer.innerHeight,e.plotContainer.innerWidth))/i,t=Object(o.c)(100*n)}}else(t=e.innerRadius)instanceof o.a&&e&&(t=Object(o.c)(t.value*e.innerRadiusModifyer*100));return t}},set:function(e){this.setPercentProperty("innerRadius",e,!1,!1,10,!1)&&this.axis&&this.axis.invalidate()},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"useChartAngles",{get:function(){return this.getPropertyValue("useChartAngles")},set:function(e){this.setPropertyValue("useChartAngles",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"pixelInnerRadius",{get:function(){return C.relativeRadiusToValue(this.innerRadius,this.pixelRadiusReal)||0},enumerable:!0,configurable:!0}),t.prototype.positionToPoint=function(e,t){m.isNumber(t)||(t=1);var i=this.positionToCoordinate(e),n=this.startAngle+(this.endAngle-this.startAngle)*i/this.axisLength,a=this.pixelRadius,r=this.pixelInnerRadius;if(this.axisRendererY){var o=u.fitToRange(this.axisRendererY.positionToCoordinate(t),0,1/0);return{x:o*u.cos(n),y:o*u.sin(n)}}return{x:u.cos(n)*r+(a-r)*u.cos(n)*t,y:u.sin(n)*r+(a-r)*u.sin(n)*t}},t.prototype.positionToAngle=function(e){var t,i=this.axis,n=(this.endAngle-this.startAngle)/(i.end-i.start);return t=i.renderer.inversed?this.startAngle+(i.end-e)*n:this.startAngle+(e-i.start)*n,u.round(t,3)},t.prototype.angleToPosition=function(e){var t,i=this.axis,n=(this.endAngle-this.startAngle)/(i.end-i.start);return t=i.renderer.inversed?i.end-(e-this.startAngle)/n:(e-this.startAngle)/n+i.start,u.round(t,5)},t.prototype.updateAxisLine=function(){var e=this.pixelRadius,t=this.startAngle,i=this.endAngle,n=u.min(360,i-t);this.line.path=h.moveTo({x:e*u.cos(t),y:e*u.sin(t)})+h.arcTo(t,n,e,e)},t.prototype.updateGridElement=function(e,t,i){t+=(i-t)*e.location;var n=this.positionToPoint(t);if(m.isNumber(n.x)&&m.isNumber(n.y)&&e.element){var a=u.DEGREES*Math.atan2(n.y,n.x),r=C.relativeRadiusToValue(m.hasValue(e.radius)?e.radius:Object(o.c)(100),this.pixelRadius),s=C.relativeRadiusToValue(e.innerRadius,this.pixelRadius);e.zIndex=0;var l=C.relativeRadiusToValue(m.isNumber(s)?s:this.innerRadius,this.pixelRadiusReal,!0);m.isNumber(l)||(l=0),e.path=h.moveTo({x:l*u.cos(a),y:l*u.sin(a)})+h.lineTo({x:r*u.cos(a),y:r*u.sin(a)})}this.toggleVisibility(e,t,0,1)},t.prototype.updateTickElement=function(e,t,i){t+=(i-t)*e.location;var n=this.positionToPoint(t);if(e.element){var a=this.pixelRadius,r=u.DEGREES*Math.atan2(n.y,n.x),o=e.length;e.inside&&(o=-o),e.zIndex=1,e.path=h.moveTo({x:a*u.cos(r),y:a*u.sin(r)})+h.lineTo({x:(a+o)*u.cos(r),y:(a+o)*u.sin(r)})}this.toggleVisibility(e,t,0,1)},t.prototype.updateBullet=function(e,t,i){var n=.5;e instanceof A.a&&(n=e.location),t+=(i-t)*n;var a=this.positionToPoint(t),r=this.pixelRadius,o=u.DEGREES*Math.atan2(a.y,a.x);a={x:r*u.cos(o),y:r*u.sin(o)},this.positionItem(e,a),this.toggleVisibility(e,t,0,1)},t.prototype.updateLabelElement=function(e,t,i,n){m.hasValue(n)||(n=e.location),t+=(i-t)*n,e.fixPosition(this.positionToAngle(t),this.pixelRadius),e.zIndex=2,this.toggleVisibility(e,t,this.minLabelPosition,this.maxLabelPosition)},t.prototype.fitsToBounds=function(e){return!0},Object.defineProperty(t.prototype,"startAngle",{get:function(){return this.getPropertyValue("startAngle")},set:function(e){this.setPropertyValue("startAngle",e)&&(this.invalidateAxisItems(),this.axis&&this.axis.invalidateSeries())},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"endAngle",{get:function(){return this.getPropertyValue("endAngle")},set:function(e){this.setPropertyValue("endAngle",e)&&(this.invalidateAxisItems(),this.axis&&this.axis.invalidateSeries())},enumerable:!0,configurable:!0}),t.prototype.getPositionRangePath=function(e,t,i,n,a){var r="";if(m.isNumber(e)&&m.isNumber(t)){m.hasValue(i)||(i=this.radius),e=u.max(e,this.axis.start),(t=u.min(t,this.axis.end))<e&&(t=e);var o=C.relativeRadiusToValue(i,this.pixelRadius),s=C.relativeRadiusToValue(n,this.pixelRadius,!0),l=this.positionToAngle(e),c=this.positionToAngle(t)-l;r=h.arc(l,c,o,s,o,a)}return r},t.prototype.createGrid=function(){return new b},t.prototype.createFill=function(e){return new v(e)},t.prototype.createLabel=function(){return new P.a},t.prototype.pointToPosition=function(e){var t=u.fitAngleToRange(u.getAngle(e),this.startAngle,this.endAngle);return this.coordinateToPosition((t-this.startAngle)/360*this.axisLength)},t}(y.a);l.c.registeredClasses.AxisRendererCircular=I;var D=i("OXm9"),T=i("VB2N"),O=i("Vk33"),V=i("hD5A"),_=function(e){function t(){var t=e.call(this)||this;return t._chart=new V.d,t.pixelRadiusReal=0,t.className="AxisRendererRadial",t.isMeasured=!1,t.startAngle=-90,t.endAngle=270,t.minGridDistance=30,t.gridType="circles",t.axisAngle=-90,t.isMeasured=!1,t.layout="none",t.radius=Object(o.c)(100),t.line.strokeOpacity=0,t.labels.template.horizontalCenter="middle",t._disposers.push(t._chart),t.applyTheme(),t}return Object(a.c)(t,e),t.prototype.validate=function(){this.chart&&this.chart.invalid&&this.chart.validate(),e.prototype.validate.call(this)},Object.defineProperty(t.prototype,"axisLength",{get:function(){return this.pixelRadius-this.pixelInnerRadius},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"radius",{get:function(){return this.getPropertyValue("radius")},set:function(e){this.setPercentProperty("radius",e,!1,!1,10,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"pixelRadius",{get:function(){return C.relativeRadiusToValue(this.radius,this.pixelRadiusReal)||0},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"innerRadius",{get:function(){var e=this.chart,t=this.getPropertyValue("innerRadius");if(e)if(m.hasValue(t)){if(t instanceof o.a&&e){var i=e.mr,n=t.value;n=Math.max(i*n,i-Math.min(e.plotContainer.innerHeight,e.plotContainer.innerWidth))/i,t=Object(o.c)(100*n)}}else(t=e.innerRadius)instanceof o.a&&e&&(t=Object(o.c)(t.value*e.innerRadiusModifyer*100));return t},set:function(e){this.setPercentProperty("innerRadius",e,!1,!1,10,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"pixelInnerRadius",{get:function(){return C.relativeRadiusToValue(this.innerRadius,this.pixelRadiusReal)||0},enumerable:!0,configurable:!0}),t.prototype.positionToPoint=function(e,t){var i=u.fitToRange(this.positionToCoordinate(e),0,1/0);return{x:i*u.cos(this.axisAngle),y:i*u.sin(this.axisAngle)}},t.prototype.updateAxisLine=function(){this.line.path=h.moveTo({x:this.pixelInnerRadius*u.cos(this.axisAngle),y:this.pixelInnerRadius*u.sin(this.axisAngle)})+h.lineTo({x:this.pixelRadius*u.cos(this.axisAngle),y:this.pixelRadius*u.sin(this.axisAngle)});var e=this.axis.title;e.valign="none",e.horizontalCenter="middle",e.verticalCenter="bottom",e.y=-this.axisLength/2;var t=90;this.opposite?this.inside||(t=-90):this.inside&&(t=-90),e.rotation=t},t.prototype.updateGridElement=function(e,t,i){t+=(i-t)*e.location;var n,a=this.positionToPoint(t),r=u.getDistance(a),o=this.startAngle,s=this.endAngle,l=this.chart;if(m.isNumber(r)&&e.element&&l){var c=l.xAxes.getIndex(0),p=0,d=l.series.getIndex(0);if(d&&(p=d.dataItems.length),"polygons"==this.gridType&&p>0&&d&&c&&c instanceof T.a){var g=c.renderer.grid.template.location,y=c.getAngle(d.dataItems.getIndex(0),"categoryX",g);n=h.moveTo({x:r*u.cos(y),y:r*u.sin(y)});for(var f=1;f<p;f++)y=c.getAngle(d.dataItems.getIndex(f),"categoryX",g),n+=h.lineTo({x:r*u.cos(y),y:r*u.sin(y)});y=c.getAngle(d.dataItems.getIndex(p-1),"categoryX",c.renderer.cellEndLocation),n+=h.lineTo({x:r*u.cos(y),y:r*u.sin(y)})}else n=h.moveTo({x:r*u.cos(o),y:r*u.sin(o)})+h.arcTo(o,s-o,r,r);e.path=n}this.toggleVisibility(e,t,0,1)},t.prototype.updateLabelElement=function(e,t,i,n){m.hasValue(n)||(n=e.location),t+=(i-t)*n;var a=this.positionToPoint(t);this.positionItem(e,a),this.toggleVisibility(e,t,this.minLabelPosition,this.maxLabelPosition)},t.prototype.updateBaseGridElement=function(){},t.prototype.fitsToBounds=function(e){return!0},Object.defineProperty(t.prototype,"startAngle",{get:function(){return this.getPropertyValue("startAngle")},set:function(e){this.setPropertyValue("startAngle",e)&&this.invalidateAxisItems()},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"endAngle",{get:function(){return this.getPropertyValue("endAngle")},set:function(e){this.setPropertyValue("endAngle",e)&&this.invalidateAxisItems()},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"axisAngle",{get:function(){return this.getPropertyValue("axisAngle")},set:function(e){this.setPropertyValue("axisAngle",u.normalizeAngle(e)),this.invalidateAxisItems()},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"gridType",{get:function(){return this.chart.xAxes.getIndex(0)instanceof T.a?this.getPropertyValue("gridType"):"circles"},set:function(e){this.setPropertyValue("gridType",e,!0)},enumerable:!0,configurable:!0}),t.prototype.getPositionRangePath=function(e,t){var i,n=this.pixelInnerRadius,a=this.axisLength+n,r=u.fitToRange(this.positionToCoordinate(e),n,a),o=u.fitToRange(this.positionToCoordinate(t),n,a),s=this.startAngle,l=this.endAngle-s,c=this.chart,p=c.xAxes.getIndex(0),d=c.series.getIndex(0),g=0;if(d&&(g=d.dataItems.length),"polygons"==this.gridType&&g>0&&d&&p&&p instanceof T.a){var y=p.renderer.grid.template.location,f=p.getAngle(d.dataItems.getIndex(0),"categoryX",y);i=h.moveTo({x:o*u.cos(f),y:o*u.sin(f)});for(var m=1;m<g;m++)f=p.getAngle(d.dataItems.getIndex(m),"categoryX",y),i+=h.lineTo({x:o*u.cos(f),y:o*u.sin(f)});f=p.getAngle(d.dataItems.getIndex(g-1),"categoryX",p.renderer.cellEndLocation),i+=h.lineTo({x:o*u.cos(f),y:o*u.sin(f)}),i+=h.moveTo({x:r*u.cos(f),y:r*u.sin(f)});for(m=g-1;m>=0;m--)f=p.getAngle(d.dataItems.getIndex(m),"categoryX",y),i+=h.lineTo({x:r*u.cos(f),y:r*u.sin(f)})}else i=h.arc(s,l,o,r);return i},t.prototype.updateBreakElement=function(e){var t=e.startLine,i=e.endLine,n=e.fillShape,a=e.startPoint,r=e.endPoint;t.radius=Math.abs(a.y),i.radius=Math.abs(r.y),n.radius=Math.abs(r.y),n.innerRadius=Math.abs(a.y)},t.prototype.createBreakSprites=function(e){e.startLine=new O.a,e.endLine=new O.a,e.fillShape=new O.a},t.prototype.updateTooltip=function(){if(this.axis){var e=this.axisAngle;e<0&&(e+=360);var t="vertical";(e>45&&e<135||e>225&&e<315)&&(t="horizontal"),this.axis.updateTooltip(t,{x:-4e3,y:-4e3,width:8e3,height:8e3})}},t.prototype.updateTickElement=function(e,t,i){t+=(i-t)*e.location;var n=this.positionToPoint(t);if(e.element){var a=u.normalizeAngle(this.axisAngle+90);a/90!=Math.round(a/90)?e.pixelPerfect=!1:e.pixelPerfect=!0;var r=-e.length;e.inside&&(r*=-1),e.path=h.moveTo({x:0,y:0})+h.lineTo({x:r*u.cos(a),y:r*u.sin(a)})}this.positionItem(e,n),this.toggleVisibility(e,t,0,1)},t.prototype.updateBullet=function(e,t,i){var n=.5;e instanceof A.a&&(n=e.location),t+=(i-t)*n;var a=this.positionToPoint(t);this.positionItem(e,a),this.toggleVisibility(e,t,0,1)},t.prototype.positionToCoordinate=function(e){var t,i=this.axis,n=i.axisFullLength,a=this.pixelInnerRadius;return t=i.renderer.inversed?(i.end-e)*n+a:(e-i.start)*n+a,u.round(t,1)},t.prototype.pointToPosition=function(e){var t=u.getDistance(e)-this.pixelInnerRadius;return this.coordinateToPosition(t)},Object.defineProperty(t.prototype,"chart",{get:function(){return this._chart.get()},set:function(e){this._chart.set(e,null)},enumerable:!0,configurable:!0}),t}(D.a);l.c.registeredClasses.AxisRendererRadial=_;var k=i("Wglt"),L=function(e){function t(){var t=e.call(this)||this;return t.className="RadarChartDataItem",t.applyTheme(),t}return Object(a.c)(t,e),t}(r.b),S=function(e){function t(){var t=e.call(this)||this;t._axisRendererX=I,t._axisRendererY=_,t.innerRadiusModifyer=1,t.mr=1,t.className="RadarChart",t.startAngle=-90,t.endAngle=270,t.radius=Object(o.c)(80),t.innerRadius=0;var i=t.plotContainer.createChild(d.a);return i.shouldClone=!1,i.layout="absolute",i.align="center",i.valign="middle",t.seriesContainer.parent=i,t.radarContainer=i,t.bulletsContainer.parent=i,t.axisBulletsContainer=i,t._cursorContainer=i,t.chartContainer.events.on("maxsizechanged",t.invalidate,t,!1),t._bulletMask=i.createChild(g.a),t._bulletMask.shouldClone=!1,t._bulletMask.element=t.paper.add("path"),t._bulletMask.opacity=0,t.applyTheme(),t}return Object(a.c)(t,e),t.prototype.applyInternalDefaults=function(){e.prototype.applyInternalDefaults.call(this),m.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("Radar chart"))},t.prototype.processAxis=function(t){e.prototype.processAxis.call(this,t);var i=t.renderer;i.gridContainer.parent=i,i.breakContainer.parent=i,t.parent=this.radarContainer,i.toBack()},t.prototype.handleXAxisRangeChange=function(){e.prototype.handleXAxisRangeChange.call(this),k.each(this.yAxes.iterator(),function(e){e.invalidate()})},t.prototype.handleYAxisRangeChange=function(){e.prototype.handleYAxisRangeChange.call(this),k.each(this.xAxes.iterator(),function(e){e.invalidate()})},t.prototype.processConfig=function(t){if(t&&(m.hasValue(t.cursor)&&!m.hasValue(t.cursor.type)&&(t.cursor.type="RadarCursor"),m.hasValue(t.series)&&m.isArray(t.series)))for(var i=0,n=t.series.length;i<n;i++)t.series[i].type=t.series[i].type||"RadarSeries";e.prototype.processConfig.call(this,t)},t.prototype.beforeDraw=function(){e.prototype.beforeDraw.call(this);var t=this.plotContainer,i=u.getArcRect(this.startAngle,this.endAngle,1),n={x:0,y:0,width:0,height:0},a=t.innerWidth/i.width,r=t.innerHeight/i.height,s=this.innerRadius;if(s instanceof o.a){var l=s.value,c=Math.min(a,r);this.mr=c,l=Math.max(c*l,c-Math.min(t.innerHeight,t.innerWidth))/c,n=u.getArcRect(this.startAngle,this.endAngle,l),this.innerRadiusModifyer=l/s.value,s=Object(o.c)(100*l)}i=u.getCommonRectangle([i,n]);var p=Math.min(t.innerWidth/i.width,t.innerHeight/i.height),d=2*C.relativeRadiusToValue(this.radius,p)||0,g=d/2,y=this.startAngle,f=this.endAngle;this._pixelInnerRadius=C.relativeRadiusToValue(s,g),this._bulletMask.path=h.arc(y,f-y,g,this._pixelInnerRadius),k.each(this.xAxes.iterator(),function(e){e.renderer.useChartAngles&&(e.renderer.startAngle=y,e.renderer.endAngle=f),e.width=d,e.height=d,e.renderer.pixelRadiusReal=g}),k.each(this.yAxes.iterator(),function(e){e.renderer.startAngle=y,e.renderer.endAngle=f,e.width=d,e.height=d,e.renderer.pixelRadiusReal=g});var m=this.cursor;m&&(m.width=d,m.height=d,m.startAngle=y,m.endAngle=f),this.radarContainer.definedBBox={x:g*i.x,y:g*i.y,width:g*i.width,height:g*i.height},this.radarContainer.validatePosition()},t.prototype.createSeries=function(){return new p},Object.defineProperty(t.prototype,"startAngle",{get:function(){return this.getPropertyValue("startAngle")},set:function(e){this.setPropertyValue("startAngle",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"endAngle",{get:function(){return this.getPropertyValue("endAngle")},set:function(e){this.setPropertyValue("endAngle",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"radius",{get:function(){return this.getPropertyValue("radius")},set:function(e){this.setPercentProperty("radius",e,!0,!1,10,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"pixelInnerRadius",{get:function(){return this._pixelInnerRadius},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"innerRadius",{get:function(){return this.getPropertyValue("innerRadius")},set:function(e){this.setPercentProperty("innerRadius",e,!0,!1,10,!1)},enumerable:!0,configurable:!0}),t.prototype.updateXAxis=function(e){e&&e.processRenderer()},t.prototype.updateYAxis=function(e){e&&e.processRenderer()},t}(r.a);l.c.registeredClasses.RadarChart=S;var R=i("vMqJ"),j=i("DziZ"),w=i("MIZb"),N=function(e){function t(){var t=e.call(this)||this;t._axis=new V.d,t.className="ClockHand";var i=new w.a;t.fill=i.getFor("alternativeBackground"),t.stroke=t.fill;var n=new g.a;n.radius=5,t.pin=n,t.isMeasured=!1,t.startWidth=5,t.endWidth=1,t.width=Object(o.c)(100),t.height=Object(o.c)(100),t.radius=Object(o.c)(100),t.innerRadius=Object(o.c)(0);var a=new j.a;return t.hand=a,t._disposers.push(t._axis),t.applyTheme(),t}return Object(a.c)(t,e),t.prototype.validate=function(){e.prototype.validate.call(this);var t=this.hand;t.width=this.pixelWidth;var i=Math.max(this.startWidth,this.endWidth);if(t.height=i,t.leftSide=Object(o.c)(this.startWidth/i*100),t.rightSide=Object(o.c)(this.endWidth/i*100),this.axis){var n=this.axis.renderer,a=C.relativeRadiusToValue(this.innerRadius,n.pixelRadius),r=C.relativeRadiusToValue(this.radius,n.pixelRadius);t.x=a,t.y=-i/2,t.width=r-a}},Object.defineProperty(t.prototype,"pin",{get:function(){return this._pin},set:function(e){this._pin&&this.removeDispose(this._pin),e&&(this._pin=e,e.parent=this,this._disposers.push(e))},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"hand",{get:function(){return this._hand},set:function(e){this._hand&&this.removeDispose(this._hand),e&&(this._hand=e,e.parent=this,this._disposers.push(e))},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"radius",{get:function(){return this.getPropertyValue("radius")},set:function(e){this.setPercentProperty("radius",e,!0,!1,10,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"innerRadius",{get:function(){return this.getPropertyValue("innerRadius")},set:function(e){this.setPercentProperty("innerRadius",e,!0,!1,10,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"startWidth",{get:function(){return this.getPropertyValue("startWidth")},set:function(e){this.setPropertyValue("startWidth",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"endWidth",{get:function(){return this.getPropertyValue("endWidth")},set:function(e){this.setPropertyValue("endWidth",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"rotationDirection",{get:function(){return this.getPropertyValue("rotationDirection")},set:function(e){this.setPropertyValue("rotationDirection",e)},enumerable:!0,configurable:!0}),t.prototype.showValue=function(e,t,i){if(this._value=e,void 0!=e&&(m.isNumber(t)||(t=0),this.axis)){var n=this.axis.renderer.positionToAngle(this.axis.anyToPosition(e)),a=this.rotation;"clockWise"==this.rotationDirection&&n<a&&(this.rotation=a-360),"counterClockWise"==this.rotationDirection&&n>a&&(this.rotation=a+360),this.animate({property:"rotation",to:n},t,i)}},Object.defineProperty(t.prototype,"currentPosition",{get:function(){if(this.axis)return this.axis.renderer.angleToPosition(this.rotation)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"value",{get:function(){return this._value},set:function(e){this.showValue(e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"axis",{get:function(){return this._axis.get()},set:function(e){if(this.axis!=e&&this._axis.set(e,new V.c([e.events.on("datavalidated",this.updateValue,this,!1),e.events.on("datarangechanged",this.updateValue,this,!1),e.events.on("dataitemsvalidated",this.updateValue,this,!1),e.events.on("propertychanged",this.invalidate,this,!1)])),e){var t=e.chart;t&&(this.rotation=t.startAngle)}this.parent=e.renderer,this.zIndex=5},enumerable:!0,configurable:!0}),t.prototype.updateValue=function(){this.value=this.value},t.prototype.processConfig=function(t){t&&m.hasValue(t.axis)&&m.isString(t.axis)&&this.map.hasKey(t.axis)&&(t.axis=this.map.getKey(t.axis)),e.prototype.processConfig.call(this,t)},t}(d.a);l.c.registeredClasses.ClockHand=N;var F=function(e){function t(){var t=e.call(this)||this;return t.className="GaugeChartDataItem",t.applyTheme(),t}return Object(a.c)(t,e),t}(L),Y=function(e){function t(){var t=e.call(this)||this;return t.className="GaugeChart",t.startAngle=180,t.endAngle=360,t.hands=new R.e(new N),t.hands.events.on("inserted",t.processHand,t,!1),t._disposers.push(new R.c(t.hands)),t._disposers.push(t.hands.template),t.applyTheme(),t}return Object(a.c)(t,e),t.prototype.applyInternalDefaults=function(){e.prototype.applyInternalDefaults.call(this),m.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("Gauge chart"))},t.prototype.processHand=function(e){var t=e.newValue;t.axis||(t.axis=this.xAxes.getIndex(0))},t.prototype.configOrder=function(t,i){return t==i?0:"hands"==t?1:"hands"==i?-1:e.prototype.configOrder.call(this,t,i)},t}(S);l.c.registeredClasses.GaugeChart=Y;var W=i("2I/e"),X=i("quKg"),M=i("Puh1"),H=i("nPzZ"),B=function(e){function t(){var t=e.call(this)||this;return t.className="PieSeries3DDataItem",t.values.depthValue={},t.applyTheme(),t}return Object(a.c)(t,e),Object.defineProperty(t.prototype,"depthValue",{get:function(){return this.values.depthValue.value},set:function(e){this.setValue("depthValue",e)},enumerable:!0,configurable:!0}),t}(M.b),z=function(e){function t(){var t=e.call(this)||this;return t.className="PieSeries3D",t.applyTheme(),t}return Object(a.c)(t,e),t.prototype.createDataItem=function(){return new B},t.prototype.createSlice=function(){return new H.a},t.prototype.validateDataElement=function(t){var i=t.slice,n=this.depth;m.isNumber(n)||(n=this.chart.depth);var a=t.values.depthValue.percent;m.isNumber(a)||(a=100),i.depth=a*n/100;var r=this.angle;m.isNumber(r)||(r=this.chart.angle),i.angle=r,e.prototype.validateDataElement.call(this,t)},t.prototype.validate=function(){e.prototype.validate.call(this);for(var t=this._workingStartIndex;t<this._workingEndIndex;t++){var i=this.dataItems.getIndex(t).slice,n=i.startAngle;n>=-90&&n<90?i.toFront():n>=90&&i.toBack()}},Object.defineProperty(t.prototype,"depth",{get:function(){return this.getPropertyValue("depth")},set:function(e){this.setPropertyValue("depth",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"angle",{get:function(){return this.getPropertyValue("angle")},set:function(e){this.setPropertyValue("angle",e)},enumerable:!0,configurable:!0}),t.prototype.positionBullet=function(t){e.prototype.positionBullet.call(this,t);var i=t.dataItem.slice;t.y=t.pixelY-i.depth},t}(M.a);l.c.registeredClasses.PieSeries3D=z,l.c.registeredClasses.PieSeries3DDataItem=B;var E=function(e){function t(){var t=e.call(this)||this;return t.className="PieChart3DDataItem",t.applyTheme(),t}return Object(a.c)(t,e),t}(X.b),G=function(e){function t(){var t=e.call(this)||this;return t.className="PieChart3D",t.depth=20,t.angle=10,t.applyTheme(),t}return Object(a.c)(t,e),Object.defineProperty(t.prototype,"depth",{get:function(){return this.getPropertyValue("depth")},set:function(e){this.setPropertyValue("depth",e)&&this.invalidateDataUsers()},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"angle",{get:function(){return this.getPropertyValue("angle")},set:function(e){e=u.fitToRange(e,0,90),this.setPropertyValue("angle",e)&&this.invalidateDataUsers()},enumerable:!0,configurable:!0}),t.prototype.createSeries=function(){return new z},t}(X.a);l.c.registeredClasses.PieChart3D=G;var q=i("DXFp"),K=function(e){function t(){var t=e.call(this)||this;return t.className="SlicedChartDataItem",t.applyTheme(),t}return Object(a.c)(t,e),t}(q.b),U=function(e){function t(){var t=e.call(this)||this;return t.className="SlicedChart",t.seriesContainer.layout="horizontal",t.padding(15,15,15,15),t.applyTheme(),t}return Object(a.c)(t,e),t.prototype.applyInternalDefaults=function(){e.prototype.applyInternalDefaults.call(this),m.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("Sliced chart"))},t.prototype.validate=function(){e.prototype.validate.call(this)},t}(q.a);l.c.registeredClasses.SlicedChart=U,l.c.registeredClasses.SlicedChartDataItem=K;var Z=i("VIOb"),Q=i("+qIf"),J=i("Vs7R"),$=i("wUYf"),ee=i("MlsF"),te=i("3Cxr"),ie=i("CnhP"),ne=i("Qkdp"),ae=i("uWmK"),re=function(e){function t(){var t=e.call(this)||this;return t.legendSettings=new ae.c,t.className="FlowDiagramNode",t.isMeasured=!1,new w.a,t.draggable=!0,t.inert=!0,t.setStateOnChildren=!0,t.events.on("positionchanged",t.invalidateLinks,t,!1),t.events.on("sizechanged",t.invalidateLinks,t,!1),t}return Object(a.c)(t,e),t.prototype.handleHit=function(e){this.isHidden||this.isHiding?this.show():this.hide()},t.prototype.show=function(t){var i=e.prototype.show.call(this,t);return this.outgoingDataItems.each(function(e){(!e.toNode||e.toNode&&!e.toNode.isHidden)&&e.setWorkingValue("value",e.getValue("value"),t)}),this.incomingDataItems.each(function(e){(!e.fromNode||e.fromNode&&!e.fromNode.isHidden)&&e.setWorkingValue("value",e.getValue("value"),t)}),i},t.prototype.hide=function(t){var i=e.prototype.hide.call(this,t);return this.outgoingDataItems.each(function(e){e.setWorkingValue("value",0,t)}),this.incomingDataItems.each(function(e){e.setWorkingValue("value",0,t)}),i},t.prototype.validate=function(){this.isDisposed()||(e.prototype.validate.call(this),this.invalidateLinks())},t.prototype.invalidateLinks=function(){var e=this;this.outgoingDataItems.each(function(t){var i=t.link;if("fromNode"==i.colorMode&&(i.fill=i.dataItem.fromNode.color),"gradient"==i.colorMode){i.fill=i.gradient,i.stroke=i.gradient;var n=i.gradient.stops.getIndex(0);n&&(n.color=e.color,i.gradient.validate())}}),this.incomingDataItems.each(function(t){var i=t.link;if("toNode"==i.colorMode&&(i.fill=i.dataItem.toNode.color),"gradient"==i.colorMode){i.fill=i.gradient,i.stroke=i.gradient;var n=i.gradient.stops.getIndex(1);n&&(n.color=e.color,i.gradient.validate())}})},Object.defineProperty(t.prototype,"incomingDataItems",{get:function(){var e=this;if(!this._incomingDataItems){var t=new R.b;t.events.on("inserted",function(){"name"==e.chart.sortBy?e._incomingSorted=k.sort(e._incomingDataItems.iterator(),function(e,t){return $.order(e.fromName,t.fromName)}):"value"==e.chart.sortBy?e._incomingSorted=k.sort(e._incomingDataItems.iterator(),function(e,t){return ee.b(te.order(e.value,t.value))}):e._incomingSorted=e._incomingDataItems.iterator()},void 0,!1),this._incomingDataItems=t}return this._incomingDataItems},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"outgoingDataItems",{get:function(){var e=this;if(!this._outgoingDataItems){var t=new R.b;t.events.on("inserted",function(){"name"==e.chart.sortBy?e._outgoingSorted=k.sort(e._outgoingDataItems.iterator(),function(e,t){return $.order(e.fromName,t.fromName)}):"value"==e.chart.sortBy?e._outgoingSorted=k.sort(e._outgoingDataItems.iterator(),function(e,t){return ee.b(te.order(e.value,t.value))}):e._outgoingSorted=e._outgoingDataItems.iterator()},void 0,!1),this._outgoingDataItems=t}return this._outgoingDataItems},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"name",{get:function(){return this.getPropertyValue("name")},set:function(e){this.setPropertyValue("name",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"total",{get:function(){return this.getPropertyValue("total")},set:function(e){this.setPropertyValue("total",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"totalIncoming",{get:function(){return this.getPropertyValue("totalIncoming")},set:function(e){this.setPropertyValue("totalIncoming",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"totalOutgoing",{get:function(){return this.getPropertyValue("totalOutgoing")},set:function(e){this.setPropertyValue("totalOutgoing",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"color",{get:function(){return this.getPropertyValue("color")},set:function(e){this.setColorProperty("color",e),this._background&&(this._background.fill=e),this.fill=e},enumerable:!0,configurable:!0}),t.prototype.createLegendMarker=function(e){var t=e.pixelWidth,i=e.pixelHeight;e.removeChildren();var n=e.createChild(ie.a);n.shouldClone=!1,ne.copyProperties(this,n,J.b),n.stroke=this.fill,n.copyFrom(this),n.padding(0,0,0,0),n.width=t,n.height=i;var a=e.dataItem;a.color=n.fill,a.colorOrig=n.fill},Object.defineProperty(t.prototype,"legendDataItem",{get:function(){return this._legendDataItem},set:function(e){this._legendDataItem=e,this._legendDataItem.itemContainer.deepInvalidate()},enumerable:!0,configurable:!0}),t}(d.a);l.c.registeredClasses.FlowDiagramNode=re;var oe=i("sxA1"),se=i("TXRX"),le=i("8ZqG"),ue=i("jfaP"),he=i("PTiM"),ce=function(e){function t(){var t=e.call(this)||this;t.className="FlowDiagramLink";var i=new w.a;return t.maskBullets=!1,t.colorMode="fromNode",t.layout="none",t.isMeasured=!1,t.startAngle=0,t.endAngle=0,t.strokeOpacity=0,t.verticalCenter="none",t.horizontalCenter="none",t.tooltipText="{fromName}→{toName}:{value.value}",t.tooltipLocation=.5,t.link=t.createChild(J.a),t.link.shouldClone=!1,t.link.setElement(t.paper.add("path")),t.link.isMeasured=!1,t.fillOpacity=.2,t.fill=i.getFor("alternativeBackground"),t.applyTheme(),t}return Object(a.c)(t,e),t.prototype.positionBullets=function(){var e=this;k.each(this.bullets.iterator(),function(t){t.parent=e.bulletsContainer,t.maxWidth=e.maxWidth,t.maxHeight=e.maxHeight,e.positionBullet(t)})},Object.defineProperty(t.prototype,"bulletsContainer",{get:function(){if(!this._bulletsContainer){var e=this.createChild(d.a);e.shouldClone=!1,e.layout="none",this._bulletsContainer=e}return this._bulletsContainer},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"bulletsMask",{get:function(){if(!this._bulletsMask){var e=this.createChild(J.a);e.shouldClone=!1,e.setElement(this.paper.add("path")),e.isMeasured=!1,this._bulletsMask=e}return this._bulletsMask},enumerable:!0,configurable:!0}),t.prototype.positionBullet=function(e){var t=e.locationX;m.isNumber(t)||(t=e.locationY),m.isNumber(t)||(t=.5);var i=this.middleLine.positionToPoint(t);e.moveTo(i);var n,a=e.propertyFields.rotation;e.dataItem&&(n=e.dataItem.dataContext[a]);m.isNumber(n)||(n=i.angle),e.rotation=n},Object.defineProperty(t.prototype,"startAngle",{get:function(){return this.getPropertyValue("startAngle")},set:function(e){this.setPropertyValue("startAngle",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"endAngle",{get:function(){return this.getPropertyValue("endAngle")},set:function(e){this.setPropertyValue("endAngle",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"colorMode",{get:function(){return this.getPropertyValue("colorMode")},set:function(e){if("gradient"==e){var t=this.fill;this.gradient.stops.clear(),t instanceof le.a&&(this.gradient.addColor(t),this.gradient.addColor(t)),this.fill=this.gradient,this.stroke=this.gradient}this.setPropertyValue("colorMode",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"maskBullets",{get:function(){return this.getPropertyValue("maskBullets")},set:function(e){this.setPropertyValue("maskBullets",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"tooltipLocation",{get:function(){return this.getPropertyValue("tooltipLocation")},set:function(e){this.setPropertyValue("tooltipLocation",e,!0)},enumerable:!0,configurable:!0}),t.prototype.setFill=function(t){e.prototype.setFill.call(this,t);var i=this._gradient;i&&t instanceof le.a&&(i.stops.clear(),i.addColor(t),i.addColor(t))},t.prototype.measureElement=function(){},Object.defineProperty(t.prototype,"bullets",{get:function(){var e=this;return this._bullets||(this._bullets=new R.e(new se.a),this._disposers.push(new R.c(this._bullets)),this._disposers.push(this._bullets.template),this._bullets.events.on("inserted",function(t){t.newValue.events.on("propertychanged",function(t){"locationX"!=t.property&&"locationY"!=t.property||e.positionBullet(t.target)},void 0,!1)},void 0,!1)),this._bullets},enumerable:!0,configurable:!0}),t.prototype.copyFrom=function(t){e.prototype.copyFrom.call(this,t),this.bullets.copyFrom(t.bullets);var i=this.middleLine;i&&(i instanceof he.a&&t.middleLine instanceof he.a&&i.copyFrom(t.middleLine),i instanceof ue.a&&t.middleLine instanceof ue.a&&i.copyFrom(t.middleLine)),this.link.copyFrom(t.link)},t.prototype.getTooltipX=function(){if(this.middleLine)return this.middleLine.positionToPoint(this.tooltipLocation).x},t.prototype.getTooltipY=function(){if(this.middleLine)return this.middleLine.positionToPoint(this.tooltipLocation).y},Object.defineProperty(t.prototype,"gradient",{get:function(){return this._gradient||(this._gradient=new oe.a),this._gradient},enumerable:!0,configurable:!0}),t}(d.a);l.c.registeredClasses.FlowDiagramLink=ce;var pe=i("/e9j"),de=i("DHte"),ge=function(e){function t(){var t=e.call(this)||this;return t.className="FlowDiagramDataItem",t.values.value={},t.applyTheme(),t}return Object(a.c)(t,e),Object.defineProperty(t.prototype,"fromName",{get:function(){return this.properties.fromName},set:function(e){this.setProperty("fromName",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"toName",{get:function(){return this.properties.toName},set:function(e){this.setProperty("toName",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"color",{get:function(){return this.properties.color},set:function(e){this.setProperty("color",Object(le.e)(e))},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"value",{get:function(){return this.values.value.value},set:function(e){this.setValue("value",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"link",{get:function(){var e=this;if(!this._link){var t=this.component.links.create();this._link=t,this.addSprite(t),this._disposers.push(new V.b(function(){e.component&&e.component.links.removeValue(t)}))}return this._link},enumerable:!0,configurable:!0}),t}(Z.b),ye=function(e){function t(){var t=e.call(this)||this;t.colors=new de.a,t.className="FlowDiagram",t.nodePadding=20,t.sortBy="none",t.sequencedInterpolation=!0,t.colors.step=2,t.minNodeSize=.02;var i=t.chartContainer.createChild(d.a);i.shouldClone=!1,i.layout="none",i.isMeasured=!1,t.linksContainer=i;var n=t.chartContainer.createChild(d.a);return n.shouldClone=!1,n.layout="none",n.isMeasured=!1,t.nodesContainer=n,t.dataItem=t.createDataItem(),t.dataItem.component=t,t.applyTheme(),t}return Object(a.c)(t,e),t.prototype.dispose=function(){e.prototype.dispose.call(this),this.dataItem.dispose()},t.prototype.validateData=function(){var t=this;0==this._parseDataFrom&&this.nodes.clear(),this.sortNodes(),this.colors.reset(),e.prototype.validateData.call(this);var i,n,a=0,r=0;k.each(this.dataItems.iterator(),function(e){var o=e.fromName;o&&((s=t.nodes.getKey(o))||((s=t.nodes.create(o)).name=o,s.chart=t,s.dataItem=e),e.fromNode=s,e.fromNode.outgoingDataItems.push(e));var s,l=e.toName;l&&((s=t.nodes.getKey(l))||((s=t.nodes.create(l)).name=l,s.chart=t,s.dataItem=e),e.toNode=s,e.toNode.incomingDataItems.push(e));if(!e.fromNode){var u=new pe.a;u.opacities=[0,1],e.link.strokeModifier=u}if(!e.toNode){var h=new pe.a;h.opacities=[1,0],e.link.strokeModifier=h}var c=e.value;m.isNumber(c)&&(a+=c,r++,(i>c||!m.isNumber(i))&&(i=c),(n<c||!m.isNumber(n))&&(n=c))});var o="value";this.dataItem.setCalculatedValue(o,n,"high"),this.dataItem.setCalculatedValue(o,i,"low"),this.dataItem.setCalculatedValue(o,a,"sum"),this.dataItem.setCalculatedValue(o,a/r,"average"),this.dataItem.setCalculatedValue(o,r,"count"),k.each(this.nodes.iterator(),function(e){var i=e[1];i.fill instanceof le.a&&(i.color=i.fill),void 0==i.color&&(i.color=t.colors.next()),void 0!=i.dataItem.color&&(i.color=i.dataItem.color),i.dataItem.visible||i.hide(0),t.getNodeValue(i)}),this.sortNodes(),this.feedLegend()},t.prototype.handleDataItemWorkingValueChange=function(e,t){this.invalidate()},t.prototype.sortNodes=function(){"name"==this.sortBy?this._sorted=this.nodes.sortedIterator():"value"==this.sortBy?this._sorted=k.sort(this.nodes.iterator(),function(e,t){return ee.b(te.order(e[1].total,t[1].total))}):this._sorted=this.nodes.iterator()},t.prototype.getNodeValue=function(e){var t=0,i=0;k.each(e.incomingDataItems.iterator(),function(e){var i=e.getWorkingValue("value");m.isNumber(i)&&(t+=i)}),k.each(e.outgoingDataItems.iterator(),function(e){var t=e.getWorkingValue("value");m.isNumber(t)&&(i+=t)}),e.total=t+i,e.totalIncoming=t,e.totalOutgoing=i},t.prototype.changeSorting=function(){this.sortNodes()},t.prototype.applyInternalDefaults=function(){e.prototype.applyInternalDefaults.call(this),m.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("Flow diagram"))},t.prototype.createDataItem=function(){return new ge},Object.defineProperty(t.prototype,"nodePadding",{get:function(){return this.getPropertyValue("nodePadding")},set:function(e){this.setPropertyValue("nodePadding",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"sortBy",{get:function(){return this.getPropertyValue("sortBy")},set:function(e){this.setPropertyValue("sortBy",e),this.changeSorting()},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"minNodeSize",{get:function(){return this.getPropertyValue("minNodeSize")},set:function(e){this.setPropertyValue("minNodeSize",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"nodes",{get:function(){if(!this._nodes){var e=this.createNode();e.events.on("hit",function(e){e.target.handleHit(e)}),this._nodes=new Q.c(e),this._disposers.push(new Q.b(this._nodes))}return this._nodes},enumerable:!0,configurable:!0}),t.prototype.createNode=function(){var e=new re;return this._disposers.push(e),e},Object.defineProperty(t.prototype,"links",{get:function(){return this._links||(this._links=new R.e(this.createLink()),this._disposers.push(new R.c(this._links))),this._links},enumerable:!0,configurable:!0}),t.prototype.createLink=function(){var e=new ce;return this._disposers.push(e),e},t.prototype.feedLegend=function(){var e=this.legend;if(e){var t=[];this.nodes.each(function(e,i){t.push(i)}),e.data=t,e.dataFields.name="name"}},t.prototype.disposeData=function(){e.prototype.disposeData.call(this),this.nodes.clear()},t}(Z.a);l.c.registeredClasses.FlowDiagram=ye;var fe=i("p9TX"),me=function(e){function t(){var t=e.call(this)||this;t.className="LabelBullet";var i=t.createChild(fe.a);return i.shouldClone=!1,i.verticalCenter="middle",i.horizontalCenter="middle",i.truncate=!0,i.hideOversized=!1,i.maxWidth=500,i.maxHeight=500,i.stroke=Object(le.c)(),i.strokeOpacity=0,i.fill=(new w.a).getFor("text"),t.events.on("maxsizechanged",t.handleMaxSize,t,!1),t.label=i,t.applyTheme(),t}return Object(a.c)(t,e),t.prototype.handleMaxSize=function(){this.label.maxWidth=this.maxWidth,this.label.maxHeight=this.maxHeight},t.prototype.copyFrom=function(t){e.prototype.copyFrom.call(this,t),this.label.copyFrom(t.label)},t}(se.a);l.c.registeredClasses.LabelBullet=me;var ve=function(e){function t(){var t=e.call(this)||this;t.nextInCoord=0,t.nextOutCoord=0,t.className="SankeyNode",t.width=10,t.height=10;var i=t.createChild(me);i.shouldClone=!1,i.locationX=1,i.locationY=.5,i.label.text="{name}",i.width=150,i.height=150,i.label.horizontalCenter="left",i.label.padding(0,5,0,5),t.nameLabel=i;var n=t.createChild(me);n.shouldClone=!1,n.label.hideOversized=!1,n.locationX=.5,n.locationY=.5,n.width=150,n.height=150,n.label.horizontalCenter="middle",t.valueLabel=n;var a=t.hiddenState;return a.properties.fill=(new w.a).getFor("disabledBackground"),a.properties.opacity=.5,a.properties.visible=!0,t.background.hiddenState.copyFrom(a),t}return Object(a.c)(t,e),t.prototype.invalidateLinks=function(){var t=this;e.prototype.invalidateLinks.call(this),this.nextInCoord=0,this.nextOutCoord=0;var i=this.chart;if(i){var n=i.orientation;this._incomingSorted&&k.each(this._incomingSorted,function(e){var a=e.link,r=e.getWorkingValue("value");if(m.isNumber(r)){a.parent=t.chart.linksContainer;var o=void 0,s=void 0,l=void 0;if("horizontal"==n?(o=t.pixelX+t.dx,s=t.nextInCoord+t.pixelY+t.dy,l=0):(s=t.pixelY+t.dy,o=t.nextInCoord+t.pixelX+t.dx,l=90),a.endX=o,a.endY=s,a.startAngle=l,a.endAngle=l,a.gradient.rotation=l,a.linkWidth=r*i.valueHeight,!e.fromNode){"horizontal"==n?(a.maxWidth=200,a.startX=t.pixelX+t.dx-a.maxWidth,a.startY=a.endY):(a.maxHeight=200,a.startX=a.endX,a.startY=t.pixelY+t.dy-a.maxHeight),C.used(a.gradient),a.fill=e.toNode.color;var u=a.gradient.stops.getIndex(0);u&&("gradient"==a.colorMode&&(u.color=t.color),u.opacity=0,a.fill=a.gradient,a.stroke=a.gradient,a.gradient.validate())}t.nextInCoord+=a.linkWidth}}),this._outgoingSorted&&k.each(this._outgoingSorted,function(e){var i=e.link;i.parent=t.chart.linksContainer;var a=e.getWorkingValue("value");if(m.isNumber(a)){var r=void 0,o=void 0,s=void 0;if("horizontal"==n?(s=0,r=t.pixelX+t.pixelWidth+t.dx-1,o=t.nextOutCoord+t.pixelY+t.dy):(s=90,r=t.nextOutCoord+t.pixelX+t.dx,o=t.pixelY+t.pixelHeight+t.dy-1),i.startX=r,i.startY=o,i.startAngle=s,i.endAngle=s,i.gradient.rotation=s,i.linkWidth=a*t.chart.valueHeight,!e.toNode){"horizontal"==n?(i.maxWidth=200,i.endX=t.pixelX+i.maxWidth+t.dx,i.endY=i.startY):(i.maxHeight=200,i.endX=i.startX,i.endY=t.pixelY+i.maxHeight+t.dy),i.opacity=t.opacity;var l=i.gradient.stops.getIndex(1);l&&("gradient"==i.colorMode&&(l.color=t.color),l.opacity=0,i.fill=i.gradient,i.stroke=i.gradient,i.gradient.validate())}t.nextOutCoord+=i.linkWidth}})}this.positionBullet(this.nameLabel),this.positionBullet(this.valueLabel)},t.prototype.positionBullet=function(e){e&&(e.x=this.measuredWidth*e.locationX,e.y=this.measuredHeight*e.locationY)},Object.defineProperty(t.prototype,"level",{get:function(){return this.getPropertyValue("level")},set:function(e){this.setPropertyValue("level",e,!0)},enumerable:!0,configurable:!0}),t.prototype.copyFrom=function(t){e.prototype.copyFrom.call(this,t),this.nameLabel.copyFrom(t.nameLabel),this.valueLabel.copyFrom(t.valueLabel)},t}(re);l.c.registeredClasses.SankeyNode=ve;var xe=i("xgTw"),be=i("aFzC"),Pe=function(e){function t(){var t=e.call(this)||this;return t.className="SankeyLink",new w.a,t.tension=.8,t.controlPointDistance=.2,t.startAngle=0,t.endAngle=0,t.linkWidth=0,t.startX=0,t.endX=0,t.startY=0,t.endY=0,t.middleLine=t.createChild(xe.a),t.middleLine.shouldClone=!1,t.middleLine.strokeOpacity=0,t.applyTheme(),t}return Object(a.c)(t,e),t.prototype.makeBackwards=function(){void 0!=this.states.getKey("backwards")&&this.setState("backwards")},t.prototype.validate=function(){var t,i,n,r;if(e.prototype.validate.call(this),!this.isTemplate){var o=this.startX,s=this.startY,l=this.endX,c=this.endY;if(this.states.getKey("backwards")&&this.setState("default"),this.dataItem){var p=this.dataItem.component;p&&("horizontal"==p.orientation?l<o&&(o=(t=Object(a.e)([l,o],2))[0],l=t[1],s=(i=Object(a.e)([c,s],2))[0],c=i[1],this.makeBackwards()):c<s&&(s=(n=Object(a.e)([c,s],2))[0],c=n[1],o=(r=Object(a.e)([l,o],2))[0],l=r[1],this.makeBackwards()))}m.isNumber(l)||(l=o),m.isNumber(c)||(c=s);var d=this.startAngle,g=this.endAngle,y=this.linkWidth,f="",v=o,x=s,b=l,P=c,C=o+y*u.sin(d),A=l+y*u.sin(g),I=s+y*u.cos(d),D=c+y*u.cos(g),T=o+y/2*u.sin(d),O=l+y/2*u.sin(g),V=s+y/2*u.cos(d),_=c+y/2*u.cos(g);this.zIndex=this.zIndex||this.dataItem.index;var k=this.tension+(1-this.tension)*u.sin(d),L=this.tension+(1-this.tension)*u.cos(d);if(this.middleLine.tensionX=k,this.middleLine.tensionY=L,m.isNumber(y)&&m.isNumber(o)&&m.isNumber(l)&&m.isNumber(s)&&m.isNumber(c)){u.round(v,3)==u.round(b,3)&&(b+=.01),u.round(x,3)==u.round(P,3)&&(P+=.01),u.round(C,3)==u.round(A,3)&&(A+=.01),u.round(I,3)==u.round(D,3)&&(D+=.01);var S=Math.min(C,A,v,b),R=Math.min(I,D,x,P),j=Math.max(C,A,v,b),w=Math.max(I,D,x,P);this._bbox={x:S,y:R,width:j-S,height:w-R};var N=this.controlPointDistance,F=v+(b-v)*N*u.cos(d),Y=x+(P-x)*N*u.sin(d),W=b-(b-v)*N*u.cos(g),X=P-(P-x)*N*u.sin(g),M=T+(O-T)*N*u.cos(d),H=V+(_-V)*N*u.sin(d),B=O-(O-T)*N*u.cos(g),z=_-(_-V)*N*u.sin(g),E=u.getAngle({x:F,y:Y},{x:W,y:X}),G=(y/u.cos(E)-y)/u.tan(E)*u.cos(d),q=(y/u.sin(E)-y)*u.tan(E)*u.sin(d),K=-G/2+C+(A-C)*N*u.cos(d),U=-q/2+I+(D-I)*N*u.sin(d),Z=-G/2+A-(A-C)*N*u.cos(g),Q=-q/2+D-(D-I)*N*u.sin(g);this.middleLine.segments=[[{x:T,y:V},{x:M,y:H},{x:B,y:z},{x:O,y:_}]],F+=G/2,Y+=q/2,W+=G/2,X+=q/2,f+=h.moveTo({x:v,y:x}),f+=new be.d(k,L).smooth([{x:v,y:x},{x:F,y:Y},{x:W,y:X},{x:b,y:P}]),f+=h.lineTo({x:A,y:D}),f+=new be.d(k,L).smooth([{x:A,y:D},{x:Z,y:Q},{x:K,y:U},{x:C,y:I}]),f+=h.closePath()}this.link.path=f,this.maskBullets&&(this.bulletsMask.path=f,this.bulletsContainer.mask=this.bulletsMask),this.positionBullets()}},Object.defineProperty(t.prototype,"startX",{get:function(){return this.getPropertyValue("startX")},set:function(e){this.setPropertyValue("startX",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"endX",{get:function(){return this.getPropertyValue("endX")},set:function(e){this.setPropertyValue("endX",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"startY",{get:function(){return this.getPropertyValue("startY")},set:function(e){this.setPropertyValue("startY",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"endY",{get:function(){return this.getPropertyValue("endY")},set:function(e){this.setPropertyValue("endY",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"linkWidth",{get:function(){return this.getPropertyValue("linkWidth")},set:function(e){this.setPropertyValue("linkWidth",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"controlPointDistance",{get:function(){return this.getPropertyValue("controlPointDistance")},set:function(e){this.setPropertyValue("controlPointDistance",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"tension",{get:function(){return this.getPropertyValue("tension")},set:function(e){this.setPropertyValue("tension",e,!0)},enumerable:!0,configurable:!0}),t}(ce);l.c.registeredClasses.SankeyLink=Pe;var Ce=i("1yyj"),Ae=function(e){function t(){var t=e.call(this)||this;return t.className="SankeyDiagramDataItem",t.applyTheme(),t}return Object(a.c)(t,e),t}(ge),Ie=function(e){function t(){var t=e.call(this)||this;return t.className="SankeyDiagram",t.orientation="horizontal",t.nodeAlign="middle",t.nodesContainer.width=Object(o.c)(100),t.nodesContainer.height=Object(o.c)(100),t.linksContainer.width=Object(o.c)(100),t.linksContainer.height=Object(o.c)(100),t.applyTheme(),t}return Object(a.c)(t,e),t.prototype.validateData=function(){var t=this;e.prototype.validateData.call(this),this._levelCount=0,this.nodes.each(function(e,t){t.level=void 0}),this.nodes.each(function(e,i){i.level=t.getNodeLevel(i,0),t._levelCount=u.max(t._levelCount,i.level)})},t.prototype.getNodeLevel=function(e,t){var i=this,n=[t];return k.each(e.incomingDataItems.iterator(),function(e){e.fromNode&&(m.isNumber(e.fromNode.level)?n.push(e.fromNode.level+1):(i._counter=0,i.checkLoop(e.fromNode),i._counter<i.dataItems.length&&n.push(i.getNodeLevel(e.fromNode,t+1))))}),Math.max.apply(Math,Object(a.f)(n))},t.prototype.checkLoop=function(e){var t=this;this._counter++,this._counter>this.dataItems.length||k.each(e.incomingDataItems.iterator(),function(e){t.checkLoop(e.fromNode)})},t.prototype.calculateValueHeight=function(){var e=this;this._levelSum={},this._levelNodesCount={},this.maxSum=0;var t,i,n,a=this.dataItem.values.value.sum;k.each(this._sorted,function(t){var i=t[1];e.getNodeValue(i)}),this.nodes.each(function(t,i){var n=i.level,r=Math.max(i.totalIncoming,i.totalOutgoing);r/a<e.minNodeSize&&(r=a*e.minNodeSize),m.isNumber(e._levelSum[n])?e._levelSum[n]+=r:e._levelSum[n]=r,m.isNumber(e._levelNodesCount[n])?e._levelNodesCount[n]++:e._levelNodesCount[n]=1}),t="horizontal"==this.orientation?this.chartContainer.maxHeight-1:this.chartContainer.maxWidth-1,ne.each(this._levelSum,function(a,r){var o=r,s=e._levelNodesCount[a],l=(t-(s-1)*e.nodePadding)/o;l==1/0&&(l=0),(n>l||!m.isNumber(n))&&(n=l,e.maxSum=o,i=m.toNumber(a))}),this._maxSumLevel=i;var r=this._levelNodesCount[this._maxSumLevel],o=(t-(r-1)*this.nodePadding)/this.maxSum;if(o==1/0&&(o=0),m.isNumber(this.valueHeight)){var s=void 0;try{s=this._heightAnimation.animationOptions[0].to}catch(e){}if(s!=o){var l=this.interpolationDuration;try{l=this.nodes.template.states.getKey("active").transitionDuration}catch(e){}this._heightAnimation=new Ce.a(this,{property:"valueHeight",from:this.valueHeight,to:o},l,this.interpolationEasing).start(),this._disposers.push(this._heightAnimation)}}else this.valueHeight=o},t.prototype.validate=function(){var t=this;e.prototype.validate.call(this),this.calculateValueHeight();var i,n=this.nodesContainer,a={},r=this._levelNodesCount[this._maxSumLevel],o=this.dataItem.values.value.sum;i="horizontal"==this.orientation?this.chartContainer.maxHeight-1:this.chartContainer.maxWidth-1,k.each(this._sorted,function(e){var s,l,u,h=e[1],c=h.level,p=0,d=t._levelNodesCount[c];switch(t.nodeAlign){case"bottom":p=(t.maxSum-t._levelSum[c])*t.valueHeight-(d-r)*t.nodePadding;break;case"middle":p=(t.maxSum-t._levelSum[c])*t.valueHeight/2-(d-r)*t.nodePadding/2}if(0==t.maxSum)switch(t.nodeAlign){case"bottom":p=i-d*(t.minNodeSize*i+t.nodePadding);break;case"middle":p=i/2-d/2*(t.minNodeSize*i+t.nodePadding)}h.parent=n;var g=Math.max(h.totalIncoming,h.totalOutgoing);if(g/o<t.minNodeSize&&(g=o*t.minNodeSize),"horizontal"==t.orientation){l=(s=(t.innerWidth-h.pixelWidth)/t._levelCount)*h.level,u=a[c]||p;var y=g*t.valueHeight;0==o&&0==y&&(y=t.minNodeSize*i),h.height=y,h.minX=l,h.maxX=l,a[c]=u+y+t.nodePadding}else{s=(t.innerHeight-h.pixelHeight)/t._levelCount,l=a[c]||p,u=s*h.level;var f=g*t.valueHeight;0==o&&0==f&&(f=t.minNodeSize*i),h.width=f,h.minY=u,h.maxY=u,a[c]=l+f+t.nodePadding}h.x=l,h.y=u})},t.prototype.showReal=function(t){var i=this;if(!this.preventShow){if(this.interpolationDuration>0){var n=this.nodesContainer,a=0;k.each(this.links.iterator(),function(e){e.hide(0)}),k.each(this._sorted,function(e){var t,r=e[1];"horizontal"==i.orientation?(r.dx=-(n.pixelWidth-r.pixelWidth)/Math.max(i._levelCount,1),t="dx"):(r.dy=-(n.pixelHeight-r.pixelHeight)/Math.max(i._levelCount,1),t="dy");var o=0,s=i.interpolationDuration;i.sequencedInterpolation&&(o=i.sequencedInterpolationDelay*a+s*a/k.length(i.nodes.iterator())),r.opacity=0,r.invalidateLinks(),r.animate([{property:"opacity",from:0,to:1},{property:t,to:0}],i.interpolationDuration,i.interpolationEasing).delay(o),k.each(r.outgoingDataItems.iterator(),function(e){var t=e.link.show(i.interpolationDuration);t&&!t.isFinished()&&t.delay(o)}),k.each(r.incomingDataItems.iterator(),function(e){if(!e.fromNode){var t=e.link.show(i.interpolationDuration);t&&!t.isFinished()&&t.delay(o)}}),a++})}return e.prototype.showReal.call(this)}},t.prototype.changeSorting=function(){var e=this;this.sortNodes();var t={};k.each(this._sorted,function(i){var n,a,r=i[1],o=r.level,s=(e.maxSum-e._levelSum[o])*e.valueHeight/2;"horizontal"==e.orientation?(n="y",a=r.pixelHeight):(n="x",a=r.pixelWidth),r.animate({property:n,to:t[o]||s},e.interpolationDuration,e.interpolationEasing),t[o]=(t[o]||s)+a+e.nodePadding,r.invalidateLinks()})},t.prototype.applyInternalDefaults=function(){e.prototype.applyInternalDefaults.call(this),m.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("Sankey diagram"))},t.prototype.createDataItem=function(){return new Ae},Object.defineProperty(t.prototype,"nodeAlign",{get:function(){return this.getPropertyValue("nodeAlign")},set:function(e){this.setPropertyValue("nodeAlign",e),this.changeSorting()},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"orientation",{get:function(){return this.getPropertyValue("orientation")},set:function(e){this.setPropertyValue("orientation",e,!0);var t=this.nodes.template.nameLabel;"vertical"==e?(this.nodes.template.width=void 0,t.label.horizontalCenter="middle",t.locationX=.5):(this.nodes.template.height=void 0,t.label.horizontalCenter="left",t.locationX=1)},enumerable:!0,configurable:!0}),t.prototype.createNode=function(){var e=new ve;return this._disposers.push(e),e},t.prototype.createLink=function(){var e=new Pe;return this._disposers.push(e),e},Object.defineProperty(t.prototype,"valueHeight",{get:function(){return this._valueHeight},set:function(e){e!=this._valueHeight&&(this._valueHeight=e,this.invalidate())},enumerable:!0,configurable:!0}),t.prototype.disposeData=function(){e.prototype.disposeData.call(this),this._sorted=this.nodes.iterator()},t}(ye);l.c.registeredClasses.SankeyDiagram=Ie;var De=i("Inf5"),Te=function(e){function t(){var t=e.call(this)||this;t.className="ChordNode";var i=t.createChild(P.a);i.location=.5,i.radius=5,i.text="{name}",i.zIndex=1,i.shouldClone=!1,t.label=i,t.layout="none",t.events.on("positionchanged",t.updateRotation,t,!1),t.isMeasured=!1,t.slice=t.createChild(De.a),t.slice.isMeasured=!1;var n=t.hiddenState;return n.properties.fill=(new w.a).getFor("disabledBackground"),n.properties.opacity=.5,n.properties.visible=!0,t.setStateOnChildren=!1,t.slice.hiddenState.properties.visible=!0,t.adapter.add("tooltipX",function(e,t){return t.slice.ix*(t.slice.radius-(t.slice.radius-t.slice.pixelInnerRadius)/2)}),t.adapter.add("tooltipY",function(e,t){return t.slice.iy*(t.slice.radius-(t.slice.radius-t.slice.pixelInnerRadius)/2)}),t}return Object(a.c)(t,e),t.prototype.invalidateLinks=function(){var t=this;e.prototype.invalidateLinks.call(this);var i=this.label,n=this.slice,a=this.chart;if(a&&n){var r=this.total,o=n.arc,s=n.startAngle;this.children.each(function(e){if(e instanceof se.a){var t=e.locationX;m.isNumber(t)||(t=.5);var i=e.locationY;m.isNumber(i)||(i=1);var a=s+o*t,r=i*n.radius;e.x=r*u.cos(a),e.y=r*u.sin(a)}});var l=s+o*i.location,h=s+(1-r/this.adjustedTotal)*o*.5;m.isNaN(h)&&(h=s),i.fixPosition(l,n.radius),this.nextAngle=h,this._outgoingSorted&&k.each(this._outgoingSorted,function(e){var i=e.link;i.parent=t.chart.linksContainer;var r=e.getWorkingValue("value");if(m.isNumber(r)){if(a.nonRibbon){var l=i.percentWidth;m.isNumber(l)||(l=5),l/=100,i.startAngle=s+o/2-o/2*l,i.arc=o*l}else i.arc=r*a.valueAngle,i.startAngle=t.nextAngle,t.nextAngle+=i.arc;e.toNode||(i.endAngle=i.startAngle),i.radius=n.pixelInnerRadius}}),this._incomingSorted&&k.each(this._incomingSorted,function(e){var i=e.link;if(i.radius=n.pixelInnerRadius,a.nonRibbon){var r=i.percentWidth;m.isNumber(r)||(r=5),r/=100,i.endAngle=s+o/2-o/2*r,i.arc=o*r}else{i.endAngle=t.nextAngle;var l=e.getWorkingValue("value");m.isNumber(l)&&(i.arc=l*a.valueAngle,t.nextAngle+=i.arc)}e.fromNode||(i.startAngle=i.endAngle)})}},t.prototype.updateRotation=function(){var e=this.slice,t=this.trueStartAngle+e.arc/2,i=e.radius,n=i*u.cos(t),a=i*u.sin(t),r=u.getAngle({x:n+this.pixelX,y:a+this.pixelY});e.startAngle=this.trueStartAngle+(r-t),this.dx=-this.pixelX,this.dy=-this.pixelY},t.prototype.copyFrom=function(t){e.prototype.copyFrom.call(this,t),this.label.copyFrom(t.label),this.slice.copyFrom(t.slice)},t}(re);l.c.registeredClasses.ChordNode=Te;var Oe=function(e){function t(){var t=e.call(this)||this;return t.className="QuadraticCurve",t.element=t.paper.add("path"),t.pixelPerfect=!1,t.fill=Object(le.c)(),t.applyTheme(),t}return Object(a.c)(t,e),t.prototype.draw=function(){if(m.isNumber(this.x1+this.x2+this.y1+this.y2+this.cpx+this.cpy)){var e={x:this.x1,y:this.y1},t={x:this.x2,y:this.y2},i={x:this.cpx,y:this.cpy},n=h.moveTo(e)+h.quadraticCurveTo(t,i);this.path=n}},Object.defineProperty(t.prototype,"cpx",{get:function(){return this.getPropertyValue("cpx")},set:function(e){this.setPropertyValue("cpx",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"cpy",{get:function(){return this.getPropertyValue("cpy")},set:function(e){this.setPropertyValue("cpy",e,!0)},enumerable:!0,configurable:!0}),t.prototype.positionToPoint=function(e){var t={x:this.x1,y:this.y1},i={x:this.cpx,y:this.cpy},n={x:this.x2,y:this.y2},a=u.getPointOnQuadraticCurve(t,n,i,e),r=u.getPointOnQuadraticCurve(t,n,i,e+.001);return{x:a.x,y:a.y,angle:u.getAngle(a,r)}},t}(he.a),Ve=function(e){function t(){var t=e.call(this)||this;return t.className="ChordLink",t.middleLine=t.createChild(Oe),t.middleLine.shouldClone=!1,t.middleLine.strokeOpacity=0,t.applyTheme(),t}return Object(a.c)(t,e),t.prototype.validate=function(){if(e.prototype.validate.call(this),!this.isTemplate){var t=this.startAngle,i=this.endAngle,n=this.arc,a=this.radius,r=this.dataItem.fromNode,o=this.dataItem.toNode,s=0,l=0;r&&(s=r.pixelX+r.dx,l=r.pixelY+r.dy);var c=0,p=0;if(o&&(c=o.pixelX+o.dx,p=o.pixelY+o.dy),a>0){var d=a*u.cos(t)+s,g=a*u.sin(t)+l,y=a*u.cos(i)+c,f=a*u.sin(i)+p,m={x:0,y:0},v=h.moveTo({x:d,y:g});v+=h.arcTo(t,n,a),v+=h.quadraticCurveTo({x:y,y:f},m),v+=h.arcTo(i,n,a),v+=h.quadraticCurveTo({x:d,y:g},m),this.link.path=n>0?v:"",this.maskBullets&&(this.bulletsMask.path=v,this.bulletsContainer.mask=this.bulletsMask);var x=t+n/2,b=i+n/2,P=this.middleLine;P.x1=a*u.cos(x)+s,P.y1=a*u.sin(x)+l,P.x2=a*u.cos(b)+c,P.y2=a*u.sin(b)+p,P.cpx=0,P.cpy=0,P.stroke=this.fill,this.positionBullets()}}},Object.defineProperty(t.prototype,"radius",{get:function(){return this.getPropertyValue("radius")},set:function(e){this.setPropertyValue("radius",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"arc",{get:function(){return this.getPropertyValue("arc")},set:function(e){this.setPropertyValue("arc",e,!0)},enumerable:!0,configurable:!0}),t}(ce);l.c.registeredClasses.ChordLink=Ve;var _e=function(e){function t(){var t=e.call(this)||this;return t.className="ChordDiagramDataItem",t.applyTheme(),t}return Object(a.c)(t,e),t}(ge),ke=function(e){function t(){var t=e.call(this)||this;t.valueAngle=0,t.className="ChordDiagram",t.startAngle=-90,t.endAngle=270,t.radius=Object(o.c)(80),t.innerRadius=-15,t.nodePadding=5;var i=t.chartContainer.createChild(d.a);return i.align="center",i.valign="middle",i.shouldClone=!1,i.layout="absolute",t.chordContainer=i,t.nodesContainer.parent=i,t.linksContainer.parent=i,t.chartContainer.events.on("maxsizechanged",t.invalidate,t,!1),t.applyTheme(),t}return Object(a.c)(t,e),t.prototype.validate=function(){var t=this,i=this.chartContainer,n=this.endAngle,a=this.startAngle+this.nodePadding/2,r=u.getArcRect(this.startAngle,this.endAngle,1);r=u.getCommonRectangle([r,{x:0,y:0,width:0,height:0}]);var o=Math.min(i.innerWidth/r.width,i.innerHeight/r.height);m.isNumber(o)||(o=0);var s=C.relativeRadiusToValue(this.radius,o),l=C.relativeRadiusToValue(this.innerRadius,s,!0),h=this.dataItem.values.value.sum,c=0,p=0;k.each(this._sorted,function(e){var i=e[1];t.getNodeValue(i),c++;var n=i.total;i.total/h<t.minNodeSize&&(n=h*t.minNodeSize),p+=n}),this.valueAngle=(n-this.startAngle-this.nodePadding*c)/p,k.each(this._sorted,function(e){var i=e[1],r=i.slice;r.radius=s,r.innerRadius=l;var o,u=i.total;i.total/h<t.minNodeSize&&(u=h*t.minNodeSize),i.adjustedTotal=u,o=t.nonRibbon?(n-t.startAngle)/c-t.nodePadding:t.valueAngle*u,r.arc=o,r.startAngle=a,i.trueStartAngle=a,i.parent=t.nodesContainer,i.validate(),a+=o+t.nodePadding}),this.chordContainer.definedBBox={x:s*r.x,y:s*r.y,width:s*r.width,height:s*r.height},this.chordContainer.invalidateLayout(),e.prototype.validate.call(this)},t.prototype.applyInternalDefaults=function(){e.prototype.applyInternalDefaults.call(this),m.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("Chord diagram"))},t.prototype.createDataItem=function(){return new _e},Object.defineProperty(t.prototype,"startAngle",{get:function(){return this.getPropertyValue("startAngle")},set:function(e){this.setPropertyValue("startAngle",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"endAngle",{get:function(){return this.getPropertyValue("endAngle")},set:function(e){this.setPropertyValue("endAngle",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"radius",{get:function(){return this.getPropertyValue("radius")},set:function(e){this.setPercentProperty("radius",e,!0,!1,10,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"innerRadius",{get:function(){return this.getPropertyValue("innerRadius")},set:function(e){this.setPercentProperty("innerRadius",e,!0,!1,10,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"nonRibbon",{get:function(){return this.getPropertyValue("nonRibbon")},set:function(e){this.setPropertyValue("nonRibbon",e,!0),this.links.template.middleLine.strokeOpacity=1,this.links.template.link.fillOpacity=0},enumerable:!0,configurable:!0}),t.prototype.createNode=function(){var e=new Te;return this._disposers.push(e),e},t.prototype.createLink=function(){var e=new Ve;return this._disposers.push(e),e},t}(ye);l.c.registeredClasses.ChordDiagram=ke;var Le=i("pR7v"),Se=i("5vid"),Re=function(e){function t(){var t=e.call(this)||this;return t.className="TreeMapSeriesDataItem",t.applyTheme(),t}return Object(a.c)(t,e),Object.defineProperty(t.prototype,"parentName",{get:function(){var e=this.treeMapDataItem;if(e&&e.parent)return e.parent.name},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"value",{get:function(){var e=this.treeMapDataItem;if(e)return e.value},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"treeMapDataItem",{get:function(){return this._dataContext},enumerable:!0,configurable:!0}),t.prototype.hide=function(t,i,n,a){var r=this.treeMapDataItem;return r&&r.hide(t),e.prototype.hide.call(this,t,i,n,a)},t.prototype.show=function(t,i,n){var a=this.treeMapDataItem;return a&&a.show(t,i,n),e.prototype.show.call(this,t,i,n)},t}(Se.b),je=function(e){function t(){var t=e.call(this)||this;t.className="TreeMapSeries",t.applyTheme(),t.fillOpacity=1,t.strokeOpacity=1,t.minBulletDistance=0,t.columns.template.tooltipText="{parentName} {name}: {value}",t.columns.template.configField="config";var i=new w.a;return t.stroke=i.getFor("background"),t.dataFields.openValueX="x0",t.dataFields.valueX="x1",t.dataFields.openValueY="y0",t.dataFields.valueY="y1",t.sequencedInterpolation=!1,t.showOnInit=!1,t.columns.template.pixelPerfect=!1,t}return Object(a.c)(t,e),t.prototype.processDataItem=function(t,i){i.seriesDataItem=t,e.prototype.processDataItem.call(this,t,i)},t.prototype.createDataItem=function(){return new Re},t.prototype.show=function(t){if(!this.preventShow){var i=this.defaultState.transitionDuration;return m.isNumber(t)&&(i=t),this.dataItems.each(function(e){e.show(t)}),e.prototype.showReal.call(this,i)}},t.prototype.hide=function(t){var i=this.defaultState.transitionDuration;m.isNumber(t)&&(i=t);var n=e.prototype.hideReal.call(this,i);return this.dataItems.each(function(e){e.hide(t)}),n},t.prototype.processValues=function(){},t.prototype.getStartLocation=function(e){return 0},t.prototype.getEndLocation=function(e){return 1},t.prototype.dataChangeUpdate=function(){},t.prototype.processConfig=function(t){t&&(m.hasValue(t.dataFields)&&m.isObject(t.dataFields)||(t.dataFields={})),e.prototype.processConfig.call(this,t)},t.prototype.createLegendMarker=function(e){var t=e.pixelWidth,i=e.pixelHeight;e.removeChildren();var n=e.createChild(ie.a);n.shouldClone=!1,ne.copyProperties(this,n,J.b),n.padding(0,0,0,0),n.width=t,n.height=i;var a=e.dataItem;a.color=n.fill,a.colorOrig=n.fill},t.prototype.disableUnusedColumns=function(t){e.prototype.disableUnusedColumns.call(this,t),t.column&&(t.column.__disabled=!1)},t}(Se.a);l.c.registeredClasses.TreeMapSeries=je,l.c.registeredClasses.TreeMapSeriesDataItem=Re;var we=i("qCRI"),Ne=i("hJ5i"),Fe=function(e){function t(){var t=e.call(this)||this;return t.rows=[],t.className="TreeMapDataItem",t.values.value={},t.values.x0={},t.values.y0={},t.values.x1={},t.values.y1={},t.hasChildren.children=!0,t.applyTheme(),t}return Object(a.c)(t,e),Object.defineProperty(t.prototype,"legendDataItem",{get:function(){return this._legendDataItem},set:function(e){this._legendDataItem=e,e.label&&(e.label.dataItem=this),e.valueLabel&&(e.valueLabel.dataItem=this)},enumerable:!0,configurable:!0}),t.prototype.getDuration=function(){return 0},Object.defineProperty(t.prototype,"value",{get:function(){var e=0;return this.children&&0!=this.children.length?k.each(this.children.iterator(),function(t){var i=t.value;m.isNumber(i)&&(e+=i)}):e=this.values.value.workingValue,e},set:function(e){this.setValue("value",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"percent",{get:function(){return this.parent?this.value/this.parent.value*100:100},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"x0",{get:function(){return this.values.x0.value},set:function(e){this.setValue("x0",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"x1",{get:function(){return this.values.x1.value},set:function(e){this.setValue("x1",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"y0",{get:function(){return this.values.y0.value},set:function(e){this.setValue("y0",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"y1",{get:function(){return this.values.y1.value},set:function(e){this.setValue("y1",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"name",{get:function(){return this.properties.name},set:function(e){this.setProperty("name",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"children",{get:function(){return this.properties.children},set:function(e){this.setProperty("children",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"level",{get:function(){return this.parent?this.parent.level+1:0},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"color",{get:function(){var e=this.properties.color;return void 0==e&&this.parent&&(e=this.parent.color),void 0==e&&this.component&&(e=this.component.colors.getIndex(this.component.colors.step*this.index)),e},set:function(e){this.setProperty("color",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"fill",{get:function(){return this.color},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"series",{get:function(){return this._series},set:function(e){e!=this._series&&(this._series&&(this.component.series.removeValue(this._series),this._series.dispose()),this._series=e,this._disposers.push(e))},enumerable:!0,configurable:!0}),t.prototype.hide=function(t,i,n,a){this.setWorkingValue("value",0),this.children&&this.children.each(function(e){e.hide(t,i,n,a)});var r=this.seriesDataItem;return r&&r.bullets.each(function(e,t){t.hide(),t.preventShow=!0}),e.prototype.hide.call(this,t,i,n,a)},t.prototype.show=function(t,i,n){this.setWorkingValue("value",this.values.value.value),this.children&&this.children.each(function(e){e.show(t,i,n)});var a=this.seriesDataItem;return a&&a.bullets.each(function(e,t){t.preventShow=!1}),e.prototype.show.call(this,t,i,n)},t}(r.b),Ye=function(e){function t(){var t=e.call(this)||this;t.layoutAlgorithm=t.squarify,t.zoomable=!0,t.className="TreeMap",t._usesData=!0,t.maxLevels=2,t.currentLevel=0,t.hideParentColumns=!1,t.colors=new de.a,t.sorting="descending";var i=t.xAxes.push(new Le.a);i.title.disabled=!0,i.strictMinMax=!0;var n=i.renderer;n.inside=!0,n.labels.template.disabled=!0,n.ticks.template.disabled=!0,n.grid.template.disabled=!0,n.axisFills.template.disabled=!0,n.minGridDistance=100,n.line.disabled=!0,n.baseGrid.disabled=!0;var a=t.yAxes.push(new Le.a);a.title.disabled=!0,a.strictMinMax=!0;var r=a.renderer;r.inside=!0,r.labels.template.disabled=!0,r.ticks.template.disabled=!0,r.grid.template.disabled=!0,r.axisFills.template.disabled=!0,r.minGridDistance=100,r.line.disabled=!0,r.baseGrid.disabled=!0,r.inversed=!0,t.xAxis=i,t.yAxis=a;var o=new je;return t.seriesTemplates=new Q.c(o),o.virtualParent=t,t._disposers.push(new Q.b(t.seriesTemplates)),t._disposers.push(o),t.zoomOutButton.events.on("hit",function(){t.zoomToChartDataItem(t._homeDataItem)},void 0,!1),t.seriesTemplates.events.on("insertKey",function(e){e.newValue.isTemplate=!0},void 0,!1),t.applyTheme(),t}return Object(a.c)(t,e),Object.defineProperty(t.prototype,"navigationBar",{get:function(){return this._navigationBar},set:function(e){var t=this;this._navigationBar!=e&&(this._navigationBar=e,e.parent=this,e.toBack(),e.links.template.events.on("hit",function(e){var i=e.target.dataItem.dataContext;i.isDisposed()||(t.zoomToChartDataItem(i),t.createTreeSeries(i))},void 0,!0),this._disposers.push(e))},enumerable:!0,configurable:!0}),t.prototype.validateData=function(){this.series.clear(),this._tempSeries=[],e.prototype.validateData.call(this),this._homeDataItem&&this._homeDataItem.dispose();var t=this.dataItems.template.clone();this._homeDataItem=t,k.each(this.dataItems.iterator(),function(e){e.parent=t}),t.children=this.dataItems,t.x0=0,t.y0=0,t.name=this._homeText;var i=10*Math.round(1e3*this.pixelHeight/this.pixelWidth/10)||1e3;t.x1=1e3,t.y1=i,this.xAxis.min=0,this.xAxis.max=1e3,this.xAxis.getMinMax(),this.yAxis.min=0,this.yAxis.max=i,this.yAxis.getMinMax(),this.layoutItems(t),this.createTreeSeries(t),this.feedLegend()},t.prototype.layoutItems=function(e,t){if(e){var i=e.children;t||(t=this.sorting),"ascending"==t&&i.values.sort(function(e,t){return e.value-t.value}),"descending"==t&&i.values.sort(function(e,t){return t.value-e.value}),this._updateDataItemIndexes(0),this.layoutAlgorithm(e);for(var n=0,a=i.length;n<a;n++){var r=i.getIndex(n);r.children&&this.layoutItems(r)}}},t.prototype.createTreeSeries=function(e){var t=this;this._tempSeries=[];for(var i=[e],n=e.parent;void 0!=n;)this.initSeries(n),i.push(n),n=n.parent;i.reverse(),this.navigationBar&&(this.navigationBar.data=i),this.createTreeSeriesReal(e),Ne.each(this._tempSeries,function(e){-1==t.series.indexOf(e)&&t.series.push(e),e.zIndex=e.level})},t.prototype.createTreeSeriesReal=function(e){if(e.children&&e.level<this.currentLevel+this.maxLevels){this.initSeries(e);for(var t=0;t<e.children.length;t++){var i=e.children.getIndex(t);i.children&&this.createTreeSeriesReal(i)}}},t.prototype.setData=function(t){this.currentLevel=0,this.currentlyZoomed=void 0,this.xAxis.start=0,this.xAxis.end=1,this.yAxis.start=0,this.yAxis.end=1,e.prototype.setData.call(this,t)},t.prototype.seriesAppeared=function(){return!0},t.prototype.initSeries=function(e){var t=this;if(!e.series){var i=void 0,n=this.seriesTemplates.getKey(e.level.toString());(i=n?n.clone():this.series.create()).dataItem.dataContext=e,i.name=e.name,i.parentDataItem=e,e.series=i;var a=e.level;i.level=a;var r=e.dataContext;r&&(i.config=r.config),this.dataUsers.removeValue(i),i.data=e.children.values,i.fill=e.color,i.columnsContainer.hide(0),i.bulletsContainer.hide(0),i.columns.template.adapter.add("fill",function(e,t){var i=t.dataItem;if(i){var n=i.treeMapDataItem;if(n)return t.fill=n.color,t.adapter.remove("fill"),n.color}}),this.zoomable&&(e.level>this.currentLevel||e.children&&e.children.length>0)&&(i.columns.template.cursorOverStyle=we.a.pointer,this.zoomable&&i.columns.template.events.on("hit",function(i){var n=i.target.dataItem;e.level>t.currentLevel?t.zoomToChartDataItem(n.treeMapDataItem.parent):t.zoomToSeriesDataItem(n)},this,void 0))}this._tempSeries.push(e.series)},t.prototype.toggleBullets=function(e){var t=this;k.each(this.series.iterator(),function(i){-1==t._tempSeries.indexOf(i)?(i.columnsContainer.hide(),i.bulletsContainer.hide(e)):(i.columnsContainer.show(),i.bulletsContainer.show(e),i.dataItems.each(function(e){e.bullets.each(function(e,t){t.show()})}),i.level<t.currentLevel?(t.hideParentColumns&&i.columnsContainer.hide(),i.bulletsContainer.hide(e)):i.level==t.currentLevel&&t.maxLevels>1&&i.dataItems.each(function(e){e.treeMapDataItem.children&&e.bullets.each(function(e,t){t.hide()})}))})},t.prototype.zoomToSeriesDataItem=function(e){this.zoomToChartDataItem(e.treeMapDataItem)},t.prototype.zoomToChartDataItem=function(e){var t=this;e||(e=this._homeDataItem);var i=this.zoomOutButton;if(i&&(e!=this._homeDataItem?i.show():i.hide()),e&&e.children){this.xAxis.zoomToValues(e.x0,e.x1),this.yAxis.zoomToValues(e.y0,e.y1),this.currentLevel=e.level,this.currentlyZoomed=e,this.createTreeSeries(e);var n=this.xAxis.rangeChangeAnimation||this.yAxis.rangeChangeAnimation;!n||n.isDisposed()||n.isFinished()?this.toggleBullets():(this._dataDisposers.push(n),n.events.once("animationended",function(){t.toggleBullets()}))}},t.prototype.applyInternalDefaults=function(){e.prototype.applyInternalDefaults.call(this),m.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("TreeMap chart"))},t.prototype.createDataItem=function(){return new Fe},Object.defineProperty(t.prototype,"maxLevels",{get:function(){return this.getPropertyValue("maxLevels")},set:function(e){this.setPropertyValue("maxLevels",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"currentLevel",{get:function(){return this.getPropertyValue("currentLevel")},set:function(e){this.setPropertyValue("currentLevel",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"hideParentColumns",{get:function(){return this.getPropertyValue("hideParentColumns")},set:function(e){this.setPropertyValue("hideParentColumns",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"sorting",{get:function(){return this.getPropertyValue("sorting")},set:function(e){this.setPropertyValue("sorting",e,!0)},enumerable:!0,configurable:!0}),t.prototype.createSeries=function(){return new je},Object.defineProperty(t.prototype,"homeText",{get:function(){return this._homeText},set:function(e){this._homeText=e,this._homeDataItem&&(this._homeDataItem.name=this._homeText)},enumerable:!0,configurable:!0}),t.prototype.processConfig=function(t){if(t){if(m.hasValue(t.layoutAlgorithm)&&m.isString(t.layoutAlgorithm))switch(t.layoutAlgorithm){case"squarify":t.layoutAlgorithm=this.squarify;break;case"binaryTree":t.layoutAlgorithm=this.binaryTree;break;case"slice":t.layoutAlgorithm=this.slice;break;case"dice":t.layoutAlgorithm=this.dice;break;case"sliceDice":t.layoutAlgorithm=this.sliceDice;break;default:delete t.layoutAlgorithm}m.hasValue(t.navigationBar)&&!m.hasValue(t.navigationBar.type)&&(t.navigationBar.type="NavigationBar"),e.prototype.processConfig.call(this,t)}},t.prototype.validateLayout=function(){e.prototype.validateLayout.call(this),this.layoutItems(this.currentlyZoomed)},t.prototype.validateDataItems=function(){e.prototype.validateDataItems.call(this),this.layoutItems(this._homeDataItem),k.each(this.series.iterator(),function(e){e.validateRawData()}),this.zoomToChartDataItem(this._homeDataItem)},t.prototype.binaryTree=function(e){var t,i,n=e.children,a=n.length,r=new Array(a+1);for(r[0]=i=t=0;t<a;++t)r[t+1]=i+=n.getIndex(t).value;a>0&&function e(t,i,a,o,s,l,u){if(t>=i-1){var h=n.getIndex(t);return h.x0=o,h.y0=s,h.x1=l,void(h.y1=u)}var c=r[t],p=a/2+c,d=t+1,g=i-1;for(;d<g;){var y=d+g>>>1;r[y]<p?d=y+1:g=y}p-r[d-1]<r[d]-p&&t+1<d&&--d;var f=r[d]-c,m=a-f;if(l-o>u-s){var v=(o*m+l*f)/a;e(t,d,f,o,s,v,u),e(d,i,m,v,s,l,u)}else{var x=(s*m+u*f)/a;e(t,d,f,o,s,l,x),e(d,i,m,o,x,l,u)}}(0,a,e.value,e.x0,e.y0,e.x1,e.y1)},t.prototype.slice=function(e){for(var t,i=e.x0,n=e.x1,a=e.y0,r=e.y1,o=e.children,s=-1,l=o.length,u=e.value&&(r-a)/e.value;++s<l;)(t=o.getIndex(s)).x0=i,t.x1=n,t.y0=a,a+=t.value*u,t.y1=a},t.prototype.dice=function(e){for(var t,i=e.x0,n=e.x1,a=e.y0,r=e.y1,o=e.children,s=-1,l=o.length,u=e.value&&(n-i)/e.value;++s<l;)(t=o.getIndex(s)).y0=a,t.y1=r,t.x0=i,i+=t.value*u,t.x1=i},t.prototype.sliceDice=function(e){1&e.level?this.slice(e):this.dice(e)},t.prototype.squarify=function(e){for(var t,i,n,a,r,o,s,l,u,h,c=(1+Math.sqrt(5))/2,p=e.x0,d=e.x1,g=e.y0,y=e.y1,f=e.children,m=0,v=0,x=f.length,b=e.value;m<x;){i=d-p,n=y-g;do{a=f.getIndex(v++).value}while(!a&&v<x);for(r=o=a,h=a*a*(u=Math.max(n/i,i/n)/(b*c)),l=Math.max(o/h,h/r);v<x;++v){if(a+=t=f.getIndex(v).value,t<r&&(r=t),t>o&&(o=t),h=a*a*u,(s=Math.max(o/h,h/r))>l){a-=t;break}l=s}var P=this.dataItems.template.clone();P.value=a,P.dice=i<n,P.children=f.slice(m,v),P.x0=p,P.y0=g,P.x1=d,P.y1=y,P.dice?(P.y1=b?g+=n*a/b:y,this.dice(P)):(P.x1=b?p+=i*a/b:d,this.slice(P)),b-=a,m=v}},t.prototype.handleSeriesAdded2=function(){},t.prototype.handleDataItemValueChange=function(e,t){"value"==t&&this.invalidateDataItems()},t.prototype.handleDataItemWorkingValueChange=function(e,t){"value"==t&&this.invalidateDataItems()},t.prototype.getLegendLevel=function(e){if(e&&e.children){if(e.children.length>1)return e;if(1==e.children.length){var t=e.children.getIndex(0);return t.children?this.getLegendLevel(t):e}return e}},Object.defineProperty(t.prototype,"homeDataItem",{get:function(){return this._homeDataItem},enumerable:!0,configurable:!0}),t.prototype.feedLegend=function(){var e=this.legend;if(e){e.dataFields.name="name";var t=this.getLegendLevel(this._homeDataItem);if(t){var i=[];t.children.each(function(e){i.push(e)}),e.data=i}}},t.prototype.disposeData=function(){e.prototype.disposeData.call(this),this._homeDataItem=void 0,this.series.clear(),this.navigationBar&&this.navigationBar.disposeData(),this.xAxis.disposeData(),this.yAxis.disposeData()},t.prototype.getExporting=function(){var t=this,i=e.prototype.getExporting.call(this);return i.adapter.add("formatDataFields",function(e){return"csv"!=e.format&&"xlsx"!=e.format||m.hasValue(t.dataFields.children)&&delete e.dataFields[t.dataFields.children],e}),i},t}(r.a);l.c.registeredClasses.TreeMap=Ye;var We=i("k6kv"),Xe=function(e){function t(){var t=e.call(this)||this;return t._chart=new V.d,t.className="AxisRendererX3D",t._disposers.push(t._chart),t.applyTheme(),t}return Object(a.c)(t,e),t.prototype.updateGridElement=function(e,t,i){t+=(i-t)*e.location;var n=this.positionToPoint(t);if(e.element){var a=this.chart.dx3D||0,r=this.chart.dy3D||0,o=this.getHeight();e.path=h.moveTo({x:a,y:r})+h.lineTo({x:a,y:o+r})+h.lineTo({x:0,y:o})}this.positionItem(e,n),this.toggleVisibility(e,t,0,1)},t.prototype.updateBaseGridElement=function(){e.prototype.updateBaseGridElement.call(this);var t=this.getHeight(),i=this.chart.dx3D||0,n=this.chart.dy3D||0;this.baseGrid.path=h.moveTo({x:i,y:n})+h.lineTo({x:0,y:0})+h.lineTo({x:0,y:t})},Object.defineProperty(t.prototype,"chart",{get:function(){return this._chart.get()},set:function(e){e&&this._chart.set(e,e.events.on("propertychanged",this.handle3DChanged,this,!1))},enumerable:!0,configurable:!0}),t.prototype.handle3DChanged=function(e){"depth"!=e.property&&"angle"!=e.property||this.invalidate()},t}(We.a);l.c.registeredClasses.AxisRendererX3D=Xe;var Me=function(e){function t(){var t=e.call(this)||this;return t._chart=new V.d,t.className="AxisRendererY3D",t._disposers.push(t._chart),t.applyTheme(),t}return Object(a.c)(t,e),t.prototype.updateGridElement=function(e,t,i){t+=(i-t)*e.location;var n=this.positionToPoint(t);if(e.element){var a=this.chart.dx3D||0,r=this.chart.dy3D||0,o=this.getWidth();e.path=h.moveTo({x:0,y:0})+h.lineTo({x:a,y:r})+h.lineTo({x:o+a,y:r})}this.positionItem(e,n),this.toggleVisibility(e,t,0,1)},t.prototype.updateBaseGridElement=function(){e.prototype.updateBaseGridElement.call(this);var t=this.chart.dx3D||0,i=this.chart.dy3D||0,n=this.getWidth();this.baseGrid.path=h.moveTo({x:0,y:0})+h.lineTo({x:n,y:0})+h.lineTo({x:n+t,y:i})},Object.defineProperty(t.prototype,"chart",{get:function(){return this._chart.get()},set:function(e){e&&this._chart.set(e,e.events.on("propertychanged",this.handle3DChanged,this,!1))},enumerable:!0,configurable:!0}),t.prototype.handle3DChanged=function(e){"depth"!=e.property&&"angle"!=e.property||this.invalidate()},t}(D.a),He=i("DG6Q"),Be=i("Mr4Y"),ze=function(e){function t(){var t=e.call(this)||this;return t.className="Column3D",t}return Object(a.c)(t,e),t.prototype.createAssets=function(){this.column3D=this.createChild(Be.a),this.column3D.shouldClone=!1,this.column3D.strokeOpacity=0,this.column=this.column3D},t.prototype.validate=function(){e.prototype.validate.call(this),this.column3D&&(this.column3D.width=this.pixelWidth,this.column3D.height=this.pixelHeight,this.column3D.invalid&&this.column3D.validate())},t.prototype.copyFrom=function(t){e.prototype.copyFrom.call(this,t),this.column3D&&this.column3D.copyFrom(t.column3D)},t.prototype.setFill=function(t){e.prototype.setFill.call(this,t),this.column.fill=t},t}(He.a);l.c.registeredClasses.Column3D=ze;var Ee=function(e){function t(){var t=e.call(this)||this;return t.className="ColumnSeries3DDataItem",t.applyTheme(),t}return Object(a.c)(t,e),t}(Se.b),Ge=function(e){function t(){var t=e.call(this)||this;return t.className="ColumnSeries3D",t.columns.template.column3D.applyOnClones=!0,t.columns.template.hiddenState.properties.visible=!0,t.applyTheme(),t}return Object(a.c)(t,e),Object.defineProperty(t.prototype,"columnsContainer",{get:function(){var e=this.chart;return e&&e.columnsContainer&&"vertical"!=e.leftAxesContainer.layout&&"vertical"!=e.rightAxesContainer.layout&&"horizontal"!=e.bottomAxesContainer.layout&&"horizontal"!=e.topAxesContainer.layout?e.columnsContainer:this._columnsContainer},enumerable:!0,configurable:!0}),t.prototype.validateDataElementReal=function(t){e.prototype.validateDataElementReal.call(this,t),t.column&&(t.column.dx=this.dx,t.column.dy=this.dy)},t.prototype.validateDataElements=function(){e.prototype.validateDataElements.call(this),this.chart&&this.chart.invalidateLayout()},t.prototype.createColumnTemplate=function(){return new ze},Object.defineProperty(t.prototype,"depth",{get:function(){return this.getPropertyValue("depth")},set:function(e){this.setPropertyValue("depth",e,!0),this.columns.template.column3D.depth=e},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"angle",{get:function(){return this.getPropertyValue("angle")},set:function(e){this.setPropertyValue("angle",e),this.columns.template.column3D.angle=e},enumerable:!0,configurable:!0}),t}(Se.a);l.c.registeredClasses.ColumnSeries3D=Ge,l.c.registeredClasses.ColumnSeries3DDataItem=Ee;var qe=function(e){function t(){var t=e.call(this)||this;return t.className="XYChart3DDataItem",t.applyTheme(),t}return Object(a.c)(t,e),t}(r.b),Ke=function(e){function t(){var t=e.call(this)||this;t._axisRendererX=Xe,t._axisRendererY=Me,t.className="XYChart3D",t.depth=30,t.angle=30;var i=t.seriesContainer.createChild(d.a);return i.shouldClone=!1,i.isMeasured=!1,i.layout="none",t.columnsContainer=i,t.columnsContainer.mask=t.createChild(J.a),t.applyTheme(),t}return Object(a.c)(t,e),t.prototype.updateSeriesMasks=function(){if(e.prototype.updateSeriesMasks.call(this),C.isIE()){var t=this.columnsContainer,i=t.mask;t.mask=void 0,t.mask=i}},Object.defineProperty(t.prototype,"depth",{get:function(){return this.getPropertyValue("depth")},set:function(e){this.setPropertyValue("depth",e),this.fixLayout(),this.invalidateDataUsers()},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"angle",{get:function(){return this.getPropertyValue("angle")},set:function(e){this.setPropertyValue("angle",e),this.fixLayout(),this.invalidateDataUsers()},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"dx3D",{get:function(){return u.cos(this.angle)*this.depth},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"dy3D",{get:function(){return-u.sin(this.angle)*this.depth},enumerable:!0,configurable:!0}),t.prototype.validateLayout=function(){e.prototype.validateLayout.call(this),this.fixColumns()},t.prototype.fixLayout=function(){this.chartContainer.marginTop=-this.dy3D,this.chartContainer.paddingRight=this.dx3D,this.scrollbarX&&(this.scrollbarX.dy=this.dy3D,this.scrollbarX.dx=this.dx3D),this.scrollbarY&&(this.scrollbarY.dy=this.dy3D,this.scrollbarY.dx=this.dx3D),this.fixColumns(),e.prototype.fixLayout.call(this)},t.prototype.fixColumns=function(){var e=this,t=1,i=0;k.each(this.series.iterator(),function(e){e instanceof Ge&&(!e.clustered&&i>0&&t++,e.depthIndex=t-1,i++)});var n=0;k.each(this.series.iterator(),function(i){if(i instanceof Ge){i.depth=e.depth/t,i.angle=e.angle,i.columnsContainer==e.columnsContainer&&(i.dx=e.depth/t*u.cos(e.angle)*i.depthIndex,i.dy=-e.depth/t*u.sin(e.angle)*i.depthIndex);var a=!1;(i.baseAxis==i.xAxis&&i.xAxis.renderer.inversed||i.baseAxis==i.yAxis&&i.yAxis.renderer.inversed)&&(a=!0);var r=1;i.dataItems.each(function(e){var t=e.column;t&&(t.zIndex=a?1e3*(1e3-r)+n-100*i.depthIndex:1e3*r+n-100*i.depthIndex),r++}),a?n--:n++}}),this.maskColumns()},t.prototype.processConfig=function(t){if(t&&m.hasValue(t.series)&&m.isArray(t.series))for(var i=0,n=t.series.length;i<n;i++)t.series[i].type=t.series[i].type||"ColumnSeries3D";e.prototype.processConfig.call(this,t)},t.prototype.maskColumns=function(){var e=this.plotContainer.pixelWidth,t=this.plotContainer.pixelHeight,i=this.dx3D,n=this.dy3D,a=h.moveTo({x:0,y:0})+h.lineTo({x:i,y:n})+h.lineTo({x:e+i,y:n})+h.lineTo({x:e+i,y:t+n})+h.lineTo({x:e,y:t})+h.lineTo({x:e,y:t})+h.lineTo({x:0,y:t})+h.closePath(),r=this.columnsContainer;r&&r.mask&&(r.mask.path=a)},t}(r.a);l.c.registeredClasses.XYChart3D=Ke;var Ue=i("2OXf"),Ze=i("aM7D"),Qe=i("Uf57"),Je=i("YOID"),$e=function(e){function t(){var t=e.call(this)||this;return t.className="Candlestick",t.layout="none",t}return Object(a.c)(t,e),t.prototype.createAssets=function(){e.prototype.createAssets.call(this),this.lowLine=this.createChild(he.a),this.lowLine.shouldClone=!1,this.highLine=this.createChild(he.a),this.highLine.shouldClone=!1},t.prototype.copyFrom=function(t){e.prototype.copyFrom.call(this,t),this.lowLine&&this.lowLine.copyFrom(t.lowLine),this.highLine&&this.highLine.copyFrom(t.highLine)},t}(He.a);l.c.registeredClasses.Candlestick=$e;var et=function(e){function t(){var t=e.call(this)||this;return t.values.lowValueX={},t.values.lowValueY={},t.values.highValueX={},t.values.highValueY={},t.className="CandlestickSeriesDataItem",t.applyTheme(),t}return Object(a.c)(t,e),Object.defineProperty(t.prototype,"lowValueX",{get:function(){return this.values.lowValueX.value},set:function(e){this.setValue("lowValueX",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"lowValueY",{get:function(){return this.values.lowValueY.value},set:function(e){this.setValue("lowValueY",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"highValueX",{get:function(){return this.values.highValueX.value},set:function(e){this.setValue("highValueX",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"highValueY",{get:function(){return this.values.highValueY.value},set:function(e){this.setValue("highValueY",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"closeValueX",{get:function(){return this.values.valueX.value},set:function(e){this.setValue("valueX",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"closeValueY",{get:function(){return this.values.valueY.value},set:function(e){this.setValue("valueY",e)},enumerable:!0,configurable:!0}),t}(Se.b),tt=function(e){function t(){var t=e.call(this)||this;t.className="CandlestickSeries",t.groupFields.lowValueX="low",t.groupFields.lowValueY="low",t.groupFields.highValueX="high",t.groupFields.highValueY="high",t.strokeOpacity=1;var i=new w.a,n=i.getFor("positive"),a=i.getFor("negative");return t.dropFromOpenState.properties.fill=a,t.dropFromOpenState.properties.stroke=a,t.riseFromOpenState.properties.fill=n,t.riseFromOpenState.properties.stroke=n,t.applyTheme(),t}return Object(a.c)(t,e),t.prototype.applyInternalDefaults=function(){e.prototype.applyInternalDefaults.call(this),m.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("Candlestick Series"))},t.prototype.createDataItem=function(){return new et},t.prototype.validateDataElementReal=function(t){e.prototype.validateDataElementReal.call(this,t),this.validateCandlestick(t)},t.prototype.validateCandlestick=function(e){var t=e.column;if(t){var i=t.lowLine,n=t.highLine;if(this.baseAxis==this.xAxis){var a=t.pixelWidth/2;i.x=a,n.x=a;var r=e.getWorkingValue(this.yOpenField),o=e.getWorkingValue(this.yField),s=this.yAxis.getY(e,this.yOpenField),l=this.yAxis.getY(e,this.yField),u=this.yAxis.getY(e,this.yLowField),h=this.yAxis.getY(e,this.yHighField),c=t.pixelY;i.y1=u-c,n.y1=h-c,r<o?(i.y2=s-c,n.y2=l-c):(i.y2=l-c,n.y2=s-c)}if(this.baseAxis==this.yAxis){var p=t.pixelHeight/2;i.y=p,n.y=p;var d=e.getWorkingValue(this.xOpenField),g=e.getWorkingValue(this.xField),y=this.xAxis.getX(e,this.xOpenField),f=this.xAxis.getX(e,this.xField),m=this.xAxis.getX(e,this.xLowField),v=this.xAxis.getX(e,this.xHighField),x=t.pixelX;i.x1=m-x,n.x1=v-x,d<g?(i.x2=y-x,n.x2=f-x):(i.x2=f-x,n.x2=y-x)}k.each(this.axisRanges.iterator(),function(t){var a=e.rangesColumns.getKey(t.uid);if(a){var r=a.lowLine;r.x=i.x,r.y=i.y,r.x1=i.x1,r.x2=i.x2,r.y1=i.y1,r.y2=i.y2;var o=a.highLine;o.x=n.x,o.y=n.y,o.x1=n.x1,o.x2=n.x2,o.y1=n.y1,o.y2=n.y2}})}},Object.defineProperty(t.prototype,"xLowField",{get:function(){return this._xLowField},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"yLowField",{get:function(){return this._yLowField},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"xHighField",{get:function(){return this._xHighField},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"yHighField",{get:function(){return this._yHighField},enumerable:!0,configurable:!0}),t.prototype.defineFields=function(){e.prototype.defineFields.call(this);var t=this.xAxis,i=this.yAxis;if(t&&i){if(this.baseAxis==t){var n=C.capitalize(i.axisFieldName);this._yLowField="low"+n+"Y",this._yHighField="high"+n+"Y"}if(this.baseAxis==i){var a=C.capitalize(t.axisFieldName);this._xLowField="low"+a+"X",this._xHighField="high"+a+"X"}this.addValueField(t,this._xValueFields,this._xLowField),this.addValueField(t,this._xValueFields,this._xHighField),this.addValueField(i,this._yValueFields,this._yLowField),this.addValueField(i,this._yValueFields,this._yHighField)}},t.prototype.createLegendMarker=function(e){var t=e.pixelWidth,i=e.pixelHeight;e.removeChildren();var n,a,r=e.createChild($e);r.shouldClone=!1,r.copyFrom(this.columns.template);var o=r.lowLine,s=r.highLine;this.baseAxis==this.yAxis?(n=t/3,a=i,o.y=i/2,s.y=i/2,o.x2=t/3,s.x2=t/3,s.x=t/3*2,r.column.x=t/3):(n=t,a=i/3,o.x=t/2,s.x=t/2,o.y2=i/3,s.y2=i/3,s.y=i/3*2,r.column.y=i/3),r.width=n,r.height=a,ne.copyProperties(this,e,J.b),ne.copyProperties(this.columns.template,r,J.b),r.stroke=this.riseFromOpenState.properties.stroke,r.fill=r.stroke;var l=e.dataItem;l.color=r.fill,l.colorOrig=r.fill},t.prototype.createColumnTemplate=function(){return new $e},t}(Se.a);l.c.registeredClasses.CandlestickSeries=tt,l.c.registeredClasses.CandlestickSeriesDataItem=et;var it=function(e){function t(){var t=e.call(this)||this;return t.className="OHLC",t.layout="none",t}return Object(a.c)(t,e),t.prototype.createAssets=function(){this.openLine=this.createChild(he.a),this.openLine.shouldClone=!1,this.highLowLine=this.createChild(he.a),this.highLowLine.shouldClone=!1,this.closeLine=this.createChild(he.a),this.closeLine.shouldClone=!1},t.prototype.copyFrom=function(t){e.prototype.copyFrom.call(this,t),this.openLine&&this.openLine.copyFrom(t.openLine),this.highLowLine&&this.highLowLine.copyFrom(t.highLowLine),this.closeLine&&this.closeLine.copyFrom(t.closeLine)},t}($e);l.c.registeredClasses.OHLC=it;var nt=function(e){function t(){var t=e.call(this)||this;return t.className="OHLCSeriesDataItem",t.applyTheme(),t}return Object(a.c)(t,e),t}(et),at=function(e){function t(){var t=e.call(this)||this;return t.className="OHLCSeries",t.applyTheme(),t}return Object(a.c)(t,e),t.prototype.applyInternalDefaults=function(){e.prototype.applyInternalDefaults.call(this),m.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("OHLC Series"))},t.prototype.createDataItem=function(){return new nt},t.prototype.validateCandlestick=function(e){var t=e.column;if(t){var i=t.openLine,n=t.highLowLine,a=t.closeLine;if(this.baseAxis==this.xAxis){var r=t.pixelWidth/2;n.x=r,e.getWorkingValue(this.yOpenField),e.getWorkingValue(this.yField);var o=this.yAxis.getY(e,this.yOpenField),s=this.yAxis.getY(e,this.yField),l=this.yAxis.getY(e,this.yLowField),u=this.yAxis.getY(e,this.yHighField),h=t.pixelY;i.y1=o-h,i.y2=o-h,i.x1=0,i.x2=r,a.y1=s-h,a.y2=s-h,a.x1=r,a.x2=2*r,n.y1=u-h,n.y2=l-h}if(this.baseAxis==this.yAxis){var c=t.pixelHeight/2;n.y=c,e.getWorkingValue(this.xOpenField),e.getWorkingValue(this.xField);var p=this.xAxis.getX(e,this.xOpenField),d=this.xAxis.getX(e,this.xField),g=this.xAxis.getX(e,this.xLowField),y=this.xAxis.getX(e,this.xHighField),f=t.pixelX;i.x1=p-f,i.x2=p-f,i.y1=c,i.y2=2*c,a.x1=d-f,a.x2=d-f,a.y1=0,a.y2=c,n.x1=y-f,n.x2=g-f}k.each(this.axisRanges.iterator(),function(t){var r=e.rangesColumns.getKey(t.uid);if(r){var o=r.openLine;o.x=i.x,o.y=i.y,o.x1=i.x1,o.x2=i.x2,o.y1=i.y1,o.y2=i.y2;var s=r.closeLine;s.x=a.x,s.y=a.y,s.x1=a.x1,s.x2=a.x2,s.y1=a.y1,s.y2=a.y2;var l=r.highLowLine;l.x=n.x,l.y=n.y,l.x1=n.x1,l.x2=n.x2,l.y1=n.y1,l.y2=n.y2}})}},t.prototype.createLegendMarker=function(e){var t=e.pixelWidth,i=e.pixelHeight;e.removeChildren();var n,a,r=e.createChild(it);r.shouldClone=!1,r.copyFrom(this.columns.template);var o=r.openLine,s=r.closeLine,l=r.highLowLine;this.baseAxis==this.yAxis?(n=t/3,a=i,l.y=i/2,l.x2=t,o.x=t/3*2,o.y2=i/2,s.x=t/3,s.y2=i,s.y1=i/2):(n=t,a=i/3,l.x=t/2,l.y2=i,o.y=i/3*2,o.x2=t/2,s.y=i/3,s.x2=t,s.x1=t/2),r.width=n,r.height=a,ne.copyProperties(this,e,J.b),ne.copyProperties(this.columns.template,r,J.b),r.stroke=this.riseFromOpenState.properties.stroke;var u=e.dataItem;u.color=r.stroke,u.colorOrig=r.stroke},t.prototype.createColumnTemplate=function(){return new it},t}(tt);l.c.registeredClasses.OHLCSeries=at,l.c.registeredClasses.OHLCSeriesDataItem=nt;var rt=function(e){function t(){var t=e.call(this)||this;return t.className="StepLineSeriesSegment",t}return Object(a.c)(t,e),t.prototype.drawSegment=function(e,t,i,n,a,r){if(e.length>0&&t.length>0)if(a){var o=h.moveTo(e[0]);if(e.length>0)for(var s=1;s<e.length;s++){var l=e[s];s/2==Math.round(s/2)?o+=h.moveTo(l):o+=h.lineTo(l)}this.strokeSprite.path=o,(this.fillOpacity>0||this.fillSprite.fillOpacity>0)&&(o=h.moveTo(e[0])+h.polyline(e),o+=h.lineTo(t[0])+h.polyline(t),o+=h.lineTo(e[0]),o+=h.closePath(),this.fillSprite.path=o)}else{o=h.moveTo(e[0])+h.polyline(e);this.strokeSprite.path=o,(this.fillOpacity>0||this.fillSprite.fillOpacity>0)&&(o+=h.lineTo(t[0])+h.polyline(t),o+=h.lineTo(e[0]),o+=h.closePath(),this.fillSprite.path=o)}},t}(Je.a);l.c.registeredClasses.StepLineSeriesSegment=rt;var ot=function(e){function t(){var t=e.call(this)||this;return t.className="StepLineSeriesDataItem",t.applyTheme(),t}return Object(a.c)(t,e),t}(s.b),st=function(e){function t(){var t=e.call(this)||this;return t.className="StepLineSeries",t.applyTheme(),t.startLocation=0,t.endLocation=1,t}return Object(a.c)(t,e),t.prototype.createDataItem=function(){return new ot},t.prototype.addPoints=function(e,t,i,n,a){var r,o,s,l;this.baseAxis==this.xAxis&&(r=this.startLocation,o=this.endLocation,s=this.getAdjustedXLocation(t,this.yOpenField),l=this.getAdjustedXLocation(t,this.yField)),this.baseAxis==this.yAxis&&(s=this.startLocation,l=this.endLocation,r=this.getAdjustedXLocation(t,this.xOpenField),o=this.getAdjustedXLocation(t,this.xField));var h=this.xAxis.getX(t,i,r),c=this.yAxis.getY(t,n,s),p=this.xAxis.getX(t,i,o),d=this.yAxis.getY(t,n,l);if(h=u.fitToRange(h,-1e5,1e5),c=u.fitToRange(c,-1e5,1e5),p=u.fitToRange(p,-1e5,1e5),d=u.fitToRange(d,-1e5,1e5),!this.noRisers&&e.length>1){var g=e[e.length-1];this.baseAxis==this.xAxis&&(a?e.push({x:g.x,y:d}):e.push({x:h,y:g.y})),this.baseAxis==this.yAxis&&(a?e.push({x:p,y:g.y}):e.push({x:g.x,y:c}))}var y={x:h,y:c},f={x:p,y:d};a?e.push(f,y):e.push(y,f)},t.prototype.drawSegment=function(e,t,i){var n=!1;this.yAxis==this.baseAxis&&(n=!0),e.drawSegment(t,i,this.tensionX,this.tensionY,this.noRisers,n)},t.prototype.createSegment=function(){return new rt},Object.defineProperty(t.prototype,"noRisers",{get:function(){return this.getPropertyValue("noRisers")},set:function(e){this.setPropertyValue("noRisers",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"startLocation",{get:function(){return this.getPropertyValue("startLocation")},set:function(e){this.setPropertyValue("startLocation",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"endLocation",{get:function(){return this.getPropertyValue("endLocation")},set:function(e){this.setPropertyValue("endLocation",e,!0)},enumerable:!0,configurable:!0}),t}(s.a);l.c.registeredClasses.StepLineSeries=st,l.c.registeredClasses.StepLineSeriesDataItem=ot;var lt=function(e){function t(){var t=e.call(this)||this;return t.className="RadarColumn",t}return Object(a.c)(t,e),t.prototype.createAssets=function(){this.radarColumn=this.createChild(De.a),this.radarColumn.shouldClone=!1,this.radarColumn.strokeOpacity=void 0,this.column=this.radarColumn},t.prototype.copyFrom=function(t){e.prototype.copyFrom.call(this,t),this.radarColumn&&this.radarColumn.copyFrom(t.radarColumn)},t.prototype.getTooltipX=function(){var e=this.getPropertyValue("tooltipX");return m.isNumber(e)?e:this.radarColumn.getTooltipX()},t.prototype.getTooltipY=function(){var e=this.getPropertyValue("tooltipX");return m.isNumber(e)?e:this.radarColumn.getTooltipY()},t}(He.a);l.c.registeredClasses.RadarColumn=lt;var ut=function(e){function t(){var t=e.call(this)||this;return t.className="ColumnSeriesDataItem",t.applyTheme(),t}return Object(a.c)(t,e),t}(Se.b),ht=function(e){function t(){var t=e.call(this)||this;return t.className="RadarColumnSeries",t.applyTheme(),t}return Object(a.c)(t,e),t.prototype.createColumnTemplate=function(){return new lt},t.prototype.validate=function(){this.chart.invalid&&this.chart.validate(),e.prototype.validate.call(this)},t.prototype.disableUnusedColumns=function(e){e&&(e.column&&(e.column.__disabled=!0),k.each(this.axisRanges.iterator(),function(t){var i=e.rangesColumns.getKey(t.uid);i&&(i.__disabled=!0)}))},t.prototype.validateDataElementReal=function(e){var t,i,n,a,r=this,s=this.chart.startAngle,l=this.chart.endAngle,h=this.yField,c=this.yOpenField,p=this.xField,d=this.xOpenField,g=this.getStartLocation(e),y=this.getEndLocation(e),f=(l-s)/(this.dataItems.length*(this.end-this.start)),m=e.column;m||(m=this.columns.create(),e.column=m,ne.copyProperties(this,m,J.b),ne.copyProperties(this.columns.template,m,J.b),e.addSprite(m),m.paper=this.paper,this.setColumnStates(m));var v=m.width,x=100;v instanceof o.a&&(x=v.percent);var b=u.round((y-g)*(1-x/100)/2,5);if(g+=b,y-=b,this.xAxis instanceof T.a&&this.yAxis instanceof T.a?(n=u.getDistance({x:this.yAxis.getX(e,h,0,"valueY"),y:this.yAxis.getY(e,h,0,"valueY")}),a=u.getDistance({x:this.yAxis.getX(e,c,1,"valueY"),y:this.yAxis.getY(e,c,1,"valueY")}),t=this.xAxis.getAngle(e,d,0,"valueX"),i=this.xAxis.getAngle(e,p,1,"valueX"),s+=g*f,l-=(1-y)*f):this.baseAxis==this.xAxis?(n=u.getDistance({x:this.yAxis.getX(e,h,e.locations[h],"valueY"),y:this.yAxis.getY(e,h,e.locations[h],"valueY")}),a=u.getDistance({x:this.yAxis.getX(e,c,e.locations[c],"valueY"),y:this.yAxis.getY(e,c,e.locations[c],"valueY")}),t=this.xAxis.getAngle(e,d,g,"valueX"),i=this.xAxis.getAngle(e,p,y,"valueX"),s+=g*f,l-=(1-y)*f):(n=u.getDistance({x:this.yAxis.getX(e,h,g,"valueY"),y:this.yAxis.getY(e,h,g,"valueY")}),a=u.getDistance({x:this.yAxis.getX(e,c,y,"valueY"),y:this.yAxis.getY(e,c,y,"valueY")}),t=this.xAxis.getAngle(e,p,e.locations[p],"valueX"),i=this.xAxis.getAngle(e,d,e.locations[d],"valueX")),i<t){var P=i;i=t,t=P}t=u.fitToRange(t,s,l),i=u.fitToRange(i,s,l);var C=m.radarColumn;C.startAngle=t;var A=i-t;A>0?(C.arc=A,C.radius=n,C.innerRadius=a,m.__disabled=!1,m.parent=this.columnsContainer,k.each(this.axisRanges.iterator(),function(i){var o=e.rangesColumns.getKey(i.uid);o||(o=r.columns.create(),ne.forceCopyProperties(r.columns.template,o,J.b),ne.copyProperties(i.contents,o,J.b),o.dataItem&&Ne.remove(o.dataItem.sprites,o),e.addSprite(o),o.paper=r.paper,r.setColumnStates(o),e.rangesColumns.setKey(i.uid,o));var s=o.radarColumn;s.startAngle=t,s.arc=A,s.radius=n,s.innerRadius=a,s.invalid&&(s.paper=r.paper,s.validate()),o.__disabled=!1,o.parent=i.contents})):this.disableUnusedColumns(e)},t.prototype.getPoint=function(e,t,i,n,a,r,o){r||(r="valueX"),o||(o="valueY");var s=this.yAxis.getX(e,i,a,o),l=this.yAxis.getY(e,i,a,o),h=u.getDistance({x:s,y:l});0==h&&(h=1e-5);var c=this.xAxis.getAngle(e,t,n,r);return{x:h*u.cos(c),y:h*u.sin(c)}},t.prototype.getMaskPath=function(){var e=this.yAxis.renderer;return h.arc(e.startAngle,e.endAngle-e.startAngle,e.pixelRadius,e.pixelInnerRadius)},t.prototype.positionBulletReal=function(e,t,i){var n=this.xAxis,a=this.yAxis;(t<n.start||t>n.end||i<a.start||i>a.end)&&(e.visible=!1),e.moveTo(this.xAxis.renderer.positionToPoint(t,i))},t.prototype.setXAxis=function(t){e.prototype.setXAxis.call(this,t),this.updateRendererRefs()},t.prototype.setYAxis=function(t){e.prototype.setYAxis.call(this,t),this.updateRendererRefs()},t.prototype.updateRendererRefs=function(){var e=this.xAxis.renderer,t=this.yAxis.renderer;e.axisRendererY=t},t}(Se.a);l.c.registeredClasses.RadarColumnSeries=ht,l.c.registeredClasses.RadarColumnSeriesDataItem=ut;var ct=i("AC2I"),pt=function(e){function t(){var t=e.call(this)||this;return t.slice=t.createChild(J.a),t.slice.shouldClone=!1,t.slice.setElement(t.paper.add("path")),t.slice.isMeasured=!1,t.orientation="vertical",t.bottomWidth=Object(o.c)(100),t.topWidth=Object(o.c)(100),t.isMeasured=!1,t.width=10,t.height=10,t.expandDistance=0,t.className="FunnelSlice",t.applyTheme(),t}return Object(a.c)(t,e),t.prototype.draw=function(){e.prototype.draw.call(this);var t=this.pixelPaddingTop,i=this.pixelPaddingBottom,n=this.pixelPaddingRight,a=this.pixelPaddingLeft,r=this.pixelWidth-n-a,o=this.pixelHeight-t-i,s=this.expandDistance,l="";if("vertical"==this.orientation){var u={x:(r-(v=C.relativeToValue(this.topWidth,r)))/2+a,y:t},c={x:(r+v)/2+a,y:t},p={x:(r+(x=C.relativeToValue(this.bottomWidth,r)))/2+a,y:t+o},d={x:(r-x)/2+a,y:t+o},g={x:c.x+(p.x-c.x)/2+s*o,y:c.y+.5*o},y={x:u.x+(d.x-u.x)/2-s*o,y:u.y+.5*o},f=h.lineTo(p),m="";0!=s&&(f=h.quadraticCurveTo(p,g),m=h.quadraticCurveTo(u,y)),l=h.moveTo(u)+h.lineTo(c)+f+h.lineTo(d)+m,this.tickPoint={x:c.x+(p.x-c.x)/2,y:c.y+(p.y-c.y)/2}}else{var v,x,b={x:a,y:(o-(v=C.relativeToValue(this.topWidth,o)))/2+t},P={x:a,y:(o+v)/2+t},A={x:a+r,y:(o-(x=C.relativeToValue(this.bottomWidth,o)))/2+t},I={x:a+r,y:(o+x)/2+t};g={y:b.y+(A.y-b.y)/2-s*r,x:b.x+.5*r},y={y:P.y+(I.y-P.y)/2+s*r,x:P.x+.5*r},f=h.lineTo(A),m="";0!=s&&(f=h.quadraticCurveTo(A,g),m=h.quadraticCurveTo(P,y)),l=h.moveTo(P)+h.lineTo(b)+f+h.lineTo(I)+m,this.tickPoint={y:P.y+(I.y-P.y)/2,x:P.x+(I.x-P.x)/2}}this.slice.path=l,this.invalidateLayout()},t.prototype.getPoint=function(e,t){var i=this.pixelPaddingTop,n=this.pixelPaddingBottom,a=this.pixelPaddingRight,r=this.pixelPaddingLeft,o=this.pixelWidth-a-r,s=this.pixelHeight-i-n;if("vertical"==this.orientation){var l={x:(o-(p=C.relativeToValue(this.topWidth,o)))/2+r,y:i},u={x:(o+p)/2+r,y:i},h={x:(o+(d=C.relativeToValue(this.bottomWidth,o)))/2+r,y:i+s},c=l.x+({x:(o-d)/2+r,y:i+s}.x-l.x)*t;return{x:c+(u.x+(h.x-u.x)*t-c)*e,y:u.y+(h.y-u.y)*t}}var p,d,g={x:r,y:(s-(p=C.relativeToValue(this.topWidth,s)))/2+i},y={x:r,y:(s+p)/2+i},f={x:r+o,y:(s-(d=C.relativeToValue(this.bottomWidth,s)))/2+i},m=g.y+(f.y-g.y)*e;return{y:m+(y.y+({x:r+o,y:(s+d)/2+i}.y-y.y)*e-m)*t,x:g.x+(f.x-g.x)*e}},Object.defineProperty(t.prototype,"bottomWidth",{get:function(){return this.getPropertyValue("bottomWidth")},set:function(e){this.setPercentProperty("bottomWidth",e,!0,!1,10,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"topWidth",{get:function(){return this.getPropertyValue("topWidth")},set:function(e){this.setPercentProperty("topWidth",e,!0,!1,10,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"orientation",{get:function(){return this.getPropertyValue("orientation")},set:function(e){this.setPropertyValue("orientation",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"expandDistance",{get:function(){return this.getPropertyValue("expandDistance")},set:function(e){this.setPropertyValue("expandDistance",e,!0)},enumerable:!0,configurable:!0}),t.prototype.copyFrom=function(t){e.prototype.copyFrom.call(this,t),this.slice&&this.slice.copyFrom(t.slice)},t}(d.a);l.c.registeredClasses.FunnelSlice=pt;var dt=i("qzbU"),gt=function(e){function t(){var t=e.call(this)||this;return t._label=new V.d,t._slice=new V.d,t.className="FunnelTick",t.element=t.paper.add("path"),t._disposers.push(t._label),t._disposers.push(t._slice),t.setPropertyValue("locationX",0),t.setPropertyValue("locationY",0),t.applyTheme(),t}return Object(a.c)(t,e),t.prototype.draw=function(){e.prototype.draw.call(this);var t=this.slice,i=t.getPoint(this.locationX,this.locationY);if(i){var n=this.label,a=t.dataItem.component,r=void 0,o=void 0,s=void 0;if("vertical"==a.orientation){var l=n.pixelX,u=n.pixelY;a.labelsOpposite||(l+=n.maxRight),r=C.spritePointToSprite(i,t,this.parent),s=C.spritePointToSprite({x:l,y:u},n.parent,this.parent),o={x:n.parent.pixelX-this.length,y:s.y},a.labelsOpposite||(o.x=n.parent.measuredWidth+this.length)}else{l=n.pixelX,u=n.pixelY;a.labelsOpposite||(u+=n.maxBottom),r=C.spritePointToSprite(i,t,this.parent),o={x:(s=C.spritePointToSprite({x:l,y:u},n.parent,this.parent)).x,y:n.parent.pixelY-this.length},a.labelsOpposite||(o.y=n.parent.measuredHeight+this.length)}this.path=h.moveTo(r)+h.lineTo(o)+h.lineTo(s)}},Object.defineProperty(t.prototype,"slice",{get:function(){return this._slice.get()},set:function(e){this._slice.set(e,new V.c([e.events.on("transformed",this.invalidate,this,!1),e.events.on("validated",this.invalidate,this,!1)]))},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"label",{get:function(){return this._label.get()},set:function(e){this._label.set(e,e.events.on("transformed",this.invalidate,this,!1))},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"locationX",{get:function(){return this.getPropertyValue("locationX")},set:function(e){this.setPropertyValue("locationX",e,!1,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"locationY",{get:function(){return this.getPropertyValue("locationY")},set:function(e){this.setPropertyValue("locationY",e,!1,!0)},enumerable:!0,configurable:!0}),t}(dt.a);l.c.registeredClasses.FunnelTick=gt;var yt=i("Q4nc"),ft=function(e){function t(){var t=e.call(this)||this;return t.className="FunnelSeriesDataItem",t.events.on("visibilitychanged",function(){t.component&&t.component.invalidateDataItems()},t,!1),t.applyTheme(),t}return Object(a.c)(t,e),Object.defineProperty(t.prototype,"sliceLink",{get:function(){var e=this;if(!this._sliceLink){var t=this.component.sliceLinks.create();this._sliceLink=t,this._disposers.push(t),t.parent=this.component.slicesContainer,this._disposers.push(new V.b(function(){e.component&&e.component.sliceLinks.removeValue(t)})),this.addSprite(t),t.visible=this.visible}return this._sliceLink},enumerable:!0,configurable:!0}),t}(ct.b),mt=function(e){function t(){var t=e.call(this)||this;return t._nextY=0,t.className="FunnelSeries",t.orientation="vertical",t.width=Object(o.c)(100),t.height=Object(o.c)(100),t.slicesContainer.width=Object(o.c)(100),t.slicesContainer.height=Object(o.c)(100),t._disposers.push(t.slicesContainer.events.on("maxsizechanged",t.invalidateDataItems,t,!1)),t.labelsOpposite=!0,t.labelsContainer.layout="absolute",t.bottomRatio=0,t.applyTheme(),t}return Object(a.c)(t,e),t.prototype.createSlice=function(){return new pt},t.prototype.createTick=function(){return new gt},t.prototype.applyInternalDefaults=function(){e.prototype.applyInternalDefaults.call(this),m.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("Funnel Series"))},t.prototype.createDataItem=function(){return new ft},t.prototype.initSlice=function(e){e.isMeasured=!1,e.defaultState.properties.scale=1,e.observe("scale",this.handleSliceScale,this),e.observe(["dx","dy","x","y"],this.handleSliceMove,this),e.tooltipText="{category}: {value.percent.formatNumber('#.#')}% ({value.value})",e.states.create("hover").properties.expandDistance=.2},t.prototype.initLabel=function(t){e.prototype.initLabel.call(this,t),t.verticalCenter="middle",t.horizontalCenter="middle",t.isMeasured=!0,t.padding(5,5,5,5)},t.prototype.validate=function(){e.prototype.validate.call(this),this._nextY=0},t.prototype.validateDataElements=function(){var t=this,i=this.slicesContainer,n=this.labelsContainer,a=this.labels.template;this.alignLabels?(a.interactionsEnabled=!0,i.isMeasured=!0,n.isMeasured=!0):(a.interactionsEnabled=!1,i.isMeasured=!1,n.isMeasured=!1);var r=0,o=0;this.dataItems.each(function(e){m.hasValue(e.value)&&(o++,e.value>0?r+=Math.abs(e.getWorkingValue("value")/e.value):t.ignoreZeroValues?o--:!e.visible||e.__disabled||e.isHiding?o--:r+=1)}),this._total=1/o*r,this._count=o,e.prototype.validateDataElements.call(this),this.arrangeLabels()},t.prototype.getNextValue=function(e){var t=e.index,i=e.getWorkingValue("value");if(t<this.dataItems.length-1){var n=this.dataItems.getIndex(t+1);if(i=n.getWorkingValue("value"),!n.visible||n.isHiding||n.__disabled||0==n.value&&this.ignoreZeroValues)return this.getNextValue(n)}return i},t.prototype.formDataElement=function(){},t.prototype.validateDataElement=function(t){var i=this,n=t.slice;n.orientation=this.orientation;var a=t.sliceLink;a.orientation=this.orientation;var r=t.tick,o=t.label;r.slice=n,r.label=o,m.hasValue(t.value)?(this.decorateSlice(t),Ne.each(t.sprites,function(e){0==t.value&&i.ignoreZeroValues?e.__disabled=!0:e.__disabled=!1})):Ne.each(t.sprites,function(e){e.__disabled=!0}),t.index==this.dataItems.length-1&&(a.disabled=!0),e.prototype.validateDataElement.call(this,t),a.fill=n.fill},t.prototype.decorateSlice=function(e){var t=e.slice,i=e.sliceLink,n=e.label,a=e.tick,r=this.slicesContainer.innerWidth,o=this.slicesContainer.innerHeight,s=this.getNextValue(e),l=Math.abs(e.getWorkingValue("value")),h=this.bottomRatio,c=1;if(0!=e.value?c=l/Math.abs(e.value):(e.__disabled||e.isHiding||!e.visible)&&(c=1e-6),this.ignoreZeroValues&&0==e.value)e.__disabled=!0;else if(e.__disabled=!1,this._nextY==1/0&&(this._nextY=0),"vertical"==this.orientation){var p=i.pixelHeight*c;o+=p,t.topWidth=l/this.dataItem.values.value.high*r,t.bottomWidth=(l-(l-s)*h)/this.dataItem.values.value.high*r,i.topWidth=t.bottomWidth,i.bottomWidth=(l-(l-s))/this.dataItem.values.value.high*r,t.y=this._nextY,t.height=Math.min(1e5,u.max(0,o/this._count*c/this._total-p)),t.x=r/2,this.alignLabels?n.x=void 0:n.x=t.x,n.y=t.pixelY+t.pixelHeight*a.locationY,this._nextY+=t.pixelHeight+p,i.y=this._nextY-p,i.x=t.x}else{var d=i.pixelWidth*c;r+=d,t.topWidth=l/this.dataItem.values.value.high*o,t.bottomWidth=(l-(l-s)*h)/this.dataItem.values.value.high*o,i.topWidth=t.bottomWidth,i.bottomWidth=(l-(l-s))/this.dataItem.values.value.high*o,t.x=this._nextY,t.width=Math.min(1e5,r/this._count*c*1/this._total-d),t.y=o/2,this.alignLabels?n.y=this.labelsContainer.measuredHeight:n.y=t.y,n.x=t.pixelX+t.pixelWidth*a.locationX,this._nextY+=t.pixelWidth+d,i.x=this._nextY-d,i.y=t.y}},t.prototype.getLastLabel=function(e){if(e>0){var t=this.labels.getIndex(e);return t.__disabled||!t.visible?this.getLastLabel(e-1):t}},t.prototype.arrangeLabels=function(){if(this.alignLabels){var e=this.labels.length;if(e>1){var t=this.getLastLabel(e-1);if(t){var i=t.pixelY,n=t.pixelX;if(e>1){for(var a=e-2;a>=0;a--){(r=this.labels.getIndex(a)).visible&&!r.__disabled&&(r.invalid&&r.validate(),"vertical"==this.orientation?r.pixelY+r.measuredHeight>i&&(r.y=Math.min(1e6,i-r.measuredHeight)):r.pixelX+r.measuredWidth>n&&(r.x=Math.min(1e6,n-r.measuredWidth)),i=r.pixelY,n=r.pixelX)}i=0,n=0;for(a=0;a<e;a++){var r;(r=this.labels.getIndex(a)).visible&&!r.__disabled&&(r.invalid&&r.validate(),"vertical"==this.orientation?r.pixelY<i&&(r.y=Math.min(1e6,i)):r.pixelX<n&&(r.x=Math.min(1e6,n)),i+=r.measuredHeight,n+=r.measuredWidth)}}}}}},t.prototype.positionBullet=function(t){e.prototype.positionBullet.call(this,t);var i=t.dataItem.slice,n=t.locationX;m.isNumber(n)||(n=.5);var a=t.locationY;m.isNumber(a)||(a=1),t.x=i.pixelX+i.measuredWidth*n,t.y=i.pixelY+i.measuredHeight*a},Object.defineProperty(t.prototype,"orientation",{get:function(){return this.getPropertyValue("orientation")},set:function(e){this.setPropertyValue("orientation",e)&&(this.labelsOpposite=this.labelsOpposite,this.invalidate(),"vertical"==e?(this.ticks.template.locationX=1,this.ticks.template.locationY=.5,this.labels.template.rotation=0,this.layout="horizontal"):(this.ticks.template.locationX=.5,this.ticks.template.locationY=1,this.labels.template.rotation=-90,this.layout="vertical"))},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"bottomRatio",{get:function(){return this.getPropertyValue("bottomRatio")},set:function(e){this.setPropertyValue("bottomRatio",e)&&this.invalidate()},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"sliceLinks",{get:function(){if(!this._sliceLinks){var e=new pt;e.applyOnClones=!0,e.fillOpacity=.5,e.expandDistance=-.3,e.hiddenState.properties.opacity=0,this._disposers.push(e),this._sliceLinks=new R.e(e),this._disposers.push(new R.c(this._sliceLinks))}return this._sliceLinks},enumerable:!0,configurable:!0}),t.prototype.show=function(t){var i=this,n=this.startIndex,a=this.endIndex,r=this.defaultState.transitionDuration;m.isNumber(t)&&(r=t),yt.a.animationsEnabled||(r=0);var o=0;return k.each(k.indexed(this.dataItems.iterator()),function(e){var t=e[0],s=e[1];i.sequencedInterpolation&&(o=i.sequencedInterpolationDelay*t+r*(t-n)/(a-n)),s.show(r,o,["value"])}),e.prototype.show.call(this,t)},t.prototype.hide=function(t){var i=this,n=["value"],a=this.startIndex,r=this.endIndex,o=0,s=this.hiddenState.transitionDuration;m.isNumber(t)&&(s=t),yt.a.animationsEnabled||(s=0),k.each(k.indexed(this.dataItems.iterator()),function(e){var t=e[0],l=e[1];i.sequencedInterpolation&&(o=i.sequencedInterpolationDelay*t+s*(t-a)/(r-a)),l.hide(s,o,0,n)});var l=e.prototype.hide.call(this,t);return l&&!l.isFinished()&&l.delay(o),l},t.prototype.setAlignLabels=function(t){e.prototype.setAlignLabels.call(this,t),this.ticks.template.disabled=!t;var i=this.labelsContainer;i&&(t?(i.height=void 0,i.width=void 0,i.margin(10,10,10,10)):(i.width=Object(o.c)(100),i.height=Object(o.c)(100))),this.labelsOpposite=this.labelsOpposite},Object.defineProperty(t.prototype,"labelsOpposite",{get:function(){return this.getPropertyValue("labelsOpposite")},set:function(e){this.setPropertyValue("labelsOpposite",e);var t=this.labels.template,i="none",n="none";this.alignLabels?e?(this.labelsContainer.toFront(),"vertical"==this.orientation?(this.ticks.template.locationX=1,t.horizontalCenter="left",i="right"):(this.ticks.template.locationY=1,t.horizontalCenter="right",n="bottom")):(this.labelsContainer.toBack(),"vertical"==this.orientation?(this.ticks.template.locationX=0,i="left"):(n="top",this.ticks.template.locationY=0)):"vertical"==this.orientation?i="center":n="middle",t.align=i,t.valign=n,this.validateLayout(),this.ticks.each(function(e){e.invalidate()}),this.invalidateDataItems()},enumerable:!0,configurable:!0}),t}(ct.a);l.c.registeredClasses.FunnelSeries=mt,l.c.registeredClasses.FunnelSeriesDataItem=ft;var vt=function(e){function t(){var t=e.call(this)||this;return t.className="PyramidSeriesDataItem",t.applyTheme(),t}return Object(a.c)(t,e),t}(ft),xt=function(e){function t(){var t=e.call(this)||this;return t.className="PyramidSeries",t.topWidth=Object(o.c)(0),t.bottomWidth=Object(o.c)(100),t.pyramidHeight=Object(o.c)(100),t.valueIs="area",t.sliceLinks.template.width=0,t.sliceLinks.template.height=0,t.applyTheme(),t}return Object(a.c)(t,e),t.prototype.applyInternalDefaults=function(){e.prototype.applyInternalDefaults.call(this),m.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("Pyramid Series"))},t.prototype.createDataItem=function(){return new vt},t.prototype.validate=function(){e.prototype.validate.call(this),this._nextWidth=void 0},t.prototype.getNextValue=function(e){var t=e.index,i=e.getWorkingValue("value");t<this.dataItems.length-1&&(i=this.dataItems.getIndex(t+1).getWorkingValue("value"));return 0==i&&(i=1e-6),i},t.prototype.validateDataElements=function(){var t=this,i=this.slicesContainer.innerWidth,n=this.slicesContainer.innerHeight;if(this.dataItems.each(function(e){if(e.value>0){var a=e.getWorkingValue("value")/e.value,r=e.sliceLink;"vertical"==t.orientation?n-=r.pixelHeight*a:i-=r.pixelWidth*a}}),this._pyramidHeight=C.relativeToValue(this.pyramidHeight,n),this._pyramidWidth=C.relativeToValue(this.pyramidHeight,i),"vertical"==this.orientation){var a=(n-this._pyramidHeight)/2;this.slicesContainer.y=a,this.labelsContainer.y=a,this.ticksContainer.y=a}else{var r=(i-this._pyramidWidth)/2;this.slicesContainer.x=r,this.labelsContainer.x=r,this.ticksContainer.x=r}e.prototype.validateDataElements.call(this)},t.prototype.decorateSlice=function(e){var t=this.dataItem.values.value.absoluteSum;if(0!=t){var i=e.slice,n=e.sliceLink,a=e.label,r=e.tick;this.getNextValue(e);var o=Math.abs(e.getWorkingValue("value")),s=this._pyramidWidth,l=this._pyramidHeight,u=this.slicesContainer.innerWidth,h=this.slicesContainer.innerHeight,c=n.pixelWidth,p=n.pixelHeight;if(0==e.value&&this.ignoreZeroValues?e.__disabled=!0:e.__disabled=!1,"vertical"==this.orientation){var d=C.relativeToValue(this.topWidth,u);m.isNumber(this._nextWidth)||(this._nextWidth=d);var g=C.relativeToValue(this.bottomWidth,u),y=this._nextWidth,f=Math.atan2(l,d-g);0==(A=Math.tan(Math.PI/2-f))&&(A=1e-8);var v=void 0,x=void 0;if("area"==this.valueIs){var b=(d+g)/2*l*o/t,P=Math.abs(y*y-2*b*A);x=(v=(y-Math.sqrt(P))/A)>0?(2*b-v*y)/v:y}else x=y-(v=l*o/t)*A;i.height=v,i.width=u,i.bottomWidth=x,i.topWidth=y,n.topWidth=i.bottomWidth,n.bottomWidth=i.bottomWidth,i.y=this._nextY,this.alignLabels?a.x=0:a.x=u/2,a.y=i.pixelY+i.pixelHeight*r.locationY+i.dy,this._nextY+=i.pixelHeight+p*o/Math.max(Math.abs(e.value),1e-8),n.y=this._nextY-p,n.x=u/2}else{d=C.relativeToValue(this.topWidth,h);m.isNumber(this._nextWidth)||(this._nextWidth=d);var A;g=C.relativeToValue(this.bottomWidth,h),y=this._nextWidth,f=Math.atan2(s,d-g);0==(A=Math.tan(Math.PI/2-f))&&(A=1e-8);var I=void 0;x=void 0;if("area"==this.valueIs)x=(2*(b=(d+g)/2*s*o/t)-(I=(y-Math.sqrt(y*y-2*b*A))/A)*y)/I;else x=y-(I=s*o/t)*A;i.width=I,i.height=h,i.bottomWidth=x,i.topWidth=y,n.topWidth=i.bottomWidth,n.bottomWidth=i.bottomWidth,i.x=this._nextY,this.alignLabels?a.y=this.labelsContainer.measuredHeight:a.y=h/2,a.x=i.pixelX+i.pixelWidth*r.locationX+i.dx,this._nextY+=i.pixelWidth+c*o/Math.max(Math.abs(e.value),1e-8),n.x=this._nextY-c,n.y=h/2}this._nextWidth=i.bottomWidth}},Object.defineProperty(t.prototype,"topWidth",{get:function(){return this.getPropertyValue("topWidth")},set:function(e){this.setPercentProperty("topWidth",e,!1,!1,10,!1)&&this.invalidate()},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"pyramidHeight",{get:function(){return this.getPropertyValue("pyramidHeight")},set:function(e){this.setPercentProperty("pyramidHeight",e,!1,!1,10,!1)&&this.invalidate()},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"bottomWidth",{get:function(){return this.getPropertyValue("bottomWidth")},set:function(e){this.setPercentProperty("bottomWidth",e,!1,!1,10,!1)&&this.invalidate()},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"valueIs",{get:function(){return this.getPropertyValue("valueIs")},set:function(e){this.setPropertyValue("valueIs",e)&&this.invalidate()},enumerable:!0,configurable:!0}),t}(mt);l.c.registeredClasses.PyramidSeries=xt,l.c.registeredClasses.PyramidSeriesDataItem=vt;var bt=function(e){function t(){var t=e.call(this)||this;return t.className="PictorialStackedSeriesDataItem",t.applyTheme(),t}return Object(a.c)(t,e),t}(vt),Pt=function(e){function t(){var t=e.call(this)||this;return t.className="PictorialStackedSeries",t.topWidth=Object(o.c)(100),t.bottomWidth=Object(o.c)(100),t.valueIs="height",t.applyTheme(),t.startLocation=0,t.endLocation=1,t.align="center",t.valign="middle",t._maskSprite=t.slicesContainer.createChild(J.a),t._maskSprite.visible=!1,t._maskSprite.zIndex=100,t._maskSprite.shouldClone=!1,t}return Object(a.c)(t,e),t.prototype.validateDataElements=function(){var t=this.slicesContainer.maxWidth,i=this.slicesContainer.maxHeight,n=this._maskSprite,a=n.measuredWidth/n.scale,r=n.measuredHeight/n.scale,o=u.min(i/r,t/a);o==1/0&&(o=1),o=u.max(.001,o);var s,l,h=this.startLocation,c=this.endLocation,p=u.min(t,a*o),d=u.min(i,r*o);n.scale=o,"vertical"==this.orientation?(this.topWidth=p+4,this.bottomWidth=p+4,this.pyramidHeight=d*(c-h),n.x=t/2,n.y=d/2):(this.topWidth=d+4,this.bottomWidth=d+4,this.pyramidHeight=p*(c-h),n.valign="middle",n.x=p/2,n.y=i/2),n.verticalCenter="middle",n.horizontalCenter="middle",e.prototype.validateDataElements.call(this),"vertical"==this.orientation?("bottom"==this.valign&&(s=i-d),"middle"==this.valign&&(s=(i-d)/2),"top"==this.valign&&(s=0),"left"==this.align&&(l=-(t-p)/2),"center"==this.align&&(l=0),"right"==this.align&&(l=(t-p)/2),this.slices.template.dy=h*d,this.alignLabels&&(this.slicesContainer.dx=l)):("bottom"==this.valign&&(s=(i-d)/2),"middle"==this.valign&&(s=0),"top"==this.valign&&(s=-(i-d)/2),"left"==this.align&&(l=0),"center"==this.align&&(l=(t-p)/2),"right"==this.align&&(l=t-p),this.slices.template.dx=h*p,this.alignLabels&&(this.slicesContainer.dy=s)),this.slicesContainer.x=l,this.labelsContainer.x=l,this.ticksContainer.x=l,this.slicesContainer.y=s,this.labelsContainer.y=s,this.ticksContainer.y=s,p>0&&d>0&&(this.slicesContainer.mask=n)},t.prototype.applyInternalDefaults=function(){e.prototype.applyInternalDefaults.call(this),m.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("Pyramid Series"))},t.prototype.createDataItem=function(){return new bt},Object.defineProperty(t.prototype,"maskSprite",{get:function(){return this._maskSprite},enumerable:!0,configurable:!0}),t.prototype.initSlice=function(t){e.prototype.initSlice.call(this,t);var i=t.states.getKey("hover");i&&(i.properties.expandDistance=0)},Object.defineProperty(t.prototype,"startLocation",{get:function(){return this.getPropertyValue("startLocation")},set:function(e){this.setPropertyValue("startLocation",e)&&this.invalidateDataItems()},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"endLocation",{get:function(){return this.getPropertyValue("endLocation")},set:function(e){this.setPropertyValue("endLocation",e)&&this.invalidateDataItems()},enumerable:!0,configurable:!0}),t}(xt);l.c.registeredClasses.PictorialStackedSeries=Pt,l.c.registeredClasses.PictorialStackedSeriesDataItem=bt;var Ct=i("BmDP"),At=i("ncT3"),It=function(e){function t(){var t=e.call(this)||this;return t.className="ConeColumn",t}return Object(a.c)(t,e),t.prototype.createAssets=function(){this.coneColumn=this.createChild(At.a),this.coneColumn.shouldClone=!1,this.column=this.coneColumn},t.prototype.copyFrom=function(t){e.prototype.copyFrom.call(this,t),this.coneColumn&&this.coneColumn.copyFrom(t.coneColumn)},t}(He.a);l.c.registeredClasses.ConeColumn=It;var Dt=function(e){function t(){var t=e.call(this)||this;return t.className="ConeSeriesDataItem",t.applyTheme(),t}return Object(a.c)(t,e),t}(Se.b),Tt=function(e){function t(){var t=e.call(this)||this;return t.className="ConeSeries",t.applyTheme(),t}return Object(a.c)(t,e),t.prototype.createColumnTemplate=function(){return new It},t.prototype.getMaskPath=function(){var e=0,t=0,i=this.columns.getIndex(0);if(i)return this.baseAxis==this.xAxis?t=i.coneColumn.innerWidth/2+1:e=i.coneColumn.innerHeight/2+1,h.rectToPath({x:-e,y:0,width:this.xAxis.axisLength+e,height:this.yAxis.axisLength+t})},t.prototype.validateDataElementReal=function(t){if(e.prototype.validateDataElementReal.call(this,t),t.column){var i=t.column.coneColumn;i.fill=t.column.fill,this.baseAxis==this.yAxis?i.orientation="horizontal":i.orientation="vertical"}},t}(Se.a);l.c.registeredClasses.ConeSeries=Tt,l.c.registeredClasses.ConeSeriesDataItem=Dt;var Ot=function(e){function t(){var t=e.call(this)||this;return t.className="CurvedColumn",t}return Object(a.c)(t,e),t.prototype.createAssets=function(){this.curvedColumn=this.createChild(J.a),this.curvedColumn.shouldClone=!1,this.setPropertyValue("tension",.7),this.width=Object(o.c)(120),this.height=Object(o.c)(120),this.column=this.curvedColumn},t.prototype.draw=function(){e.prototype.draw.call(this);var t,i=this.realWidth,n=this.realHeight,a=this.realX-this.pixelX,r=this.realY-this.pixelY;C.used(this.width);var o=1,s=1;"vertical"==this.orientation?(o=this.tension,t=[{x:0,y:n+r},{x:i/2,y:r},{x:i,y:n+r}]):(s=this.tension,t=[{x:a,y:n=Math.abs(n)},{x:a+i,y:n/2},{x:a,y:0}]);var l=h.moveTo(t[0])+new be.d(o,s).smooth(t);this.column.path=l},t.prototype.copyFrom=function(t){e.prototype.copyFrom.call(this,t),this.curvedColumn&&this.curvedColumn.copyFrom(t.curvedColumn)},Object.defineProperty(t.prototype,"tension",{get:function(){return this.getPropertyValue("tension")},set:function(e){this.setPropertyValue("tension",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"orientation",{get:function(){return this.getPropertyValue("orientation")},set:function(e){this.setPropertyValue("orientation",e,!0)},enumerable:!0,configurable:!0}),t}(He.a);l.c.registeredClasses.CurvedColumn=Ot;var Vt=function(e){function t(){var t=e.call(this)||this;return t.className="CurvedColumnSeriesDataItem",t.applyTheme(),t}return Object(a.c)(t,e),t}(Se.b),_t=function(e){function t(){var t=e.call(this)||this;return t.className="CurvedColumnSeries",t.applyTheme(),t}return Object(a.c)(t,e),t.prototype.createColumnTemplate=function(){return new Ot},t.prototype.validateDataElementReal=function(t){e.prototype.validateDataElementReal.call(this,t);var i=t.column;(i=t.column)&&(t.column.curvedColumn.fill=t.column.fill,this.baseAxis==this.yAxis?i.orientation="horizontal":i.orientation="vertical")},t}(Se.a);l.c.registeredClasses.CurvedColumnSeries=_t,l.c.registeredClasses.CurvedColumnSeriesDataItem=Vt;var kt=i("AAkI"),Lt=i("eN1s"),St=i("TDx+"),Rt=i("eAid"),jt=i("Uslz"),wt=i("+K/x"),Nt=i("KknQ"),Ft=i("ncgu"),Yt=i("9ZsQ"),Wt=i("ZoDA"),Xt=function(e){function t(){var t=e.call(this)||this;t.className="CircleBullet";var i=t.createChild(g.a);return i.shouldClone=!1,i.radius=5,i.isMeasured=!1,t.circle=i,t.applyTheme(),t}return Object(a.c)(t,e),t.prototype.copyFrom=function(t){e.prototype.copyFrom.call(this,t),this.circle.copyFrom(t.circle)},t}(se.a);l.c.registeredClasses.CircleBullet=Xt;var Mt=function(e){function t(){var t=e.call(this)||this;return t.className="ErrorBullet",t.errorLine=t.createChild(J.a),t.errorLine.shouldClone=!1,t.width=20,t.height=20,t.strokeOpacity=1,t.isDynamic=!0,t}return Object(a.c)(t,e),t.prototype.validatePosition=function(){e.prototype.validatePosition.call(this);var t=this.pixelWidth/2,i=this.pixelHeight/2;this.errorLine.path=h.moveTo({x:-t,y:-i})+h.lineTo({x:t,y:-i})+h.moveTo({x:0,y:-i})+h.lineTo({x:0,y:i})+h.moveTo({x:-t,y:i})+h.lineTo({x:t,y:i})},t.prototype.copyFrom=function(t){e.prototype.copyFrom.call(this,t),this.errorLine.copyFrom(t.errorLine)},t}(se.a);l.c.registeredClasses.ErrorBullet=Mt;var Ht=i("C6Lh"),Bt=i("Y9w3"),zt=i("A6AV"),Et=i("Trvg"),Gt=i("Rnbi"),qt=function(e){function t(){var t=e.call(this)||this;return t.className="NavigationBarDataItem",t.applyTheme(),t}return Object(a.c)(t,e),Object.defineProperty(t.prototype,"name",{get:function(){return this.properties.name},set:function(e){this.setProperty("name",e)},enumerable:!0,configurable:!0}),t}(zt.a),Kt=function(e){function t(){var t=e.call(this)||this;t.className="NavigationBar";var i=new w.a,n=new Et.a;n.valign="middle",n.paddingTop=8,n.paddingBottom=8,t.paddingBottom=2,t.links=new R.e(n),t._disposers.push(new R.c(t.links)),t._disposers.push(n),t._linksIterator=new k.ListIterator(t.links,function(){return t.links.create()}),t._linksIterator.createNewItems=!0;var a=new Gt.a;a.direction="right",a.width=8,a.height=12,a.fill=i.getFor("alternativeBackground"),a.fillOpacity=.5,a.valign="middle",a.marginLeft=10,a.marginRight=10,t.separators=new R.e(a),t._disposers.push(new R.c(t.separators)),t._disposers.push(a);var r=new Et.a;return t.activeLink=r,r.copyFrom(n),r.valign="middle",r.fontWeight="bold",t.width=Object(o.c)(100),t.layout="grid",t.dataFields.name="name",t.applyTheme(),t}return Object(a.c)(t,e),t.prototype.validateDataElements=function(){this.removeChildren(),this._linksIterator.reset(),e.prototype.validateDataElements.call(this)},t.prototype.validateDataElement=function(t){var i;if(e.prototype.validateDataElement.call(this,t),t.index<this.dataItems.length-1){(i=this._linksIterator.getLast()).parent=this;var n=this.separators.create();n.parent=this,n.valign="middle"}else(i=this.activeLink).events.copyFrom(this.links.template.events),i.hide(0),i.show(),i.parent=this;i.dataItem=t,i.text=t.name,i.validate()},t}(Bt.a);l.c.registeredClasses.NavigationBar=Kt,l.c.registeredClasses.NavigationBarDataItem=qt;var Ut=i("gqvf"),Zt=i("1Fjw"),Qt=function(e){function t(){var t=e.call(this)||this;return t.className="RadarCursor",t.radius=Object(o.c)(100),t.innerRadius=Object(o.c)(0),t.applyTheme(),t.mask=void 0,t}return Object(a.c)(t,e),t.prototype.fitsToBounds=function(e){var t=u.getDistance(e);return t<this.truePixelRadius+1&&t>this.pixelInnerRadius-1},Object.defineProperty(t.prototype,"startAngle",{get:function(){return this.getPropertyValue("startAngle")},set:function(e){this.setPropertyValue("startAngle",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"endAngle",{get:function(){return this.getPropertyValue("endAngle")},set:function(e){this.setPropertyValue("endAngle",e,!0)},enumerable:!0,configurable:!0}),t.prototype.triggerMoveReal=function(t,i){this.xAxis&&(!this.xAxis||this.xAxis.cursorTooltipEnabled&&!this.xAxis.tooltip.disabled)||this.updateLineX(this.point),this.yAxis&&(!this.yAxis||this.yAxis.cursorTooltipEnabled&&!this.yAxis.tooltip.disabled)||this.updateLineY(this.point),this.updateSelection(),e.prototype.triggerMoveReal.call(this,t,i)},t.prototype.updateLineX=function(e){var t=this.pixelRadius,i=this.startAngle,n=this.endAngle,a=this.pixelInnerRadius;if(t>0&&m.isNumber(i)&&m.isNumber(n)&&m.isNumber(a)){var r=u.fitAngleToRange(u.getAngle(e),i,n),o=void 0;if(this.lineX&&this.lineX.visible){if(this.lineX.moveTo({x:0,y:0}),this.xAxis&&this.fullWidthLineX){var s=this.xAxis.currentItemStartPoint,l=this.xAxis.currentItemEndPoint;if(s&&l){var c=u.fitAngleToRange(u.getAngle(s),i,n),p=u.fitAngleToRange(u.getAngle(l),i,n)-c;i<n?p<0&&(p+=360):p>0&&(p-=360),r-=p/2,o=h.moveTo({x:a*u.cos(r),y:a*u.sin(r)})+h.lineTo({x:t*u.cos(r),y:t*u.sin(r)})+h.arcTo(r,p,t)+h.lineTo({x:a*u.cos(r+p),y:a*u.sin(r+p)})+h.arcTo(r+p,-p,a)}}o||(o=h.moveTo({x:a*u.cos(r),y:a*u.sin(r)})+h.lineTo({x:t*u.cos(r),y:t*u.sin(r)})),this.lineX.path=o}}},t.prototype.updateLineY=function(e){if(this.lineY&&this.lineY.visible){var t=this.startAngle,i=this.endAngle,n=this.truePixelRadius,a=u.fitToRange(u.getDistance(e),0,this.truePixelRadius);if(m.isNumber(a)&&m.isNumber(t)){this.lineY.moveTo({x:0,y:0});var r=void 0,o=i-t;if(this.yAxis&&this.fullWidthLineY){var s=this.yAxis.currentItemStartPoint,l=this.yAxis.currentItemEndPoint;if(s&&l){var c=u.fitToRange(u.getDistance(s),0,n);a=u.fitToRange(u.getDistance(l),0,n),r=h.moveTo({x:a*u.cos(t),y:a*u.sin(t)})+h.arcTo(t,o,a),r+=h.moveTo({x:c*u.cos(i),y:c*u.sin(i)})+h.arcTo(i,-o,c)}}r||(r=h.moveTo({x:a*u.cos(t),y:a*u.sin(t)})+h.arcTo(t,i-t,a)),this.lineY.path=r}}},t.prototype.updateSelection=function(){if(this._usesSelection){var e=this.downPoint;if(e){var t=this.point,i=this.pixelRadius,n=this.truePixelRadius,a=this.pixelInnerRadius,r=Math.min(this.startAngle,this.endAngle),o=Math.max(this.startAngle,this.endAngle),s=u.fitAngleToRange(u.getAngle(e),r,o),l=u.fitAngleToRange(u.getAngle(t),r,o),c=u.getDistance(e);if(c<n){var p=u.fitToRange(u.getDistance(t),0,n);this._prevAngle=l;var d=h.moveTo({x:0,y:0}),g=u.sin(s),y=u.cos(s),f=u.sin(l),m=u.cos(l),v=this.behavior;"zoomX"==v||"selectX"==v?d+=h.lineTo({x:i*y,y:i*g})+h.arcTo(s,l-s,i)+h.lineTo({x:a*m,y:a*f})+h.arcTo(l,s-l,a):"zoomY"==v||"selectY"==v?d=h.moveTo({x:p*u.cos(r),y:p*u.sin(r)})+h.arcTo(r,o-r,p)+h.lineTo({x:c*u.cos(o),y:c*u.sin(o)})+h.arcTo(o,r-o,c)+h.closePath():"zoomXY"==v&&(d=h.moveTo({x:p*u.cos(s),y:p*u.sin(s)})+h.arcTo(s,l-s,p)+h.lineTo({x:c*u.cos(l),y:c*u.sin(l)})+h.arcTo(l,s-l,c)+h.closePath()),this.selection.path=d}this.selection.moveTo({x:0,y:0})}}},t.prototype.getPositions=function(){if(this.chart){var e=this.pixelInnerRadius,t=this.truePixelRadius-e,i=this.startAngle,n=this.endAngle,a=(u.fitAngleToRange(u.getAngle(this.point),i,n)-i)/(n-i);this.xPosition=a,this.yPosition=u.fitToRange((u.getDistance(this.point)-e)/t,0,1)}},t.prototype.updatePoint=function(e){},t.prototype.handleXTooltipPosition=function(e){if(this.xAxis.cursorTooltipEnabled){var t=this.xAxis.tooltip;this.updateLineX(C.svgPointToSprite({x:t.pixelX,y:t.pixelY},this))}},t.prototype.handleYTooltipPosition=function(e){if(this.yAxis.cursorTooltipEnabled){var t=this.yAxis.tooltip;this.updateLineY(C.svgPointToSprite({x:t.pixelX,y:t.pixelY},this))}},t.prototype.updateLinePositions=function(e){},t.prototype.getRanges=function(){var e=this.downPoint;if(e){var t=this.upPoint;if(this.chart){var i=this.pixelRadius,n=this.startAngle,a=this.endAngle,r=u.fitAngleToRange(u.getAngle(e),this.startAngle,this.endAngle),o=u.fitAngleToRange(u.getAngle(t),this.startAngle,this.endAngle),s=u.fitToRange(u.getDistance(e),0,i),l=u.fitToRange(u.getDistance(t),0,i),h=0,c=1,p=0,d=1,g=this.behavior;if("zoomX"==g||"selectX"==g||"zoomXY"==g||"selectXY"==g){var y=a-n;h=u.round((r-n)/y,5),c=u.round((o-n)/y,5)}"zoomY"!=g&&"selectY"!=g&&"zoomXY"!=g&&"selectXY"!=g||(p=u.round(s/i,5),d=u.round(l/i,5)),this.xRange={start:Math.min(h,c),end:Math.max(h,c)},this.yRange={start:Math.min(p,d),end:Math.max(p,d)},"selectX"==this.behavior||"selectY"==this.behavior||"selectXY"==this.behavior||this.selection.hide()}}},t.prototype.updateSize=function(){},Object.defineProperty(t.prototype,"radius",{get:function(){return this.getPropertyValue("radius")},set:function(e){this.setPercentProperty("radius",e,!1,!1,10,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"pixelRadius",{get:function(){return C.relativeRadiusToValue(this.radius,this.truePixelRadius)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"truePixelRadius",{get:function(){return C.relativeToValue(Object(o.c)(100),u.min(this.innerWidth/2,this.innerHeight/2))},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"innerRadius",{get:function(){return this.getPropertyValue("innerRadius")},set:function(e){this.setPercentProperty("innerRadius",e,!1,!1,10,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"pixelInnerRadius",{get:function(){var e=this.innerRadius;return e instanceof o.a&&(e=Object(o.c)(100*e.value*this.chart.innerRadiusModifyer)),C.relativeRadiusToValue(e,this.truePixelRadius)||0},enumerable:!0,configurable:!0}),t.prototype.fixPoint=function(e){return e},t}(Ut.a);l.c.registeredClasses.RadarCursor=Qt,window.am4charts=n}},["XFs4"]);