<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
</head>
<body>
	<div id="map" class="odf-view" style="height:550px;"></div>
	<div class="btnDiv">
		<input type="button" id="changeStyle" class="onoffBtn toggle" value="라벨 스타일 변경" onclick="setStyle();">
	</div>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::,::coordy::);
	var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);

	/*포인트 레이어 추가*/
	var pointLayer = odf.LayerFactory.produce('geoserver', {
		method : 'get',
		server : '::WfsAPI::',
		layer : '::pointLayer::', // 발행된 레이어 명칭 (ex. 저장소명:레이어명)
		crtfckey : '::crtfckey::',
		service : 'wfs',
	});
	pointLayer.setMap(map);
	pointLayer.fit();

	var styleFlag = true;
	setStyle();
	function setStyle() {
		/*라벨 스타일1 생성*/
		var labelStyleFunction1 = odf.StyleFactory.produceFunction([ {
			seperatorFunc : "default",
			style : {
				text : {
					text : '',//텍스트 내용
					//offsetX : 0,//기준점으로부터 텍스트 x좌표 위치 이동
					//offsetY : 0,//기준점으로부터 텍스트 Y좌표 위치 이동
					//rotation : (Math.PI*270/180), //회전
					//textAlign : 'left',//텍스트 수평정렬
					//textBaseline : 'middle',//텍스트 수직정렬
					font : 'bold 20px Courier New',//폰트 크기(필수) 및 글씨체(필수), 두께(옵션)
					fill : {
						color : [ 0, 0, 0, 0.95 ]
					},
					stroke : {//text 안의 stroke는 width/lineCap/lineJoin/lineDash/lineDashOffset/miterLimit 옵션 적용  x
						color : [ 255, 255, 255, 0.8 ],
					},
					//padding : [0.5,0.5,0.5,0.5],//text와 background영역 사이의 여백 //placement :'line' 일 경우 미적용
					backgroundStroke : {
						color : 'black'
					},//placement :'line' 일경우 미적용
					backgroundFill : {
						color : 'white'
					},//placement :'line' 일경우 미적용
					//placement :'line',//텍스트를 나열하는 위치를 line을 따라 나타나게 할지, 특정 point에 나타나게 할지
					//maxAngle : 90*Math.PI/180,//placement :'line' 일경우 적용
					//overflow : false,//placement :'line' 일경우 적용//텍스트를 나열한 길이보다 선이 짧을 경우, 넘치는 글자를 쭉 나열할지 여부
					scale : 1, //텍스트 크기를 정해진 값의 n배로 셋팅
					rotateWithView : true
				//지도가 회전할때 텍스트도 적절하게 회전할지 여부
				}

			},
			callbackFunc : function(style, feature, resolution) {

				//id 속성으로 text 셋팅
				style.getText().setText(feature.getProperties().id + '');

				//글자 색상 변경
				if (styleFlag) {
					//글자색 변경 및 텍스트 내용 변경
					chagedStyle = style.getText().getFill().setColor('red');
				} else {
					//글자색 변경 및 텍스트 내용 변경
					chagedStyle = style.getText().getFill().setColor('black');
				}
			},
		} ])
		pointLayer.setStyle(labelStyleFunction1);
		styleFlag = !styleFlag;
	}
</script>
</html>
