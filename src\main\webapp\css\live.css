@charset "UTF-8";
body {
	background-color: #fff;
	margin: 0;
	padding: 0;
}

a, a:link, a:visited {
	color: #111;
	text-decoration: none;
}

#toggle-menu-set {
	width:100%;
	text-align: center;
	margin-top: 30px;
}

#toggle-menu-set a.on {
	background: #393939;
	color: #fff;
}

.toggle-button {
	display: inline-block;
	width: 140px;
	height: 30px;
	background: #bdc0c5;
	color: #fff;
	text-align: center;
	font-size: 16px;
}

.toggle-button span {
	vertical-align: middle;
}

.toggle-button:hover {
	background-color: #e2e2e2;	
}

.menu-button span.icon {
	background: url(../images/live/livecoding.png) no-repeat;
}

.controlArea {
    padding: 5px 10px;
}

.controlArea button {
    padding: 5px 5px 5px 25px;
    color: #585858;
    background-color: transparent;
    font-size: 14px;
    font-family: '맑은 고딕';
    font-weight: bold;
    cursor: pointer;
}

#btn-run {
	float: left;
    padding-left: 5px;
    margin-right: 4px;
    border: 1px solid #dee1e7;
}

#btn-run:hover {
    color: #fff;
    background: #376fee;
}

#btn-auto-run {
	float: left;
    padding-left: 20px;
    border: 1px solid #dee1e7;
    background: #fff url(../images/common/btn-autoplay.png) no-repeat 5px center;
}

#btn-auto-run.on {
	color: #fff;
    background: #376fee url(../images/common/btn-autoplay-hover.png) no-repeat 5px center;
}

#btn-reset {
	float: right;
	background: url("../images/common/ico-reset-type2.png") no-repeat left center;
	border: none;
}

#btn-reset:hover {
    color: #376fee;
    background: url(../images/common/ico-reset-type2-hover.png) no-repeat left center;
}

#btn-run .icon {
	background-position: 0 -27px;
	padding-left: 19px;
}

#btn-auto-run .icon{
	background-position: 0 -85px;
	padding-left: 10px;
}

#btn-auto-run.on .icon{
	background-position: 0 -65px;
	padding-left: 10px;
}

#btn-reset .icon{
	background-position: 0 -46px;
	padding-left: 16px;
}

.contents {
	/* border: 1px solid #000; */
	position: absolute;
	top: 100px;
	left: 0;
	right:0;
	bottom: 0;
	margin: 0 0 10px 0;	
}

.pannel {
	background-color: #fff;
	position: absolute;
	top: 0;
	bottom:0;
	border: 1px solid #000;
}

.title {
	color: #fff;
	background-color: #111;
	text-align: center;
}

.hide {
	display: none;
}

#preview-frame {
	width:100%;
	height:calc(100% - 23px);
	border: none;
}

.clearFix:after {
    content: "";
    display: block;
    clear: both;
}
