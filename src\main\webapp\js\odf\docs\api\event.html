<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>GeOnPaas ui widgets: event</title>
    
      <link type="text/css" rel="stylesheet" href="styles/vendor/prism-tomorrow-night.css">
    
    <link type="text/css" rel="stylesheet" href="styles/styles.css">
    
    
    <style>
      :root {
      
      
        --nav-width: 370px;
      
      
        --nav-heading-margin-top: 0.5em;
      
      }
    </style>
    
</head>
<body>

<header class="layout-header">
  
  <h1>
    <a href="./index.html">
      GeOnPaas ui widgets
    </a>
  </h1>
  <nav class="layout-nav">
    <ul><li class="nav-heading">Classes</li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="BasemapControl.html">BasemapControl</a></span><span class="nav-desc"><p>배경지도 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="BookmarkControl.html">BookmarkControl</a></span><span class="nav-desc"><p>북마크 컨트롤 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ClearControl.html">ClearControl</a></span><span class="nav-desc"><p>지도 그리기 이벤트 초기화 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ColorFactory.html">ColorFactory</a></span><span class="nav-desc"><p>색 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Control.html">Control</a></span><span class="nav-desc"><p>사용자 정의 컨트롤 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Coordinate.html">Coordinate</a></span><span class="nav-desc"><p>좌표 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DivideMapControl.html">DivideMapControl</a></span><span class="nav-desc"><p>지도 분할 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DownloadControl.html">DownloadControl</a></span><span class="nav-desc"><p>다운로드 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DrawControl.html">DrawControl</a></span><span class="nav-desc"><p>그리기 도구 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Easing.html">Easing</a></span><span class="nav-desc"><p>애니메이션 효과</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="event.html">event</a></span><span class="nav-desc"><p>이벤트 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Extent.html">Extent</a></span><span class="nav-desc"><p>영역 관련 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Feature.html">Feature</a></span><span class="nav-desc"><p>Feature 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FeatureFactory.html">FeatureFactory</a></span><span class="nav-desc"><p>Feature 생성을 위한 FeatureFactory 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FormatFactory.html">FormatFactory</a></span><span class="nav-desc"></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FullScreenControl.html">FullScreenControl</a></span><span class="nav-desc"><p>전체화면 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="HomeControl.html">HomeControl</a></span><span class="nav-desc"><p>홈 이동 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Layer.html">Layer</a></span><span class="nav-desc"><p>레이어 관리 클래스로, 레이어는 odf.LayerFactory를 통해서만 생성가능하다.</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LayerFactory.html">LayerFactory</a></span><span class="nav-desc"><p>레이어 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LayerInfoControl.html">LayerInfoControl</a></span><span class="nav-desc"><p>레이어 정보 조회 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Map.html">Map</a></span><span class="nav-desc"><p>지도 생성, 조작, 컴퍼넌트, 레이어 추가 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Marker.html">Marker</a></span><span class="nav-desc"><p>마커 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MeasureControl.html">MeasureControl</a></span><span class="nav-desc"><p>지도 측정 도구 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MousePositionControl.html">MousePositionControl</a></span><span class="nav-desc"><p>마우스 좌표 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MoveControl.html">MoveControl</a></span><span class="nav-desc"><p>현재 화면 기준으로 이전/다음 화면 이동 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="OverviewMapControl.html">OverviewMapControl</a></span><span class="nav-desc"><p>인덱스맵 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Popup.html">Popup</a></span><span class="nav-desc"><p>팝업 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="PrintControl.html">PrintControl</a></span><span class="nav-desc"><p>프린트 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Projection.html">Projection</a></span><span class="nav-desc"><p>좌표 변환 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="RotationControl.html">RotationControl</a></span><span class="nav-desc"><p>화면을 회전 시키는 기능
alt + shift 드래그로 지도 회전</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ScaleControl.html">ScaleControl</a></span><span class="nav-desc"><p>축척 표시 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="SLD.html">SLD</a></span><span class="nav-desc"><p>WMS 스타일 관리 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Style.html">Style</a></span><span class="nav-desc"><p>스타일 관리 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="StyleFactory.html">StyleFactory</a></span><span class="nav-desc"><p>스타일 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="StyleFunction.html">StyleFunction</a></span><span class="nav-desc"><p>스타일 Function 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="SwiperControl.html">SwiperControl</a></span><span class="nav-desc"><p>지도 스와이퍼 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ZipControl.html">ZipControl</a></span><span class="nav-desc"><p>Server없이 Layer 생성하는 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ZoomControl.html">ZoomControl</a></span><span class="nav-desc"><p>지도 줌 설정클래스</p></span></li></ul><li class="nav-heading"><a href="global.html">Globals</a></li>
  </nav>
</header>


<main class="layout-main ">
  <div class="container">
    <p class="page-kind">Class</p>
    <h1 class="page-title">event</h1>
    




<section>


<header class="class">


    
        
        <!-- <h2>event</h2> -->

        

        

        
            
            <div class="class-description"><p>이벤트 생성 클래스</p></div>
        
    
</header>

<article>
    <div class="container-overview">



    
        





    


    
    <h3 class="subtitle">Constructor</h3>
    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="event">new event<span class="signature">()</span><span class="return-type-signature"></span>
    </h4>

















<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    
    </div>

    

    

     

    

    


    

    
        <h3 class="subtitle">Methods</h3>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".addListener">addListener<span class="signature">(target, eventType, callback, expiration)</span><span class="return-type-signature"> &rarr; {String}</span>
    </h4>





<div class="method-description">
    
    <p>이벤트 등록</p>
<pre class="prettyprint source lang-javascript"><code>//이벤트 연결
   let map = new odf.Map(document.getElementById('map'), {
  center: new odf.Coordinate(955673.3095379177, 1954893.4607722224),
  zoom: 17,
  projection: 'EPSG:5179',
  maxZoom: 20,
  minZoom: 8,
});
   //map 객체에 클릭 이벤트 연결
   let clickEventId = odf.event.addListener(map,'click',(evt)=>{
    //클릭한 위치에 마커 추가
  let marker = new odf.Marker({
    position : new odf.Coordinate(evt.coordinate)
  });
  marker.setMap(map);
});
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">target</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    |

    <span class="param-type">
        <code>HTMLElement</code>
    </span>
    |

    <span class="param-type">
        <code>odf.ExtObject</code>
    </span>
    

            

            

            
                
            

            <div class="param-description"><p>이벤트를 연결할 대상(css 선택자 텍스트, odf.ExtObject) (ex)
'#moveBtn' | '.mtbBtn' | (new Map({...}))</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">eventType</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#event_type">event_type</a></code>
    </span>
    

            

            

            
                
            

            <div class="param-description"><p>target에 연결 가능한 이벤트 종류 (ex) 'click', 'mousedown',
'moveend','custom-event-01'</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">callback</span>
            

            
                


    <span class="param-type">
        <code>function</code>
    </span>
    

            

            

            
                
            

            <div class="param-description"><p>이벤트가 발생 될때 실행할 function</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">expiration</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#event_expiration">event_expiration</a></code>
    </span>
    

            

            

            
                
                    <span class="param-default">
                        odfDeleted
                    </span>
                
            

            <div class="param-description"><p>event 해제 시기별 type.</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>String</code>
            
            
                <p>이벤트를 특정하는 Id</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".excludeEvent">excludeEvent<span class="signature">(parameter)</span><span class="return-type-signature"> &rarr; {Array.&lt;<a href="global.html#event_excludeEvent_item">event_excludeEvent_item</a>>}</span>
    </h4>





<div class="method-description">
    
    <p>queue에 등록된 event의 상태값 변경</p>
<pre class="prettyprint source lang-javascript"><code>    let backupData = [];
    // 등록된 이벤트 중에 이벤트 id 'click_1553666324943' 에 해당하는 타겟과 이벤트 타입을 갖는 이벤트 목록 반환
    let targetList = odf.event.excludeEvent('click_1553666324943');
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">parameter</span>
            

            
                


    <span class="param-type">
        <code>Object</code>
    </span>
    

            

            

            

            <div class="param-description"><p>대상 이벤트</p></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">eventId</span>
            

            
                


    <span class="param-type">
        <code>string</code>
    </span>
    

            

            

            

            <div class="param-description"><p>이벤트 id</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">target</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    |

    <span class="param-type">
        <code>HTMLElement</code>
    </span>
    |

    <span class="param-type">
        <code>odf.ExtObject</code>
    </span>
    

            

            

            

            <div class="param-description"><p>이벤트 타겟</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">eventType</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#event_type">event_type</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>이벤트 타입</p></div>
            
        </li>

    
</ul>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Array.&lt;event_excludeEvent_item></code>
            
            
                <ul>
<li>대상 이벤트 id 배열</li>
</ul>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".hasEvent">hasEvent<span class="signature">(target, eventType)</span><span class="return-type-signature"> &rarr; {number}</span>
    </h4>





<div class="method-description">
    
    <p>특정 객체에 특정 타입의 이벤트가 있는지 확인</p>
<pre class="prettyprint source lang-javascript"><code>odf.event.addListener(map,'click',(evt)=>{...});
//map 객체에 'click' 이벤트가 등록된것이 있나 확인
odf.event.hasEvent(map,'click'); //true
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">target</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    |

    <span class="param-type">
        <code>HTMLElement</code>
    </span>
    |

    <span class="param-type">
        <code>odf.ExtObject</code>
    </span>
    

            

            

            

            <div class="param-description"><p>이벤트 타겟</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">eventType</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#event_type">event_type</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>target에 연결 가능한 이벤트 종류
(ex) 'click', 'mousedown', 'moveend','custom-event-01'</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>number</code>
            
            
                <p>이벤트 타겟에 등록된 이벤트 갯수</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".hasEventId">hasEventId<span class="signature">(eventId)</span><span class="return-type-signature"> &rarr; {Boolean}</span>
    </h4>





<div class="method-description">
    
    <p>특정 id를 갖는 이벤트가 등록되어있는지 확인</p>
<pre class="prettyprint source lang-javascript"><code>let clickEventId = odf.event.addListener(map,'click',(evt)=>{...});
//clickEventId를 id로 하는 이벤트가 등록되있는 것이 있는지 확인
odf.event.hasEventId(clickEventId); //true
odf.event.hasEventId('aaaa'); //false
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">eventId</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>이벤트 등록 여부를 확인할 eventId</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Boolean</code>
            
            
                <ul>
<li>이벤트 등록 여부 반환</li>
</ul>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".removeListener">removeListener<span class="signature">(evtneId)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>이벤트 해제</p>
<pre class="prettyprint source lang-javascript"><code>  //map 객체 생성
   let map = new odf.Map(document.getElementById('map'), {
  center: new odf.Coordinate(955673.3095379177, 1954893.4607722224),
  zoom: 17,
  projection: 'EPSG:5179',
  maxZoom: 20,
  minZoom: 8,
});

   //map 객체에 클릭 이벤트 연결
   let clickEventId = odf.event.addListener(map,'click',(evt)=>{
    //클릭한 위치에 마커 추가
  let marker = new odf.Marker({
    position : new odf.Coordinate(evt.coordinate)
  });
  marker.setMap(map);
});

   //클릭 이벤트 해제
   odf.event.removeListener(clickEventId);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">evtneId</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>이벤트를 특정하는 Id</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".stateChange">stateChange<span class="signature">(eventId, state, excludeThis)</span><span class="return-type-signature"> &rarr; {boolean}</span>
    </h4>





<div class="method-description">
    
    <p>queue에 등록된 event의 상태값 변경</p>
<pre class="prettyprint source lang-javascript"><code>    //이벤트 활성화
    odf.event.stateChange('click_1553666324943','normal');
    //이벤트 비활성화
    odf.event.stateChange('click_1553666324943','stopped');
    //click_1553666324943 이벤트 활성화
    odf.event.stateChange('click_1553666324943','normal',false);
    //click_1553666324943 이벤트 비활성화
    odf.event.stateChange('click_1553666324943','stopped',false);
    //click_1553666324943 이벤트와 eventType과 target이 동일한 이벤트들을 활성화
    odf.event.stateChange('click_1553666324943','normal',true);
    //click_1553666324943 이벤트와 eventType과 target이 동일한 이벤트들을 비활성화
    odf.event.stateChange('click_1553666324943','stopped',true);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">eventId</span>
            

            
                


    <span class="param-type">
        <code>string</code>
    </span>
    

            

            

            

            <div class="param-description"><p>상태를 변경할 이벤트의 id</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">state</span>
            

            
                


    <span class="param-type">
        <code>event_state</code>
    </span>
    

            

            

            

            <div class="param-description"><p>이벤트 상태</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">excludeThis</span>
            

            
                


    <span class="param-type">
        <code>boolean</code>
    </span>
    

            

            

            

            <div class="param-description"><p>입력한 eventId를 제외하고 eventId에 해당하는 이벤트 target과 이벤트 type이 일치하는 다른 이벤트들을 대상으로 할지 여부</p>
<ul>
<li>true : 입력한 eventId를 제외하고 eventId에 해당하는 이벤트 target과 이벤트 type이 일치하는 다른 이벤트들을 대상으로 처리</li>
<li>false : (기본값)입력한 eventId를 대상으로 처리</li>
</ul></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>boolean</code>
            
            
                <ul>
<li>작업 성공 여부 (ex) true=&gt; 성공, false =&gt;실패</li>
</ul>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".trigger">trigger<span class="signature">(target, eventType, param)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>이벤트 강제로 실행</p>
<pre class="prettyprint source lang-javascript"><code>//사용자 정의 이벤트 생성
odf.event.addListener(map,'custom_event',(obj)=>{
 console.log(obj.a+obj.b);
});

//사용자 정의 이벤트 강제 실행
odf.event.trigger(map,'custom_event' ,{a:1000,b:4});
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">target</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    |

    <span class="param-type">
        <code>HTMLElement</code>
    </span>
    |

    <span class="param-type">
        <code>odf.ExtObject</code>
    </span>
    |

    <span class="param-type">
        <code>MapBase</code>
    </span>
    

            

            

            

            <div class="param-description"><ul>
<li>강제로 이벤트를 실행시킬 대상(css 선택자 텍스트, odf.ExtObject)
(ex) '#moveBtn' | '.mtbBtn' | (new Map({...}))</li>
</ul></div>
            
        </li>

    

        <li>
            
                <span class="param-name">eventType</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>강제로 실행시킬 이벤트의 종류
(ex) 'click', 'mousedown', 'moveend','custom-event-01'</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">param</span>
            

            
                


    <span class="param-type">
        <code>Object</code>
    </span>
    

            

            

            

            <div class="param-description"><p>이벤트를 강제 실행시키면서 해당 콜백 function에 넘길 파라미터</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
    

    

    
</article>

</section>




  </div>
</main>

<footer class="layout-footer">
  <div class="container">
    Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.11</a> on Tue Jan 21 2025 11:05:52 GMT+0900 (대한민국 표준시)
  </div>
</footer>



<script src="scripts/prism.dev.js"></script>
</body>
</html>