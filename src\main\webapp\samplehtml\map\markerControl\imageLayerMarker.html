<!DOCTYPE HTML>
<html>
<head>
    <meta charset="utf-8">
    <link href="::OdfUrl::/odf.css" rel="stylesheet">
    <script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
</head>
<body>
<div id="map" class="odf-view" ></div>
</body>
<style>
    .imageLayer{
        background-size: cover; /* 이미지 크기 조정 */
        background-position: center; /* 이미지 위치 조정 */
        border:1px solid #000000;
        pointer-events: none;
        opacity: 30%;
    }
</style>
<script>

    /* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
    var mapContainer = document.getElementById('map');
    var coord = new odf.Coordinate(::coordx::,::coordy::);
    var mapOption = "::mapOpt::";
    mapOption.preload = 3;
    var map = new odf.Map(mapContainer, mapOption);

    /* 그리기 도구 컨트롤 생성 */
    var drawControl = new odf.DrawControl();
    drawControl.setMap(map);


    var originExtent = [190715.23594570666, 542402.4687575398, 190775.48202033833, 542462.4165842772];
    var projectionCode= ::srid::;
    //입력 좌표계 -> 지도 자표계로 변환
    var transformedExtent = map.getProjection().projectExtent(originExtent,projectionCode);
    var [minX,minY,maxX,maxY] = transformedExtent;

    var center = [(minX+maxX)/2,(minY+maxY)/2];
    //마커가 잘 보이는 위치로 이동, 줌
    map.setCenter(center);
    map.getView().fit(transformedExtent)

    //getPixelFromCoordinate 함수는 view가 초기화된 후 사용할 수 있습니다.
    odf.event.addListener(map,'postrender',()=>{


        var [minX_px,minY_px] = map.getPixelFromCoordinate([minX,minY]);
        var [maxX_px,maxY_px] = map.getPixelFromCoordinate([maxX,maxY]);

        var orignWidth = maxX_px - minX_px;
        var orignHeight = maxY_px - minY_px;

        var imageLayer = document.createElement('img');
        imageLayer.classList.add('imageLayer');
        imageLayer.src = '::developerUrl::/images/wavus.png';
        imageLayer.style.height=`${orignHeight}px`;
        imageLayer.style.width=`${orignWidth}px`;


        var marker = new odf.Marker({
            /*마커의 위치*/
            position : center,
            /*드래그 가능 여부(기본 값 false)*/
            draggable : false,
            /*마커의 상대적 위치
             - 'top-left' : position 값 기준 상단 좌측에 위치
             - 'top-center' : position 값 기준 상단 중앙에 위치
             - 'top-right' : position 값 기준 상단 우측에 위치
             - 'center-left' : position 값 기준 중앙 좌측에 위치
             - 'center-center' : position 값 기준 중앙 중앙에 위치
             - 'center-right' : position 값 기준 중앙 우측에 위치
             - 'bottom-left' : position 값 기준 하단 우측에 위치
             - 'bottom-center' : position 값 기준 하단 중앙에 위치(기본값)
             - 'bottom-right' : position 값 기준 하단 좌측에 위치
            */
            positioning : 'center-center',
            /*기준점으로 부터 정의한 값만큼 마커를 이동(픽셀) (기본값 [0,0])
             - 첫번째 요소 : 수평 오프셋 값. 양수는 마커를 오른쪽으로 이동시킴
                - 두번째 요소 : 수직 오프셋 값. 양수는 마커를 아래로 이동시킴
            */
            offset : [0,2],
            /*마커 이벤트 전파 중지 여부.
             - true : 이벤트 전파 중지,(마커 클릭해도 지도 클릭 이벤트 발생하지 않음)
             - false : 이벤트 전파 (기본값 false)
            */
            stopEvent : false,
            //마커의 스타일 정의
            style :{
                //width : '300px' ,
                //height : '300px' ,
                // 이미지 경로
                // src : 'images/wavus.png' ,
                // 사용자 정의 마커
                element : imageLayer
            },
            /*지도 화면에서 팝업이 잘려나오게 될 경우, 지도를 이동할지 여부 (기본값 false)*/
            autoPan : false,
            /* autoPan이 ture일때 사용되는 옵션. 지도를 이동하는데 사용되는 애니메이션.
            (기본값 : 250) 최소 :0 최대 : 100000 */
            autoPanAnimation : 0,
            /* autoPan이 ture일때 사용되는 옵션. 팝업과 지도 사이의 여백(픽셀) 지정 (기본값 : 20) */
            autoPanMargin : 20,
        });
        marker.setMap(map);


        var oldResolution = map.getView().getResolution();
        odf.event.addListener(map.getView(),'change:resolution',(evt)=>{
            var newResolution = evt.target.getResolution();
            var rate = oldResolution/newResolution;
            orignHeight *= rate;
            orignWidth *= rate;

            imageLayer.style.height=`${orignHeight}px`;
            imageLayer.style.width=`${orignWidth}px`;
            oldResolution = newResolution;
        })
    },true/*이벤트를 한번만 호출되고 이벤트가 해제*/);

</script>
</html>
