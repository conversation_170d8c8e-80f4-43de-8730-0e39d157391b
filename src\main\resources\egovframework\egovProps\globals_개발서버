#-----------------------------------------------------------------------
#
#   globals.properties : \uc2dc\uc2a4\ud15c
#
#-----------------------------------------------------------------------
#   1.  key = value \uad6c\uc870\uc785\ub2c8\ub2e4.
#   2.  key\uac12\uc740 \uacf5\ubc31\ubb38\uc790\ub97c \ud3ec\ud568\ubd88\uac00, value\uac12\uc740 \uacf5\ubc31\ubb38\uc790\ub97c \uac00\ub2a5
#   3.  key\uac12\uc73c\ub85c \ud55c\uae00\uc744 \uc0ac\uc6a9\ubd88\uac00,   value\uac12\uc740 \ud55c\uae00\uc0ac\uc6a9\uc774 \uac00\ub2a5
#   4.  \uc904\uc744 \ubc14\uafc0 \ud544\uc694\uac00 \uc788\uc73c\uba74 '\'\ub97c \ub77c\uc778\uc758 \ub05d\uc5d0 \ucd94\uac00(\ub9cc\uc57d  '\'\ubb38\uc790\ub97c \uc0ac\uc6a9\ud574\uc57c \ud558\ub294 \uacbd\uc6b0\ub294 '\\'\ub97c \uc0ac\uc6a9)
#   5.  Windows\uc5d0\uc11c\uc758 \ub514\ub809\ud1a0\ub9ac \ud45c\uc2dc : '\\' or '/'  ('\' \uc0ac\uc6a9\ud558\uba74 \uc548\ub428)
#   6.  Unix\uc5d0\uc11c\uc758 \ub514\ub809\ud1a0\ub9ac \ud45c\uc2dc : '/'
#   7.  \uc8fc\uc11d\ubb38 \ucc98\ub9ac\ub294  #\uc0ac\uc6a9
#   8.  value\uac12 \ub4a4\uc5d0 \uc2a4\ud398\uc774\uc2a4\uac00 \uc874\uc7ac\ud558\ub294 \uacbd\uc6b0 \uc11c\ube14\ub9bf\uc5d0\uc11c \ucc38\uc870\ud560\ub54c\ub294 \uc5d0\ub7ec\ubc1c\uc0dd\ud560 \uc218 \uc788\uc73c\ubbc0\ub85c trim()\ud558\uac70\ub098 \ub9c8\uc9c0\ub9c9 \uacf5\ubc31\uc5c6\uc774 properties \uac12\uc744 \uc124\uc815\ud560\uac83
#-----------------------------------------------------------------------

Url.WfsAPI =  /map/api/map/wfs
Url.WmsAPI = /map/api/map/wms
Url.WmtsAPI = /map/api/map/wmts
Url.APIGW = https://api.gonp.duckdns.org
Url.Geoserver = https://geoserver.gonp.duckdns.org:14026/geoserver
Url.ODF = http://100.10.50.111:8000/dev
Url.DOCS = /docs/api/

Service.ODF = /dev
Service.Layer = /map
#Service.API = ana,pub,cdr,bag,est,ctt,adg
Service.API = smt,map,analysis,publish,coord,addrgeo,workflow,lyrgroup
#,cdr,bag,est,ctt,adg
Service.API.Order = API \uc0ac\uc6a9 \uc815\ubcf4 \uad00\ub9ac,\uacf5\ud1b5 \ucf54\ub4dc \uc870\ud68c,\ub808\uc774\uc5b4 \uc18d\uc131\ud544\ud130 \uc124\uc815,\ub808\uc774\uc5b4 \uc2a4\ud0c0\uc77c \uc124\uc815,\ub808\uc774\uc5b4 \uc815\ubcf4 \uad00\ub9ac,\ub808\uc774\uc5b4 \uceec\ub7fc \uc815\ubcf4 \uad00\ub9ac,\ub808\uc774\uc5b4 \ud31d\uc5c5 \uc124\uc815,\ubca0\uc774\uc2a4\ub9f5 \uad00\ub9ac,\uc0ac\uc6a9\uc790 \ubd81\ub9c8\ud06c \uad00\ub9ac,\uc0ac\uc6a9\uc790 \uc774\ubbf8\uc9c0 \uad00\ub9ac,\uc0ac\uc6a9\uc790 \uc9c0\ub3c4 \uc791\uc5c5 \uc54c\ub9bc \uad00\ub9ac,\uc6f9\ub808\uc774\uc5b4\uc11c\ube44\uc2a4 \uad00\ub9ac,\uc6f9\ub9f5 \uad00\ub9ac,\uc6f9\uc571 \ud15c\ud50c\ub9bf \uad00\ub9ac,\uc9c0\ub3c4 TOC \uad00\ub9ac,\uc9c0\uc624\ucf54\ub529 \uacb0\uacfc \ud30c\uc77c \uad00\ub9ac,\
                    \uc9c0\ub3c4 \uc694\uccad,\ucee8\ud150\uce20\uad00\ub9ac,\ud3ec\ud0c8 \uc694\uccad,\
                    \ub3c4\ud615\ubd84\uc11d-\uacf5\uac04\ud328\ud134 \ubd84\uc11d,\ub3c4\ud615\ubd84\uc11d-\uadfc\uc811\ub3c4 \ubd84\uc11d,\ub3c4\ud615\ubd84\uc11d-\ub370\uc774\ud130 \uad00\ub9ac \ubd84\uc11d,\ub3c4\ud615\ubd84\uc11d-\ub370\uc774\ud130 \uc694\uc57d \ubd84\uc11d,\ub3c4\ud615\ubd84\uc11d-\uc704\uce58\ucc3e\uae30 \ubd84\uc11d,\ub808\uc774\uc5b4 \ud30c\uc77c \ub2e4\uc6b4\ub85c\ub4dc,\uc544\ud2c0\ub780-Api,\uc791\uc5c5\uc54c\ub9bc,\
                    \ub3c4\ud615\uc815\ubcf4 \uc5c5\ub85c\ub4dc,\ub808\uc774\uc5b4 \uad00\ub9ac,\
                    \uc88c\ud45c\ubcc0\ud658,\
                    \uc704\uce58\uac80\uc0c9,\uc8fc\uc18c\uc815\uc81c,\uc9c0\uc624\ucf54\ub529,\ud589\uc815\uad6c\uc5ed \uac80\uc0c9,\
                    \ub77c\uc774\ube0c\ub7ec\ub9ac \uc815\ubcf4 \uad00\ub9ac,\ub77c\uc774\ube0c\ub7ec\ub9ac \ud30c\ub77c\ubbf8\ud130 \uc815\ubcf4 \uad00\ub9ac,\uc6cc\ud06c\ud50c\ub85c\uc6b0 \uacf5\uc720 \uad00\ub9ac,\uc6cc\ud06c\ud50c\ub85c\uc6b0 \ub77c\uc774\ube0c\ub7ec\ub9ac \uad00\ub9ac,\uc6cc\ud06c\ud50c\ub85c\uc6b0 \uc774\ub825 \uad00\ub9ac,\uc6cc\ud06c\ud50c\ub85c\uc6b0 \uc815\ubcf4 \uad00\ub9ac,\ub808\uc774\uc5b4\uadf8\ub8f9 \uc815\ubcf4

#,\uc791\uc5c5\uc54c\ub9bc,\ub808\uc774\uc5b4 \uad00\ub9ac,\ucee8\ud150\uce20 \uc815\ubcf4,\uc9c0\ub3c4 \uc694\uccad,\
				\uc8fc\uc18c\uac80\uc0c9,\uc8fc\uc18c\uc815\uc81c,\uc9c0\uc624\ucf54\ub529,\uc88c\ud45c\ubcc0\ud658,\uacf5\uac04\uc815\ubcf4 \ud30c\uc77c \ub2e4\uc6b4\ub85c\ub4dc,\
				\ub370\uc774\ud130\ucd94\ucd9c,\uc77c\ud544\uc9c0 \uc885\ud569 \uc815\ubcf4,\ud1a0\uc9c0 \uc870\ud68c(\uc77c\ud544\uc9c0),\uac74\ubb3c \uc870\ud68c(\uc77c\ud544\uc9c0),\uac00\uaca9 \uc815\ubcf4 \uc870\ud68c(\uc77c\ud544\uc9c0),\ud1a0\uc9c0\uc774\uc6a9\uacc4\ud68d \uc870\ud68c(\uc77c\ud544\uc9c0),\
				\ub3c4\ud615\uc601\uc5ed\uc73c\ub85c \uac80\uc0c9,\ud589\uc815\uacbd\uacc4\ub85c \uac80\uc0c9,\uad6d\uac00\ubc95\ub839\uc815\ubcf4,\uc815\ubd80\ub514\ub809\ud130\ub9ac,SMS \uba54\uc2dc\uc9c0
sysSeCode = 05
APIGW.Apikey = clkfhRg9g4fUj6NeClhx4qZXRXlW9HD9

#2023-07-27 개발자지원센터 개선하면서 추가된 properties 들
#샘플에 사용할 배경지도 (VWORLD, BAROEMAP)
#Sample.Basemap = VWORLD
Sample.Basemap = BAROEMAP
#샘플 예제 SRID (전체 예제 동일하게 적용됨 -> 5179, 5186)
#Sample.Srid = 5179
Sample.Srid = 5186
#Kakao Key
AppKey.Kakao = 1292bf8d492b48c32219cd6cb549c276
#브이월드 Key
VWorld.ApiKey = 998FA064-9D48-32C2-8DC8-4DBA90793E9F
VWorld.Domain = https://developer.geon.kr
#국가공간정보 포털 Key
Nsdi.ApiKey = 132d391c091bf7d7e0659e


Url.Tif = https://developer.geon.kr
Url.Pbf = https://developer.geon.kr
