<!DOCTYPE html>
<html lang="ko" xmlns:th="http://www.thymeleaf.org"
	xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout">

<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>개발자 지원센터</title>
	<!-- Tell the browser to be responsive to screen width -->
	<meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
	
	<link rel="shortcut icon" href="#" />
	<link rel="stylesheet" th:href="@{/css/live.css(ms=${ms})}" />
	<link rel="stylesheet" th:href="@{/vendor/codemirror/lib/codemirror.css}" />
	
	<script th:src="@{/vendor/jquery/jquery-1.12.4.js}"></script>
	<script type="text/javascript" th:src="@{/vendor/codemirror/lib/codemirror.js}"></script>
	<script type="text/javascript" th:src="@{/vendor/codemirror/mode/xml/xml.js}"></script>
	<script type="text/javascript" th:src="@{/vendor/codemirror/mode/javascript/javascript.js}"></script>
	<script type="text/javascript" th:src="@{/vendor/codemirror/mode/css/css.js}"></script>
	<script type="text/javascript" th:src="@{/vendor/codemirror/mode/htmlmixed/htmlmixed.js}"></script>
	<script type="text/javascript" th:src="@{/vendor/codemirror/addon/display/autorefresh.js}"></script>
</head>

<body>
	<div id="toggle-menu-set">
		<a id="btn-html" class="toggle-button on" href="#"><span>HTML + JS</span></a>
		<a id="btn-css" class="toggle-button" href="#"><span>CSS</span></a>
		<a id="btn-result" class="toggle-button on" href="#"><span>결과</span></a>
	</div>
	<div class="clearFix">
		<div id="menu" class="controlArea clearFix">
	        <button id="btn-run" type="button" class="btnPlay left">▶ 실행</button>
	        <button id="btn-auto-run" type="button" class="btnAutoPlay left">자동실행</button>
	        <button id="btn-reset" type="button" class="btnReset right">초기화</button>
	    </div>
		<div class="contents">
			<div id="pannel-html" class="pannel" style="display:none;">
				<div class="title">HTML + JS</div>
				<textarea id="text-html" class="hide"></textarea>
			</div>
			<div id="pannel-css" class="pannel" style="display:none;">
				<div class="title">CSS</div>
				<textarea id="text-css" class="hide"></textarea>
			</div>
			<div id="pannel-result" class="pannel" style="display:none;">
				<div class="title">결과</div>
				<iframe id="preview-frame"></iframe>
			</div>
		</div>
	</div>
	<script th:src="@{/js/live/index.js(ms=${ms})}"></script>
</body>

</html>