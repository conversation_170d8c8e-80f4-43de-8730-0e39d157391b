package gops.developer.proxy.vo;

import com.google.gson.Gson;

public class ResponseVo {
	
	private int code;
	private String message;
	
	public ResponseVo(int code, String message) {
		this.code = code;
		this.message = message;
	}
	
	public int getCode() {
		return code;
	}

	public void setCode(int code) {
		this.code = code;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}
	
	public static String getJson(int code, String message) {
		ResponseVo vo = new ResponseVo(code, message);
		return new Gson().toJson(vo);
	}
}
