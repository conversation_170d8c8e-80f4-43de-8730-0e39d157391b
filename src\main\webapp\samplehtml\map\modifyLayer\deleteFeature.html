<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
</head>
<body>
	<div id="map" class="odf-view" style="height:550px;"></div>
	<div class="btnLogArea">
		<div class="innerBox">
			<button class="onoffOnlyBtn" onclick="setRemoveTarget()">삭제 도형 선택</button>
			<button class="onoffOnlyBtn" onclick="save()">저장</button>
		</div>
	</div>
	<p>'삭제 도형 선택' 버튼을 클릭하여 삭제할 도형을 선택합니다.</p>
	<p>'저장' 버튼 클릭 시 해당 결과값을 받을 수 있습니다.</p>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::,::coordy::);
	var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);

	/*ui 없이 그리기 도구 생성*/
	var drawControl = new odf.DrawControl({
		message : {
			DRAWSTART_POLYGON : '추가할 도형을 그리기 위해 지도를 클릭하세요.'
		}
	});
	drawControl.setMap(map,false);
	//그리기 레이어
	var drawLayer = drawControl.findDrawVectorLayer();

	//wfs 레이어 생성
	var wfsLayer = odf.LayerFactory.produce('geoserver', {
		method : 'get',
		server : '::WfsAPI::',
		layer : '::polygonLayer1::',
		service : 'wfs',
	});
	wfsLayer.setMap(map);
	wfsLayer.fit();

	//툴팁 메세지 셋팅
	var divElem = document.createElement('div');
	divElem.innerHTML = '삭제를 원하는 도형을 클릭하세요.';
	var tooltipMessage = new odf.Marker({
      position: [::coordx::,::coordy::],
      style: {
    	  	element: divElem
      },
	});
	//마우스 이동시 툴팁메세지 위치 이동
	odf.event.addListener(map, 'pointermove', function(event){
		tooltipMessage.setPosition(event.coordinate);
	});

	// 삭제할 도형 선택 목록
	var deleteTargetList = [];
	// 선택 도형 스타일 정의
	var selectedFeatureStyle = odf.StyleFactory.produce({
	    fill: { color: [0, 0, 0, 0.2] },
	    stroke: {
	      color: 'red',
	      width: 5,
	    },
	  });


	//삭제할 도형 선택 이벤트 리스너 등록 후 비활성화
	var selectFeatureEventId = odf.event.addListener(map, 'click', function(evt) {
		var selectFeature = map.selectFeatureOnClick(evt);
		if(selectFeature.length===0){
			return;
		}

		var deleteTarget =  selectFeature[0].feature;
		deleteTargetList.push(deleteTarget);
		deleteTarget.setStyle(selectedFeatureStyle);
	});
	odf.event.stateChange(selectFeatureEventId,'stopped');

	//삭제할 도형 선택
	function setRemoveTarget(){
		//삭제 도형 목록 초기화
		clear();
		//툴팁메세지등록
		tooltipMessage.setMap(map);
		//삭제할 도형 선택 이벤트 리스너 활성화
		odf.event.stateChange(selectFeatureEventId,'normal');
	}

	// 그린 도형을 서버로 전송
	function save(){
		if(deleteTargetList.length==0){
			alert(`삭제할 도형이 선택되지 않았습니다. '삭제 도형 선택' 버튼을 이용하여 삭제할 도형을 선택한 후 다시 시도하세요.`);
			return;
		}

		// 지오서버에 보낼 xml 문자열
		var xmlText = map.sendToServerModified(deleteTargetList, wfsLayer, 'delete');
		alert('콘솔 창을 확인하세요.');
		console.log(xmlText);

		// 지오서버에 해당 xml 보내기
		//  'Content-Type': 'application/xml'으로 요청 보내야함

		//삭제 도형 목록 초기화
		clear();
		//툴팁 메세지 제거
		tooltipMessage.removeMap();
		//삭제할 도형 선택 이벤트 리스너 사용 중지
		odf.event.stateChange(selectFeatureEventId,'stopped');
	}


	//삭제 도형 목록 초기화
	function clear(){
		if(deleteTargetList.length>0){
			deleteTargetList.forEach(deleteTarget=>{
				deleteTarget.setStyle();
			})
			deleteTargetList = [];
		}
	}
</script>
</html>
