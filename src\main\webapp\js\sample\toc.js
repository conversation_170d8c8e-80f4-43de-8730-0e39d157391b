/*
 * @트리 제어
 */
var TOC = function(_map, data, _options) {
	
		try{
			this.map = _map;
			this.mapfn = new MapFn(this.map);//mapfn 바인딩
		}
	catch(e){
		throw new Error('map은 필수값입니다.');
		}
		
	//01. 옵션 기본값 셋팅
	if(!_options)_options = {};
	if(!_options.level1)_options.level1 = {};
	_options.level1.useFlag = true;
	if(!_options.level1.option)_options.level1.option = {};
	//01.01. topButton 옵션 기본값 셋팅
	if(!_options.level1.option.topButton)_options.level1.option.topButton = {};
	if(!_options.level1.option.topButton.addGroup)_options.level1.option.topButton.addGroup = false;
	if(!_options.level1.option.topButton.addLayer)_options.level1.option.topButton.addLayer = false;
	if(!_options.level1.option.topButton.exportData)_options.level1.option.topButton.exportData = false;
	//01.02 layerBtton 옵션 기본값 셋팅 
	if(!_options.level1.option.layerButton)_options.level1.option.layerButton = {};
	if(!_options.level1.option.layerButton.label)_options.level1.option.layerButton.label = false;
	if(!_options.level1.option.layerButton.onoff)_options.level1.option.layerButton.onoff = true;
	if(!_options.level1.option.layerButton.remove)_options.level1.option.layerButton.remove = false;
	if (!data) data = [];

	//02. TOC DOM생성	
	var _targetElement = document.createElement('div');
	_targetElement.id = 'toc';
	_targetElement.classList.add('toc_div');
	this.map.getTarget().parentElement.parentElement.appendChild(_targetElement)
	this.targetElement = _targetElement;
	
	let html =  '<div class="tocTop"><div class="toc_title trow"><h4>레이어</h4>';
	//topButton 생성
	if(_options.level1.option.topButton.addGroup)html+='<button type="button" name="toc_addGroupBtn" class="addGroup"><span class="hidden">그룹추가</span></button>';
	if(_options.level1.option.topButton.addLayer)html+='<button type="button" name="toc_addDataBtn" class="addData"><span class="hidden">레이어 추가</span></button>';
	if(_options.level1.option.topButton.exportData)html+='<button type="button" name="toc_saveDataBtn" class="saveData"><span class="hidden">추출</span></button>';
	html+='</div></div><div class="toc_body"><div id="jstree"></div></div>';
	this.targetElement.innerHTML = html;
	
	//02.02. topButton 이벤트 연결
	if(_options.level1.option.topButton.addGroup){
		//그룹 추가 버튼 클릭
		this.targetElement.querySelector('button[name=toc_addGroupBtn]').addEventListener('click',function(e){
			var parent = "#";
			var selectNode = $tree.jstree("get_selected");
			if (selectNode && selectNode.length) {
				selectNode = this.getNode(selectNode[0]);
				parent = (selectNode) ? selectNode.parent : "#";
			}
			
			var sample = {id: "11", parent: "", text: "", lyrId: "",  onOff: false, labelOnoff: false, symbolCndCn: "", filterCndCn: "", lyrTySeCode: ""};
			var randomInt = this.getRandomInt(1, 999);
			sample.id = randomInt;
			sample.parent = parent;
			sample.text = "그룹" + randomInt;
			this.addData(sample);
		}.bind(this));
	}
	if(_options.level1.option.topButton.addLayer){
		// 레이어 추가 버튼 클릭
		this.targetElement.querySelector('button[name=toc_addDataBtn]').addEventListener('click',function(e){			
`	`	}.bind(this));
	}
	if(_options.level1.option.topButton.exportData){
		// 추출 버튼 클릭
		this.targetElement.querySelector('button[name=toc_saveDataBtn]').addEventListener('click',function(e){
			var data = this.getData();
			console.log(data);
			alert(JSON.stringify(data));
		}.bind(this));
	}
	if(data && data.length>0){
		for(var i=0; i< data.length; i++){
			var item = data[i];
			var isGroup = (item.lyrId===""|| !item.lyrId ? true:false);
			if(!isGroup){
				if(!item.lyrNm){
				item.lyrNm = item.text
				}
				//개발자 지원센터 위자드 데이터 유효성 검증
				if(!item.lyrTySeCode && (item.service==='wms'||item.service==='wfs')){
					if(item.serverUrl.includes('wmts')){
						array.splice(i, 1);
						if(wizardChangeCode){
							wizardChangeCode("tocTypControl", false);
						}
						return alert('URL 주소와 레이어 타입을 다시 확인해주시기 바랍니다.');
					}
					$.ajax({
							url : item.wfsServerUrl+ "?request=Describefeaturetype&service=wfs&version=1.0.0&outputformat=application/json&typeName="+item.lyrId,
							type : 'GET',
							async : false,
							success : function(data) {
								if(!data.featureTypes){
									array.map((itm, idx)=>{
										if (item.lyrId === itm.lyrId) {
											array.splice(idx, 1);
										}
									});
									if(wizardChangeCode){
										wizardChangeCode("tocTypControl", false);
									}
									return alert('존재하지 않는 레이어 입니다. 레이어명과 서버 URL을 확인해주시기바랍니다.');
								}
								data.featureTypes[0].properties.forEach(function(p){
	   							 if(p.type.includes('gml')){
			   							 let geometryType = p.localType;
			        					 let lyrTySeCode = '';
										 if(geometryType.includes('Polygon'))lyrTySeCode='3';
										 if(geometryType.includes('LineString'))lyrTySeCode='2';
										 if(geometryType.includes('Point'))lyrTySeCode='1';
			
				       					  item.lyrTySeCode = lyrTySeCode;
			    					 }
								 });
							}.bind(this),
							error : function(xhr, status, err) {
								item.lyrTySeCode = '';
							}
						});
				}//개발자 지원센터 위자드 데이터 유효성 검증
				else if(item.service==='wmts'){
					if(!item.serverUrl.includes('wmts')){
						array.splice(i, 1);
						if(wizardChangeCode){
							wizardChangeCode("tocTypControl", false);
						}
						return alert('URL 주소를 다시 확인해주시기 바랍니다.');
					};
					$.ajax({
						url : item.serverUrl+ "?request=getCapabilities&service=wmts&version=1.0.0&outputformat=json",
						type : 'GET',
						async : false,
						success : function(data) {
							let layerTitle = data.querySelectorAll('Contents Layer Title');
							let passFlag;
							for(let i=0; i< layerTitle.length;i++){
								if(layerTitle[i].textContent == item.lyrId.split(':')[1]){
									passFlag = true;
								}
							}
							if(!passFlag){
									array.splice(i, 1);
									if(wizardChangeCode){
										wizardChangeCode("tocTypControl", false);
									}
									return alert('레이어명을  다시 확인해주시기 바랍니다.');
								};
						}.bind(this),
						error : function(xhr, status, err) {
							item.lyrTySeCode = '';
						}
					});
					
				}
			}
			else{
				item.lyrTySeCode = "";
			}
		}
	}
	
	//03. jstree 초기화
	this.treeId = 'jstree';
	
	//03.01. 트리 객체 생성
	var $tree = $(this.targetElement.querySelector('#'+this.treeId));
	
	//03.02 트리 구조 생성
	$tree.jstree({
		plugins : ["json_data", "dnd"],
		core : {
			themes : {
				dots : false,
				icons : false,
				responsive : false
			},
			// 텍스트 더블클릭하면 노드 토클 방지
			dblclick_toggle : false, // false
			// 다중선택
			multiple : false,
			// 생성, 이름 변경, 삭제, 이동, 복사 이벤트
			check_callback : true,
			// 트리구조 데이터
			data : data.map(function(item) {
				item["parent"] = (item["parent"]) ?  item["parent"] : "#";
				return item;
			})
		}
	});
	
	//03.03. jstree 이벤트 연결
	// 트리 구조 로딩 완료 시
	$tree.bind("loaded.jstree", function(e) {
		var data = this.getCoreData();
		
		for (var i=0; i<data.length; i++) {
			var d = data[i];
			if (d.lyrId /*&& d.onOff*/) {
				
				this.mapfn.addLayer(d);
			}
		}

		this.refresh($tree.jstree(true).settings.core.data);
	}.bind(this));
	// 노드 이동
	$tree.bind("move_node.jstree", function(e, node) {
		
		if(this.subLayout){
			this.subLayout.hide();
		}
		
		var parentType = this.findCoreData(node.parent)===null? '00':(this.findCoreData(node.parent).lyrId===''?'01'/*group*/:'02'/*layer*/);
		var selectType =  this.findCoreData(node.node.id).lyrId===''?'01'/*group*/:'02'/*layer*/;

		if(parentType==='00'/*그룹/레이어-> 단순 */){
			return true;
		}
		//이동을 막아야 하는 경우
		if((selectType==='01'&&parentType==='01')/*그룹->그룹*/||
			(selectType==='01'&&parentType==='02')/*그룹->레이어*/||
			(selectType==='02'&&parentType==='02')/*레이어->레이어*/){	
			var parentNode = $tree.jstree(true).get_node(node.parent);
			parentNode.children = null;
			this.refresh(this.getCoreData());
		}
		else{
			this.refresh(this.getData());
		}
	}.bind(this));
	

	$tree.bind("dblclick.jstree", function(e) {
		
		var $e = $(e.target);
		if(!$e.hasClass('name') ){
			return;
		}
		
		var id = $e.parents('li[role=presentation]').attr('id');
		var another =$e.parents("#jstree")[0].getElementsByClassName('changedName'); 
		for(var i=0; i<another.length;i++){
			var span = another[i].parentElement;
			var originalName = span.querySelector('.originalName').value;
			span.innerHTML = originalName;
		}
		
		var originalData = e.target.innerHTML.trimLeft();
		e.target.innerHTML = '<input type="text" class="changedName" value="'+originalData+'"/><input type="hidden" class="originalName" value="'+originalData+'"/><input type="hidden" class="id" value="'+id+'"/><button class="btnChangeName">변경</button><button class="btnChangeNameClear">취소</button>';
		
		$e.find('button.btnChangeName').click(function(evt){
			var id = $(evt.target).parent().find('.id').val();
			var changeName = $(evt.target).parent().find('.changedName').val();
			
			var _data = this.findCoreData(id);
			
			if(_data.lyrId!==''){
				this.setLyrNm(id,changeName);
			}
			this.setText(id,changeName);
	
			//정보탭 초기화		
			if(this.subLayout && this.subLayout.infoTab){
				this.subLayout.infoTab.init()
			}
		}.bind(this));

		$e.find('button.btnChangeNameClear').click(function(evt){
			this.refresh(this.getData());
		}.bind(this));
			
	}.bind(this));
	
	// 버튼 클릭 - 더보기, 라벨, onoff, 삭제
	$tree.bind("click.jstree", function(e) {
		var $e = $(e.target);
		var id = $e.parent().parent().parent().parent().attr('id');

		if ($e.hasClass("plusminus")) {
			this.more($e, id);
		} else if ($e.hasClass("label")) { 
			this.label($e, id);
		} else if ($e.hasClass("onoff")) {
			this.onoff($e, id);
		} else if ($e.hasClass("remove")) {
			this.remove( id);
		} 
		else if($e.hasClass("changedName")){
			$e.focus(); 
			return false;
		}
		else if($e.hasClass("btnChangeName")||$e.hasClass("btnChangeNameClear")){
			
			return false;
		}
		else {
			$('.clicked').removeClass('clicked');
			if ($e[0].nodeName.toLowerCase() === "a") {
				$($e[0].parentElement).addClass('clicked');
			} else if ($e[0].nodeName.toLowerCase() === "li") {
				$($e[0]).addClass('clicked');
			} else if ($e[0].nodeName.toLowerCase() === "div") {
				$($e[0].parentElement.parentElement).addClass('clicked');
			}
			
			id = $e.parents('li[role=presentation]').attr('id');
			if(this.subLayout){
				this.showLayout($e, id);
			}

			var _data = this.findCoreData(id);
			if(_data.lyrId!==''){
				if (_data.onOff === true) {
					_data.layerObject.fit(false);
				} else {
					alert("레이어를 활성화 해주세요.");
					return false;
				}
			}
		}
	}.bind(this));
	// 전체열기
	this.openAll = function() {
		$tree.jstree("open_all");
	};
	// 전체 닫기
	this.closeAll = function() {
		$tree.jstree("close_all");
	};
	// 특정 노드 열기
	this.openNode = function(id) {
		$tree.jstree("open_node", id)
	};
	// 특정 노드 닫기
	this.closeNode = function(id) {
		$tree.jstree("close_node", id)
	};
	// 특정노드 삭제
	this.removeNode = function(id) {
		$tree.jstree(true).delete_node($(this.targetElement).find("#"+id));
	};
	// 특정노드 이름변경
	this.renameNode = function(node, text) {
		$tree.jstree("rename_node", node, text);
	};
	
	// 부모노드
	this.parentNode = function(id) {
		var pid = this.getNode(id).parent;
		return this.getNode(pid);
	};
	
	// 자식노드
	this.childrenNode = function(id) {
		var ids = this.getNode(id).children;
		if(!ids) return [];
		
		var children = [];
		for (var i=0; i<ids.length; i++) {
			children.push(this.getNode(ids[i]));
		}
		return children;
	};
	
	// 특정노드
	this.getNode = function(id) {
		
		return $tree.jstree(true).get_node(id);
	};

	// 임의의 숫자 생성
	this.getRandomInt = function(min, max) {
		min = Math.ceil(min);
		max = Math.floor(max);
		var random = window.crypto.getRandomValues(new Uint16Array(1))[0]%1000/1000;
		return Math.floor(random* (max - min)) + min;
	};

	this.getLyrNm= function(id){
		
		var data = this.getData();
		var flag = false;
		for (var i=0; i<data.length; i++) {
			if (id == data[i].id) {
				flag = true;
				return data[i].lyrNm
			}	
		}
		if(!flag){
			throw new Error('존재하는 id가 아닙니다.');
		}
		return undefined;
	}
	
	this.setLyrNm= function(id,lyrNm){
		
		var data = this.getData();
		var flag = false;
		if(!lyrNm ||  typeof lyrNm!=='string'){
			throw new Error('lyrNm는 string 타입으로 제한되며, 필수값입니다. ');
		}
		for (var i=0; i<data.length; i++) {
			if (id == data[i].id) {
				flag = true;
				data[i].lyrNm = lyrNm;
				break;
			}	
		}
		if(!flag){
			throw new Error('존재하는 id가 아닙니다.');
		}
		this.setCoreData(data);
		this.refresh(data);
	}

	this.getText= function(id){
		
		var data = this.getData();
		var flag = false;
		
		for (var i=0; i<data.length; i++) {
			if (id == data[i].id) {
				flag = true;
				return data[i].text
			}	
		}
		if(!flag){
			throw new Error('존재하는 id가 아닙니다.');
		}
		return undefined;
	}
	
	this.setText= function(id,text){
		
		var data = this.getData();
		var flag = false;
		if(!text ||  typeof text!=='string'){
			throw new Error('text는 string 타입으로 제한되며, 필수값입니다. ');
		}
		for (var i=0; i<data.length; i++) {
			if (id == data[i].id) {
				flag = true;
				data[i].text = text;
				break;
			}	
		}
		if(!flag){
			throw new Error('존재하는 id가 아닙니다.');
		}
		this.setCoreData(data);
		this.refresh(data);
	}

	var buttons = {
		plus : '<input type="button" class="plusminus open toc-btn"/>',
		minus : '<input type="button" class="plusminus toc-btn close"/>',
		label :  '<input type="button" class="label toc-btn"/>',  
		on :  '<input type="button" class="onoff on toc-btn"/>',
		off :  '<input type="button" class="onoff off toc-btn"/>',
		remove :  '<input type="button" class="remove toc-btn"/>',
	};
	
	// 더보기(그룹 펼치기/접기)
	this.more = function($e, id) {
		var status = $e.hasClass("open");
		if (status) {
			this.openNode(id);
			$e.removeClass("open").addClass("close");
			this.findCoreData(id).plusminus = false;
		} else {
			this.closeNode(id);
			$e.removeClass("close").addClass("open");
			this.findCoreData(id).plusminus = true;
		}
	};
	
	// 노드 라벨 변경
	this.label = function($e, id) {
		// 레이어 타입이 VECTOR 만 해당
		var data = this.findCoreData(id);
		var odfId = data.odfId;
		var layer = this.mapfn.findLayer(odfId);
		if (layer && layer.type == 'VECTOR') {
			var source = layer.getSource();
			var fetures = source.getFeatures();
			if (!fetures && !fetures.length) {
				return;	
			}
			this.mapfn.style.label(data);
			data.labelOnoff = !data.labelOnoff;
		}
	};
 
	// 레이어 onoff
	this.onoff  = function($e, id) {
		var data = this.findCoreData(id);
		var odfId = data.odfId;
		var _onoff = data.onOff;
		var groupChild = this.getNode(id).children;
		var childOnOff = [];
		var groupFlag = data.lyrId===""?true:false;
		
		if(groupFlag){
			for(var i = 0; i<groupChild.length; i++){
				if (!this.findCoreData(groupChild[i]).odfId) {
					this.mapfn.addLayer(this.findCoreData(groupChild[i]));
				} else {
					this.mapfn.setVisible(this.findCoreData(groupChild[i]).odfId, !_onoff);
				}
				
				var $target = $($e.parents('li').find('ul li .layerBtn .onoff')[i]);
				
				if(_onoff == true){
					$target.removeClass('on').addClass('off');
				}else{
					$target.removeClass('off').addClass('on');
				}
				this.findCoreData(groupChild[i]).onOff = !_onoff;
			}
		}else{
			if (!odfId) {
				this.mapfn.addLayer(data);	
			} else {
				this.mapfn.setVisible(odfId, !_onoff);
			}
		}
		
		if(!(groupFlag && groupChild.length==0)){
			if($e.hasClass('on')){
				$e.removeClass('on').addClass('off');
			}else{
				$e.removeClass('off').addClass('on');
			}
			data.onOff = !_onoff;
		}

		if(data.parent != '#'){
			var groupSrc = $($e.parents('li').find('a .layerPd .groupBtn .onoff'));
			for(var j=0; j<this.getNode(data.parent).children.length; j++){
				childOnOff.push(this.findCoreData(this.getNode(data.parent).children[j]).onOff);
			}
			if(childOnOff.includes(false)){
				this.findCoreData(data.parent).onOff = false;
				if(this.findCoreData(data.parent).onOff == false){
					groupSrc.removeClass('on').addClass('off');
				}else{
					groupSrc.removeClass('off').addClass('on');
				}
			}else{
				this.findCoreData(data.parent).onOff = true;
				if(this.findCoreData(data.parent).onOff == true){
					groupSrc.removeClass('off').addClass('on');
				}else{
					groupSrc.removeClass('on').addClass('off');
				}
			}
		}
	};
	
	
	// 노드 제거 및 레이어 삭제
	this.remove = function( id) {
		var scrollTop = $(this.targetElement).find(".toc_body").scrollTop();
		var nodes = this.childrenNode(id);
		if (nodes.length) {
			for (var i=0; i<nodes.length; i++) {
				var node = nodes[i];
				var data = this.findCoreData(node.id);
				this.mapfn.removeLayer(data.odfId);
			}
		}
		var data = this.findCoreData(id);
		this.mapfn.removeLayer(data.odfId);
		if(this.subLayout){
			this.subLayout.hide();
		}
		this.removeNode(id);
		$(".toc_body").scrollTop(scrollTop);
	};
	
	// 레이어 정보 보기
	this.showLayout = function($e, id) {
		this.subLayout.hide();
		var data = this.findCoreData(id);// 레이어별 기초 데이터 셋
		if (data && data.lyrId) {
			this.subLayout.viewData = data;
			this.subLayout.init();// 레이어 유형(wfs/wms/wmts)별 처리
			this.subLayout.show();// 서브 메뉴 표출
		}
	};

	// 노드 갱신
	this.refresh = function(data) {
		var newData = [];
		
		for (var i=0; i<data.length; i++) {
			var item = data[i];

			var childNodes = this.childrenNode(item.id);
			item["parent"] = (item["parent"]) ?  item["parent"] : "#";
			
			$e = $("<div></div>");
			$e.append(item.text);
			newText = $e.text();
			var _class = 'group';
			if(item.service=='wms'){
				switch(item.lyrTySeCode){
				case "1" : _class='point'; break;
				case "2" : _class='line'; break;
				case "3" : _class='polygon'; break;
				}
			}
			else if(item.service=='wfs'){
				_class='icon';
			}
			else if(item.service=='wmts'){
				_class='wmts'
			}
			
			 if(item.service=='wfs'){

			 if(!item.odfId){
					 this.mapfn.addLayer(item);	
				 }
				
				 var style = item.layerObject.getStyle();
				 if(style){
					newText = this.mapfn.getStyleElemText(style)+ " <span class='name'>" + newText+"</span>"; 
				 }
				 else{
					 newText = "<span class='"+_class+"'></span> <span class='name'>" + newText+"</span>"; 
				 }
			}else{
				newText = "<span class='"+_class+"'></span> <span class='name'>" + newText+"</span>";
			}
			
			var onoffBtn,labelBtn,removeBtn = "";
			onoffBtn = (item.onOff) ? buttons.on : buttons.off;
			removeBtn = _options.level1.option.layerButton.remove? buttons.remove: "";
			
			if (item.lyrId) {
				labelBtn = (item.service == 'wfs' && _options.level1.option.layerButton.label) ? buttons.label : "";
				newText += "<div class='layerBtn'>"+ labelBtn + onoffBtn + removeBtn + "</div>";
			} else {
				item.onOff = false;
				newText += "<div class='groupBtn'>"+ buttons.off + removeBtn + "</div>";
				
				var childOnOff = [];
				for(var j=0; j<childNodes.length; j++){
					childOnOff.push(this.findCoreData(childNodes[j].id).onOff);
				}
				if(childOnOff.includes(false)){
					item.onOff = false;
				}else{
					item.onOff = true;
				}
				
				if(item.plusminus == false){
					item.plusminus = false;
				}else {
					item.plusminus = true;
				}
				
				var plusminusBtn = (item.plusminus) ? buttons.plus : buttons.minus
				var onoffBtn = (item.onOff) ? buttons.on : buttons.off
				newText = "<span class='group'></span><span class='name'>" + $e.text() + "</span><div class='groupBtn'>"+ plusminusBtn + onoffBtn + removeBtn + "</div>";
			}
			if (childNodes.length) {
				
			}
			
			item.text = "<div class='layerPd'>"+ newText +"</div>";
			newData.push(item);
			
			// 지도 z-index
			if (item.odfId) {
				this.mapfn.setZIndex(item.odfId, data.length-i);
			}
		}
		$tree.jstree(true).settings.core.data = data;

		$tree.jstree(true).refresh(); 
		
		for (var i=0; i<data.length; i++) {
			var item = data[i];
			if(item.lyrId && item.onOff){
				var _odfId =item.odfId; 
				if(!_odfId){
					this.mapfn.addLayer(item);	
				}
			}
		}
		

	};
	
	// 해당 ID 값의 데이터를 리턴
	this.findCoreData = function(id) {
		var coreData = $tree.jstree(true).settings.core.data;
		for (var i=0; i<coreData.length; i++) {
			var data = coreData[i];
			if (data.id == id) {
				return data;
			}
		}
		return null;
	};
	
	// 노드의 원시 데이터
	this.setCoreData = function(data) {
		$tree.jstree(true).settings.core.data = data;
	};
	
	// 노드의 원시 데이터
	this.getCoreData = function() {
		return $tree.jstree(true).settings.core.data;
	};
	
	// 데이터 추가
	this.addData = function(d) {
		if (!d) return ;
		
		//기본값 셋팅
		if(!d.parent) d.parent='#';
		if(!d.lyrId) d.lyrId='';
		if(!d.lyrNm) d.lyrNm=d.text;
		if(!d.onOff) d.onOff=false;
		if(!d.service) d.service='';
		if(!d.labelOnoff) d.labelOnoff=false;
		if(!d.symbolCndCn) d.symbolCndCn='';
		if(!d.filterCndCn) d.filterCndCn='';
		if(!d.lyrTySeCode) d.lyrTySeCode='';
		
		if(d.lyrId!==""&&d.lyrId ){
			if( !d.lyrNm){
				d.lyrNm = d.text;
			}

			if(!d.lyrTySeCode && (d.service==='wms'||d.service==='wfs')){
				$.ajax({
						url : d.wfsServerUrl+ "?request=Describefeaturetype&service=wfs&version=1.0.0&outputformat=application/json&typeName="+d.lyrId,
						type : 'GET',
						async : false,
						success : function(data) {
							if(!data.featureTypes){
								array.map((itm, idx)=>{
									if (d.lyrId === itm.lyrId) {
										array.splice(idx, 1);
									}
								});
								if(wizardChangeCode){
									wizardChangeCode("tocTypControl", false);
								}
								return alert('존재하지 않는 레이어 입니다. 레이어명과 서버 URL을 확인해주시기바랍니다.');
							}
							data.featureTypes[0].properties.forEach(function(p){
   							 if(p.type.includes('gml')){
		   							 let geometryType = p.localType;
		        					 let lyrTySeCode = '';
									 if(geometryType.includes('Polygon'))lyrTySeCode='3';
									 if(geometryType.includes('LineString'))lyrTySeCode='2';
									 if(geometryType.includes('Point'))lyrTySeCode='1';
		
			       					  d.lyrTySeCode = lyrTySeCode;
		    					 }
							 });
						}.bind(this),
					});
			}
		}
		
		
		var data = this.getData();
		for (var i=0; i<data.length; i++) {
			if (d.id == data[i].id) {
				alert("중복된 ID 값이 존재 합니다.");
				return ;
			}	
		}
		data.splice(data.length,0,d);

		this.setCoreData(data);

		this.refresh(data);
	};
		
	// 현재 트리구조 + 원본 데이터를 Merge 하여 데이터를 리턴
	this.getData = function() {
		var newData = [];
		var jdata = $tree.jstree(true).get_json("#", {flat:true});
		for (var i=0; i<jdata.length; i++) {
			var current = jdata[i];
			var id = current.id;
			var data = this.findCoreData(id);
			if (data) {
				current.text = $("<div>"+current.text+"</div>").text();
				newData.push($.extend(data, current));
			}
		}
		return newData; 
	};
	
	
	//상세 정보 보기
	if(!_options.level2)_options.level2 = {};
	if(!_options.level2.useFlag)_options.level2.useFlag = false;
	//상세정보 생성
	if(_options.level2.useFlag){
		
		this.subLayout = new SubLayout(this.targetElement,_options.level2.option,this.mapfn);
	}
};

/*
 * @서브 레이아웃
 */
var SubLayout = function(_targetElement,_options,_mapfn) {
	
	this.mapfn = _mapfn;
	//option 기본 값 셋팅
	if(!_options)_options= {};
	if(!_options.tab)_options.tab = {};
	_options.tab.info = true;//무조건 true
	if(!_options.tab.filter)_options.tab.filter = false;
	if(!_options.tab.style)_options.tab.style = false;
	if(!_options.tab.legend)_options.tab.legend = false;
	
	if(_options.tab.style && !_options.imageUrl){
		throw new Error('styleTab이 활성화될 경우, imageUrl은 필수값입니다.');
	}
	
	//서브 레이아웃 DOM 생성
	SubLayout.createDOM(_targetElement,_options);
	this.targetElement = $(_targetElement.parentElement.querySelector('.toc_sub_div'));
	
	// 닫기 버튼 이벤트
	this.targetElement.find('.closeBtn').bind('click',function(e){
		this.hide();
	}.bind(this));
	// 탭 선택 이벤트
	this.targetElement.find('.submenu a').bind('click',function(e){
		this.targetElement.find('.tab_body').hide()
		this.targetElement.find("#"+e.target.getAttribute('data-for')).show();
	}.bind(this));
	
	
	// 레이아웃 열기
	this.show = function() {
		$e = $(this.targetElement);
		$e.show();
		$e.find(".tab_body").hide();
		$e.find(".tab_body:first").show();
		
		var isWmts = this.viewData.service==='wmts';
		
		// 정보 탭 초기화
		if(_options.tab.info){
			this.infoTab = new LayerInfoTab(this);
			this.infoTab.init(); 
		}
		// 필터 탭 초기화
		if(_options.tab.filter &&!isWmts){			
			this.filterTab = new LayerFilterTab(this);
			this.filterTab.init(); 
		}
		// 스타일 탭 초기화
		if(_options.tab.style&&!isWmts){
			this.styleTab = new LayerStyleTab(this, {imageUrl : _options.imageUrl});
			this.styleTab.init(); 
		}
		// 범례 탭 초기화
		if(_options.tab.legend&&!isWmts){
			this.legendTab = new LayerLegendTab(this);
			this.legendTab.init(); 
		}
	};
	
	// 레이어 타입별 subLayout 초기화
	
	this.init = function() {
		
		if (this.viewData.service!=='wfs') {
			// 스타일 탭과 범례 탭 초기화
			this.targetElement.find("#layerStyleLi").hide();
			this.targetElement.find("#layerLegendLi").hide();
		} else {
			// 스타일 탭과 범례 탭 초기화
			this.targetElement.find("#layerStyleLi").show();
			this.targetElement.find("#layerLegendLi").show();
		}
		
		
		if(this.viewData.service==='wmts'){
			this.targetElement.find("#layerFilterLi").hide();
			
			this.targetElement.find(".layerFeature_btn").hide();
			this.targetElement.find(".layerInfo_btn").hide();
		}else{
			this.targetElement.find("#layerFilterLi").show();

			this.targetElement.find(".layerFeature_btn").show();
			this.targetElement.find(".layerInfo_btn").show();
		}
	};
	
	// 레이아웃 닫기
	this.hide = function() {
		this.targetElement.hide();
		this.viewData = null;
	};
	
	// 선택한 레이어 정보
	this.viewData = null;
	
	//dom 생성
	
};
SubLayout.createDOM = function(targetElement, createOption){
	var html = '<div class="toc_sub_div" style="height: auto;"><div class="title"><h4 name="layerTitle">상세정보<button name="closeBtn" class="closeBtn">X</button></h4></div><div class="body"><div class="submenu"><ul>';
	if(createOption.tab.info)html+='<li id="layerInfoLi"><a href="javascript:void(0);" data-for="layerInfoTab">정보</a></li>';
	if(createOption.tab.filter)html+='<li id="layerFilterLi"><a href="javascript:void(0);" data-for="layerFilterTab">필터</a></li>';
	if(createOption.tab.style)html+='<li id="layerStyleLi"><a href="javascript:void(0);" data-for="layerStyleTab">스타일</a></li>';
	if(createOption.tab.legend)html+='<li id="layerLegendLi"><a href="javascript:void(0);" data-for="layerLegendTab">범례</a></li>';
	html+='</ul></div>';//subMenu end
	//정보 탭
	if(createOption.tab.info){
		html += '<div class="tab_body" id="layerInfoTab">';
		html += '	<div><form class="tab_form" name="info_form">레이어 명칭 <input input="text" name="layerNm" value="" /><br /> 서비스유형 <input input="text" name="service" value="" readonly /></form></div>';
		html += '	<div class="filter_btn"><input type="button" class="layerExtent_btn"name="layerExtent_btn" value="이동"> <input type="button"class="layerFeature_btn" name="layerFeature_btn" value="속성"> <input type="button" class="layerInfo_btn" name="layerInfo_btn" value="속성 조회"></div>';
		html += '</div>';
	}
	//필터탭
	if(createOption.tab.filter){
		html += '<div class="tab_body" id="layerFilterTab">';
		html += '	<div class="filter_btn">';
		html += '		<input type="radio" name="where" id="whereAND" value="AND" /><label for="whereAND">AND</label> <input type="radio" name="where" id="whereOR" value="OR" /><label for="whereOR">OR</label> <input type="button" class="addFilter_btn" name="q_more" value="+추가">';
		html += '	</div>';
		html += '	<div id="filter_area"><div>';
		html += '		<form class="tab_form" name="filter_form">';
		html += '			<div>';
		html += '				<select class="key" name="key"><option value="EMD_CD">EMD_CD</option></select><br />';
		html += '				<select class="sepator" name="sepator"><option value="=">다음과 같음</option><option value=">=">다음 이상</option><option value="<=">다음 이하</option><option value="<>">같지 않음</option><option value="LIKE">다음을 포함</option></select><br />';
		html += '				<input class="value" type="text" name="value" value="" placeholder="내용입력" />';
		html += '				<select class="value" name="value" style="display: none;"><option value=""></option></select>';
		html += '			</div><div>';
		html += '				<input type="radio" name="valueType" value="inputType" checked /><label for="">직접</label>';
		html += '				<input type="radio" name="valueType" value="selectType" /><label for="">값선택</label>';
		html += '			</div>'
		html += '		</form>';
		html += '	</div></div><div class="filter_btn">';
		html += '		<input type="button" class="apply_btn" name="apply_btn" value="필터적용">';
		html += '		<input type="button" class="reset_btn" name="reset_btn" value="초기화">';
		html += '	</div></div>';
	}
	//스타일탭
	if(createOption.tab.style){
		html +='<div class="tab_body" id="layerStyleTab" style="max-height: 480px;">';
		html +='  <label for="selectRenderer" class="labelTitle">타입선택&nbsp;&nbsp;</label><select id="selectRenderer"></select>';
		//범위별 심볼 설정 영역 start
		html +='  <div id="classBreaksArea" class="area-asideScroll" style="display: none;">';
		html +='  	<div id="classBreaksDiv" class="area-asideScroll">';
			//윤곽선/채우기 선택 영역
		html +='			<div>';
		html +='				<input type="radio" id="cbFillColorRadio" name="cbColor" checked><label for="cbFillColorRadio" id="cbFillColorLb">채우기</label>'; 
		html +='				<input type="radio" id="cbLineColorRadio" name="cbColor"><label for="cbLineColorRadio" id="cbLineColorLb">윤곽선</label>';
		html +='			</div>';
			//채우기 선택영역
		html+='			<div id="cbFillColorDiv" class="wrap  mt-20"><div id="" class="inline ">';
		html+='				<div><label>투명도</label> <input type="range" id="cbFillOpacity" class="opacity" min="0" max="1" step="0.1" value="1" style="width: 80%"></div>';
		html+='				<div><label>필&nbsp;&nbsp;드</label> <select id="cbSelectField" style="width: 100px;"></select></div>';
		html+='				<div><label>색&nbsp;&nbsp;상</label> <select id="cbSelectColor" style="width: 100px;"></select></div>';
		html+='				<div><label>단계</label> <select id="cbSelectLevel" style="width: 100px;"><option value="">선택</option>';
		for(var i =1 ; i<11;i++){html+='<option value="'+i+'">'+i+'단계</option>';};
		html+='				</select></div>';
		html+='				<div id="cbLegendArea"  style="max-height: 100px;overflow: auto;"><table id="cbLegend"><thead><th>색상</th><th>단계</th></thead><tbody></tbody></table></div>';
		html+='				<div><label>크&nbsp;&nbsp;기&nbsp;&nbsp;</label> <input type="number"	id="cbCircleRadius" value="10" min="1" max="50">px</div>';
		html+='			</div></div>';
			//윤곽선 선택
		html+='			<div id="cbLineColorDiv" class="wrap mt-20" style="display: none;">';
		html+='				<div class="inline colorPickerBox  ">';
		html+='					<div>투명도<input type="range" id="cbLineOpacity" class="colorOpacity" min="0" max="1" step="0.1" value="1" style="width: 70%"></div>';
		html+='					<table id="cbLineColorBox" class="colorBox" style="min-height: 72px; width: 100%; margin: 20 auto 3px auto;"></table>';
		html+='					<div class="mt-20 symbolcolorDiv">';
		html+='						<label class="labelTitle">색&nbsp;&nbsp;상&nbsp;&nbsp;</label> <input type="text" title="컬러값" class="colorValue" value="000000" maxlength="6" readonly>';
		html+='						<div class="colorView" style="background-color: rgb(0, 0, 0); width: 20px; height: 20px; display: inline-block;"></div>';
		html+='						<input type="button" class="moreColor" class="moreColor" value="더보기">';
		html+='					</div>';
		html+='				</div>';
		html+='				<div class="inline"><label for="lineThick">선두께</label><input type="number" id="cbLineThick" value="1" min="1" max="20"></div>';
		html+='			</div>';
		html+='		</div>'
		html+='		<div class="inline styleBtn"><input type="button" id="cbApply_btn" value="적용" /> <input type="button" id="cbReset_btn" value="초기화" /></div>';
		html+='  </div>';	//범위별 심볼 설정 영역 end
		//유형별 심볼 설정 영역
		html+='	<div id="uniqueValueArea" class="area-asideScroll" style="display: none;">';
		html+='		<div>';
		html+='			<input type="radio" id="uvFillColorRadio" name="uvColor" checked><label for="uvFillColorRadio" id="uvFillColorLb">채우기</label>';
		html+='			<input type="radio" id="uvLineColorRadio" name="uvColor"><label for="uvLineColorRadio" id="uvLineColorLb">윤곽선</label>';
		html+='		</div><div id="uvFillColorDiv" class="wrap  mt-20">';
		html+='			<div id="" class="inline ">';
		html+='				<div><label>투명도</label> <input type="range" id="uvFillOpacity" class="opacity" min="0" max="1" step="0.1" value="1" style="width: 80%"></div>';
		html+='				<div><label>필&nbsp;&nbsp;드</label> <select id="uvSelectField" style="width: 100px;"></select></div>';
		html+='				<div><label>색&nbsp;&nbsp;상</label> <select id="uvSelectColor" style="width: 100px;"></select></div>';
		html+='				<div id="uvLegendArea" style="overflow-y: scroll; max-height: 150px;">';
		html+='					<table id="uvLegend" style="margin:2px"><thead><th>색상</th><th>단계</th></thead><tbody></tbody></table>';
		html+='				</div>';
		html+='				<div><label>크&nbsp;&nbsp;기</label> <input type="number" id="uvCircleRadius" value="10" min="1" max="50">px</div>';
		html+='		</div></div>';
		html+='		<div id="uvLineColorDiv" class="wrap mt-20" style="display: none;">';
		html+='			<div class="inline colorPickerBox  ">';
		html+='				<div>투명도<input type="range" id="uvLineOpacity" class="colorOpacity" min="0" max="1" step="0.1" value="1" style="width: 70%"></div>';
		html+='				<table class="colorBox" style="min-height: 72px; width: 100%; margin: 20 auto 3px auto;"></table>';
		html+='				<div class="mt-20 symbolcolorDiv">';
		html+='					<label class="labelTitle">색&nbsp;&nbsp;상</label> <input type="text" title="컬러값" class="colorValue" value="000000" maxlength="6" readonly>';
		html+='					<div class="colorView" style="background-color: rgb(0, 0, 0); width: 20px; height: 20px; display: inline-block;"></div>';
		html+='					<input type="button" class="moreColor" class="moreColor" value="더보기">';
		html+='				</div>';
		html+='			</div>';
		html+='			<div class="inline"><label for="lineThick">선두께</label> <input type="number" id="uvLineThick" value="1" min="1" max="20"></div>';
		html+='		</div>';
		html+='		<div class="inline styleBtn"><input type="button" id="uvApply_btn" value="적용" /> <input type="button" id="uvReset_btn" value="초기화" /> </div>';
		html+='	</div>';		//유형별 심볼 설정 영역 end
		//라벨 설정 영역
		html+='	<div id="labelArea" class="area-asideScroll" style="display: none;">';
		html+='		<div class="inline colorPickerBox">';
		html+='			<table id="labelColorBox" class="colorBox" style="min-height: 72px; width: 100%; margin: 20 auto 3px auto;"></table>';
		html+='			<div class="mt-20 symbolcolorDiv">';
		html+='				<label for="labelColor" class="labelTitle">색&nbsp;&nbsp;상</label>';
		html+='				<input id="labelColor" type="text" title="컬러값" class="colorValue" value="000000" maxlength="6" readonly>';
		html+='				<input type="button" class="moreColor" id="moreLabelColor" value="더보기">';
		html+='				<div id="labelColorCheckBox" class="colorView" style="background-color: rgb(0, 0, 0); width: 30px; height: 30px"></div>';
		html+='			</div>';
		html+='			<div><label>필&nbsp;&nbsp;드</label> <select id="cbLabelSelectField"></select><span id="labelMsg" style="color: red;"></span></div>';
		html+='			<div><label for="labelFontSize" class="labelTitle">글&nbsp;&nbsp;꼴</label><select id="labelFont"><option id="Gulim" value="Gulim">굴림</option><option id="Dotum" value="Dotum">돋움</option></select></div>';
		html+='			<div><label for="labelFontSize" class="labelTitle">크&nbsp;&nbsp;기</label><input id="labelFontSize" type="number" class="symbolColorTxt" value="10">px</div>';
		html+='			<div><label for="labelFontStyle" class="labelTitle">스타일</label> <input type="checkbox" id="bold" value="bold">굵게 <input type="checkbox" id="oblique" value="oblique">기울임</div>';
		html+='			<div><label for="labelFontStyle" class="labelTitle">정&nbsp;&nbsp;렬</label><table>';
		for(var i =0 ; i<3;i++){
			html+='<tr>';
			for(var j =0 ; j<3;j++){
				var checked ='';
				if(i===1 && j===1){
					checked=' checked ';
				}
				html+='<td><input type="radio" name="align" id="'+j+'-'+i+'" '+checked+'></option>';
			}
			html+='</tr>';
		};
		html+='			</table></div>';
		html+='			<div><label for="labelFontStyle" class="labelTitle">후&nbsp;&nbsp;광</label><input type="checkbox" id="stroke" value="stroke"></div>';
		html+='			<div><label>가시범위</label> <select id="resolution"><option id="totalResolution" value="">전체보기</option><option id="gsd" value="11">광역시, 도</option><option id="sgg" value="5">군, 구</option><option id="emd" value="2">읍, 면, 동</option></select></div>';
		html+='		</div><div class="inline styleBtn">';
		html+='			<input type="button" id="labelApply_btn" value="적용" /> <input type="button" id="labelReset_btn" value="초기화" />';
		html+='		</div>';
		html+='	</div>';		//라벨 설정 영역 end
		//단일 심볼 영역
		html+='	<div id="simpleArea" class="area-asideScroll" style="display: none;">';
		html+='		<div class="symbolSelect">';
		html+='			<input type="radio" id="circleRadio" name="symbolSelect" value="circle" checked="checked" /><label for="circleRadio">색상설정</label>';
		html+='			<input type="radio" id="iconRadio" name="symbolSelect" value="icon" /><label for="iconRadio">이미지설정</label>';
		html+='		</div>';
		html+='		<div id="circleDiv" class="area-asideScroll">';
		html+='			<div>';
		html+='				<input type="radio" id="fillColorRadio" name="simpleSymbolColor" checked><label for="fillColorRadio" id="fillColorLb">채우기</label>';
		html+='				<input type="radio" id="lineColorRadio" name="simpleSymbolColor"><label for="lineColorRadio" id="lineColorLb">윤곽선</label>';
		html+='			</div><div id="lineColorDiv" class="wrap" style="display: none;">';
		html+='				<div class="inline">투명도 <input type="range" id="lineColorOpacityInputBox" class="styleChangeOption" name="lineColorOpacityInputBox" min="0" max="1" step="0.1" value="1"></div>';
		html+='				<div class="inline colorPickerBox">';
		html+='					<table id="lineColorBox" class="colorBox" style="min-height: 72px; width: 100%; margin: 20 auto 3px auto;"></table>';
		html+='					<div class="mt-20 symbolcolorDiv">';
		html+='						<label for="lineColor" class="labelTitle">색&nbsp;&nbsp;상</label>';
		html+='						<input id="lineColor" type="text" title="컬러값" class="colorValue" value="000000" maxlength="6" readonly>';
		html+='						<input type="button" class="moreColor" id="moreLineColor" value="더보기">';
		html+='						<div id="lineColorCheckBox" class="colorView" style="background-color: rgb(0, 0, 0); width: 30px; height: 30px"></div>';
		html+='					</div>';
		html+='					<div><label for="lineThick">선두께</label> <input type="number" id="lineThickInputBox" name="lineThickInputBox" value="1" min="1" max="20"></div>';
		html+='				</div>';
		html+='			</div><div id="fillColorDiv" class="wrap">';
		html+='				<div class="inline">투명도 <input type="range" id="fillColorOpacityInputBox" class="styleChangeOption" name="fillColorOpacityInputBox" min="0" max="1" step="0.1" value="1"></div>';
		html+='				<div class="inline colorPickerBox">';
		html+='					<table id="fillColorBox" class="colorBox" style="min-height: 72px; width: 100%; margin: 20 auto 3px auto;"></table>';
		html+='					<div class="mt-20 symbolcolorDiv">';
		html+='						<label for="fillColor" class="labelTitle">색&nbsp;&nbsp;상</label>';
		html+='						<input id="fillColor" type="text" title="컬러값" class="colorValue" value="000000" maxlength="6" readonly>';
		html+='						<input type="button" class="moreColor" id="moreFillColor" value="더보기">';
		html+='						<div class="colorCheckBox colorView" id="fillColorCheckBox" style="background-color: #3F0099; width: 30px; height: 30px"></div>';
		html+='					</div>';
		html+='					<div id="pointRadiousArea"><label for="pointRadius">크&nbsp;&nbsp;기</label> <input type="number" id="pointRadius" name="pointRadius" value="10" min="1" max="50"></div>';
		html+='				</div>';
		html+='			</div>';
		html+='			<div class="inline styleBtn"><input type="button" id="colorApply_btn" value="적용" /><input type="button" id="colorReset_btn" value="초기화" /></div>';
		html+='		</div><div id="imgSymbolDiv" class="area-asideScroll" style="display: none;">';
		html+='			<div class="inline"> 투명도 <input type="range" id="imgOpacity" class="styleChangeOption" name="opacity" min="0" max="1" step="0.1" value="1"></div>';
		html+='			<div>';
		html+='				<input type="radio" id="themeRadio" name="iconSymbolRadio" checked><label for="themeRadio">주제별</label>';
		html+='				<input type="radio" id="customRadio" name="iconSymbolRadio"><label for="customRadio">사용자</label>';
		html+='			</div><div><select id="themeSelect"></select></div>';
		html+='			<div id="themeSymbolDiv" class="imageList"><div id="themeIconsDiv" class="iconsListDiv"></div></div>';
		html+='			<div id="customSymbolDiv" style="display:none;"><input type="file" id="customFile" name="customFile"><div id="customIconsDiv" class="iconsListDiv"></div></div>';
		html+='			<div class="applyImageDiv">';
		html+='				<p>적용될 이미지</p><img id="imageViewer" src="'+createOption.imageUrl+'/images/smileIcon2.png" value="" class="imageViewer">';
		html+='				<div class="sizeDiv mt-10" style="display: block">';
		html+='					<label for="size" class="labelTitle">크&nbsp;&nbsp;기</label><input type="number" id="imgSize" value="1" min="1" max="10" step="1" /><strong class="tip">※ 기존 이미지의 n배</strong>';
		html+='				</div>';
		html+='			</div>';
		html+='			<div class="inline styleBtn"><input type="button" id="iconApply_btn" value="적용" /><input type="button" id="iconReset_btn" value="초기화" /></div>';
		html+='		</div>';
		html+='	</div>';		//단일 심볼 영역 end
		html+='</div>'
	}
	//범례탭
	if(createOption.tab.legend){ 
		html+='<div class="tab_body" id="layerLegendTab" style="max-height: 480px;overflow: auto;">';
		html+='	<table><thead><th>색상</th><th>단계</th></thead><tbody id="legendArea"></tbody></table>';
		html+= '</div>';
	}
	html+='</div></div>'//body end
	$(targetElement.parentElement).append(html);
}
/*
 * 레이어 정보
 */
var LayerInfoTab = function(_subLayout) {
	
	this.targetElement = $(_subLayout.targetElement.find('#layerInfoTab')[0]);
	this.mapfn = _subLayout.mapfn;
	
	// 이동 버튼
	this.targetElement.find(".layerExtent_btn").bind('click',function(e){
		this.moveToLayer();
	}.bind(this));
	
	// 속성 버튼
	this.targetElement.find(".layerFeature_btn").bind('click',function(e){
		this.getLayerFeature();
	}.bind(this));
	// 속성 조회 버튼

	// 속성 조회 버튼
	var _infoEvtId = null;
	this.targetElement.find(".layerInfo_btn").bind('click',function(e){
		var eTarget = $(e.target);
		if (!eTarget.hasClass('infoOn')) {
			eTarget.addClass('infoOn');
		} else {
			eTarget.removeClass('infoOn');
			odf.event.removeListener(_infoEvtId);
			
			_infoEvtId = odf.event.addListener(this.mapfn.map, 'click', function (evt) {
				var data = _subLayout.viewData;
				const options = {
						clickEvt: evt, // click 이벤트로 발생한 이벤트
						callback: function(result) { // 이벤트 성공시 실행시킬 함수
							console.log(result);							
						},
						odfId: data.layerObject.getODFId()
				};
				
				var layerInfo = new odf.LayerInfoControl(options);
				layerInfo.setMap(this.mapfn.map);
			}.bind(this));
		}
	}.bind(this));

	// 탭 정보 초기화
	this.init = function () {
		var data = _subLayout.viewData;
		if (!data) return ;
		this.targetElement.find("input[name=layerNm]").val(data.lyrNm);
		this.targetElement.find("input[name=service]").val(data.service);
	};
	
	// 레이어 이동
	this.moveToLayer = function() {
		var data = _subLayout.viewData;
		
		if (data.lyrId!==""&&!data.onOff) {
			alert("레이어를 활성화 해주세요.");
					return false;
		}
		data.layerObject.fit();
	};
	
	// 레이어 속성
	this.getLayerFeature = function() {
		var data = _subLayout.viewData;
		if (data && data.lyrId) {
			var res = this.mapfn.wfsRequest.getFeature(data.lyrId,data.wfsServerUrl);
			res.then(function(res) {
				console.log(res);
				alert(JSON.stringify(res));
			});
		}
	};
};


/*
 * 레이어 필터
 */
var LayerFilterTab = function(_subLayout) {
	
	this.targetElement = $(_subLayout.targetElement.find('#layerFilterTab')[0]);
	this.mapfn = _subLayout.mapfn;

	// 적용 버튼
	this.targetElement.find(".apply_btn").bind('click',function(e){
		this.apply();
	}.bind(this));
	
	// 초기화 버튼
	this.targetElement.find(".reset_btn").bind('click',function(e){
		var data = _subLayout.viewData;
		this.mapfn.filter.reset(data);
		this.reset();
	}.bind(this));

	//  필터 추가 버튼
	this.targetElement.find(".addFilter_btn").bind('click',function(e){
		this.add();
	}.bind(this));

	//  필터 추가 버튼
	this.targetElement.find(".removefilter_btn").bind('click',function(e){
		$(this).parent().remove();
	});
	

	// 값입력, 값선택 라디오 버튼
	this.targetElement.find("input[name=valueType]").bind('click',function(e){
		var $form = $(e.target).parent().parent();
		var $input = $form.find(".value").eq(0);
		var $select = $form.find(".value").eq(1);
		$select.html("");
		if (e.target.value == "inputType") {
			$input.show();
			$select.hide();
		} else {
			$input.hide();
			$select.show();
			var data = _subLayout.viewData;
			var typeName = data.lyrId;
			var key = $form.find(".key").val();
			this.uniqueValue(typeName, key, $select);
		}
	}.bind(this));
	

	// 필드 변경
	this.targetElement.find(".key").bind('change',function(e){
		var $form = $(this).parent().parent();
		$form.find("input[name=valueType]:checked").click();
	});
	
	// 탭 정보 초기화
	this.init = function () {
		this.reset();
		var data = _subLayout.viewData;
		if (!data) return ;
		var typeName = data.lyrId;
		var $key =this.targetElement.find('.tab_form select[name=key]').eq(0);
		this.fieldValue(typeName, $key).then(function(res) {
			this.parse();
		}.bind(this));
	};
	
	// 필드 정보
	this.fieldValue = function(typeName, $obj) {
		var data = _subLayout.viewData;
		var response = this.mapfn.wfsRequest.Describefeaturetype(typeName,data.wfsServerUrl);
		
		return response.then(function(res) {
			if (res && res.featureTypes && res.featureTypes[0].properties) {
				var properties = res.featureTypes[0].properties;
				var options = [];
				$.each(properties, function(i, item) {
					if (item.name == "the_geom") return ;
					options.push("<option data-type='" + item.type + "' value='"+item.name+"'>" + item.name + "</option>");
				});
				$obj.html(options.join(""));
			}
		});		
	};
	
	// 유니크 값 option
	this.uniqueValue = function(typeName, fieldName, $obj) {
		var data = _subLayout.viewData;
		var fieldInfo = data.layerObject.getAttributesRange(fieldName);
		
		if(fieldInfo ){
			var entries = Object.entries(fieldInfo.values);
			if(entries.length>0){
				var options = [];
				entries.forEach(function(e){
					options.push("<option value='"+e[0]+"'>" + e[0] + "</option>");
				});
				$obj.html(options.join(""));
			}
		}
	};
	
	// 필터 추가
	this.add = function() {
		var $form = this.targetElement.find('.tab_form'); 
		if ($form.length < 3) {
			var $filter =  this.targetElement.find('.tab_form:first').parent();
			
			$filter =$filter.clone();
			$filter.children().prepend("<input type='button' class='removefilter_btn' name='removefilter_btn' value='X'/>");
			$filter.find("input[name=valueType]").prop("checked", true);
			var filterArea =this.targetElement.find('#filter_area');
			filterArea.append("<div>" + $filter.html() + "<div>");
			filterArea.find("input[value=inputType]").last().click();	
		} else {
			alert("필터는 최대 3개까지 적용 가능 합니다.");
		}
	};
	
	// 필터 정보 구조
	this.format = function(key, sepator, value, where, type, valueType) {
		return $.extend({}, {key: key, sepator: sepator, value:  value, where: where, type: type, valueType: valueType});
	};
	
	// 필터 적용
	this.apply = function() {
		var data = _subLayout.viewData;
		if (!data.onOff) {
			alert("레이어를 활성화 해주세요.");
			return false;
		} 
		var filters = [];
		var $e =  this.targetElement;
		var $form = this.targetElement.find('.tab_form');
		$.each($form, function(i, item) {
			var $e = $(item);
			var where = "";
			if (i > 0) { // AND, OR 조건
				where =this.targetElement.find("input[name=where]:checked").val();
			}
			var type = $e.find("option:selected").attr('data-type');
			var key = $e.find(".key").val();
			var sepator = $e.find(".sepator").val();
			var valueType = $e.find("input[name=valueType]:checked").val();
			var value = (valueType == "inputType") ? $e.find("input[name=value]").val() :$e.find(".value option:selected").val();
			
			filters.push(this.format(key, sepator, value, where, type, valueType));
		}.bind(this)); 
		data.filterCndCn = filters;
		this.mapfn.filter.apply(data);
	}; 
	
	// 필터 초기화
	this.reset = function () {
		var $e = this.targetElement;
		$e.find("input[value=AND]").prop("checked", true);
		$e.find("input[value=inputType]").prop("checked", true);
		$e.find("input[name=key]").val("");
		$e.find("select[name=sepator]").val("=");
		$e.find("input[name=value]").val("").show();
		$e.find("select[name=value]").hide().html("");
		$e.find(".value").val("");
		$e.find(".tab_form").not("form:first").remove();
	};
	
	// 필터 정보 UI 파싱
	this.parse = function() {
		var data = _subLayout.viewData;
		if (data && data.filterCndCn) {
			$.each(data.filterCndCn, function(i, item){
				if (i > 0) this.add();	
			}.bind(this));
			$.each(data.filterCndCn, function(i, item){
				var $e = this.targetElement;
				if (item.where) {
					$e.find("input[value=" + item.where + "]").prop("checked", true);
				}
				var $input = $e.find("input[name=value]").eq(i);
				var $select = $e.find("select[name=value]").eq(i);
				$e.find(".key").eq(i).val(item.key);
				$e.find(".sepator").eq(i).val(item.sepator);
				$e.find("input[value="+item.valueType+"]").eq(i).prop("checked", true);
				if (item.valueType == "inputType") {
					$select.hide();
					$input.show()
					$input.val(item.value);
				} else {
					$input.hide();
					$select.show();
					uniqueValue(data.lyrId, item.key, $select).then(function() {
						$select.val(item.value);
					});	
				}
			}.bind(this));	
		}
	};
};




/*
 * @레이어 스타일
 * 
 */
var LayerStyleTab = function(_subLayout, _styleOptions) {
		
	this.targetElement = $(_subLayout.targetElement.find('#layerStyleTab')[0]);
	this.mapfn = _subLayout.mapfn;
	this.imageUrl = _styleOptions.imageUrl;

	this.targetElement.find(".colorBox").each(function(idx,box){
		
		$(box).html("");
		var colors = [
			['FF0000','FF5E00','FFBB00','FFE400','ABF200','1DDB16','00D8FF','0054FF','0100FF','5F00FF','000000','FFFFFF'],
			['F15F5F','F29661','F2CB61','E5D85C','BCE55C','86E57F','5CD1E5','6799FF','6B66FF','A566FF','A6A6A6','8C8C8C'],
			['CC3D3D','CC723D','CCA63D','C4B73B','9FC93C','47C83E','3DB7CC','4374D9','4641D9','8041D9','747474','5D5D5D'],
			['980000','993800','997000','998A00','6B9900','2F9D27','008299','003399','050099','3F0099','4C4C4C','353535']
		];
		colors.forEach(innerColors=>{
			var tr = '<tr>';
			innerColors.forEach(color=>{
				tr += '<td style="background-color:#'+color+'" class="'+color+'"></td>';
			})
			tr += '</tr>';
			$(box).append(tr);
		})
	});

	// 적용 버튼
	this.targetElement.find(".moreColor").ColorPicker({
		color : 'FF0000',
		onShow : function(colpkr){
			$(colpkr).fadeIn(500);
			$(colpkr).css('z-index', 99999);
			$(colpkr).css('width', 280);
			
			// 윤곽선 더보기인지, 채우기, 더보기인지
			parentDiv = $(this).parents('div.colorPickerBox');
			return false;
		},
		onChange : function(hsb, hex, rgb){
			var rgb_ = [];
			rgb_.push(rgb.r);
			rgb_.push(rgb.g);
			rgb_.push(rgb.b);
			var hexColor = "";
			$.each(rgb_, function(index, c){
				hexColor += parseInt(c).toString(16).length != 1 ? parseInt(c).toString(16) : "0" + parseInt(c).toString(16);
			});
	
			hexColor = hexColor.toUpperCase();
			var colorValue = parentDiv.find(".colorValue"); 
			colorValue.val(hexColor);
			colorValue.trigger("change");
		}.bind(this)
	});		
		
	// 적용
	this.targetElement.find("#colorApply_btn").bind('click',function(e){
		this.setColorStyle();
	}.bind(this));
	
	// 리셋
	this.targetElement.find("#colorReset_btn").bind('click',function(e){
		this.resetSymbols();
	}.bind(this));

	// 범위별 심볼 초기화
	this.targetElement.find("#uvReset_btn").bind('click',function(e){
		this.resetUv();
		this.resetStyle();
	}.bind(this));
	
	// 범위별 심볼 적용버튼
	this.targetElement.find("#cbApply_btn").bind('click',function(e){
		this.setCbStyle();
	}.bind(this));
	
	// 유형별 심볼 적용버튼
	this.targetElement.find("#uvApply_btn").bind('click',function(e){
		this.setUvStyle();
	}.bind(this));
	
	// 유형별 심볼 초기화
	this.targetElement.find("#cbReset_btn").bind('click',function(e){
		this.resetCb();
		this.resetStyle();
	}.bind(this));

	this.targetElement.find('#themeSelect').on("change", function() {
		this.imageList();
	 }.bind(this));
	
	// 아이콘 적용
	this.targetElement.find("#iconApply_btn").bind('click',function(e){
		this.setIconStyle();// 이미지 적용

		var checked = $('#customRadio').is(':checked');
		if (checked) {
			var result = this.getUserSymbol(); // 이미지 스타일 Json Format 으로 return
			alert(result.sysbolInfo);
		}
	}.bind(this));
	
	// 아이콘 리셋
	this.targetElement.find("#iconReset_btn").bind('click',function(e){
		this.resetSymbols();
		this.setIconStyle();// 이미지 적용
	}.bind(this));
	
	// 라벨스타일 적용
	this.targetElement.find("#labelApply_btn").bind('click',function(e){

		this.setLabelStyle();

	}.bind(this));
	
	// 라벨스타일 리셋
	this.targetElement.find("#labelReset_btn").bind('click',function(e){
		this.resetLabel();
	}.bind(this));
	
	// 색 box 클릭시
	this.targetElement.find("div.colorPickerBox table.colorBox td").bind('click',function(e){
		var color =  $(this).attr("class");
		var parentDiv = $(this).parents('div.colorPickerBox');
		var colorValue =parentDiv.find(".colorValue"); 
		colorValue.val(color);
		colorValue.trigger("change");
	});
	
	// 색상 값 변경됬을 경우
	this.targetElement.find("div.colorPickerBox  input.colorValue").bind('change',function(e){
		var color =  $(e.target).val();
		var parentDiv = $(e.target).parents('div.colorPickerBox');
		parentDiv.find(".colorView").css("background-color",this.hex2rgb(color,'css'));
	}.bind(this));
	
	// hex to rgb
	this.hex2rgb=function(hex,type){
		hex = parseInt(((hex.indexOf('#') > -1) ? hex.substring(1) : hex), 16);
		var rgb =[hex >> 16, (hex & 0x00FF00) >> 8,(hex & 0x0000FF)]; 
		
		if(type==='css'){
			return 'rgb('+rgb[0]+','+rgb[1]+','+rgb[2]+')';
		}
		return rgb;
	}
	
	// 유형별 심볼 채우기/윤곽선 선택시
	this.targetElement.find("input[type=radio][name=uvColor]").bind('click',function(e){
		var $e = this.targetElement;
		if($e.find("#uvFillColorRadio").is(':checked')){
			// 채우기 선택
			$e.find("#uvFillColorDiv").show();
			$e.find("#uvLineColorDiv").hide();
			var arr = _subLayout.viewData.layerObject.getAttributes(['int']);
			$e.find("#uvSelectField").html("<option value=''>선택</option>");
			for(var i=0; i<arr.length;i++){
				$e.find("#uvSelectField").append("<option value='"+arr[i].name+"'>"+arr[i].name+"</option>");
			}
			var keyArr = Object.keys(odf.ColorFactory.colorPallet);
			$e.find("#uvSelectColor").html("");
			for(var i=0; i<keyArr.length;i++){
				$e.find("#uvSelectColor").append("<option value='"+keyArr[i]+"'>"+odf.ColorFactory.colorPallet[keyArr[i]].name+"</option>");
			}
		}
		else{// 윤곽선 선택
			$e.find("#uvLineColorDiv").show();
			$e.find("#uvFillColorDiv").hide();
		}
	}.bind(this));
	

	this.targetElement.find("#uvSelectField, #uvSelectColor").bind('change',function(e){
		// 초기화
		var $e = this.targetElement;
		$e.find("#uvLegend tbody").html("");
		if($e.find("#uvSelectField").val()==="" ){
			return;
		}

		// 범례 탭 초기화
		var styleOption = this.createUvStyleOption();
		for(var i = 0 ; i<styleOption.legend.length;i++){
			var tr = document.createElement('tr');
			var td = document.createElement('td');
			td.appendChild(styleOption.legend[i].elem);
			tr.appendChild(td);
			td = document.createElement('td');
			td.innerHTML = styleOption.legend[i].styleOption.name;
			tr.appendChild(td);
			$e.find("#uvLegend tbody").append(tr);	
		}
	}.bind(this));
	
	// 범위별 심볼
	this.targetElement.find("#cbSelectLevel, #cbSelectField, #cbSelectColor").bind('change',function(e){
		var $e = this.targetElement;
		
		// 초기화
		$e.find("#cbLegend tbody").html("");
		if($e.find("#cbSelectField").val()==="" || $e.find("#cbSelectLevel").val()===""){
			return ;
		}
		
		// 범례 탭 초기화
		var styleOption = this.createCbStyleOption();
		
		
		var insertTarget =$e.find("#cbLegend tbody"); 
		insertTarget.html("");
		
		for(var i = 0 ; i<styleOption.legend.length;i++){
			var trHtml = "<tr><td>"+styleOption.legend[i].elem.outerHTML+"</td>";
			trHtml+='<td><input type="text" id="min-'+(i+1)+'" class="min" value = "'+styleOption.legend[i].min+'" disabled > ~ ';
			trHtml+='<input type="text" id="max-'+(i+1)+'" class="max" value = "'+styleOption.legend[i].max+'"'+(i==(styleOption.legend.length-1)?' disabled ':"")+'></td>';
			insertTarget.append(trHtml);
		}

		

		this.targetElement.find("input.max").bind('change',function(e){
			var eTarget = $(e.target);
			var len = eTarget.parents('#cbLegend tbody').find("tr").length;
			
			var reg = new RegExp(/^[0-9]+(.[0-9]*)?$/gi);
			if(!reg.test(eTarget.val())){
				eTarget.val(eTarget.val().replace(/[^0-9.]/gi,''));
			}else{
				var idx = Number(eTarget.attr("id").replace('max-',''));
				this.targetElement.find("#cbLegend input#min-"+(idx+1)).val(eTarget.val());	
			}
		}.bind(this));
		
		
	}.bind(this));
	// 범위별 심볼 채우기/윤곽선 선택
	this.targetElement.find("input[type=radio][name=cbColor]").bind('click',function(e){
		var $e = this.targetElement;
		if($e.find("#cbFillColorRadio").is(':checked')){
			// 채우기 선택
			$e.find("#cbFillColorDiv").show();
			$e.find("#cbLineColorDiv").hide();
			var arr = _subLayout.viewData.layerObject.getAttributes(['int','float']);
			$e.find("#cbSelectField").html("<option value=''>선택</option>");
			for(var i=0; i<arr.length;i++){
				$e.find("#cbSelectField").append("<option value='"+arr[i].name+"'>"+arr[i].name+"</option>");
			}
			var keyArr = Object.keys(odf.ColorFactory.colorPallet);
			$e.find("#cbSelectColor").html("");
			for(var i=0; i<keyArr.length;i++){
				$e.find("#cbSelectColor").append("<option value='"+keyArr[i]+"'>"+odf.ColorFactory.colorPallet[keyArr[i]].name+"</option>");
			}
		}
		else{// 윤곽선 선택
			$e.find("#cbLineColorDiv").show();
			$e.find("#cbFillColorDiv").hide();
		}
	}.bind(this));
	
	// 색상심볼 <-> 이미지심볼
	this.targetElement.find("input:radio[name=symbolSelect]").bind('click',function(e){
		
		var $e = this.targetElement;
		
		if ($e.find('input:radio[id=circleRadio]').is(':checked')) {
			// 색상설정 선택시
			$e.find('#circleDiv').show();
			$e.find('#imgSymbolDiv').hide();
			
			
			// 포인트 타입의 예외처리
			if(_subLayout.viewData.lyrTySeCode==='1'){
				$e.find("#pointRadiousArea").show();// 원 반지름 크기 설정 영역 활성화
				$e.find(".symbolSelect").show();// 원형, 이미지형 선택 영역 활성화
			}
			else{
				$e.find("#pointRadiousArea").hide();// 원 반지름 크기 설정 영역 비활성화
				$e.find(".symbolSelect").hide();// 원형, 이미지형 선택 영역 비활성화
			}
			
		} else if ($e.find('input:radio[id=iconRadio]').is(':checked')) {
			// 이미지 설정 선택시
			$e.find('#imgSymbolDiv').show();
			$e.find('#circleDiv').hide();
		} 
	}.bind(this));
	
	// 색상심볼 : 채우기 <-> 윤곽선
	this.targetElement.find("input:radio[name=simpleSymbolColor]").bind('click',function(e){
		var $e = this.targetElement;
		if($e.find('#lineColorRadio').is(':checked')){
			$e.find('#fillColorDiv').hide();
			$e.find('#lineColorDiv').show();
		}
		else if ($e.find('#fillColorRadio').is(':checked')) {
			$e.find('#lineColorDiv').hide();
			$e.find('#fillColorDiv').show();
		} 
	}.bind(this));
	

	// 이미지심볼 : 주제별 <-> 사용자
	this.targetElement.find("input:radio[name=iconSymbolRadio]").bind('click',function(e){
		var $e = this.targetElement;
		
		if ($e.find('input:radio[id=themeRadio]').is(':checked')) {
			$e.find('#customSymbolDiv').hide();
			$e.find('#themeSymbolDiv').show();
			$e.find('#themeSelect').show();
		} else if ($e.find('input:radio[id=customRadio]').is(':checked')) {
			$e.find('#customSymbolDiv').show();
			$e.find('#themeSymbolDiv').hide();
			$e.find('#themeSelect').hide();
		}
	}.bind(this));
	
	// 사용자 심볼 선택시 유효성 검사
	this.targetElement.find("#customFile").bind('change',function(e){
		var file = e.target;
		this.addApplyImage(file);
	}.bind(this));	
	
	// 타입 선택시 처리
	this.targetElement.find("#selectRenderer").bind('change',function(e){
		var $e = this.targetElement;
		
		// 점선면 타입에 따른 처리
		var lyrTySeCode = _subLayout.viewData.lyrTySeCode;// 1:점, 2:선,
		// show/hide처리
		var value = $(e.target).val();
		
		// 스타일 영역 초기화
		$e.find("#simpleArea").hide(); // 채우기, 윤곽선 설정 영역 hide
		$e.find("#classBreaksArea").hide(); // 범위별 심볼 설정 영역 hide
		$e.find("#uniqueValueArea").hide(); // 유형별 심볼 설정 영역 hide
		$e.find("#labelArea").hide(); // 텍스트 설정 영역 hide
		
		if(value==="simple"){
			
			// 단일 심볼일때 색상 설정,이미지 설정 가능케
			$e.find("#simpleArea").show(); // 채우기, 윤곽선 설정 영역 show
			$e.find('input:radio[id=circleRadio]').prop("checked", true).click();
			
			// 라인 타입일 경우, 채우기 선택 숨기기
			if(lyrTySeCode==='2'){
				$e.find("#fillColorRadio").hide();
				$e.find("#fillColorLb").hide();
				$e.find('#lineColorRadio').prop("checked", true).trigger("click");
			}
			else{
				$e.find("#fillColorRadio").show();
				$e.find("#fillColorLb").show();
				$e.find('#fillColorRadio').prop("checked", true).trigger("click");
			}
	
		}else if(value==="classBreaks"){
			// 범위별 심볼 선택시 처리
			$e.find("#classBreaksArea").show(); // 범위별 심볼 설정 영역 show
			// 기본 채우기 선택
			$e.find("#cbFillColorRadio").trigger("click");
			
		}else if(value==="uniqueValue"){
			// 유형별 선택시 처리
			$e.find("#uniqueValueArea").show(); // 유형별 심볼 설정 영역 show
			// 기본 채우기 선택
			$e.find("#uvFillColorRadio").trigger("click");
			
		}else if(value==="label"){
			// 라벨 선택시 처리
			$e.find("#labelArea").show(); // 텍스트 설정 영역 show
			$e.find("#cbLabelSelectField").html("");
			var data = _subLayout.viewData;
			var lyrId = data.lyrId;
			this.mapfn.wfsRequest.Describefeaturetype(data.lyrId,data.wfsServerUrl).then(function(res) {
				if (res && res.featureTypes && res.featureTypes[0].properties) {
					var properties = res.featureTypes[0].properties; 
					var options = [];
					$.each(properties, function(i, item) {
						if (item.name == "the_geom") return ;
						options.push("<option data-type='" + item.type + "' value='"+item.name+"'>" + item.name + "</option>");
					});
					$e.find("#cbLabelSelectField").html(options.join(""));
				}
			}.bind(this));
		}
	}.bind(this));
	
	this.init = function() {
		this.resetSymbols();
		this.getImages();
		this.resetLabel();
		this.resetCb();// 범위별 심볼 초기화
		this.resetUv();// 유형별 심볼 초기화
		this.geometryTypeSet();		
	};
	
	// 레이어 스타일 구분
	this.geometryTypeSet = function(type) {
		
		var $e = this.targetElement;
		// 점선면 공통
		$e.find(".symbolSelect").hide(); // 색상설정/이미지 설정 선택 영역 hide
		$e.find("#selectRenderer").html("");
		$e.find("#selectRenderer").append('<option value="simple" selected="selected">단일심볼</option>');
		
		// 점선면 타입에 따른 처리 1:점, 2:선,3: 면
		var lyrTySeCode = _subLayout.viewData.lyrTySeCode;
		 
		// 점 타입일때만 범위별/유형별 심볼 선택 가능
		if(lyrTySeCode==='1') {
			$e.find("#selectRenderer").append('<option value="classBreaks">범위별 심볼</option>');	
			$e.find("#selectRenderer").append('<option value="uniqueValue">유형별 심볼</option>');
			$e.find(".symbolSelect").show(); // 색상설정/이미지 설정 선택 영역 show
		}
		$e.find("#selectRenderer").append('<option value="label">라벨</option>');
		$e.find("#selectRenderer").trigger('change');
	}
	
	// 초기화
	this.resetSymbols = function () {
		
		var $e = this.targetElement;
		// 모든 값 디폴트로 세팅
		$e.find('#pointRadius').val('10');
		$e.find('#lineColor').val('00000');
		$e.find('#lineColorCheckBox').val([0,0,0,1]);
		$e.find('#lineColorCheckBox').css('background-color','#000000');
		$e.find('#lineColorOpacityInputBox').val('1');
		$e.find('#fillColor').val('00000');
		$e.find('#fillColorCheckBox').val([0,0,0,1]);
		$e.find('#fillColorCheckBox').css('background-color','#000000');
		$e.find('#fillColorOpacityInputBox').val('1');
		$e.find('#lineThickInputBox').val('1');
		$e.find('#imageViewer').val(this.imageUrl+'/images/smileIcon2.png');
		$e.find('#imageViewer').attr('src',this.imageUrl+'/images/smileIcon2.png');
		$e.find('#imgSize').val('1');
		$e.find('#imgOpacity').val('1');
	};
	
	// 유형별 심볼 초기화
	this.resetUv = function(){

		var $e = this.targetElement;
		
		// 채우기
		$e.find("#uvFillOpacity").val(1);// 투명도
		$e.find("#uvSelectField").val("");// 필드
		$e.find("#uvSelectColor").val("");// 색상
		$e.find("#uvCircleRadius").val("10")// 크기
		$e.find("#uvLegend tbody").html("");// 범례 초기화
		
		// 윤곽선
		$e.find("#uvLineOpacity").val(1)// 투명도
		$e.find("#uvLineColorDiv .colorValue").val("000000");// 색상
		$e.find("#uvLineColorDiv .colorValue").trigger("change");// 색상 변경 트리거 호출
		$e.find("#uvLineThick").val(1);// 선두께
	};

	this.createUvStyleOption=function(elemSize){
		if(!elemSize){
			elemSize = 15;
		}
		var $e = this.targetElement;
		
		// 필드, 색상, 단계 값이 모두 선택되었을때,
		var field = $e.find("#uvSelectField").val();
		var colorType = $e.find("#uvSelectColor").val();
		var fillOpacity =Number($e.find("#uvFillOpacity").val());
		// 크기
		var radius =$e.find("#uvCircleRadius").val();
		// 라인옵션
		var lineOpacity =Number( $e.find("#uvLineOpacity").val());
		var linehex =$e.find("#uvLineColorDiv .colorValue").val();
		var lineColor = this.hex2rgb(linehex);
		lineColor[3] = lineOpacity;
		var lineThick = Number($e.find("#uvLineThick").val());
		
		
		// 값의 종류 조회
        var range = _subLayout.viewData.layerObject.getAttributesFeaturesValueRange(field);
        
		var rangeKey = Object.keys(range.values);
        var level =rangeKey.length;
        
        // 색 생성
		var colorList = odf.ColorFactory.produce(
			colorType // 색 계열 'red', 'blue', 'yellow', 'green', 'purple',
						// 'brown', 'black', 'random' 중 하나
			,(level>50?50:level) // 1~50 사이의 정수
			,fillOpacity// 투명도
			,false
        );
		
		// level이 50 넘을때 처리
		if(level>50){
			var newColorList = [];
			for(var i = 0 ; i < level; i++){
				newColorList.push(colorList[i%50]);
			}
			colorList = newColorList;
		}
		var originalStyle = _subLayout.viewData.layerObject.getStyle().getObject()[0];
		var originalText = originalStyle.style.text;
		var originalCallback = originalStyle.callbackFunc;
		
		var reObject = {};
		reObject.legend = [];
		reObject.dynamicStyleOption = [];
		
		for(var idx=0; idx<level; idx++ ){
			
			var obj = {};
			obj.styleOption=  {
				image : {
					circle : {
						 fill:{color:colorList[idx].slice()},// 채우기
				         stroke: {
			        	 	color:lineColor
			        	 	,width:lineThick,
				        },// 윤곽선
				         radius : radius
					}
				},
				text : originalText?originalText:{},
				name : rangeKey[idx]
			};
			obj.elem =  odf.StyleFactory.produceElement(obj.styleOption, elemSize,true);
			reObject.legend.push(obj);
	    	
			reObject.dynamicStyleOption.push({
	    		seperatorFunc :"function(feature,resolution){var val = feature.getProperties()['"+field+"'];return val=='"+rangeKey[idx]+"';}",
	    		style : obj.styleOption,
	    		callbackFunc : originalCallback
	    	});
		}
		
		return reObject;
	}

	
	// 범위별 심볼 초기화
	this.resetCb = function(){
		
		var $e = this.targetElement;
		
		// 채우기
		$e.find("#cbFillOpacity").val(1);// 투명도
		$e.find("#cbSelectField").val("");// 필드
		$e.find("#cbSelectColor").val("");// 색상
		$e.find("#cbSelectLevel").val("");// 단계
		$e.find("#cbCircleRadius").val("10")// 크기
		$e.find("#cbLegend tbody").html("");// 범례 초기화
		
		// 윤곽선
		$e.find("#uvLineOpacity").val(1)// 투명도
		$e.find("#cbLineColorDiv .colorValue").val("000000");// 색상
		$e.find("#cbLineColorDiv .colorValue").trigger("change");// 색상 변경 트리거 호출
		$e.find("#cbLineThick").val(1);// 선두께
		
	};

	this.createCbStyleOption=function(autoCalculateRangeFlag ,elemSize){
		
		var $e = this.targetElement;
		
		if(autoCalculateRangeFlag===undefined){
			autoCalculateRangeFlag =  true;
		}
		// 필드, 색상, 단계 값이 모두 선택되었을때,
		var field = $e.find("#cbSelectField").val();
		var level =Number($e.find("#cbSelectLevel").val());
		var colorType = $e.find("#cbSelectColor").val();
		var fillOpacity =Number($e.find("#cbFillOpacity").val());
		// 크기
		var radius =$e.find("#cbCircleRadius").val();
		// 라인옵션
		var lineOpacity =Number($e.find("#cbLineOpacity").val());
		var linehex =$e.find("#cbLineColorDiv .colorValue").val();
		var lineColor = this.hex2rgb(linehex);
		lineColor[3] = lineOpacity;
		var lineThick = Number($e.find("#cbLineThick").val());
		
		// 값의 종류 조회
        var range = _subLayout.viewData.layerObject.getAttributesFeaturesValueRange(field);
        
        // 사용자 입력 범례 값 체크
        var customRange = {};
        if(!autoCalculateRangeFlag){
        	var trArr = $e.find("#cbLegendArea tbody tr");
        	var tMin = Number($e.find("#cbLegendArea input#min-1").val());
        	var tMax = Number($e.find("#cbLegendArea input#max-"+trArr.length).val());
        	for(var i=0;i<trArr.length;i++){

        		var idx = Number(trArr[i].querySelector('.min').id.replace('min-',''))-1;
        		var min = Number(trArr[i].querySelector('.min').value);
        		var max = Number(trArr[i].querySelector('.max').value);
        		
        		if(max >= tMin && max<=tMax){
            		customRange[idx] ={
            			min : min,
            			max : max,
            			rangeText : min+" ~ "+max
            		};
        		}
        		else{
        			alert('범위를 다시 확인하세요.');
        			return null;
        		}
        	}
        }
        
        // 색 생성
		var colorList = odf.ColorFactory.produce(
			colorType // 색 계열 'red', 'blue', 'yellow', 'green', 'purple',
						// 'brown', 'black', 'random' 중 하나
			,level // 1~50 사이의 정수
			,fillOpacity// 투명도
			,false
        );

		var originalStyle = _subLayout.viewData.layerObject.getStyle().getObject()[0];
		var originalText = originalStyle.style.text; 
		var originalCallback = originalStyle.callbackFunc;
		
		var reObject = {};
		reObject.legend = [];
		reObject.dynamicStyleOption = [];
		var gap = (range.max-range.min)/(level);
		
		for(var idx=0; idx<level; idx++ ){
			// 계산된 값
			var rangeMin =  (gap*idx).toFixed(5);
			var rangeMax = (gap*(idx+1)).toFixed(5);
			// 사용자 정의 값 적용
	        if(!autoCalculateRangeFlag){
	        	rangeMin = customRange[idx].min;
	        	rangeMax = customRange[idx].max;
	        }
	        
			var obj = {};
			obj.styleOption=  {
				image : {
					circle : {
						fill:{color:colorList[idx].slice()},// 채우기
				        stroke: {
			        	 	color : lineColor,
			        	 	width : lineThick
				        },// 윤곽선
				        radius : radius
					}
				},name : rangeMin+"~"+rangeMax,
				text : originalText?originalText:{},
		
			};
			obj.min =  rangeMin;
			obj.max =  rangeMax;
			if(!elemSize){
				elemSize =15;
			} 
			obj.elem =  odf.StyleFactory.produceElement(obj.styleOption, elemSize,true);
			reObject.legend.push(obj);
	    	
			reObject.dynamicStyleOption.push({
	    		seperatorFunc :"function(feature,resolution){var val = feature.getProperties()['"+field+"'];return val>="+rangeMin+" && val<"+rangeMax+";}",
	    		style : obj.styleOption,
	    		callbackFunc : originalCallback
	    	});
		}
		
		return reObject;
	}
	
	// 기본스타일 셋팅
	this.resetStyle = function(){
		var data = _subLayout.viewData;
		
		var style =  this.mapfn.style.produceFunction([{
			seperatorFunc:"default",
			style :  this.mapfn.style.getDefaultStyle(data)
		}]);
		data.layerObject.setStyle(style);
		data.style = style.getJSON();
	};
	
	
	// 라벨스타일초기화
	this.resetLabel = function () {

		var $e = this.targetElement;
		
		// 모든 값 디폴트로 세팅
		$e.find('#labelFontSize').val(10);
		$e.find('#labelFont').find('option:first').attr('selected', 'selected');
		$e.find("#cbLabelSelectField").find('option:first').attr('selected', 'selected');
		$e.find('#bold').prop('checked', false);
		$e.find('#oblique').prop('checked', false);
		$e.find('#stroke').prop('checked', false);
		$e.find('#midcenter').prop('checked', true);
		$e.find('#labelColor').val('000000');
		$e.find('#labelColorCheckBox').val([0,0,0]);
		$e.find('#labelColorCheckBox').css('background-color','#000000');
	};

	
	this.setWFSLayerIconStyle = function(id,style){

		
		var layerElems = _subLayout.targetElement[0].parentElement.querySelectorAll('#toc li[role=presentation]');
		var layerElem;
		
		for(var i = 0 ; i<layerElems.length;i++){
			var _layerElem = layerElems[i];
			if(_layerElem.id==id){
				layerElem = _layerElem;
			}
		}
		layerElem.querySelector('.icon').remove();
		var targetElement = layerElem.querySelector('.layerPd');
		
		targetElement.innerHTML = this.mapfn.getStyleElemText(style)+targetElement.innerHTML;
	}
	
	// 범위별 심볼 적용
	this.setCbStyle = function(){

		var $e = this.targetElement;
		
		var field = $e.find("#cbSelectField").val();
		var level = ""+$e.find("#cbSelectLevel").val();
		if(field===""){
			alert("필드를 선택하세요.");
			return;
		}
		if(level===""){
			alert("단계를 선택하세요.");
			return;
		}
		
		var data = _subLayout.viewData;
		if (!data.onOff) {
			alert("레이어를 활성화 해주세요.");
			return false;
		}
		var styleOption = this.createCbStyleOption(false);
		if(styleOption){
			this.apply(odf.StyleFactory.produceFunction(styleOption.dynamicStyleOption));
			_subLayout.legendTab.legend();	
		}
	}
	
	// 유형별 심볼 적용
	this.setUvStyle = function(){

		var $e = this.targetElement;
		
		var field = $e.find("#uvSelectField").val();
		if(field===""){
			alert("필드를 선택하세요.");
			return;
		}

		var styleOption = this.createUvStyleOption();
		
		var data = _subLayout.viewData;
		if (!data.onOff) {
			alert("레이어를 활성화 해주세요.");
			return false;
		}
		this.apply(odf.StyleFactory.produceFunction(styleOption.dynamicStyleOption));
		_subLayout.legendTab.legend();	
	}
	
	// 색상스타일 적용
	this.setColorStyle = function() {
		var $e = this.targetElement;
		
		$e.find("#lineColorCheckBox").val().pop();
		$e.find("#lineColorCheckBox").val().push($e.find('#lineColorOpacityInputBox').val());
		$e.find("#fillColorCheckBox").val().pop();
		$e.find("#fillColorCheckBox").val().push($e.find('#fillColorOpacityInputBox').val());
		$e.find('#legendStyle').css('background-color', '#' + $e.find('#fillColor').val());
		$e.find('#legendStyle').css('border', '1px solid' + '#' + $e.find('#lineColor').val());

		var data = _subLayout.viewData;
		if (!data.onOff) {
			alert("레이어를 활성화 해주세요.");
			return false;
		} 
		
		var style = null;
		var fn = this.mapfn.style;
		var fillColor = this.hex2rgb($('#fillColor').val());
		var lineColor = this.hex2rgb($('#lineColor').val());
		fillColor[3]=Number($("#fillColorOpacityInputBox").val());
		lineColor[3]=Number($("#lineColorOpacityInputBox").val());
		
		var lyrTySeCode = data.lyrTySeCode;
		if (lyrTySeCode == "1") {
			style = {
				image : {
					circle : {
						fill:{ color: fillColor },
						stroke : { color: lineColor, width: $e.find('#lineThickInputBox').val() },
						radius: $e.find('#pointRadius').val() 		
					}
				}
			};
			
		} else if(lyrTySeCode == "2") {
			style = {
				stroke : { color: lineColor, width: $e.find('#lineThickInputBox').val() }
			};
			
		} else if(lyrTySeCode == "3") {
			style = {
				fill:{ color: fillColor },
				stroke : { color: lineColor, width: $e.find('#lineThickInputBox').val() }					
			};
		}
		
		var originalStyle = data.layerObject.getStyle().getObject()[0];
		if(originalStyle.style.hasOwnProperty("text")){
			style.text = originalStyle.style.text;
		}

		this.apply(fn.produceFunction(style));
		_subLayout.legendTab.legend();
	};
	
	this.getUserSymbol = function() {
		var img = this.targetElement.find('#imageViewer');
		var reData = null;
		
		if (!img[0].src.startsWith("data")) {
			var image = new Image();
			var canvas = document.createElement('canvas');
			image.src = img[0].src;
			canvas.getContext('2d').drawImage(image, 0, 0);
			var reData = canvas.toDataURL('image/png');
		} else {
			reData = img[0].src
		}

		return {
			sysbolInfo : reData
		};
	}
	
	// 이미지 심볼 적용
	this.setIconStyle = function() {
		var $e = this.targetElement;
		$e.find('#legendStyle').css('background-color', '');
		$e.find('#legendStyle').css('border', '');
		$e.find('#legendStyle').attr('src', $e.find('#imageViewer').val());
		
		var data =_subLayout.viewData;
		var fn = this.mapfn.style;
		if (data && data.lyrTySeCode) {
			var lyrTySeCode = data.lyrTySeCode;
			if (lyrTySeCode == "1") {
				style = {
						image : {
							icon : {
								opacity: $e.find('#imgOpacity').val(),
								scale : $e.find('#imgSize').val(),
								src : $e.find('#imageViewer').val()
							}
						}
					};
				
			} else {
				alert("레이어 타입이 점형이 아닙니다.");
			}
			if(data.layerObject.getStyle().getObject()[0].style.hasOwnProperty("text")){
				style.text = data.layerObject.getStyle().getObject()[0].style.text;
			}
		}
		this.apply(fn.produceFunction(style));
		_subLayout.legendTab.legend();
	};
	
	// 라벨 스타일 적용
	this.setLabelStyle = function() {
		var $e = this.targetElement;
		var data = _subLayout.viewData;
		if (!data.onOff) {
			alert("레이어를 활성화 해주세요.");
			return false;
		}
		
		var style = null;
		var originalStyle = data.layerObject.getStyle().getObject();
		if (data && data.lyrTySeCode) {
			data.styleField = $e.find('#cbLabelSelectField option:selected').val();
			data.labelResolution = Number($e.find("#resolution").val());
			
			var boldOption = "normal"
			if($e.find("input:checkbox[id='bold']").is(":checked")) {
				boldOption = $e.find('#bold').val();
			}
			var obliqueOption = ""
			if($e.find("input:checkbox[id='oblique']").is(":checked")) {
				obliqueOption = $e.find('#oblique').val();
			}
			
			var offsetArr = $e.find("input:radio[name=align]:checked")[0].id.split('-');

			labelStyle = {
				offsetX :  (offsetArr[0]==="0"?'-20':(offsetArr[0]==="1"?'0':'20')),
				offsetY : (offsetArr[1]==="0"?'-20':(offsetArr[1]==="1"?'0':'20')),
				font : boldOption +' '+ obliqueOption +' '+ $e.find('#labelFontSize').val() + 'px ' + $e.find('#labelFont option:selected').val(),
				fill : {
					color : this.hex2rgb($e.find('#labelColor').val())  
				},
				overflow : true
			};
			
			if($e.find("input:checkbox[id='stroke']").is(":checked")) {
				labelStyle.stroke = {color : 'white', width : 5};
			}
			
			for(var i=0; i<originalStyle.length;i++){
				originalStyle[i].style.text = labelStyle;
			}
		}
		
		var fn = this.mapfn.style;
		this.apply(fn.produceFunction(originalStyle));
		_subLayout.legendTab.legend();
	};
	
	// 스타일 적용
	this.apply = function(style) {
		
		var data = _subLayout.viewData;
		if (data && data.layerObject) {
			var odfId = data.odfId;
			var layer = this.mapfn.findLayer(odfId);
			if (layer.type != "VECTOR") {
				alert("서비스 타입이 WMS 이면 스타일 기능을 활용 할 수 없습니다.");
				return ;
			}
			data.layerObject.setStyle(style);
			data.symbolCndCn = style.getJSON();
			data.labelOnoff = false;
			this.mapfn.style.label(data);
			data.labelOnoff = !data.labelOnoff; 
			

			this.setWFSLayerIconStyle(_subLayout.viewData.id,style);
			
		} else {
			alert("해당 레이어는 비활성화 상태 입니다.");
		}
	};
	
	// 이미지경로 base64로 인코딩 후 이미지 미리보기에 삽입
	this.base64 = function (file) {
		var $e = this.targetElement;
		var fileUpload = new FileReader();
		fileUpload.onload=function(){
			var result = fileUpload.result
			$e.find('#imageViewer').attr('src', result);
			$e.find('#imageViewer').val(result);
		};
		fileUpload.readAsDataURL(file);
	};
	
	this.imageList = function() {
		
		var $e = this.targetElement;
		var themeSelect = $e.find('#themeSelect');
		$.ajax({
			url : "/samplehtml/layerControl/json/" + themeSelect.val(),
			type : 'GET',
			success : function(data) {
				var imgTag = "";

				data.forEach(function(itm, idx) {
					if(itm.url != undefined) {
						var url = this.imageUrl+"/"+itm.url;
						imgTag += '<img src=' +url+ ' style="width:30px; height:30px;">';
					}
				}.bind(this));
				$e.find('#themeIconsDiv').html(imgTag);

				
				$e.find('#themeIconsDiv img').click(function(evt){
					$e.find('#imageViewer').val($(evt.target).attr('src'));
					$e.find('#imageViewer').show();
					$e.find('#imageViewer').attr('src', $e.find('#imageViewer').val());// 선택한 이미지 미리보기
				});
 
				$e.find('#themeSelect').on("change", function() {
					this.imageList();
				 }.bind(this));
			}.bind(this),
			error : function(xhr, status, err) {
				console.log(xhr, status, err);
			}
		});
	};	
	
	this.getThemeSysbolList = function(data) {
		var $e = this.targetElement;
		var selectEle = $e.find('#themeSelect');
		data.theme.forEach(function(itm, idx) {
			var optEle = document.createElement('option');	
			optEle.setAttribute('id', 'theme-'+ idx);
			optEle.setAttribute('value', itm.url);
			optEle.innerText = itm.korNm;
			selectEle.append(optEle);
		});
		this.imageList();		
	};		

	this.getImages = function(){
		
		$.ajax({
			url :this.imageUrl + "/samplehtml/layerControl/json/themeSysbol.json",
			type : 'GET',
			success : function(data) {
				
				this.getThemeSysbolList(data);
			}.bind(this),
			error : function(xhr, status, err) {
				console.log(xhr, status, err);
			}.bind(this)
		});
	};
	
	
	this.addApplyImage = (file) => {
		
		var $e = this.targetElement;
		
		if ($(file).val() != ''){
			// 1.확장자 검사
			var ext = $(file).val().split(".").pop().toLowerCase();
			if ($.inArray(ext,["png","jpg","jpeg","PNG","JPG","JPEG" ]) == -1) {
				// PNG,JPG, JPEG 파일을 선택해주세요.
				alert('PNG,JPG, JPEG 파일을 선택해주세요.');
				$e.find('#customFile').val("");
			}else{
				
				// 2.가로세로 길이 체크
				var _file = file.files[0];
				var _URL = window.URL || window.webkitURL;
				var img = new Image();
				img.src = _URL.createObjectURL(_file);
				img.onload = function() {
					if(img.width > 120 || img.height > 120 || img.width < 0 || img.height < 0) {
						// 이미지 크기는 120 * 120 픽셀 이하만 가능합니다.
						alert('이미지 크기는 120 * 120 픽셀 이하만 가능합니다.');
						$e.find('#customFile').val("");
					}else{
						
						// 3.사용자 심볼 base64로 전송
						 this.base64(_file);
					}
				}.bind(this)
			}
		}
		
	} 
};

/*
 * @레이어 범례
 */
var LayerLegendTab = function(_subLayout) {


	this.targetElement = $(_subLayout.targetElement.find('#layerLegendTab')[0]);
	this.mapfn = _subLayout.mapfn;

	
	this.legend = function() {
		var data = _subLayout.viewData;
		if (data) {
			
			var legendArea =  this.targetElement.find('#legendArea');
			legendArea.html("");
			var style = JSON.parse((data.symbolCndCn) ? 
				data.symbolCndCn : this.mapfn.style.produceFunction(this.mapfn.style.getDefaultStyle(data)).getJSON());
			for (var i=0; i<style.length; i++ ) {
				var item = $.extend({}, style[i].style);
				var tr  =  document.createElement('tr');
				var td  =  document.createElement('td');
		    	var styleElem = odf.StyleFactory.produceElement(item, 15, true);
		    	styleElem.style.float ='left';
		    	td.appendChild(styleElem);
		    	tr.appendChild(td);
		    	td = document.createElement('td');
		    	if(style[i].style.name){
		    		td.innerHTML = style[i].style.name;
		    	}
		    	tr.appendChild(td);
		    	legendArea.append(tr);
			}
		}
	};
	
	// 탭 정보 초기화
	this.init = function () {
		this.legend();
	};
};

/*
 * @지도 기능
 */
var MapFn = function(_map){
	
	this.map = _map;
	
	// 레이어 삭제
	this.removeLayer = function(id) {
		if (!this.map) return ;
		if(id){
			var layer = this.map.findLayer(id);
			if (layer) {
				this.map.removeLayer(id);
			}
		}
	};
	
	// 레이어 추가
	this.addLayer = function(data) {

		if (!this.map && !odf && !data.lyrId) return ;
		// 레이어 객체 생성
		var layer = odf.LayerFactory.produce(
			 "geoserver", {
		    method : 'get',
			server : data.serverUrl,
			layer : data.lyrId,
			service: data.service || "wms", 
			matrixSet : data.matrixSet,
			projection : data.projection,
			tileOrigin : data.tileOrigin,
			tileResolutions : data.tileResolutions,
			tileMatrixIds : data.tileMatrixIds,
		});
		layer.setODFId(layer.getODFId()+data.id);
		
		
		// 필터 적용
		if (data.filterCndCn) {
			this.filter.apply(data);				
		}
		// 스타일 적용
		if (data.service == "wfs") {
			var style = data.symbolCndCn || this.style.getDefaultStyle(data);
			style = this.style.produceFunction(style);
			layer.setStyle(style);
			data.symbolCndCn = style.getJSON();
			var arr = data.symbolCndCn.match(/(?<=var styleField=\')(.*?)(?=\';)/g);
			if (arr && arr.length) {
				data.styleField = arr[0];
				data.labelOnoff = true;
			}
		}
		layer.setMap(this.map);
		
		if(!data.onOff){
			this.setVisible(layer.getODFId(),false)
		}
		data['odfId'] = layer.getODFId();
		data['layerObject'] = layer;
	};
	// 레이어 찾기
	this.findLayer = function(id) {
		if (!this.map) return ;
		var layers = this.map.getLayers().getArray();
		for (var i=0; i<layers.length; i++) {
			var layer = layers[i];
			if (layer.odfId == id) {
				return layer;
			}
		}
	};
	// 레이어 onoff
	this.setVisible = function(id, onoff) {
		if (!this.map) return ;
		this.map.switchLayer(id, onoff);
	};
	
	// 레이어 순서
	this.setZIndex = function(id, order) {
		var layer = this.findLayer(id);
		if (!layer) return ;
		layer.setZIndex(order);
	};
	
	// 레이어 스타일
	this.style = {
		produce : function(style) {
			if (!odf) return ;
			return odf.StyleFactory.produce(style);
		},
		// 레이어 스타일
		produceFunction : function(style) {
			if (!odf) return ;
			var newStyle = [];
			var format = { seperatorFunc : "default", style : {}, callbackFunc : function() {} }; 
			if (style) {
				if (!Array.isArray(style)) {
					style = [style];
				}
				for (var i=0; i< style.length; i++) {
					var item = style[i];
					if (!item.hasOwnProperty("seperatorFunc") && 
						!item.hasOwnProperty("callbackFunc") && 
						!item.hasOwnProperty("style")) {
						// 해당 속성이 없으면 스타일 속성으로 간주한다
						item = {style: item};
					}
					newStyle.push($.extend({}, format, item));
				}
			}
			return odf.StyleFactory.produceFunction(newStyle);
		},
		// 점, 선, 명 기본 스타일
		getDefaultStyle : function(data) {
			var style = null;
			if (data && data.lyrTySeCode) {
				var lyrTySeCode = data.lyrTySeCode;
				if (lyrTySeCode == "1") {
//					style ={image : {icon : {src:"../images/Symbols/PeoplePlaces/City.png"}}};
//					style ={image : {icon : {src:"../images/Symbols/PeoplePlaces/shop.png"}}};
					style = { image : {circle : {radius : 10, fill : {color : "#FFE400"}, stroke: {color : "#FFE400",width : 1}}} };
				} else if (lyrTySeCode == "2") {
					style = { stroke : { color: 'black', width: '1px'} };
				} else if (lyrTySeCode == "3") {
					style = { fill : { color: "white" }, stroke : { color: 'black', width: '1px'} };
				}
			}
			return style;
		},
		// 라벨 스타일
		label : function(data) {
			var defaultText = { text :{text : undefined, fill: {color:"black"}, font : 'bold 12px Courier New'} };
			var layer = data.layerObject;
			var labelOnoff = data.labelOnoff;
			var styleField = data.styleField;
			var labelResolution = data.labelResolution;
			if (layer && layer.getStyle()) {
				var styleObj = layer.getStyle().getObject();
				for (var i=0; i<styleObj.length; i++) {
					var item = styleObj[i];
					var fnBody = "";
					if (!labelOnoff) {
						if (labelResolution) {
							fnBody += "if(resolution<"+labelResolution+"){";
						}
						if (styleField) {
							fnBody += "var styleField='"+styleField+"';";
							fnBody += "style.getText().setText(''+feature.get(styleField));";
						}
						if (labelResolution) {
							fnBody += "} else {style.getText().setText('')}";
						}
					}
					item.callbackFunc = new Function("style, feature, resolution", fnBody);
					item.style = (item.style) ? item.style : defaultText;
				}
				
				var newStyle = this.produceFunction(styleObj);
				layer.setStyle(newStyle);
				data.symbolCndCn = newStyle.getJSON();
			}
		}
	};
	
	// 레이어 필터
	this.filter = {
		apply : function(data) {
			if (data && data.odfId && data.filterCndCn) {
				var querys = [];
				var filter = data.filterCndCn;
				for (var i=0; i<filter.length; i++) {
					var item = filter[i];
					// 필터 입력 정보가 부족하면 알림
					if (!item.key || !item.value || !item.sepator || !item.type) {
						alert("필터 검색 정보가 정확히 입력 되지 않았습니다.");
						return ;
					}
					// 컬럼 타입이 int 형인데 like 검색이면 알림
					if (item.sepator == "LIKE" && item.type != "xsd:string") {
						alert("문자형이 아닌 핉드 타입은 '다음을 포함' 조건을 사용할 수 없습니다.");
						return ;
					}
					// 쿼리문 생성
					var query = "";
					if (i > 0) {
						query += " " + item.where;
					}
					query += " \""+ item.key + "\" " + item.sepator;
					if (item.sepator == "LIKE" && item.type == "xsd:string") {
						query += " '%" + item.value + "%'";
					} else if (item.type == "xsd:string") {
						query += " '" + item.value + "'";
					} else {
						query += " " + item.value;
					}
					querys.push(query);
				};
				
				data.layerObject.defineQuery({
					odfId : data.odfId,
					condition : querys.join(" ")
				});
			}
		},	
		reset: function(data) {
			if (data && data.odfId) {
				data.layerObject.defineQuery({odfId : data.odfId});	
			}
		}
	};
	
	// wfs 요청
	this.wfsRequest ={
		// 스키마 정보
		Describefeaturetype : function(typeName,_wfsServerUrl) {
			if ( _wfsServerUrl&& typeName) {
				var url = _wfsServerUrl+ "?request=Describefeaturetype&service=wfs&version=1.0.0&outputformat=application/json&typeName="+typeName;
				return fetch(url, {
					method : "GET"
				}).then(function(response) {
				
					return response.json();
				}).then(function(res) {
					return res;
				});
			}
		},
		// 속성 정보
		getFeature : function(typeName,_wfsServerUrl) {
			if (_wfsServerUrl && typeName) {
				var url = _wfsServerUrl + "?request=getFeature&service=wfs&version=1.0.0&outputformat=application/json&typeName="+typeName;
				return fetch(url, {
					method : "GET"
				}).then(function(response) {
					
					return response.json();
				}).then(function(res) {
					return res;
				});
			}
		}
	};
	

	this.getStyleElemText = function(style){

		let styleObject = style.getObject();
		var styleElemArray =[];
		if(typeof style ==='function'){
			for(var i=0; i<styleObject.length;i++){
				styleElemArray.push(odf.StyleFactory.produceElement(styleObject[i].style, 25, false));
			}		
		}
		else{
			
		}
		
		//elem만들기
		var _getIconElem = function(elem,type,position){
			
			if(type){
				elem.classList.add('inner_'+type+'_icon');	
			}else{
				elem.classList.add('icon');	
			}

			if(type && position){
				elem.classList.add(position);	
			}
			
			elem.innerHTML = "";
			
			return elem.outerHTML.replace("<div","<span").replace("</div>","</span>");
		}
		
		//레이어 아이콘 바꿔치기
		var styleLen = styleElemArray.length;
		if(styleLen===1){
			return _getIconElem(styleElemArray[0]);
		}else if (styleLen>=2 && styleLen<4){
			var icon = '<div class="icon">';
			var elem1 =styleElemArray[0];
			icon +=_getIconElem(elem1,'half','left');
			var elem2 =styleElemArray[1];
			icon +=_getIconElem(elem2,'half','right');
			
			icon+='</div>';
			
			return icon;
		}else if (styleLen>=4){

			var gab = styleLen/4;
			
			var icon = '<div class="icon">';
			var elem1 =styleElemArray[(gab*0).toFixed()];
			icon +=_getIconElem(elem1,'quad','left_top');
			var elem2 =styleElemArray[(gab*1).toFixed()];
			icon +=_getIconElem(elem2,'quad','right_top');
			var elem4 =styleElemArray[(gab*3).toFixed()];
			icon +=_getIconElem(elem4,'quad','left_bottom');
			var elem3 =styleElemArray[(gab*2).toFixed()];
			icon +=_getIconElem(elem3,'quad','right_bottom');
			
			icon+='</div>';
			return icon;
		}
		
	}
}
