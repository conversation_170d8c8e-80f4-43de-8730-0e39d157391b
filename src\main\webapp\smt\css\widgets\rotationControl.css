@charset "UTF-8";
.rotationControl_rotateBtn .rotationControl_rotateSpan {
	background-image: url("../../images/widget/widget-rotate-gray.png");
	height: 34px;
    background-repeat: no-repeat;
    background-position: center;
    width: 34px;
    padding-top: 0px;
    display: block;
    margin-top: 18px;
    margin-left: 8px;
}
.rotationControl_rotateBtn .rotationControl_rotateSpan:hover {
	background-image: url("../../images/widget/widget-rotate-white.png");
}