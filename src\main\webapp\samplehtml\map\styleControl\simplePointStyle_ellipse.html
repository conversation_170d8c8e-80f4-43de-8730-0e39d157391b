<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
</head>
<body>
	<div id="map" class="odf-view" style="height:550px;"></div>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::,::coordy::);
	var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);

	/*포인트 레이어 추가*/
	var pointLayer = odf.LayerFactory.produce('geoserver', {
		method : 'get',
		server : '::WfsAPI::',
		layer :  '::pointLayer::', // 발행된 레이어 명칭 (ex. 저장소명:레이어명)
		crtfckey : '::crtfckey::',
		service : 'wfs',
	});
	pointLayer.setMap(map);
	pointLayer.fit();

	/*타원 스타일 생성*/
	var eclipseStyle = odf.StyleFactory.produce({
		image : {
			circle : {
				radius : 25,//크기
				fill : {
					color : [ 255, 255, 255, 0.2 ]
				//채우기 색
				},//채우기
				stroke : {//윤곽선
					color : [ 225, 100, 100, 0.95 ],//테두리 색
					width : 2,//굵기
				//lineDash:[4, 1]//점선 설정
				},
				//축척
				//scale :1,
				//snapToPixel : true //true : sharp, false : blur
				//타원 스타일
				//원래 크기의 n배 [가로  축척, 세로 축척]
				scale : [1, 0.2],
				//회전
				rotation : (Math.PI*30/180),
				//지도 회전시 같이 회전할지 여부
				rotateWithView : true,
			}
		},
	});
	pointLayer.setStyle(eclipseStyle);
</script>
</html>
