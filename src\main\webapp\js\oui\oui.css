/*!
 App version: oui_webpack 1.0.12
 Branch: master
 Commit ID: d04c3a5f
 Build Time: 2025. 4. 4. 오전 9:13:16
 */
 @charset "UTF-8";.modal_modal {
  background-color: #e8e8e8;
  display: none;
  position: absolute;
  z-index: 99999999;
  height: auto !important;

}

.modal_modal .modal_cont {
  /* background: #fff; */
}

.modal_popup .modal_cont .modal_inner {
  /* max-height: 650px;
  padding: 30px; */
}

.modal_modal.modal_open {
  display: block;
}

.modal_modal-box-backgCtrl {
  width: 100%;
  height: 100%;
  background-color: slategray;
  z-index: 99999998;
  position: absolute;
  top: 0;
  opacity: 0.6;
}

.modal_modal-header {
  /* background-color: cornflowerblue; */
}

.modal_modal .modal_head.modal_blue {
  background: #5779FF;
}

.modal_modal .modal_head {
  background: #5779FF;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal_close {
  align-items: center;
  margin-right: 13px;
  width: 20px;
  height: 20px;
  background: url(./images/widget/popup/ico-pop-close.png) no-repeat center;
  font-family: inherit;
  font-size: inherit;
  display: inline-block;
  border: none;
  background-color: transparent;
  cursor: pointer;
}

.modal_close span {
  display: none;
}

.modal_content {
  background: #fff;
}

.modal_content>div {
  padding: 30px;
}

.modal_head>span {
  margin-left: 20px;
  line-height: 50px;
  color: #fff;
  font-size: 20px;
  font-family: '맑은 고딕';
  font-weight: normal;
}






.modal_modal .modal_head .modal_titPop {
  /* margin-left: 20px;
  line-height: 50px;
  color: #fff;
  font-size: 20px;
  font-family: '맑은 고딕';
  font-weight: normal; */
}

.modal_modal .modal_head .modal_btnGroup .modal_btnPopClose {
  /* width: 30px;
  height: 30px;
  background: url(../../../css/images/popup/ico-pop-close.png) no-repeat center; */
}.toc_hidden {
    display: none;
    margin: 0;
    padding: 0;
    width: 0;
    height: 0;
    overflow: hidden;
    font-size: 0;
    line-height: 0;
    visibility: hidden;
}

.toc_addGroupBox {
    font-size: 15px;
    font-family: '맑은 고딕';
}

.toc_addGroupBox table {
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
}

.toc_addGroupBox table tbody tr th,
.toc_addGroupBox table tbody tr td {
    font-size: 16px;
}

.toc_addGroupBox table th,
.toc_addGroupBox table td {
    height: 40px;
    color: #555;
    border-bottom: 1px solid #e9e9e9;
    border-top: 1px solid #e9e9e9;
    padding: 8px;
}

.toc_addGroupBox table th {
    background: #f9f9f9;
    font-weight: bold;
    width: 200px;
}

.toc_groupInput {
    width: 100%;
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    padding-left: 15px;
    height: 40px;
    border-radius: 4px;
    border: 1px solid #e9e9e9;
    color: #555;
    font-size: 16px;
    font-weight: normal;
    resize: none;
}

.toc_btnArea {
    margin-top: 20px;
    justify-content: flex-end;
    display: flex;
}

.toc_btnAddGroup {
    margin-left: 0;
    margin-right: 0;
    padding: 0 30px;
    background: #436aeb;
    height: 40px;
    display: flex;
    align-items: center;
    border-radius: 4px;
    box-sizing: border-box;
    font-weight: normal;
    font-size: 16px;
    color: #fff;
    vertical-align: top;
    letter-spacing: -1px;
    cursor: pointer;
    border: 0;
}

.toc_btnChangeTitle {}

.toc_btnGroupNameChangeBox {}

.toc_editBox {}

.toc_expanded {
    width: 16px;
    position: relative;
    right: 18px;
    top: 1px;
}

.toc_expandedBoxOpen {
    background: url(./images/widget/toc/ico-group-show.png) no-repeat;
    cursor: pointer;
}

.toc_expandedBoxClosed {
    background: url(./images/widget/toc/ico-group-hide.png) no-repeat;
    cursor: pointer;
}

.toc_expandedBoxHide {
    display: none;
    cursor: all-scroll;
}


/* .editBox{
    display : none;
}
.hidden{
    display : none;
} */
.toc_btnUpdateTitle {
    margin-left: 5x;
    height: 23px;
    font-size: 14px;
    padding: 0 10px;
    border-radius: 50px;
    margin-right: 5px;
    background-color: #333;
    color: #fff;
}

.toc_btnCancelTitle {
    height: 23px;
    font-size: 14px;
    padding: 0 10px;
    border-radius: 50px;
    background: #e1e1e1;
    color: #333;
}

.toc_toc {
    overflow-y: auto;
    overflow-x: hidden;
    max-height: calc(100% - 50px);
}

.toc_tocTool {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.toc_parent {}

.toc_btnGroup {
    display: flex;
}

.toc_btnTocAdd {
    background: url(./images/widget/toc/ico-toctool-add.png) no-repeat left center;
}

.toc_btnTocSearch {
    background: url(./images/widget/toc/ico-toctool-search.png) no-repeat left center;
}

.toc_btnTocUpload {
    margin-right: 0;
    background: url(./images/widget/toc/ico-toctool-upload.png) no-repeat left center;
}

.toc_tocTool .toc_btnGroup:last-of-type button {
    width: 27px;
    height: 25px;
    margin: 0;
}

.toc_btnHeaderFunc {}

.toc_btnAllView {
    background-image: url(./images/widget/toc/ico-toc-all-view-hide.png);
}

.toc_btnAllView:hover {
    background-image: url(./images/widget/toc/ico-toc-all-view-show.png);
}

.toc_btnAllRemove {
    background-image: url(./images/widget/toc/ico-toc-all-remove-hide.png);
}

.toc_btnAllRemove:hover {
    background-image: url(./images/widget/toc/ico-toc-all-remove-show.png);
}

.toc_layerContent .toc_btnLayerView {
    background-image: url(./images/widget/toc/ico-view.png);
    background-position: center;
}

.toc_btnAllView.toc_off {
    margin-right: 2px;
    background-image: url(./images/widget/toc/ico-toc-all-view-hide.png);
}

.toc_groupContent .toc_btnLayerView {
    background-image: url(./images/widget/toc/ico-group-view-hide.png);
    background-position: center;
}

.toc_tocHeaderGrp {
    color: white;
}


.toc_btnLayerView {}

.toc_btnLayerGrid {}

.toc_btnLayerDetail {}

.toc_titleBox {}

.toc_contentBox {}

.toc_contentBox.toc_children {}

.toc_tocContentBox {}

.toc_tocContentList {}

.toc_btnLabelView {}

.toc_layerContent {}

.toc_groupContent {}

.toc_layerText {}

.toc_btnHeaderApp {
    margin-right: 15px;
    text-indent: 10px;
    color: #436aeb;
    font-size: 16px;
    font-family: 'Pretendard Bold';
}

.toc_btnChangeTitle.toc_layer {
    background-image: url(./images/widget/toc/ico-edit.png);
}

.toc_btnChangeTitle.toc_layer:hover {
    background-image: url(./images/widget/toc/ico-edit-hover.png);
}

.toc_btnWebLayerUpdate {
    background-image: url(./images/widget/toc/ico-weblayer-edit.png);
}

.toc_btnWebLayerUpdate:hover {
    background-image: url(./images/widget/toc/ico-weblayer-edit-hover.png);
}

.toc_btnChangeTitle.toc_group {
    background-image: url(./images/widget/toc/ico-group-edit.png);
}

.toc_btnChangeTitle.toc_group:hover {
    background-image: url(./images/widget/toc/ico-group-edit-hover.png);
}

.toc_btnLayerDelete {}

.toc_btnLabelView {
    background-image: url(./images/widget/toc/ico-bookmark-hover.png);
}

.toc_btnLayerGrid {
    background-image: url(./images/widget/toc/ico-option.png);
    background-position: center;
}

.toc_btnLayerGrid.toc_on {
    background: url(./images/widget/toc/ico-option-hover.png) no-repeat;
    cursor: pointer;
}

.toc_layerContent .toc_btnLayerDelete {
    background-image: url(./images/widget/toc/ico-remove.png);
    background-position: center;
}

.toc_groupContent .toc_btnLayerDelete {
    background-image: url(./images/widget/toc/ico-group-remove-hide.png);
    background-position: center;
}

.toc_btnLayerDetail {
    margin-right: 0;
    background-image: url(./images/widget/toc/ico-layer-more.png);
    background-position: center;
}

.toc_attributePopup {
    background: url(./images/widget/toc/ico-map-info.png) no-repeat;
    cursor: pointer;
    background-size: 20px;
}

.toc_attributePopup.toc_on {
    background: url(./images/widget/toc/ico-map-info-on.png) no-repeat;
    background-size: 20px;
    cursor: pointer;
}

.toc_legend {
    background: url(./images/widget/toc/ico-toc-legend.png) no-repeat;
    cursor: pointer;
    background-size: 20px;
}

.toc_legend.toc_on {
    background: url(./images/widget/toc/ico-toc-legend-hover.png) no-repeat;
    background-size: 20px;
    cursor: pointer;
}

.toc_groupAddForm {
    padding: 30px;
}

.toc_validBox {
    margin-top: 5px;
    height: 3px;
}

.toc_editBox input[type=text] {
    height: 25px;
    width: 153px;
    padding: 5px;
}

.toc_errorTxt {
    display: block;
    font-size: 10px;
    color: red;
}

.toc_disable {
    color: #aaaaaa;
}
.conditionFilter_queryDiv {}

.conditionFilter_filterListDiv {
    max-height: 560px;
    /*overflow-y: auto;
    overflow-x: hidden;*/
    overflow: auto;
}

.conditionFilter_filterListDiv::-webkit-scrollbar {
    width: 14px;
    height: 14px;
}

.conditionFilter_filterListDiv::-webkit-scrollbar-button {
    display: none;
}

.conditionFilter_filterListDiv::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 5px;
}

.conditionFilter_filterListDiv::-webkit-scrollbar-track {
    background-color: #e9e9e9;
}

.conditionFilter_saveBtn {
    position: relative;
    display: flex;
    align-items: center;
    height: 40px;
    padding: 0 15px;
    border-radius: 4px;
    box-sizing: border-box;
    font-family: '맑은 고딕';
    font-weight: normal;
    font-size: 16px;
    color: #fff;
    vertical-align: top;
    letter-spacing: -1px;
    transition: .4s;
    background: #436aeb;
    padding: 0 30px;
    cursor: pointer;
    margin-right: 5px;
}

.conditionFilter_saveBtnSpan {}

.conditionFilter_conditionDiv {
    position: relative;
    border: 1px solid #e1e1e1;
    padding: 20px 10px;
    border-radius: 4px;
    margin-bottom: 5px;
}

.conditionFilter_queryListSpan {
    font-size: 18px;
    font-family: '맑은 고딕';
}

.conditionFilter_columnNmSpan {
    width: 95px;
    line-height: 28px;
    margin-right: 5px;
    text-align: center;
    letter-spacing: -1.2px;
    color: #333;
    font-size: 16px;
    font-family: '맑은 고딕';
    font-weight: bold;
}

.conditionFilter_columnSelectBox {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    padding-left: 15px;
    height: 40px;
    border-radius: 4px;
    border: 1px solid #e9e9e9;
    color: #555;
    font-size: 16px;
    font-family: '맑은 고딕';
    font-weight: normal;
    resize: none;
    width: 260px;
    margin-left: 5px;
    margin-bottom: 10px;
}

.conditionFilter_columnOption {}

.conditionFilter_cqlSpan {
    width: 95px;
    line-height: 28px;
    margin-right: 5px;
    text-align: center;
    letter-spacing: -1.2px;
    color: #333;
    font-size: 16px;
    font-family: '맑은 고딕';
    font-weight: bold;
}

.conditionFilter_valueSpan {
    width: 95px;
    line-height: 28px;
    margin-right: 5px;
    text-align: center;
    letter-spacing: -1.2px;
    color: #333;
    font-size: 16px;
    font-family: '맑은 고딕';
    font-weight: bold;
}

.conditionFilter_uniqueSpan {
    width: 95px;
    line-height: 28px;
    margin-right: 5px;
    text-align: center;
    letter-spacing: -1.2px;
    color: #333;
    font-size: 16px;
    font-family: '맑은 고딕';
    font-weight: bold;
}

.conditionFilter_valueInput {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    padding-left: 15px;
    height: 40px;
    border-radius: 4px;
    border: 1px solid #e9e9e9;
    color: #555;
    font-size: 16px;
    font-family: '맑은 고딕';
    font-weight: normal;
    resize: none;
    width: 260px;
    margin-left: 5px;
}

.conditionFilter_queryDiv {}

.conditionFilter_queryListDiv {
    margin-top: 20px;
}

.conditionFilter_operationSelecBox {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    padding-left: 15px;
    height: 40px;
    border-radius: 4px;
    border: 1px solid #e9e9e9;
    color: #555;
    font-size: 16px;
    font-family: '맑은 고딕';
    font-weight: normal;
    resize: none;
    width: 260px;
    margin-left: 5px;
    margin-bottom: 10px;
}

.conditionFilter_valueSelectBox {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    padding-left: 15px;
    height: 40px;
    border-radius: 4px;
    border: 1px solid #e9e9e9;
    color: #555;
    font-size: 16px;
    font-family: '맑은 고딕';
    font-weight: normal;
    resize: none;
    width: 260px;
    margin-left: 5px;
    margin-bottom: 10px;
}

.conditionFilter_valueDatetime {}

.conditionFilter_valueDate {}

.conditionFilter_valueDate_Input {
    width: 260px;
}

.conditionFilter_valueDate_Input.conditionFilter_table {
    width: 220px;
}

.conditionFilter_operationOption {}

.conditionFilter_title {}

.conditionFilter_logicSpan {
    width: 95px;
    line-height: 28px;
    margin-right: 5px;
    text-align: center;
    letter-spacing: -1.2px;
    color: #333;
    font-size: 16px;
    font-family: '맑은 고딕';
    font-weight: bold;
}

.conditionFilter_hidden {
    display: none;
    margin: 0;
    padding: 0;
    width: 0;
    height: 0;
    overflow: hidden;
    font-size: 0;
    line-height: 0;
    visibility: hidden;
}

.conditionFilter_logicSelectBox {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    padding-left: 15px;
    height: 40px;
    border-radius: 4px;
    border: 1px solid #e9e9e9;
    color: #555;
    font-size: 16px;
    font-family: '맑은 고딕';
    font-weight: normal;
    resize: none;
    width: 260px;
    margin-left: 5px;
    margin-bottom: 10px;
}

.conditionFilter_logicOption {}

.conditionFilter_valueOption {}

.conditionFilter_footerDiv {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
}

.conditionFilter_applyBtn {
    position: relative;
    display: flex;
    align-items: center;
    height: 40px;
    padding: 0 15px;
    border-radius: 4px;
    box-sizing: border-box;
    font-family: '맑은 고딕';
    font-weight: normal;
    font-size: 16px;
    color: #fff;
    vertical-align: top;
    letter-spacing: -1px;
    transition: .4s;
    background: #436aeb;
    padding: 0 30px;
    cursor: pointer;
    margin-right: 5px;
}

.conditionFilter_addBtn {
    position: relative;
    display: flex;
    align-items: center;
    height: 40px;
    padding: 0 15px;
    border-radius: 4px;
    box-sizing: border-box;
    font-family: '맑은 고딕';
    font-weight: normal;
    font-size: 16px;
    color: #fff;
    vertical-align: top;
    letter-spacing: -1px;
    transition: .4s;
    background: #555;
    padding: 0 30px;
    cursor: pointer
}

.conditionFilter_deleteBtn {
    position: absolute;
    right: 0px;
    top: 0px;
    width: 30px;
    height: 30px;
    border: 1px solid #e1e1e1;
    background: #fff url(./images/widget/popup/ico-card-close.png) no-repeat center;
}

.conditionFilter_applyBtnSpan {}

.conditionFilter_addBtnSpan {}

.conditionFilter_deleteBtnSpan {
    display: none;
}

/* 테이블 테마*/
.conditionFilter_addBtnSpan.conditionFilter_table {}

.conditionFilter_logicDiv.conditionFilter_table {
    display: flex;
    justify-content: space-between;
}

.conditionFilter_addBtn.conditionFilter_table {
    position: relative;
    display: flex;
    align-items: center;
    height: 40px;
    padding: 0 15px;
    border-radius: 4px;
    box-sizing: border-box;
    font-family: "Pretendard";
    font-weight: normal;
    font-size: 14px;
    color: #fff;
    vertical-align: top;
    letter-spacing: 0px;
    transition: .4s;
    background-color: #436aeb;
}

.conditionFilter_firstColHeader.conditionFilter_table {
    width: 200px;
}

.conditionFilter_secondColHeader.conditionFilter_table {
    width: 200px;
}

.conditionFilter_thirdColHeader.conditionFilter_table {
    width: 220px;
}

.conditionFilter_fourthColHeader.conditionFilter_table {
    width: 65px;
}

.conditionFilter_fifthColHeader.conditionFilter_table {
    width: 40px;
}

.conditionFilter_filterListDiv table th,
.conditionFilter_filterListDiv table td {
    height: 20px;
    color: #555;
    border-bottom: 1px solid #e9e9e9;
    padding: 8px;
}

.conditionFilter_filterListDiv table tbody tr td {
    color: #686868;
    background: #fff;
    font-size: 15px;
}

.conditionFilter_filterListDiv table thead tr:first-child th {
    position: sticky;
    top: 0;
    z-index: 10;
    border-bottom: 1px solid #cbcbcb;
}

.conditionFilter_filterListDiv table thead tr th {
    font-size: 16px;
}

.conditionFilter_filterListDiv table th {
    background: #f9f9f9;
    font-family: 'Pretendard Bold';
}

.conditionFilter_deleteBtn.conditionFilter_table {
    background: #fff url(./images/widget/common/ico-in-remove.png) no-repeat center;
    position: relative;
    width: 40px;
    padding: 0;
    border: 1px solid #e9e9e9;
}

.conditionFilter_columnSelectBox.conditionFilter_table {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    padding-left: 15px;
    padding-right: 30px;
    height: 40px;
    border-radius: 4px;
    border: 1px solid #e9e9e9;
    color: #555;
    font-size: 16px;
    font-family: '맑은 고딕';
    font-weight: normal;
    resize: none;
    width: 200px;
    margin-left: 5px;
}

.conditionFilter_valueInput.conditionFilter_table {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    padding-left: 15px;
    height: 40px;
    border-radius: 4px;
    border: 1px solid #e9e9e9;
    color: #555;
    font-size: 16px;
    font-family: '맑은 고딕';
    font-weight: normal;
    resize: none;
    width: 220px;
    margin-left: 5px;
}

.conditionFilter_operationSelecBox.conditionFilter_table {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    padding-left: 15px;
    padding-right: 30px;
    height: 40px;
    border-radius: 4px;
    border: 1px solid #e9e9e9;
    color: #555;
    font-size: 16px;
    font-family: '맑은 고딕';
    font-weight: normal;
    resize: none;
    width: 200px;
    margin-left: 5px;

}

.conditionFilter_valueSelectBox.conditionFilter_table {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    padding-left: 15px;
    padding-right: 30px;
    height: 40px;
    border-radius: 4px;
    border: 1px solid #e9e9e9;
    color: #555;
    font-size: 16px;
    font-family: '맑은 고딕';
    font-weight: normal;
    resize: none;
    width: 220px;
    margin-left: 5px;
}

.conditionFilter_tableTr {}

.conditionFilter_applyBtn.conditionFilter_table {
    background-color: #ff7200;
}

.conditionFilter_applyBtn.conditionFilter_table:hover {
    background-color: #ff5700;
}

.conditionFilter_footerDiv.conditionFilter_table {
    justify-content: flex-end;
}

.conditionFilter_uniqueCheck {
    text-align: center;
}

/* 박스 테마*/
.conditionFilter_applyBtn.conditionFilter_box {}

/* 230216 위젯 예제 개선 */
.conditionFilter_tableTd {
    text-align: center;
}

.conditionFilter_conditionFilterContent {
    border: 1px solid #e1e1e1;
    border-radius: 4px;
    padding: 20px 10px;
    overflow: auto;
}

.conditionFilter_headerFalseTable {
    width: 100%;
}

.conditionFilter_headerTrueTable {
    width: 100%;
}
.react-datepicker__year-read-view--down-arrow,
.react-datepicker__month-read-view--down-arrow,
.react-datepicker__month-year-read-view--down-arrow, .react-datepicker__navigation-icon::before {
  border-color: #ccc;
  border-style: solid;
  border-width: 3px 3px 0 0;
  content: "";
  display: block;
  height: 9px;
  position: absolute;
  top: 6px;
  width: 9px;
}
.react-datepicker-wrapper {
  display: inline-block;
  padding: 0;
  border: 0;
}

.react-datepicker {
  font-family: "Helvetica Neue", helvetica, arial, sans-serif;
  font-size: 0.8rem;
  background-color: #fff;
  color: #000;
  border: 1px solid #aeaeae;
  border-radius: 0.3rem;
  display: inline-block;
  position: relative;
  line-height: initial;
}

.react-datepicker--time-only .react-datepicker__time-container {
  border-left: 0;
}
.react-datepicker--time-only .react-datepicker__time,
.react-datepicker--time-only .react-datepicker__time-box {
  border-bottom-left-radius: 0.3rem;
  border-bottom-right-radius: 0.3rem;
}

.react-datepicker-popper {
  z-index: 1;
  line-height: 0;
}
.react-datepicker-popper .react-datepicker__triangle {
  stroke: #aeaeae;
}
.react-datepicker-popper[data-placement^=bottom] .react-datepicker__triangle {
  fill: #f0f0f0;
  color: #f0f0f0;
}
.react-datepicker-popper[data-placement^=top] .react-datepicker__triangle {
  fill: #fff;
  color: #fff;
}

.react-datepicker__header {
  text-align: center;
  background-color: #f0f0f0;
  border-bottom: 1px solid #aeaeae;
  border-top-left-radius: 0.3rem;
  padding: 8px 0;
  position: relative;
}
.react-datepicker__header--time {
  padding-bottom: 8px;
  padding-left: 5px;
  padding-right: 5px;
}
.react-datepicker__header--time:not(.react-datepicker__header--time--only) {
  border-top-left-radius: 0;
}
.react-datepicker__header:not(.react-datepicker__header--has-time-select) {
  border-top-right-radius: 0.3rem;
}

.react-datepicker__year-dropdown-container--select,
.react-datepicker__month-dropdown-container--select,
.react-datepicker__month-year-dropdown-container--select,
.react-datepicker__year-dropdown-container--scroll,
.react-datepicker__month-dropdown-container--scroll,
.react-datepicker__month-year-dropdown-container--scroll {
  display: inline-block;
  margin: 0 15px;
}

.react-datepicker__current-month,
.react-datepicker-time__header,
.react-datepicker-year-header {
  margin-top: 0;
  color: #000;
  font-weight: bold;
  font-size: 0.944rem;
}

h2.react-datepicker__current-month {
  padding: 0;
  margin: 0;
}

.react-datepicker-time__header {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.react-datepicker__navigation {
  align-items: center;
  background: none;
  display: flex;
  justify-content: center;
  text-align: center;
  cursor: pointer;
  position: absolute;
  top: 2px;
  padding: 0;
  border: none;
  z-index: 1;
  height: 32px;
  width: 32px;
  text-indent: -999em;
  overflow: hidden;
}
.react-datepicker__navigation--previous {
  left: 2px;
}
.react-datepicker__navigation--next {
  right: 2px;
}
.react-datepicker__navigation--next--with-time:not(.react-datepicker__navigation--next--with-today-button) {
  right: 85px;
}
.react-datepicker__navigation--years {
  position: relative;
  top: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}
.react-datepicker__navigation--years-previous {
  top: 4px;
}
.react-datepicker__navigation--years-upcoming {
  top: -4px;
}
.react-datepicker__navigation:hover *::before {
  border-color: rgb(165.75, 165.75, 165.75);
}

.react-datepicker__navigation-icon {
  position: relative;
  top: -1px;
  font-size: 20px;
  width: 0;
}
.react-datepicker__navigation-icon--next {
  left: -2px;
}
.react-datepicker__navigation-icon--next::before {
  transform: rotate(45deg);
  left: -7px;
}
.react-datepicker__navigation-icon--previous {
  right: -2px;
}
.react-datepicker__navigation-icon--previous::before {
  transform: rotate(225deg);
  right: -7px;
}

.react-datepicker__month-container {
  float: left;
}

.react-datepicker__year {
  margin: 0.4rem;
  text-align: center;
}
.react-datepicker__year-wrapper {
  display: flex;
  flex-wrap: wrap;
  max-width: 180px;
}
.react-datepicker__year .react-datepicker__year-text {
  display: inline-block;
  width: 4rem;
  margin: 2px;
}

.react-datepicker__month {
  margin: 0.4rem;
  text-align: center;
}
.react-datepicker__month .react-datepicker__month-text,
.react-datepicker__month .react-datepicker__quarter-text {
  display: inline-block;
  width: 4rem;
  margin: 2px;
}

.react-datepicker__input-time-container {
  clear: both;
  width: 100%;
  float: left;
  margin: 5px 0 10px 15px;
  text-align: left;
}
.react-datepicker__input-time-container .react-datepicker-time__caption {
  display: inline-block;
}
.react-datepicker__input-time-container .react-datepicker-time__input-container {
  display: inline-block;
}
.react-datepicker__input-time-container .react-datepicker-time__input-container .react-datepicker-time__input {
  display: inline-block;
  margin-left: 10px;
}
.react-datepicker__input-time-container .react-datepicker-time__input-container .react-datepicker-time__input input {
  width: auto;
}
.react-datepicker__input-time-container .react-datepicker-time__input-container .react-datepicker-time__input input[type=time]::-webkit-inner-spin-button,
.react-datepicker__input-time-container .react-datepicker-time__input-container .react-datepicker-time__input input[type=time]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
.react-datepicker__input-time-container .react-datepicker-time__input-container .react-datepicker-time__input input[type=time] {
  -moz-appearance: textfield;
}
.react-datepicker__input-time-container .react-datepicker-time__input-container .react-datepicker-time__delimiter {
  margin-left: 5px;
  display: inline-block;
}

.react-datepicker__time-container {
  float: right;
  border-left: 1px solid #aeaeae;
  width: 85px;
}
.react-datepicker__time-container--with-today-button {
  display: inline;
  border: 1px solid #aeaeae;
  border-radius: 0.3rem;
  position: absolute;
  right: -87px;
  top: 0;
}
.react-datepicker__time-container .react-datepicker__time {
  position: relative;
  background: white;
  border-bottom-right-radius: 0.3rem;
}
.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box {
  width: 85px;
  overflow-x: hidden;
  margin: 0 auto;
  text-align: center;
  border-bottom-right-radius: 0.3rem;
}
.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list {
  list-style: none;
  margin: 0;
  height: calc(195px + 1.7rem / 2);
  overflow-y: scroll;
  padding-right: 0;
  padding-left: 0;
  width: 100%;
  box-sizing: content-box;
}
.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item {
  height: 30px;
  padding: 5px 10px;
  white-space: nowrap;
}
.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item:hover {
  cursor: pointer;
  background-color: #f0f0f0;
}
.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item--selected {
  background-color: #216ba5;
  color: white;
  font-weight: bold;
}
.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item--selected:hover {
  background-color: #216ba5;
}
.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item--disabled {
  color: #ccc;
}
.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item--disabled:hover {
  cursor: default;
  background-color: transparent;
}

.react-datepicker__week-number {
  color: #ccc;
  display: inline-block;
  width: 1.7rem;
  line-height: 1.7rem;
  text-align: center;
  margin: 0.166rem;
}
.react-datepicker__week-number.react-datepicker__week-number--clickable {
  cursor: pointer;
}
.react-datepicker__week-number.react-datepicker__week-number--clickable:not(.react-datepicker__week-number--selected,
.react-datepicker__week-number--keyboard-selected):hover {
  border-radius: 0.3rem;
  background-color: #f0f0f0;
}
.react-datepicker__week-number--selected {
  border-radius: 0.3rem;
  background-color: #216ba5;
  color: #fff;
}
.react-datepicker__week-number--selected:hover {
  background-color: rgb(28.75, 93.2196969697, 143.75);
}
.react-datepicker__week-number--keyboard-selected {
  border-radius: 0.3rem;
  background-color: rgb(41.5, 134.5606060606, 207.5);
  color: #fff;
}
.react-datepicker__week-number--keyboard-selected:hover {
  background-color: rgb(28.75, 93.2196969697, 143.75);
}

.react-datepicker__day-names {
  white-space: nowrap;
  margin-bottom: -8px;
}

.react-datepicker__week {
  white-space: nowrap;
}

.react-datepicker__day-name,
.react-datepicker__day,
.react-datepicker__time-name {
  color: #000;
  display: inline-block;
  width: 1.7rem;
  line-height: 1.7rem;
  text-align: center;
  margin: 0.166rem;
}

.react-datepicker__day,
.react-datepicker__month-text,
.react-datepicker__quarter-text,
.react-datepicker__year-text {
  cursor: pointer;
}
.react-datepicker__day:not([aria-disabled=true]):hover,
.react-datepicker__month-text:not([aria-disabled=true]):hover,
.react-datepicker__quarter-text:not([aria-disabled=true]):hover,
.react-datepicker__year-text:not([aria-disabled=true]):hover {
  border-radius: 0.3rem;
  background-color: #f0f0f0;
}
.react-datepicker__day--today,
.react-datepicker__month-text--today,
.react-datepicker__quarter-text--today,
.react-datepicker__year-text--today {
  font-weight: bold;
}
.react-datepicker__day--highlighted,
.react-datepicker__month-text--highlighted,
.react-datepicker__quarter-text--highlighted,
.react-datepicker__year-text--highlighted {
  border-radius: 0.3rem;
  background-color: #3dcc4a;
  color: #fff;
}
.react-datepicker__day--highlighted:not([aria-disabled=true]):hover,
.react-datepicker__month-text--highlighted:not([aria-disabled=true]):hover,
.react-datepicker__quarter-text--highlighted:not([aria-disabled=true]):hover,
.react-datepicker__year-text--highlighted:not([aria-disabled=true]):hover {
  background-color: rgb(49.8551020408, 189.6448979592, 62.5632653061);
}
.react-datepicker__day--highlighted-custom-1,
.react-datepicker__month-text--highlighted-custom-1,
.react-datepicker__quarter-text--highlighted-custom-1,
.react-datepicker__year-text--highlighted-custom-1 {
  color: magenta;
}
.react-datepicker__day--highlighted-custom-2,
.react-datepicker__month-text--highlighted-custom-2,
.react-datepicker__quarter-text--highlighted-custom-2,
.react-datepicker__year-text--highlighted-custom-2 {
  color: green;
}
.react-datepicker__day--holidays,
.react-datepicker__month-text--holidays,
.react-datepicker__quarter-text--holidays,
.react-datepicker__year-text--holidays {
  position: relative;
  border-radius: 0.3rem;
  background-color: #ff6803;
  color: #fff;
}
.react-datepicker__day--holidays .overlay,
.react-datepicker__month-text--holidays .overlay,
.react-datepicker__quarter-text--holidays .overlay,
.react-datepicker__year-text--holidays .overlay {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: #333;
  color: #fff;
  padding: 4px;
  border-radius: 4px;
  white-space: nowrap;
  visibility: hidden;
  opacity: 0;
  transition: visibility 0s, opacity 0.3s ease-in-out;
}
.react-datepicker__day--holidays:not([aria-disabled=true]):hover,
.react-datepicker__month-text--holidays:not([aria-disabled=true]):hover,
.react-datepicker__quarter-text--holidays:not([aria-disabled=true]):hover,
.react-datepicker__year-text--holidays:not([aria-disabled=true]):hover {
  background-color: rgb(207, 82.9642857143, 0);
}
.react-datepicker__day--holidays:hover .overlay,
.react-datepicker__month-text--holidays:hover .overlay,
.react-datepicker__quarter-text--holidays:hover .overlay,
.react-datepicker__year-text--holidays:hover .overlay {
  visibility: visible;
  opacity: 1;
}
.react-datepicker__day--selected, .react-datepicker__day--in-selecting-range, .react-datepicker__day--in-range,
.react-datepicker__month-text--selected,
.react-datepicker__month-text--in-selecting-range,
.react-datepicker__month-text--in-range,
.react-datepicker__quarter-text--selected,
.react-datepicker__quarter-text--in-selecting-range,
.react-datepicker__quarter-text--in-range,
.react-datepicker__year-text--selected,
.react-datepicker__year-text--in-selecting-range,
.react-datepicker__year-text--in-range {
  border-radius: 0.3rem;
  background-color: #216ba5;
  color: #fff;
}
.react-datepicker__day--selected:not([aria-disabled=true]):hover, .react-datepicker__day--in-selecting-range:not([aria-disabled=true]):hover, .react-datepicker__day--in-range:not([aria-disabled=true]):hover,
.react-datepicker__month-text--selected:not([aria-disabled=true]):hover,
.react-datepicker__month-text--in-selecting-range:not([aria-disabled=true]):hover,
.react-datepicker__month-text--in-range:not([aria-disabled=true]):hover,
.react-datepicker__quarter-text--selected:not([aria-disabled=true]):hover,
.react-datepicker__quarter-text--in-selecting-range:not([aria-disabled=true]):hover,
.react-datepicker__quarter-text--in-range:not([aria-disabled=true]):hover,
.react-datepicker__year-text--selected:not([aria-disabled=true]):hover,
.react-datepicker__year-text--in-selecting-range:not([aria-disabled=true]):hover,
.react-datepicker__year-text--in-range:not([aria-disabled=true]):hover {
  background-color: rgb(28.75, 93.2196969697, 143.75);
}
.react-datepicker__day--keyboard-selected,
.react-datepicker__month-text--keyboard-selected,
.react-datepicker__quarter-text--keyboard-selected,
.react-datepicker__year-text--keyboard-selected {
  border-radius: 0.3rem;
  background-color: rgb(186.25, 217.0833333333, 241.25);
  color: rgb(0, 0, 0);
}
.react-datepicker__day--keyboard-selected:not([aria-disabled=true]):hover,
.react-datepicker__month-text--keyboard-selected:not([aria-disabled=true]):hover,
.react-datepicker__quarter-text--keyboard-selected:not([aria-disabled=true]):hover,
.react-datepicker__year-text--keyboard-selected:not([aria-disabled=true]):hover {
  background-color: rgb(28.75, 93.2196969697, 143.75);
}
.react-datepicker__day--in-selecting-range:not(.react-datepicker__day--in-range,
.react-datepicker__month-text--in-range,
.react-datepicker__quarter-text--in-range,
.react-datepicker__year-text--in-range),
.react-datepicker__month-text--in-selecting-range:not(.react-datepicker__day--in-range,
.react-datepicker__month-text--in-range,
.react-datepicker__quarter-text--in-range,
.react-datepicker__year-text--in-range),
.react-datepicker__quarter-text--in-selecting-range:not(.react-datepicker__day--in-range,
.react-datepicker__month-text--in-range,
.react-datepicker__quarter-text--in-range,
.react-datepicker__year-text--in-range),
.react-datepicker__year-text--in-selecting-range:not(.react-datepicker__day--in-range,
.react-datepicker__month-text--in-range,
.react-datepicker__quarter-text--in-range,
.react-datepicker__year-text--in-range) {
  background-color: rgba(33, 107, 165, 0.5);
}
.react-datepicker__month--selecting-range .react-datepicker__day--in-range:not(.react-datepicker__day--in-selecting-range,
.react-datepicker__month-text--in-selecting-range,
.react-datepicker__quarter-text--in-selecting-range,
.react-datepicker__year-text--in-selecting-range), .react-datepicker__year--selecting-range .react-datepicker__day--in-range:not(.react-datepicker__day--in-selecting-range,
.react-datepicker__month-text--in-selecting-range,
.react-datepicker__quarter-text--in-selecting-range,
.react-datepicker__year-text--in-selecting-range),
.react-datepicker__month--selecting-range .react-datepicker__month-text--in-range:not(.react-datepicker__day--in-selecting-range,
.react-datepicker__month-text--in-selecting-range,
.react-datepicker__quarter-text--in-selecting-range,
.react-datepicker__year-text--in-selecting-range),
.react-datepicker__year--selecting-range .react-datepicker__month-text--in-range:not(.react-datepicker__day--in-selecting-range,
.react-datepicker__month-text--in-selecting-range,
.react-datepicker__quarter-text--in-selecting-range,
.react-datepicker__year-text--in-selecting-range),
.react-datepicker__month--selecting-range .react-datepicker__quarter-text--in-range:not(.react-datepicker__day--in-selecting-range,
.react-datepicker__month-text--in-selecting-range,
.react-datepicker__quarter-text--in-selecting-range,
.react-datepicker__year-text--in-selecting-range),
.react-datepicker__year--selecting-range .react-datepicker__quarter-text--in-range:not(.react-datepicker__day--in-selecting-range,
.react-datepicker__month-text--in-selecting-range,
.react-datepicker__quarter-text--in-selecting-range,
.react-datepicker__year-text--in-selecting-range),
.react-datepicker__month--selecting-range .react-datepicker__year-text--in-range:not(.react-datepicker__day--in-selecting-range,
.react-datepicker__month-text--in-selecting-range,
.react-datepicker__quarter-text--in-selecting-range,
.react-datepicker__year-text--in-selecting-range),
.react-datepicker__year--selecting-range .react-datepicker__year-text--in-range:not(.react-datepicker__day--in-selecting-range,
.react-datepicker__month-text--in-selecting-range,
.react-datepicker__quarter-text--in-selecting-range,
.react-datepicker__year-text--in-selecting-range) {
  background-color: #f0f0f0;
  color: #000;
}
.react-datepicker__day--disabled,
.react-datepicker__month-text--disabled,
.react-datepicker__quarter-text--disabled,
.react-datepicker__year-text--disabled {
  cursor: default;
  color: #ccc;
}
.react-datepicker__day--disabled .overlay,
.react-datepicker__month-text--disabled .overlay,
.react-datepicker__quarter-text--disabled .overlay,
.react-datepicker__year-text--disabled .overlay {
  position: absolute;
  bottom: 70%;
  left: 50%;
  transform: translateX(-50%);
  background-color: #333;
  color: #fff;
  padding: 4px;
  border-radius: 4px;
  white-space: nowrap;
  visibility: hidden;
  opacity: 0;
  transition: visibility 0s, opacity 0.3s ease-in-out;
}

.react-datepicker__input-container {
  position: relative;
  display: inline-block;
  width: 100%;
}
.react-datepicker__input-container .react-datepicker__calendar-icon {
  position: absolute;
  padding: 0.5rem;
  box-sizing: content-box;
}

.react-datepicker__view-calendar-icon input {
  padding: 6px 10px 5px 25px;
}

.react-datepicker__year-read-view,
.react-datepicker__month-read-view,
.react-datepicker__month-year-read-view {
  border: 1px solid transparent;
  border-radius: 0.3rem;
  position: relative;
}
.react-datepicker__year-read-view:hover,
.react-datepicker__month-read-view:hover,
.react-datepicker__month-year-read-view:hover {
  cursor: pointer;
}
.react-datepicker__year-read-view:hover .react-datepicker__year-read-view--down-arrow,
.react-datepicker__year-read-view:hover .react-datepicker__month-read-view--down-arrow,
.react-datepicker__month-read-view:hover .react-datepicker__year-read-view--down-arrow,
.react-datepicker__month-read-view:hover .react-datepicker__month-read-view--down-arrow,
.react-datepicker__month-year-read-view:hover .react-datepicker__year-read-view--down-arrow,
.react-datepicker__month-year-read-view:hover .react-datepicker__month-read-view--down-arrow {
  border-top-color: rgb(178.5, 178.5, 178.5);
}
.react-datepicker__year-read-view--down-arrow,
.react-datepicker__month-read-view--down-arrow,
.react-datepicker__month-year-read-view--down-arrow {
  transform: rotate(135deg);
  right: -16px;
  top: 0;
}

.react-datepicker__year-dropdown,
.react-datepicker__month-dropdown,
.react-datepicker__month-year-dropdown {
  background-color: #f0f0f0;
  position: absolute;
  width: 50%;
  left: 25%;
  top: 30px;
  z-index: 1;
  text-align: center;
  border-radius: 0.3rem;
  border: 1px solid #aeaeae;
}
.react-datepicker__year-dropdown:hover,
.react-datepicker__month-dropdown:hover,
.react-datepicker__month-year-dropdown:hover {
  cursor: pointer;
}
.react-datepicker__year-dropdown--scrollable,
.react-datepicker__month-dropdown--scrollable,
.react-datepicker__month-year-dropdown--scrollable {
  height: 150px;
  overflow-y: scroll;
}

.react-datepicker__year-option,
.react-datepicker__month-option,
.react-datepicker__month-year-option {
  line-height: 20px;
  width: 100%;
  display: block;
  margin-left: auto;
  margin-right: auto;
}
.react-datepicker__year-option:first-of-type,
.react-datepicker__month-option:first-of-type,
.react-datepicker__month-year-option:first-of-type {
  border-top-left-radius: 0.3rem;
  border-top-right-radius: 0.3rem;
}
.react-datepicker__year-option:last-of-type,
.react-datepicker__month-option:last-of-type,
.react-datepicker__month-year-option:last-of-type {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  border-bottom-left-radius: 0.3rem;
  border-bottom-right-radius: 0.3rem;
}
.react-datepicker__year-option:hover,
.react-datepicker__month-option:hover,
.react-datepicker__month-year-option:hover {
  background-color: #ccc;
}
.react-datepicker__year-option:hover .react-datepicker__navigation--years-upcoming,
.react-datepicker__month-option:hover .react-datepicker__navigation--years-upcoming,
.react-datepicker__month-year-option:hover .react-datepicker__navigation--years-upcoming {
  border-bottom-color: rgb(178.5, 178.5, 178.5);
}
.react-datepicker__year-option:hover .react-datepicker__navigation--years-previous,
.react-datepicker__month-option:hover .react-datepicker__navigation--years-previous,
.react-datepicker__month-year-option:hover .react-datepicker__navigation--years-previous {
  border-top-color: rgb(178.5, 178.5, 178.5);
}
.react-datepicker__year-option--selected,
.react-datepicker__month-option--selected,
.react-datepicker__month-year-option--selected {
  position: absolute;
  left: 15px;
}

.react-datepicker__close-icon {
  cursor: pointer;
  background-color: transparent;
  border: 0;
  outline: 0;
  padding: 0 6px 0 0;
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  display: table-cell;
  vertical-align: middle;
}
.react-datepicker__close-icon::after {
  cursor: pointer;
  background-color: #216ba5;
  color: #fff;
  border-radius: 50%;
  height: 16px;
  width: 16px;
  padding: 2px;
  font-size: 12px;
  line-height: 1;
  text-align: center;
  display: table-cell;
  vertical-align: middle;
  content: "×";
}
.react-datepicker__close-icon--disabled {
  cursor: default;
}
.react-datepicker__close-icon--disabled::after {
  cursor: default;
  background-color: #ccc;
}

.react-datepicker__today-button {
  background: #f0f0f0;
  border-top: 1px solid #aeaeae;
  cursor: pointer;
  text-align: center;
  font-weight: bold;
  padding: 5px 0;
  clear: left;
}

.react-datepicker__portal {
  position: fixed;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.8);
  left: 0;
  top: 0;
  justify-content: center;
  align-items: center;
  display: flex;
  z-index: 2147483647;
}
.react-datepicker__portal .react-datepicker__day-name,
.react-datepicker__portal .react-datepicker__day,
.react-datepicker__portal .react-datepicker__time-name {
  width: 3rem;
  line-height: 3rem;
}
@media (max-width: 400px), (max-height: 550px) {
  .react-datepicker__portal .react-datepicker__day-name,
  .react-datepicker__portal .react-datepicker__day,
  .react-datepicker__portal .react-datepicker__time-name {
    width: 2rem;
    line-height: 2rem;
  }
}
.react-datepicker__portal .react-datepicker__current-month,
.react-datepicker__portal .react-datepicker-time__header {
  font-size: 1.44rem;
}

.react-datepicker__children-container {
  width: 13.8rem;
  margin: 0.4rem;
  padding-right: 0.2rem;
  padding-left: 0.2rem;
  height: auto;
}

.react-datepicker__aria-live {
  position: absolute;
  clip-path: circle(0);
  border: 0;
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  width: 1px;
  white-space: nowrap;
}

.react-datepicker__calendar-icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.125em;
}
.attributeEditor_date{
    width:100px;
}
.attributeEditor_date_input{
}
.attributeEditor_datetime{
    display:flex;
    align-items: center;
    height: 40px;
}
.attributeEditor_datetime  p{
    height: 40px;
    display: contents;
}
.attributeEditor_datetime .attributeEditor_date_input{
    width:100px;
    padding-left: 5px !important;
    padding-right: 5px !important;
}
.attributeEditor_hour{
    width:40px;
    padding-left: 5px !important;
    text-align: right !important;
    padding-right: 5px !important;
}
.attributeEditor_minute{
    width:40px;
    padding-left: 5px !important;
    text-align: right !important;
    padding-right: 5px !important;
}
.attributeEditor_second{
    width:40px;
    padding-left: 5px !important;
    text-align: right !important;
    padding-right: 5px !important;
}


.attributeEditor_hidden {
    display: block;
    margin: 0;
    padding: 0;
    width: 0;
    height: 0;
    overflow: hidden;
    font-size: 0;
    line-height: 0;
    visibility: hidden;
}.attributeEditor_addColumnDiv{
    border: solid 0.5px;
}
.attributeEditor_columnNmSpan{
    width: 95px;
    line-height: 28px;
    margin-right: 5px;
    text-align: center;
    letter-spacing: -1.2px;
    color: #333;
    font-size: 16px;
    font-family: '맑은 고딕';
    font-weight: bold;
}
.attributeEditor_columnNmInput{
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    padding-left: 15px;
    height: 40px;
    border-radius: 4px;
    border: 1px solid #e9e9e9;
    color: #555;
    font-size: 16px;
    font-family: '맑은 고딕';
    font-weight: normal;
    resize: none;
    width: 260px;
    margin-left:5px;
}
.attributeEditor_columnNickNameSpan{
    width: 95px;
    line-height: 28px;
    margin-right: 5px;
    text-align: center;
    letter-spacing: -1.2px;
    color: #333;
    font-size: 16px;
    font-family: '맑은 고딕';
    font-weight: bold;
}
.attributeEditor_columnNickNameInput{
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    padding-left: 15px;
    height: 40px;
    border-radius: 4px;
    border: 1px solid #e9e9e9;
    color: #555;
    font-size: 16px;
    font-family: '맑은 고딕';
    font-weight: normal;
    resize: none;
    width: 260px;
    margin-left:5px;
}
.attributeEditor_dataTypeSpan{
    width: 95px;
    line-height: 28px;
    margin-right: 5px;
    text-align: center;
    letter-spacing: -1.2px;
    color: #333;
    font-size: 16px;
    font-family: '맑은 고딕';
    font-weight: bold;
}
.attributeEditor_dataTypeSelectBox{
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    padding-left: 15px;
    height: 40px;
    border-radius: 4px;
    border: 1px solid #e9e9e9;
    color: #555;
    font-size: 16px;
    font-family: '맑은 고딕';
    font-weight: normal;
    resize: none;
    width: 260px;
    margin-bottom: 10px;
}
.attributeEditor_dataTypeOption{

}
.attributeEditor_insertColumnBtn{
    position: relative;
    display: flex;
    align-items: center;
    height: 40px;
    padding: 0 15px;
    border-radius: 4px;
    box-sizing: border-box;
    font-family: '맑은 고딕';
    font-weight: normal;
    font-size: 16px;
    color: #fff;
    vertical-align: top;
    letter-spacing: -1px;
    transition: .4s;
    background: #436aeb;
    padding: 0 30px;
    cursor : pointer;
    margin-right: 5px;
}
.attributeEditor_insertColumnSpan{

}
.attributeEditor_headerDiv{
    display : flex;
    justify-content: space-between;
}
.attributeEditor_addColumnBtn{
    position: relative;
    align-items: center;
    height: 40px;
    padding: 0 15px;
    border-radius: 4px;
    box-sizing: border-box;
    font-family: '맑은 고딕';
    font-weight: normal;
    font-size: 16px;
    color: #fff;
    vertical-align: top;
    letter-spacing: -1px;
    transition: .4s;
    background: #436aeb;
    padding: 0 30px;
    cursor : pointer;
    margin-right: 5px;
    margin-bottom: 20px;
}
.attributeEditor_resetColumnBtn{
    position: relative;
    align-items: center;
    height: 40px;
    padding: 0 15px;
    border-radius: 4px;
    box-sizing: border-box;
    font-family: '맑은 고딕';
    font-weight: normal;
    font-size: 16px;
    color: #fff;
    vertical-align: top;
    letter-spacing: -1px;
    transition: .4s;
    background: #333;
    padding: 0 30px;
    cursor : pointer;
    /*margin-right: 5px;*/
    margin-bottom: 20px;
}
.attributeEditor_addColumnSpan{

}
.attributeEditor_resetColumnSpan{

}
.attributeEditor_applyBtn {
    margin-right: 5px;
    word-break: keep-all;
    background: #333;
    display: flex;
    align-items: center;
    height: 40px;
    padding: 0 15px;
    border-radius: 4px;
    box-sizing: border-box;
    font-family: '맑은 고딕';
    font-weight: normal;
    font-size: 16px;
    color: #fff;
    background-color : #ff5700;
    vertical-align: top;
    letter-spacing: -1px;
    transition: .4s;
    margin-top: 10px;
}
.attributeEditor_applyBtnSpan {

}
.attributeEditor_helpMsgSpan{
    margin-top: 15px;
    margin-left: auto;
}
.attributeEditor_footerDiv{
    display : flex;
}

/* 230216 위젯 예제 개선 */
.attributeEditor_attributeEditorDiv {
    position: relative;
    border: 1px solid #e1e1e1;
    padding: 20px 10px;
    border-radius: 4px;
    margin-bottom: 5px;
    overflow: auto;
}

/* 230320 위젯 예제 개선 */
div[role="gridcell"] {
    text-align: center;

    /*
        240122 ellipsis 미적용으로 해당 코드 주석처리
        justify-content: center; 외에 특별한 flex 설정 없어 text-align: center; 로 대체
        display: block
    */
    /*display: flex;*/
    /*justify-content: center; !* align horizontal *!*/
    /*align-items: center;*/
}
/**
 ****************************
 * Generic Styles
 ****************************
*/
ag-grid, ag-grid-angular, ag-grid-ng2, ag-grid-polymer, ag-grid-aurelia {
  display: block;
}

.ag-hidden {
  display: none !important;
}

.ag-invisible {
  visibility: hidden !important;
}

.ag-no-transition {
  -webkit-transition: none !important;
  transition: none !important;
}

.ag-drag-handle {
  cursor: -webkit-grab;
  cursor: grab;
}

.ag-column-drop-wrapper {
  display: -webkit-box;
  display: flex;
}

.ag-column-drop-horizontal-half-width {
  display: inline-block;
  width: 50% !important;
}

.ag-unselectable {
  -moz-user-select: none;
  -webkit-user-select: none;
  user-select: none;
}

.ag-selectable {
  -moz-user-select: text;
  -webkit-user-select: text;
  user-select: text;
}

.ag-tab {
  position: relative;
}

.ag-tab-guard {
  position: absolute;
  width: 0;
  height: 0;
  display: block;
}

.ag-select-agg-func-popup {
  position: absolute;
}

.ag-input-wrapper, .ag-picker-field-wrapper {
  display: -webkit-box;
  display: flex;
  -webkit-box-flex: 1;
          flex: 1 1 auto;
  -webkit-box-align: center;
          align-items: center;
  line-height: normal;
  position: relative;
}

.ag-shake-left-to-right {
  -webkit-animation-direction: alternate;
          animation-direction: alternate;
  -webkit-animation-duration: 0.2s;
          animation-duration: 0.2s;
  -webkit-animation-iteration-count: infinite;
          animation-iteration-count: infinite;
  -webkit-animation-name: ag-shake-left-to-right;
          animation-name: ag-shake-left-to-right;
}

@-webkit-keyframes ag-shake-left-to-right {
  from {
    padding-left: 6px;
    padding-right: 2px;
  }
  to {
    padding-left: 2px;
    padding-right: 6px;
  }
}

@keyframes ag-shake-left-to-right {
  from {
    padding-left: 6px;
    padding-right: 2px;
  }
  to {
    padding-left: 2px;
    padding-right: 6px;
  }
}
.ag-root-wrapper {
  cursor: default;
  position: relative;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
          flex-direction: column;
  overflow: hidden;
}
.ag-root-wrapper.ag-layout-normal {
  height: 100%;
}

.ag-watermark {
  position: absolute;
  bottom: 20px;
  right: 25px;
  opacity: 0.5;
  -webkit-transition: opacity 1s ease-out 3s;
  transition: opacity 1s ease-out 3s;
}
.ag-watermark::before {
  content: "";
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+CjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+Cjxzdmcgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgdmlld0JveD0iMCAwIDIzNSA0MCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB4bWw6c3BhY2U9InByZXNlcnZlIiB4bWxuczpzZXJpZj0iaHR0cDovL3d3dy5zZXJpZi5jb20vIiBzdHlsZT0iZmlsbC1ydWxlOmV2ZW5vZGQ7Y2xpcC1ydWxlOmV2ZW5vZGQ7c3Ryb2tlLWxpbmVqb2luOnJvdW5kO3N0cm9rZS1taXRlcmxpbWl0OjI7Ij4KICAgIDxnIHRyYW5zZm9ybT0ibWF0cml4KDAuNjM1NzIzLDAsMCwwLjYzNTcyMywtNDkyLjkyMSwtMzIzLjYwOCkiPgogICAgICAgIDxwYXRoIGQ9Ik0xMDk5LjQsNTQ5LjRMMTA5OS40LDUzNi45TDEwNzguMSw1MzYuOUwxMDY1LjYsNTQ5LjRMMTA5OS40LDU0OS40WiIgc3R5bGU9ImZpbGw6cmdiKDI0LDI5LDMxKTtmaWxsLXJ1bGU6bm9uemVybzsiLz4KICAgICAgICA8cGF0aCBkPSJNMTEyMy40LDUxOC40TDEwOTYuNyw1MTguNEwxMDg0LjEsNTMwLjlMMTEyMy40LDUzMC45TDExMjMuNCw1MTguNFoiIHN0eWxlPSJmaWxsOnJnYigyNCwyOSwzMSk7ZmlsbC1ydWxlOm5vbnplcm87Ii8+CiAgICAgICAgPHBhdGggZD0iTTEwNTMuMiw1NjEuOUwxMDU5LjYsNTU1LjVMMTA4MS4yLDU1NS41TDEwODEuMiw1NjhMMTA1My4yLDU2OEwxMDUzLjIsNTYxLjlaIiBzdHlsZT0iZmlsbDpyZ2IoMjQsMjksMzEpO2ZpbGwtcnVsZTpub256ZXJvOyIvPgogICAgICAgIDxwYXRoIGQ9Ik0xMDU3LjksNTQzLjNMMTA3MS43LDU0My4zTDEwODQuMyw1MzAuOEwxMDU3LjksNTMwLjhMMTA1Ny45LDU0My4zWiIgc3R5bGU9ImZpbGw6cmdiKDI0LDI5LDMxKTtmaWxsLXJ1bGU6bm9uemVybzsiLz4KICAgICAgICA8cGF0aCBkPSJNMTA0Mi44LDU2MS45TDEwNTMuMiw1NjEuOUwxMDY1LjYsNTQ5LjRMMTA0Mi44LDU0OS40TDEwNDIuOCw1NjEuOVoiIHN0eWxlPSJmaWxsOnJnYigyNCwyOSwzMSk7ZmlsbC1ydWxlOm5vbnplcm87Ii8+CiAgICAgICAgPHBhdGggZD0iTTEwOTYuNyw1MTguNEwxMDkwLjMsNTI0LjhMMTA0OS41LDUyNC44TDEwNDkuNSw1MTIuM0wxMDk2LjcsNTEyLjNMMTA5Ni43LDUxOC40WiIgc3R5bGU9ImZpbGw6cmdiKDI0LDI5LDMxKTtmaWxsLXJ1bGU6bm9uemVybzsiLz4KICAgICAgICA8cGF0aCBkPSJNODI4LjYsNTU5LjdMODA5LDU1OS43TDgwNS42LDU2OC4xTDc5Nyw1NjguMUw4MTUuMSw1MjUuN0w4MjIuNiw1MjUuN0w4NDAuNyw1NjguMUw4MzIsNTY4LjFMODI4LjYsNTU5LjdaTTgyNS45LDU1M0w4MTguOCw1MzUuN0w4MTEuNyw1NTNMODI1LjksNTUzWiIgc3R5bGU9ImZpbGw6cmdiKDI0LDI5LDMxKTtmaWxsLXJ1bGU6bm9uemVybzsiLz4KICAgICAgICA8cGF0aCBkPSJNOTYwLjEsNTQxLjNDOTYyLjYsNTM3LjYgOTY4LjksNTM3LjIgOTcxLjUsNTM3LjJMOTcxLjUsNTQ0LjRDOTY4LjMsNTQ0LjQgOTY1LjEsNTQ0LjUgOTYzLjIsNTQ1LjlDOTYxLjMsNTQ3LjMgOTYwLjMsNTQ5LjIgOTYwLjMsNTUxLjVMOTYwLjMsNTY4LjFMOTUyLjUsNTY4LjFMOTUyLjUsNTM3LjJMOTYwLDUzNy4yTDk2MC4xLDU0MS4zWiIgc3R5bGU9ImZpbGw6cmdiKDI0LDI5LDMxKTtmaWxsLXJ1bGU6bm9uemVybzsiLz4KICAgICAgICA8cmVjdCB4PSI5NzUuOCIgeT0iNTM3LjIiIHdpZHRoPSI3LjgiIGhlaWdodD0iMzAuOSIgc3R5bGU9ImZpbGw6cmdiKDI0LDI5LDMxKTsiLz4KICAgICAgICA8cmVjdCB4PSI5NzUuOCIgeT0iNTIzLjQiIHdpZHRoPSI3LjgiIGhlaWdodD0iOS4yIiBzdHlsZT0iZmlsbDpyZ2IoMjQsMjksMzEpOyIvPgogICAgICAgIDxwYXRoIGQ9Ik0xMDIyLjMsNTIzLjRMMTAyMi4zLDU2OC4xTDEwMTQuOCw1NjguMUwxMDE0LjYsNTYzLjRDMTAxMy41LDU2NSAxMDEyLjEsNTY2LjMgMTAxMC40LDU2Ny4zQzEwMDguNyw1NjguMiAxMDA2LjYsNTY4LjcgMTAwNC4yLDU2OC43QzEwMDIuMSw1NjguNyAxMDAwLjEsNTY4LjMgOTk4LjQsNTY3LjZDOTk2LjYsNTY2LjggOTk1LDU2NS44IDk5My43LDU2NC40Qzk5Mi40LDU2MyA5OTEuMyw1NjEuMyA5OTAuNiw1NTkuNEM5ODkuOCw1NTcuNSA5ODkuNSw1NTUuMyA5ODkuNSw1NTIuOUM5ODkuNSw1NTAuNSA5ODkuOSw1NDguMyA5OTAuNiw1NDYuM0M5OTEuNCw1NDQuMyA5OTIuNCw1NDIuNiA5OTMuNyw1NDEuMkM5OTUsNTM5LjggOTk2LjYsNTM4LjcgOTk4LjQsNTM3LjlDMTAwMC4yLDUzNy4xIDEwMDIuMSw1MzYuNyAxMDA0LjIsNTM2LjdDMTAwNi42LDUzNi43IDEwMDguNiw1MzcuMSAxMDEwLjMsNTM4QzEwMTIsNTM4LjkgMTAxMy40LDU0MC4xIDEwMTQuNSw1NDEuOEwxMDE0LjUsNTIzLjVMMTAyMi4zLDUyMy41TDEwMjIuMyw1MjMuNFpNMTAwNS45LDU2MkMxMDA4LjUsNTYyIDEwMTAuNSw1NjEuMSAxMDEyLjEsNTU5LjRDMTAxMy43LDU1Ny43IDEwMTQuNSw1NTUuNCAxMDE0LjUsNTUyLjZDMTAxNC41LDU0OS44IDEwMTMuNyw1NDcuNiAxMDEyLjEsNTQ1LjhDMTAxMC41LDU0NC4xIDEwMDguNSw1NDMuMiAxMDA1LjksNTQzLjJDMTAwMy40LDU0My4yIDEwMDEuMyw1NDQuMSA5OTkuOCw1NDUuOEM5OTguMiw1NDcuNSA5OTcuNCw1NDkuOCA5OTcuNCw1NTIuNkM5OTcuNCw1NTUuNCA5OTguMiw1NTcuNiA5OTkuOCw1NTkuM0MxMDAxLjQsNTYxLjEgMTAwMy40LDU2MiAxMDA1LjksNTYyIiBzdHlsZT0iZmlsbDpyZ2IoMjQsMjksMzEpO2ZpbGwtcnVsZTpub256ZXJvOyIvPgogICAgICAgIDxwYXRoIGQ9Ik04ODUuOCw1NDQuMkw4NjYuNSw1NDQuMkw4NjYuNSw1NTAuOUw4NzcuNSw1NTAuOUM4NzcuMiw1NTQuMyA4NzUuOSw1NTYuOSA4NzMuNyw1NTlDODcxLjUsNTYxIDg2OC43LDU2MiA4NjUuMSw1NjJDODYzLjEsNTYyIDg2MS4yLDU2MS42IDg1OS42LDU2MC45Qzg1Ny45LDU2MC4yIDg1Ni41LDU1OS4yIDg1NS4zLDU1Ny44Qzg1NC4xLDU1Ni41IDg1My4yLDU1NC45IDg1Mi41LDU1M0M4NTEuOCw1NTEuMSA4NTEuNSw1NDkuMSA4NTEuNSw1NDYuOEM4NTEuNSw1NDQuNSA4NTEuOCw1NDIuNSA4NTIuNSw1NDAuNkM4NTMuMSw1MzguNyA4NTQuMSw1MzcuMiA4NTUuMyw1MzUuOEM4NTYuNSw1MzQuNSA4NTcuOSw1MzMuNSA4NTkuNiw1MzIuN0M4NjEuMyw1MzIgODYzLjEsNTMxLjYgODY1LjIsNTMxLjZDODY5LjQsNTMxLjYgODcyLjYsNTMyLjYgODc0LjgsNTM0LjZMODgwLDUyOS40Qzg3Ni4xLDUyNi40IDg3MS4xLDUyNC44IDg2NS4yLDUyNC44Qzg2MS45LDUyNC44IDg1OC45LDUyNS4zIDg1Ni4yLDUyNi40Qzg1My41LDUyNy41IDg1MS4yLDUyOC45IDg0OS4zLDUzMC44Qzg0Ny40LDUzMi43IDg0NS45LDUzNSA4NDQuOSw1MzcuN0M4NDMuOSw1NDAuNCA4NDMuNCw1NDMuNCA4NDMuNCw1NDYuNkM4NDMuNCw1NDkuOCA4NDMuOSw1NTIuOCA4NDUsNTU1LjVDODQ2LjEsNTU4LjIgODQ3LjUsNTYwLjUgODQ5LjQsNTYyLjRDODUxLjMsNTY0LjMgODUzLjYsNTY1LjggODU2LjMsNTY2LjhDODU5LDU2Ny45IDg2Miw1NjguNCA4NjUuMiw1NjguNEM4NjguNCw1NjguNCA4NzEuMyw1NjcuOSA4NzMuOSw1NjYuOEM4NzYuNSw1NjUuNyA4NzguNyw1NjQuMyA4ODAuNSw1NjIuNEM4ODIuMyw1NjAuNSA4ODMuNyw1NTguMiA4ODQuNyw1NTUuNUM4ODUuNyw1NTIuOCA4ODYuMiw1NDkuOCA4ODYuMiw1NDYuNkw4ODYuMiw1NDUuM0M4ODUuOSw1NDUuMSA4ODUuOCw1NDQuNiA4ODUuOCw1NDQuMiIgc3R5bGU9ImZpbGw6cmdiKDI0LDI5LDMxKTtmaWxsLXJ1bGU6bm9uemVybzsiLz4KICAgICAgICA8cGF0aCBkPSJNOTQ2LjgsNTQ0LjJMOTI3LjUsNTQ0LjJMOTI3LjUsNTUwLjlMOTM4LjUsNTUwLjlDOTM4LjIsNTU0LjMgOTM2LjksNTU2LjkgOTM0LjcsNTU5QzkzMi41LDU2MSA5MjkuNyw1NjIgOTI2LjEsNTYyQzkyNC4xLDU2MiA5MjIuMiw1NjEuNiA5MjAuNiw1NjAuOUM5MTguOSw1NjAuMiA5MTcuNSw1NTkuMiA5MTYuMyw1NTcuOEM5MTUuMSw1NTYuNSA5MTQuMiw1NTQuOSA5MTMuNSw1NTNDOTEyLjgsNTUxLjEgOTEyLjUsNTQ5LjEgOTEyLjUsNTQ2LjhDOTEyLjUsNTQ0LjUgOTEyLjgsNTQyLjUgOTEzLjUsNTQwLjZDOTE0LjEsNTM4LjcgOTE1LjEsNTM3LjIgOTE2LjMsNTM1LjhDOTE3LjUsNTM0LjUgOTE4LjksNTMzLjUgOTIwLjYsNTMyLjdDOTIyLjMsNTMyIDkyNC4xLDUzMS42IDkyNi4yLDUzMS42QzkzMC40LDUzMS42IDkzMy42LDUzMi42IDkzNS44LDUzNC42TDk0MSw1MjkuNEM5MzcuMSw1MjYuNCA5MzIuMSw1MjQuOCA5MjYuMiw1MjQuOEM5MjIuOSw1MjQuOCA5MTkuOSw1MjUuMyA5MTcuMiw1MjYuNEM5MTQuNSw1MjcuNSA5MTIuMiw1MjguOSA5MTAuMyw1MzAuOEM5MDguNCw1MzIuNyA5MDYuOSw1MzUgOTA1LjksNTM3LjdDOTA0LjksNTQwLjQgOTA0LjQsNTQzLjQgOTA0LjQsNTQ2LjZDOTA0LjQsNTQ5LjggOTA0LjksNTUyLjggOTA2LDU1NS41QzkwNy4xLDU1OC4yIDkwOC41LDU2MC41IDkxMC40LDU2Mi40QzkxMi4zLDU2NC4zIDkxNC42LDU2NS44IDkxNy4zLDU2Ni44QzkyMCw1NjcuOSA5MjMsNTY4LjQgOTI2LjIsNTY4LjRDOTI5LjQsNTY4LjQgOTMyLjMsNTY3LjkgOTM0LjksNTY2LjhDOTM3LjUsNTY1LjcgOTM5LjcsNTY0LjMgOTQxLjUsNTYyLjRDOTQzLjMsNTYwLjUgOTQ0LjcsNTU4LjIgOTQ1LjcsNTU1LjVDOTQ2LjcsNTUyLjggOTQ3LjIsNTQ5LjggOTQ3LjIsNTQ2LjZMOTQ3LjIsNTQ1LjNDOTQ2LjksNTQ1LjEgOTQ2LjgsNTQ0LjYgOTQ2LjgsNTQ0LjIiIHN0eWxlPSJmaWxsOnJnYigyNCwyOSwzMSk7ZmlsbC1ydWxlOm5vbnplcm87Ii8+CiAgICA8L2c+Cjwvc3ZnPgo=);
  background-repeat: no-repeat;
  background-size: 170px 40px;
  display: block;
  height: 40px;
  width: 170px;
  opacity: 0.5;
}

.ag-watermark-text {
  opacity: 0.5;
  font-weight: bold;
  font-family: Impact, sans-serif;
  font-size: 19px;
  padding-left: 0.7rem;
}

.ag-root-wrapper-body {
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
          flex-direction: row;
}
.ag-root-wrapper-body.ag-layout-normal {
  -webkit-box-flex: 1;
          flex: 1 1 auto;
  height: 0;
  min-height: 0;
}

.ag-root {
  position: relative;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
          flex-direction: column;
}
.ag-root.ag-layout-normal, .ag-root.ag-layout-auto-height {
  overflow: hidden;
  -webkit-box-flex: 1;
          flex: 1 1 auto;
  width: 0;
}
.ag-root.ag-layout-normal {
  height: 100%;
}

/**
 ****************************
 * Viewports
 ****************************
*/
.ag-header-viewport,
.ag-floating-top-viewport,
.ag-body-viewport,
.ag-center-cols-viewport,
.ag-floating-bottom-viewport,
.ag-body-horizontal-scroll-viewport,
.ag-virtual-list-viewport,
.ag-sticky-top-viewport {
  position: relative;
  height: 100%;
  min-width: 0px;
  overflow: hidden;
  -webkit-box-flex: 1;
          flex: 1 1 auto;
}

.ag-body-viewport {
  display: -webkit-box;
  display: flex;
}
.ag-body-viewport.ag-layout-normal {
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.ag-center-cols-viewport {
  width: 100%;
  overflow-x: auto;
}

.ag-body-horizontal-scroll-viewport {
  overflow-x: scroll;
}

.ag-virtual-list-viewport {
  overflow: auto;
  width: 100%;
}

/**
 ****************************
 * Containers
 ****************************
*/
.ag-header-container,
.ag-floating-top-container,
.ag-body-container,
.ag-pinned-right-cols-container,
.ag-center-cols-container,
.ag-pinned-left-cols-container,
.ag-floating-bottom-container,
.ag-body-horizontal-scroll-container,
.ag-full-width-container,
.ag-floating-bottom-full-width-container,
.ag-virtual-list-container,
.ag-sticky-top-container {
  position: relative;
}

.ag-header-container,
.ag-floating-top-container,
.ag-floating-bottom-container,
.ag-sticky-top-container {
  height: 100%;
  white-space: nowrap;
}

.ag-center-cols-container {
  display: block;
}

.ag-pinned-right-cols-container {
  display: block;
}

.ag-body-horizontal-scroll-container {
  height: 100%;
}

.ag-full-width-container,
.ag-floating-top-full-width-container,
.ag-floating-bottom-full-width-container,
.ag-sticky-top-full-width-container {
  position: absolute;
  top: 0px;
  left: 0px;
  pointer-events: none;
}

.ag-full-width-container {
  width: 100%;
}

.ag-floating-bottom-full-width-container, .ag-floating-top-full-width-container {
  display: inline-block;
  overflow: hidden;
  height: 100%;
  width: 100%;
}

.ag-virtual-list-container {
  overflow: hidden;
}

/**
 ****************************
 * Scrollers
 ****************************
*/
.ag-center-cols-clipper {
  -webkit-box-flex: 1;
          flex: 1 1 auto;
  min-width: 0;
  overflow: hidden;
  min-height: 100%;
  -webkit-transform: translate3d(0, 0, 0);
          transform: translate3d(0, 0, 0);
}

.ag-body-horizontal-scroll {
  min-height: 0;
  min-width: 0;
  width: 100%;
  display: -webkit-box;
  display: flex;
  position: relative;
}
.ag-body-horizontal-scroll.ag-scrollbar-invisible {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
}
.ag-body-horizontal-scroll.ag-scrollbar-invisible.ag-apple-scrollbar {
  display: none;
}
.ag-body-horizontal-scroll.ag-scrollbar-invisible.ag-apple-scrollbar.ag-scrollbar-scrolling, .ag-body-horizontal-scroll.ag-scrollbar-invisible.ag-apple-scrollbar.ag-scrollbar-active {
  display: inherit;
}

.ag-force-vertical-scroll {
  overflow-y: scroll !important;
}

.ag-horizontal-left-spacer, .ag-horizontal-right-spacer {
  height: 100%;
  min-width: 0;
  overflow-x: scroll;
}
.ag-horizontal-left-spacer.ag-scroller-corner, .ag-horizontal-right-spacer.ag-scroller-corner {
  overflow-x: hidden;
}

/**
 ****************************
 * Headers
 ****************************
*/
.ag-header, .ag-pinned-left-header, .ag-pinned-right-header {
  display: inline-block;
  overflow: hidden;
  position: relative;
}

.ag-header-cell-sortable {
  cursor: pointer;
}

.ag-header {
  display: -webkit-box;
  display: flex;
  width: 100%;
  white-space: nowrap;
}

.ag-pinned-left-header {
  height: 100%;
}

.ag-pinned-right-header {
  height: 100%;
}

.ag-header-row {
  position: absolute;
  overflow: hidden;
}

.ag-header-cell {
  display: -webkit-inline-box;
  display: inline-flex;
  -webkit-box-align: center;
          align-items: center;
  position: absolute;
  height: 100%;
  overflow: hidden;
}

.ag-header-cell.ag-header-active .ag-header-cell-menu-button {
  opacity: 1;
}

.ag-header-cell-menu-button:not(.ag-header-menu-always-show) {
  -webkit-transition: opacity 0.2s;
  transition: opacity 0.2s;
  opacity: 0;
}

.ag-header-group-cell-label, .ag-header-cell-label {
  display: -webkit-box;
  display: flex;
  -webkit-box-flex: 1;
          flex: 1 1 auto;
  overflow: hidden;
  -webkit-box-align: center;
          align-items: center;
  text-overflow: ellipsis;
  align-self: stretch;
}

.ag-header-cell-text {
  overflow: hidden;
  text-overflow: ellipsis;
}

.ag-header-cell:not(.ag-header-cell-auto-height) .ag-header-cell-comp-wrapper {
  height: 100%;
  display: -webkit-box;
  display: flex;
  -webkit-box-align: center;
          align-items: center;
}

.ag-header-cell-comp-wrapper {
  width: 100%;
  overflow: hidden;
}

.ag-header-cell-wrap-text .ag-header-cell-comp-wrapper {
  white-space: normal;
}

.ag-right-aligned-header .ag-header-cell-label {
  -webkit-box-orient: horizontal;
  -webkit-box-direction: reverse;
          flex-direction: row-reverse;
}

.ag-header-group-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.ag-header-cell-resize {
  position: absolute;
  z-index: 2;
  height: 100%;
  width: 8px;
  top: 0;
  cursor: ew-resize;
}
.ag-ltr .ag-header-cell-resize {
  right: -4px;
}
.ag-rtl .ag-header-cell-resize {
  left: -4px;
}

.ag-pinned-left-header .ag-header-cell-resize {
  right: -4px;
}

.ag-pinned-right-header .ag-header-cell-resize {
  left: -4px;
}

.ag-header-select-all {
  display: -webkit-box;
  display: flex;
}

/**
 ****************************
 * Columns
 ****************************
*/
.ag-column-moving .ag-cell {
  -webkit-transition: left 0.2s;
  transition: left 0.2s;
}
.ag-column-moving .ag-header-cell {
  -webkit-transition: left 0.2s;
  transition: left 0.2s;
}
.ag-column-moving .ag-header-group-cell {
  -webkit-transition: left 0.2s, width 0.2s;
  transition: left 0.2s, width 0.2s;
}

/**
 ****************************
 * Column Panel
 ****************************
*/
.ag-column-panel {
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
          flex-direction: column;
  overflow: hidden;
  -webkit-box-flex: 1;
          flex: 1 1 auto;
}

.ag-column-select {
  position: relative;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
          flex-direction: column;
  overflow: hidden;
  -webkit-box-flex: 3;
          flex: 3 1 0px;
}

.ag-column-select-header {
  position: relative;
  display: -webkit-box;
  display: flex;
  -webkit-box-flex: 0;
          flex: none;
}

.ag-column-select-header-icon {
  position: relative;
}

.ag-column-select-header-filter-wrapper {
  -webkit-box-flex: 1;
          flex: 1 1 auto;
}

.ag-column-select-header-filter {
  width: 100%;
}

.ag-column-select-list {
  -webkit-box-flex: 1;
          flex: 1 1 0px;
  overflow: hidden;
}

.ag-column-drop {
  position: relative;
  display: -webkit-inline-box;
  display: inline-flex;
  -webkit-box-align: center;
          align-items: center;
  overflow: auto;
  width: 100%;
}

.ag-column-drop-list {
  display: -webkit-box;
  display: flex;
  -webkit-box-align: center;
          align-items: center;
}

.ag-column-drop-cell {
  position: relative;
  display: -webkit-box;
  display: flex;
  -webkit-box-align: center;
          align-items: center;
}

.ag-column-drop-cell-text {
  overflow: hidden;
  -webkit-box-flex: 1;
          flex: 1 1 auto;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.ag-column-drop-vertical {
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
          flex-direction: column;
  overflow: hidden;
  -webkit-box-align: stretch;
          align-items: stretch;
  -webkit-box-flex: 1;
          flex: 1 1 0px;
}

.ag-column-drop-vertical-title-bar {
  display: -webkit-box;
  display: flex;
  -webkit-box-align: center;
          align-items: center;
  -webkit-box-flex: 0;
          flex: none;
}

.ag-column-drop-vertical-list {
  position: relative;
  -webkit-box-align: stretch;
          align-items: stretch;
  -webkit-box-flex: 1;
          flex-grow: 1;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
          flex-direction: column;
  overflow-x: auto;
}
.ag-column-drop-vertical-list > * {
  -webkit-box-flex: 0;
          flex: none;
}

.ag-column-drop-empty .ag-column-drop-vertical-list {
  overflow: hidden;
}

.ag-column-drop-vertical-empty-message {
  display: block;
}

.ag-column-drop.ag-column-drop-horizontal {
  white-space: nowrap;
  overflow: hidden;
}

.ag-column-drop-cell-button {
  cursor: pointer;
}

.ag-filter-toolpanel {
  -webkit-box-flex: 1;
          flex: 1 1 0px;
  min-width: 0;
}

.ag-filter-toolpanel-header {
  position: relative;
}

.ag-filter-toolpanel-header, .ag-filter-toolpanel-search {
  display: -webkit-box;
  display: flex;
  -webkit-box-align: center;
          align-items: center;
}
.ag-filter-toolpanel-header > *, .ag-filter-toolpanel-search > * {
  display: -webkit-box;
  display: flex;
  -webkit-box-align: center;
          align-items: center;
}

.ag-filter-apply-panel {
  display: -webkit-box;
  display: flex;
  -webkit-box-pack: end;
          justify-content: flex-end;
  overflow: hidden;
}

/**
 ****************************
 * Rows
 ****************************
*/
.ag-row-animation .ag-row {
  -webkit-transition: top 0.4s, background-color 0.1s, opacity 0.2s, -webkit-transform 0.4s;
  transition: top 0.4s, background-color 0.1s, opacity 0.2s, -webkit-transform 0.4s;
  transition: transform 0.4s, top 0.4s, background-color 0.1s, opacity 0.2s;
  transition: transform 0.4s, top 0.4s, background-color 0.1s, opacity 0.2s, -webkit-transform 0.4s;
}

.ag-row-animation .ag-row.ag-after-created {
  -webkit-transition: top 0.4s, height 0.4s, background-color 0.1s, opacity 0.2s, -webkit-transform 0.4s;
  transition: top 0.4s, height 0.4s, background-color 0.1s, opacity 0.2s, -webkit-transform 0.4s;
  transition: transform 0.4s, top 0.4s, height 0.4s, background-color 0.1s, opacity 0.2s;
  transition: transform 0.4s, top 0.4s, height 0.4s, background-color 0.1s, opacity 0.2s, -webkit-transform 0.4s;
}

.ag-row-no-animation .ag-row {
  -webkit-transition: background-color 0.1s;
  transition: background-color 0.1s;
}

.ag-row {
  white-space: nowrap;
  width: 100%;
}

.ag-row-loading {
  display: -webkit-box;
  display: flex;
  -webkit-box-align: center;
          align-items: center;
}

.ag-row-position-absolute {
  position: absolute;
}

.ag-row-position-relative {
  position: relative;
}

.ag-full-width-row {
  overflow: hidden;
  pointer-events: all;
}

.ag-row-inline-editing {
  z-index: 1;
}

.ag-row-dragging {
  z-index: 2;
}

.ag-stub-cell {
  display: -webkit-box;
  display: flex;
  -webkit-box-align: center;
          align-items: center;
}

/**
 ****************************
 * Cells
 ****************************
*/
.ag-cell {
  display: inline-block;
  position: absolute;
  white-space: nowrap;
  height: 100%;
}

.ag-cell-value {
  -webkit-box-flex: 1;
          flex: 1 1 auto;
}

.ag-cell-value, .ag-group-value {
  overflow: hidden;
  text-overflow: ellipsis;
}

.ag-cell-wrap-text {
  white-space: normal;
  word-break: break-all;
}

.ag-cell-wrapper {
  display: -webkit-box;
  display: flex;
  -webkit-box-align: center;
          align-items: center;
}
.ag-cell-wrapper.ag-row-group {
  -webkit-box-align: start;
          align-items: flex-start;
}

.ag-sparkline-wrapper {
  position: absolute;
  height: 100%;
  width: 100%;
  left: 0;
  top: 0;
}

.ag-full-width-row .ag-cell-wrapper.ag-row-group {
  height: 100%;
  -webkit-box-align: center;
          align-items: center;
}

.ag-cell-inline-editing {
  z-index: 1;
}
.ag-cell-inline-editing .ag-cell-wrapper,
.ag-cell-inline-editing .ag-cell-edit-wrapper,
.ag-cell-inline-editing .ag-cell-editor,
.ag-cell-inline-editing .ag-cell-editor .ag-wrapper,
.ag-cell-inline-editing .ag-cell-editor input {
  height: 100%;
  width: 100%;
  line-height: normal;
}

.ag-cell .ag-icon {
  display: inline-block;
  vertical-align: middle;
}

/**
 ****************************
 * Filters
 ****************************
*/
.ag-set-filter-item {
  display: -webkit-box;
  display: flex;
  -webkit-box-align: center;
          align-items: center;
  height: 100%;
}

.ag-set-filter-item-value {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.ag-set-filter-item-checkbox {
  display: -webkit-box;
  display: flex;
}

.ag-filter-body-wrapper {
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
          flex-direction: column;
}

.ag-filter-filter {
  -webkit-box-flex: 1;
          flex: 1 1 0px;
}

.ag-filter-condition {
  display: -webkit-box;
  display: flex;
  -webkit-box-pack: center;
          justify-content: center;
}

/**
 ****************************
 * Floating Filter
 ****************************
*/
.ag-floating-filter-body {
  position: relative;
  display: -webkit-box;
  display: flex;
  -webkit-box-flex: 1;
          flex: 1 1 auto;
  height: 100%;
}

.ag-floating-filter-full-body {
  display: -webkit-box;
  display: flex;
  -webkit-box-flex: 1;
          flex: 1 1 auto;
  height: 100%;
  width: 100%;
  -webkit-box-align: center;
          align-items: center;
  overflow: hidden;
}

.ag-floating-filter-full-body > div {
  -webkit-box-flex: 1;
          flex: 1 1 auto;
}

.ag-floating-filter-input {
  -webkit-box-align: center;
          align-items: center;
  display: -webkit-box;
  display: flex;
  width: 100%;
}
.ag-floating-filter-input > * {
  -webkit-box-flex: 1;
          flex: 1 1 auto;
}

.ag-floating-filter-button {
  display: -webkit-box;
  display: flex;
  -webkit-box-flex: 0;
          flex: none;
}

/**
 ****************************
 * Drag & Drop
 ****************************
*/
.ag-dnd-ghost {
  position: absolute;
  display: -webkit-inline-box;
  display: inline-flex;
  -webkit-box-align: center;
          align-items: center;
  cursor: move;
  white-space: nowrap;
  z-index: 9999;
}

/**
 ****************************
 * Overlay
 ****************************
*/
.ag-overlay {
  height: 100%;
  left: 0;
  pointer-events: none;
  position: absolute;
  top: 0;
  width: 100%;
}

.ag-overlay-panel {
  display: -webkit-box;
  display: flex;
  height: 100%;
  width: 100%;
}

.ag-overlay-wrapper {
  display: -webkit-box;
  display: flex;
  -webkit-box-flex: 0;
          flex: none;
  width: 100%;
  height: 100%;
  -webkit-box-align: center;
          align-items: center;
  -webkit-box-pack: center;
          justify-content: center;
  text-align: center;
}

.ag-overlay-loading-wrapper {
  pointer-events: all;
}

/**
 ****************************
 * Popup
 ****************************
*/
.ag-popup-child {
  z-index: 5;
  top: 0;
}

.ag-popup-editor {
  position: absolute;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  z-index: 1;
}

.ag-large-text-input {
  display: block;
}

/**
 ****************************
 * Virtual Lists
 ****************************
*/
.ag-virtual-list-item {
  position: absolute;
  width: 100%;
}

/**
 ****************************
 * Floating Top and Bottom
 ****************************
*/
.ag-floating-top {
  overflow: hidden;
  white-space: nowrap;
  width: 100%;
  position: relative;
  display: -webkit-box;
  display: flex;
}

.ag-pinned-left-floating-top {
  display: inline-block;
  overflow: hidden;
  position: relative;
  min-width: 0px;
}

.ag-pinned-right-floating-top {
  display: inline-block;
  overflow: hidden;
  position: relative;
  min-width: 0px;
}

.ag-floating-bottom {
  overflow: hidden;
  white-space: nowrap;
  width: 100%;
  position: relative;
  display: -webkit-box;
  display: flex;
}

.ag-pinned-left-floating-bottom {
  display: inline-block;
  overflow: hidden;
  position: relative;
  min-width: 0px;
}

.ag-pinned-right-floating-bottom {
  display: inline-block;
  overflow: hidden;
  position: relative;
  min-width: 0px;
}

/**
 ****************************
 * Sticky Top
 ****************************
*/
.ag-sticky-top {
  position: absolute;
  display: -webkit-box;
  display: flex;
  width: 100%;
}

.ag-pinned-left-sticky-top,
.ag-pinned-right-sticky-top {
  position: relative;
  height: 100%;
  overflow: hidden;
}

.ag-sticky-top-full-width-container {
  overflow: hidden;
  width: 100%;
  height: 100%;
}

/**
 ****************************
 * Dialog
 ****************************
*/
.ag-dialog, .ag-panel {
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
          flex-direction: column;
  position: relative;
  overflow: hidden;
}

.ag-panel-title-bar {
  display: -webkit-box;
  display: flex;
  -webkit-box-flex: 0;
          flex: none;
  -webkit-box-align: center;
          align-items: center;
  cursor: default;
}

.ag-panel-title-bar-title {
  -webkit-box-flex: 1;
          flex: 1 1 auto;
}

.ag-panel-title-bar-buttons {
  display: -webkit-box;
  display: flex;
}

.ag-panel-title-bar-button {
  cursor: pointer;
}

.ag-panel-content-wrapper {
  display: -webkit-box;
  display: flex;
  -webkit-box-flex: 1;
          flex: 1 1 auto;
  position: relative;
  overflow: hidden;
}

.ag-dialog {
  position: absolute;
}

.ag-resizer {
  position: absolute;
  pointer-events: none;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  z-index: 1;
}
.ag-resizer.ag-resizer-topLeft {
  top: 0;
  left: 0;
  height: 5px;
  width: 5px;
  cursor: nwse-resize;
}
.ag-resizer.ag-resizer-top {
  top: 0;
  left: 5px;
  right: 5px;
  height: 5px;
  cursor: ns-resize;
}
.ag-resizer.ag-resizer-topRight {
  top: 0;
  right: 0;
  height: 5px;
  width: 5px;
  cursor: nesw-resize;
}
.ag-resizer.ag-resizer-right {
  top: 5px;
  right: 0;
  bottom: 5px;
  width: 5px;
  cursor: ew-resize;
}
.ag-resizer.ag-resizer-bottomRight {
  bottom: 0;
  right: 0;
  height: 5px;
  width: 5px;
  cursor: nwse-resize;
}
.ag-resizer.ag-resizer-bottom {
  bottom: 0;
  left: 5px;
  right: 5px;
  height: 5px;
  cursor: ns-resize;
}
.ag-resizer.ag-resizer-bottomLeft {
  bottom: 0;
  left: 0;
  height: 5px;
  width: 5px;
  cursor: nesw-resize;
}
.ag-resizer.ag-resizer-left {
  left: 0;
  top: 5px;
  bottom: 5px;
  width: 5px;
  cursor: ew-resize;
}

/**
 ****************************
 * Tooltip
 ****************************
*/
.ag-tooltip {
  position: absolute;
  pointer-events: none;
  z-index: 99999;
}

.ag-tooltip-custom {
  position: absolute;
  pointer-events: none;
  z-index: 99999;
}

/**
 ****************************
 * Animations
 ****************************
*/
.ag-value-slide-out {
  margin-right: 5px;
  opacity: 1;
  -webkit-transition: opacity 3s, margin-right 3s;
  transition: opacity 3s, margin-right 3s;
  -webkit-transition-timing-function: linear;
          transition-timing-function: linear;
}

.ag-value-slide-out-end {
  margin-right: 10px;
  opacity: 0;
}

.ag-opacity-zero {
  opacity: 0 !important;
}

/**
 ****************************
 * Menu
 ****************************
*/
.ag-menu {
  max-height: 100%;
  overflow-y: auto;
  position: absolute;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}

.ag-menu-column-select-wrapper {
  height: 265px;
  overflow: auto;
}
.ag-menu-column-select-wrapper .ag-column-select {
  height: 100%;
}

.ag-menu-list {
  display: table;
  width: 100%;
}

.ag-menu-option, .ag-menu-separator {
  display: table-row;
}

.ag-menu-option-part, .ag-menu-separator-part {
  display: table-cell;
  vertical-align: middle;
}

.ag-menu-option-text {
  white-space: nowrap;
}

.ag-compact-menu-option {
  width: 100%;
  display: -webkit-box;
  display: flex;
  flex-wrap: nowrap;
}

.ag-compact-menu-option-text {
  white-space: nowrap;
  -webkit-box-flex: 1;
          flex: 1 1 auto;
}

/**
 ****************************
 * Rich Select
 ****************************
*/
.ag-rich-select {
  cursor: default;
  outline: none;
}

.ag-rich-select-value {
  display: -webkit-box;
  display: flex;
  -webkit-box-align: center;
          align-items: center;
}

.ag-rich-select-value-icon {
  -webkit-box-flex: 1;
          flex: 1 1 auto;
  -webkit-box-ordinal-group: 2;
          order: 1;
}
.ag-ltr .ag-rich-select-value-icon {
  text-align: right;
}
.ag-rtl .ag-rich-select-value-icon {
  text-align: left;
}

.ag-rich-select-list {
  position: relative;
}

.ag-rich-select-virtual-list-item {
  display: -webkit-box;
  display: flex;
}

.ag-rich-select-row {
  display: -webkit-box;
  display: flex;
  -webkit-box-flex: 1;
          flex: 1 1 auto;
  -webkit-box-align: center;
          align-items: center;
  white-space: nowrap;
}

/**
 ****************************
 * Pagination
 ****************************
*/
.ag-paging-panel {
  -webkit-box-align: center;
          align-items: center;
  display: -webkit-box;
  display: flex;
  -webkit-box-pack: end;
          justify-content: flex-end;
}

.ag-paging-page-summary-panel {
  display: -webkit-box;
  display: flex;
  -webkit-box-align: center;
          align-items: center;
}

.ag-paging-button {
  position: relative;
}

.ag-disabled .ag-paging-page-summary-panel {
  pointer-events: none;
}

/**
 ****************************
 * Tool Panel
 ****************************
*/
.ag-tool-panel-wrapper {
  display: -webkit-box;
  display: flex;
  overflow-y: auto;
  overflow-x: hidden;
  cursor: default;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}

.ag-column-select-column,
.ag-column-select-column-group,
.ag-select-agg-func-item {
  position: relative;
  -webkit-box-align: center;
          align-items: center;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
          flex-direction: row;
  flex-wrap: nowrap;
  text-overflow: ellipsis;
  white-space: nowrap;
  height: 100%;
}
.ag-column-select-column > *,
.ag-column-select-column-group > *,
.ag-select-agg-func-item > * {
  -webkit-box-flex: 0;
          flex: none;
}

.ag-column-select-checkbox {
  display: -webkit-box;
  display: flex;
}

.ag-tool-panel-horizontal-resize {
  cursor: ew-resize;
  height: 100%;
  position: absolute;
  top: 0;
  width: 5px;
  z-index: 1;
}

.ag-ltr .ag-side-bar-left .ag-tool-panel-horizontal-resize {
  right: -3px;
}
.ag-rtl .ag-side-bar-left .ag-tool-panel-horizontal-resize {
  left: -3px;
}

.ag-ltr .ag-side-bar-right .ag-tool-panel-horizontal-resize {
  left: -3px;
}
.ag-rtl .ag-side-bar-right .ag-tool-panel-horizontal-resize {
  right: -3px;
}

.ag-details-row {
  width: 100%;
}

.ag-details-row-fixed-height {
  height: 100%;
}

.ag-details-grid {
  width: 100%;
}

.ag-details-grid-fixed-height {
  height: 100%;
}

.ag-header-group-cell {
  display: -webkit-box;
  display: flex;
  -webkit-box-align: center;
          align-items: center;
  height: 100%;
  position: absolute;
}

.ag-cell-label-container {
  display: -webkit-box;
  display: flex;
  -webkit-box-pack: justify;
          justify-content: space-between;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: reverse;
          flex-direction: row-reverse;
  -webkit-box-align: center;
          align-items: center;
  height: 100%;
  width: 100%;
  overflow: hidden;
  padding: 5px 0px;
}

.ag-right-aligned-header .ag-cell-label-container {
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
          flex-direction: row;
}

/**
 ****************************
 * Side Bar
 ****************************
*/
.ag-side-bar {
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: reverse;
          flex-direction: row-reverse;
}

.ag-side-bar-left {
  -webkit-box-ordinal-group: 0;
          order: -1;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
          flex-direction: row;
}

.ag-side-button-button {
  position: relative;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
          flex-direction: column;
  -webkit-box-align: center;
          align-items: center;
  -webkit-box-pack: center;
          justify-content: center;
  flex-wrap: nowrap;
  white-space: nowrap;
  outline: none;
  cursor: pointer;
}

.ag-side-button-label {
  -webkit-writing-mode: vertical-lr;
          writing-mode: vertical-lr;
}

/**
 ****************************
 * Status Bar
 ****************************
*/
.ag-status-bar {
  display: -webkit-box;
  display: flex;
  -webkit-box-pack: justify;
          justify-content: space-between;
  overflow: hidden;
}

.ag-status-panel {
  display: -webkit-inline-box;
  display: inline-flex;
}

.ag-status-name-value {
  white-space: nowrap;
}

.ag-status-bar-left {
  display: -webkit-inline-box;
  display: inline-flex;
}

.ag-status-bar-center {
  display: -webkit-inline-box;
  display: inline-flex;
}

.ag-status-bar-right {
  display: -webkit-inline-box;
  display: inline-flex;
}

/**
 ****************************
 * Widgets
 ****************************
*/
.ag-icon {
  display: block;
  speak: none;
}

.ag-group {
  position: relative;
  width: 100%;
}

.ag-group-title-bar {
  display: -webkit-box;
  display: flex;
  -webkit-box-align: center;
          align-items: center;
}

.ag-group-title {
  display: block;
  -webkit-box-flex: 1;
          flex: 1 1 auto;
  min-width: 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.ag-group-title-bar .ag-group-title {
  cursor: default;
}

.ag-group-toolbar {
  display: -webkit-box;
  display: flex;
  -webkit-box-align: center;
          align-items: center;
}

.ag-group-container {
  display: -webkit-box;
  display: flex;
}

.ag-disabled .ag-group-container {
  pointer-events: none;
}

.ag-group-container-horizontal {
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
          flex-direction: row;
  flex-wrap: wrap;
}

.ag-group-container-vertical {
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
          flex-direction: column;
}

.ag-column-group-icons {
  display: block;
}
.ag-column-group-icons > * {
  cursor: pointer;
}

.ag-group-item-alignment-stretch .ag-group-item {
  -webkit-box-align: stretch;
          align-items: stretch;
}

.ag-group-item-alignment-start .ag-group-item {
  -webkit-box-align: start;
          align-items: flex-start;
}

.ag-group-item-alignment-end .ag-group-item {
  -webkit-box-align: end;
          align-items: flex-end;
}

.ag-toggle-button-icon {
  -webkit-transition: right 0.3s;
  transition: right 0.3s;
  position: absolute;
  top: -1px;
}

.ag-input-field, .ag-select {
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
          flex-direction: row;
  -webkit-box-align: center;
          align-items: center;
}

.ag-input-field-input {
  -webkit-box-flex: 1;
          flex: 1 1 auto;
  width: 100%;
  min-width: 0;
}

.ag-floating-filter-input .ag-input-field-input[type=date] {
  width: 1px;
}

.ag-range-field {
  display: -webkit-box;
  display: flex;
  -webkit-box-align: center;
          align-items: center;
}

.ag-angle-select {
  display: -webkit-box;
  display: flex;
  -webkit-box-align: center;
          align-items: center;
}

.ag-angle-select-wrapper {
  display: -webkit-box;
  display: flex;
}

.ag-angle-select-parent-circle {
  display: block;
  position: relative;
}

.ag-angle-select-child-circle {
  position: absolute;
}

.ag-slider-wrapper {
  display: -webkit-box;
  display: flex;
}
.ag-slider-wrapper .ag-input-field {
  -webkit-box-flex: 1;
          flex: 1 1 auto;
}

.ag-picker-field-display {
  -webkit-box-flex: 1;
          flex: 1 1 auto;
}

.ag-picker-field {
  display: -webkit-box;
  display: flex;
  -webkit-box-align: center;
          align-items: center;
}

.ag-picker-field-icon {
  display: -webkit-box;
  display: flex;
  border: 0;
  padding: 0;
  margin: 0;
  cursor: pointer;
}

.ag-picker-field-wrapper {
  overflow: hidden;
}

.ag-label-align-right .ag-label {
  -webkit-box-ordinal-group: 2;
          order: 1;
}
.ag-label-align-right > * {
  -webkit-box-flex: 0;
          flex: none;
}

.ag-label-align-top {
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
          flex-direction: column;
  -webkit-box-align: start;
          align-items: flex-start;
}
.ag-label-align-top > * {
  align-self: stretch;
}

.ag-color-panel {
  width: 100%;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
          flex-direction: column;
  text-align: center;
}

.ag-spectrum-color {
  -webkit-box-flex: 1;
          flex: 1 1 auto;
  position: relative;
  overflow: hidden;
  cursor: default;
}

.ag-spectrum-fill {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.ag-spectrum-val {
  cursor: pointer;
}

.ag-spectrum-dragger {
  position: absolute;
  pointer-events: none;
  cursor: pointer;
}

.ag-spectrum-hue {
  cursor: default;
  background: -webkit-gradient(linear, right top, left top, color-stop(3%, #ff0000), color-stop(17%, #ffff00), color-stop(33%, #00ff00), color-stop(50%, #00ffff), color-stop(67%, #0000ff), color-stop(83%, #ff00ff), to(#ff0000));
  background: linear-gradient(to left, #ff0000 3%, #ffff00 17%, #00ff00 33%, #00ffff 50%, #0000ff 67%, #ff00ff 83%, #ff0000 100%);
}

.ag-spectrum-alpha {
  cursor: default;
}

.ag-spectrum-hue-background {
  width: 100%;
  height: 100%;
}

.ag-spectrum-alpha-background {
  background-image: -webkit-gradient(linear, left top, right top, from(rgba(0, 0, 0, 0)), to(rgb(0, 0, 0)));
  background-image: linear-gradient(to right, rgba(0, 0, 0, 0), rgb(0, 0, 0));
  width: 100%;
  height: 100%;
}

.ag-spectrum-tool {
  cursor: pointer;
}

.ag-spectrum-slider {
  position: absolute;
  pointer-events: none;
}

.ag-recent-colors {
  display: -webkit-box;
  display: flex;
}

.ag-recent-color {
  cursor: pointer;
}

.ag-ltr .ag-column-select-indent-1 {
  padding-left: 20px;
}
.ag-rtl .ag-column-select-indent-1 {
  padding-right: 20px;
}

.ag-ltr .ag-row-group-indent-1 {
  padding-left: 20px;
}
.ag-rtl .ag-row-group-indent-1 {
  padding-right: 20px;
}

.ag-ltr .ag-column-select-indent-2 {
  padding-left: 40px;
}
.ag-rtl .ag-column-select-indent-2 {
  padding-right: 40px;
}

.ag-ltr .ag-row-group-indent-2 {
  padding-left: 40px;
}
.ag-rtl .ag-row-group-indent-2 {
  padding-right: 40px;
}

.ag-ltr .ag-column-select-indent-3 {
  padding-left: 60px;
}
.ag-rtl .ag-column-select-indent-3 {
  padding-right: 60px;
}

.ag-ltr .ag-row-group-indent-3 {
  padding-left: 60px;
}
.ag-rtl .ag-row-group-indent-3 {
  padding-right: 60px;
}

.ag-ltr .ag-column-select-indent-4 {
  padding-left: 80px;
}
.ag-rtl .ag-column-select-indent-4 {
  padding-right: 80px;
}

.ag-ltr .ag-row-group-indent-4 {
  padding-left: 80px;
}
.ag-rtl .ag-row-group-indent-4 {
  padding-right: 80px;
}

.ag-ltr .ag-column-select-indent-5 {
  padding-left: 100px;
}
.ag-rtl .ag-column-select-indent-5 {
  padding-right: 100px;
}

.ag-ltr .ag-row-group-indent-5 {
  padding-left: 100px;
}
.ag-rtl .ag-row-group-indent-5 {
  padding-right: 100px;
}

.ag-ltr .ag-column-select-indent-6 {
  padding-left: 120px;
}
.ag-rtl .ag-column-select-indent-6 {
  padding-right: 120px;
}

.ag-ltr .ag-row-group-indent-6 {
  padding-left: 120px;
}
.ag-rtl .ag-row-group-indent-6 {
  padding-right: 120px;
}

.ag-ltr .ag-column-select-indent-7 {
  padding-left: 140px;
}
.ag-rtl .ag-column-select-indent-7 {
  padding-right: 140px;
}

.ag-ltr .ag-row-group-indent-7 {
  padding-left: 140px;
}
.ag-rtl .ag-row-group-indent-7 {
  padding-right: 140px;
}

.ag-ltr .ag-column-select-indent-8 {
  padding-left: 160px;
}
.ag-rtl .ag-column-select-indent-8 {
  padding-right: 160px;
}

.ag-ltr .ag-row-group-indent-8 {
  padding-left: 160px;
}
.ag-rtl .ag-row-group-indent-8 {
  padding-right: 160px;
}

.ag-ltr .ag-column-select-indent-9 {
  padding-left: 180px;
}
.ag-rtl .ag-column-select-indent-9 {
  padding-right: 180px;
}

.ag-ltr .ag-row-group-indent-9 {
  padding-left: 180px;
}
.ag-rtl .ag-row-group-indent-9 {
  padding-right: 180px;
}

.ag-ltr {
  direction: ltr;
}
.ag-ltr .ag-body, .ag-ltr .ag-floating-top, .ag-ltr .ag-floating-bottom, .ag-ltr .ag-header, .ag-ltr .ag-body-viewport, .ag-ltr .ag-body-horizontal-scroll {
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
          flex-direction: row;
}

.ag-rtl {
  direction: rtl;
}
.ag-rtl .ag-body, .ag-rtl .ag-floating-top, .ag-rtl .ag-floating-bottom, .ag-rtl .ag-header, .ag-rtl .ag-body-viewport, .ag-rtl .ag-body-horizontal-scroll {
  -webkit-box-orient: horizontal;
  -webkit-box-direction: reverse;
          flex-direction: row-reverse;
}
.ag-rtl .ag-icon-contracted,
.ag-rtl .ag-icon-expanded,
.ag-rtl .ag-icon-tree-closed {
  display: block;
  -webkit-transform: rotate(180deg);
          transform: rotate(180deg);
}

.ag-layout-print.ag-body-viewport {
  -webkit-box-flex: 0;
          flex: none;
}
.ag-layout-print.ag-root-wrapper {
  display: -webkit-inline-box;
  display: inline-flex;
}
.ag-layout-print .ag-center-cols-clipper {
  min-width: 100%;
}
.ag-layout-print .ag-body-horizontal-scroll {
  display: none;
}
.ag-layout-print.ag-force-vertical-scroll {
  overflow-y: visible !important;
}

@media print {
  .ag-root-wrapper.ag-layout-print {
    display: table;
  }
  .ag-root-wrapper.ag-layout-print .ag-root-wrapper-body,
.ag-root-wrapper.ag-layout-print .ag-root,
.ag-root-wrapper.ag-layout-print .ag-body-viewport,
.ag-root-wrapper.ag-layout-print .ag-center-cols-container,
.ag-root-wrapper.ag-layout-print .ag-center-cols-viewport,
.ag-root-wrapper.ag-layout-print .ag-center-cols-clipper,
.ag-root-wrapper.ag-layout-print .ag-body-horizontal-scroll-viewport,
.ag-root-wrapper.ag-layout-print .ag-virtual-list-viewport {
    height: auto !important;
    overflow: hidden !important;
    display: block !important;
  }
  .ag-root-wrapper.ag-layout-print .ag-row, .ag-root-wrapper.ag-layout-print .ag-cell {
    -webkit-column-break-inside: avoid;
       -moz-column-break-inside: avoid;
            break-inside: avoid;
  }
}
.ag-body .ag-body-viewport {
  -webkit-overflow-scrolling: touch;
}

.ag-chart {
  position: relative;
  display: -webkit-box;
  display: flex;
  overflow: hidden;
  width: 100%;
  height: 100%;
}

.ag-chart-components-wrapper {
  position: relative;
  display: -webkit-box;
  display: flex;
  -webkit-box-flex: 1;
          flex: 1 1 auto;
  overflow: hidden;
}

.ag-chart-title-edit {
  position: absolute;
  display: none;
  top: 0;
  left: 0;
  text-align: center;
}

.ag-chart-title-edit.currently-editing {
  display: inline-block;
}

.ag-chart-canvas-wrapper {
  position: relative;
  -webkit-box-flex: 1;
          flex: 1 1 auto;
  overflow: hidden;
}

.ag-charts-canvas {
  display: block;
}

.ag-chart-menu {
  position: absolute;
  top: 10px;
  width: 24px;
  overflow: hidden;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
          flex-direction: column;
}
.ag-ltr .ag-chart-menu {
  right: 20px;
}
.ag-rtl .ag-chart-menu {
  left: 20px;
}

.ag-chart-docked-container {
  position: relative;
  width: 0;
  min-width: 0;
  -webkit-transition: min-width 0.4s;
  transition: min-width 0.4s;
}

.ag-chart-menu-hidden ~ .ag-chart-docked-container {
  max-width: 0;
  overflow: hidden;
}

.ag-chart-tabbed-menu {
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
          flex-direction: column;
  overflow: hidden;
}

.ag-chart-tabbed-menu-header {
  -webkit-box-flex: 0;
          flex: none;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  cursor: default;
}

.ag-chart-tabbed-menu-body {
  display: -webkit-box;
  display: flex;
  -webkit-box-flex: 1;
          flex: 1 1 auto;
  -webkit-box-align: stretch;
          align-items: stretch;
  overflow: hidden;
}

.ag-chart-tab {
  width: 100%;
  overflow: hidden;
  overflow-y: auto;
}

.ag-chart-settings {
  overflow-x: hidden;
}

.ag-chart-settings-wrapper {
  position: relative;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
          flex-direction: column;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: flex;
  overflow: hidden;
}

.ag-chart-settings-nav-bar {
  display: -webkit-box;
  display: flex;
  -webkit-box-align: center;
          align-items: center;
  width: 100%;
  height: 30px;
  padding: 0 10px;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}

.ag-chart-settings-card-selector {
  display: -webkit-box;
  display: flex;
  -webkit-box-align: center;
          align-items: center;
  justify-content: space-around;
  -webkit-box-flex: 1;
          flex: 1 1 auto;
  height: 100%;
  padding: 0 10px;
}

.ag-chart-settings-card-item {
  cursor: pointer;
  width: 10px;
  height: 10px;
  background-color: #000;
  position: relative;
}
.ag-chart-settings-card-item.ag-not-selected {
  opacity: 0.2;
}
.ag-chart-settings-card-item::before {
  content: " ";
  display: block;
  position: absolute;
  background-color: transparent;
  left: 50%;
  top: 50%;
  margin-left: -10px;
  margin-top: -10px;
  width: 20px;
  height: 20px;
}

.ag-chart-settings-prev,
.ag-chart-settings-next {
  position: relative;
  -webkit-box-flex: 0;
          flex: none;
}

.ag-chart-settings-prev-button,
.ag-chart-settings-next-button {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
  opacity: 0;
}

.ag-chart-settings-mini-charts-container {
  position: relative;
  -webkit-box-flex: 1;
          flex: 1 1 auto;
  overflow-x: hidden;
  overflow-y: auto;
}

.ag-chart-settings-mini-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
          flex-direction: column;
  width: 100%;
  min-height: 100%;
  overflow: hidden;
}
.ag-chart-settings-mini-wrapper.ag-animating {
  -webkit-transition: left 0.3s;
  transition: left 0.3s;
  -webkit-transition-timing-function: ease-in-out;
          transition-timing-function: ease-in-out;
}

.ag-chart-mini-thumbnail {
  cursor: pointer;
}

.ag-chart-mini-thumbnail-canvas {
  display: block;
}

.ag-chart-data-wrapper,
.ag-chart-format-wrapper {
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
          flex-direction: column;
  position: relative;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}

.ag-chart-data-wrapper {
  height: 100%;
  overflow-y: auto;
}

.ag-chart-data-section,
.ag-chart-format-section {
  display: -webkit-box;
  display: flex;
  margin: 0;
}

.ag-chart-empty-text {
  display: -webkit-box;
  display: flex;
  top: 0;
  width: 100%;
  height: 100%;
  -webkit-box-align: center;
          align-items: center;
  -webkit-box-pack: center;
          justify-content: center;
}

.ag-chart-menu-hidden:hover .ag-chart-menu {
  display: block;
}

.ag-chart-menu-close {
  display: none;
}

.ag-chart .ag-chart-tool-panel-button-enable .ag-chart-menu {
  display: -webkit-box;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
          flex-direction: row;
  overflow: auto;
  top: 5px;
  gap: 7px;
  width: auto;
}
.ag-ltr .ag-chart .ag-chart-tool-panel-button-enable .ag-chart-menu {
  right: 10px;
  -webkit-box-pack: right;
          justify-content: right;
}
.ag-rtl .ag-chart .ag-chart-tool-panel-button-enable .ag-chart-menu {
  left: 10px;
  -webkit-box-pack: left;
          justify-content: left;
}

.ag-chart-tool-panel-button-enable .ag-chart-menu-close {
  position: absolute;
  top: 45%;
  padding: 0;
  display: block;
  cursor: pointer;
  border: none;
}
.ag-ltr .ag-chart-tool-panel-button-enable .ag-chart-menu-close {
  right: 0px;
}
.ag-rtl .ag-chart-tool-panel-button-enable .ag-chart-menu-close {
  left: 0px;
}
.ag-chart-tool-panel-button-enable .ag-chart-menu-close .ag-icon {
  padding: 9px 0 9px 0;
}
.ag-chart-tool-panel-button-enable .ag-icon-menu {
  display: none;
}

.ag-charts-font-size-color {
  display: -webkit-box;
  display: flex;
  align-self: stretch;
  -webkit-box-pack: justify;
          justify-content: space-between;
}

.ag-charts-data-group-item {
  position: relative;
}

.ag-date-time-list-page-title-bar {
  display: -webkit-box;
  display: flex;
}

.ag-date-time-list-page-column-labels-row,
.ag-date-time-list-page-entries-row {
  display: -webkit-box;
  display: flex;
}

.ag-date-time-list-page-column-label,
.ag-date-time-list-page-entry {
  flex-basis: 0;
  -webkit-box-flex: 1;
          flex-grow: 1;
}

.ag-date-time-list-page-entry {
  cursor: pointer;
}
.ag-theme-alpine {
  -webkit-font-smoothing: antialiased;
  color: #181d1f;
  color: var(--ag-foreground-color, #181d1f);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
  font-size: 13px;
  line-height: normal;
}
@font-face {
  font-family: "agGridAlpine";
  src: url("data:application/font-woff;charset=utf-8;base64,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") format("woff");
  font-weight: normal;
  font-style: normal;
}
.ag-theme-alpine .ag-icon {
  font-family: "agGridAlpine";
  font-size: 16px;
  line-height: 16px;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.ag-theme-alpine .ag-icon-aggregation::before {
  content: "\f101";
}
.ag-theme-alpine .ag-icon-arrows::before {
  content: "\f102";
}
.ag-theme-alpine .ag-icon-asc::before {
  content: "\f103";
}
.ag-theme-alpine .ag-icon-cancel::before {
  content: "\f104";
}
.ag-theme-alpine .ag-icon-chart::before {
  content: "\f105";
}
.ag-theme-alpine .ag-icon-color-picker::before {
  content: "\f109";
}
.ag-theme-alpine .ag-icon-columns::before {
  content: "\f10a";
}
.ag-theme-alpine .ag-icon-contracted::before {
  content: "\f10b";
}
.ag-theme-alpine .ag-icon-copy::before {
  content: "\f10c";
}
.ag-theme-alpine .ag-icon-cross::before {
  content: "\f10d";
}
.ag-theme-alpine .ag-icon-csv::before {
  content: "\f10e";
}
.ag-theme-alpine .ag-icon-desc::before {
  content: "\f10f";
}
.ag-theme-alpine .ag-icon-excel::before {
  content: "\f110";
}
.ag-theme-alpine .ag-icon-expanded::before {
  content: "\f111";
}
.ag-theme-alpine .ag-icon-eye-slash::before {
  content: "\f112";
}
.ag-theme-alpine .ag-icon-eye::before {
  content: "\f113";
}
.ag-theme-alpine .ag-icon-filter::before {
  content: "\f114";
}
.ag-theme-alpine .ag-icon-first::before {
  content: "\f115";
}
.ag-theme-alpine .ag-icon-grip::before {
  content: "\f116";
}
.ag-theme-alpine .ag-icon-group::before {
  content: "\f117";
}
.ag-theme-alpine .ag-icon-last::before {
  content: "\f118";
}
.ag-theme-alpine .ag-icon-left::before {
  content: "\f119";
}
.ag-theme-alpine .ag-icon-linked::before {
  content: "\f11a";
}
.ag-theme-alpine .ag-icon-loading::before {
  content: "\f11b";
}
.ag-theme-alpine .ag-icon-maximize::before {
  content: "\f11c";
}
.ag-theme-alpine .ag-icon-menu::before {
  content: "\f11d";
}
.ag-theme-alpine .ag-icon-minimize::before {
  content: "\f11e";
}
.ag-theme-alpine .ag-icon-next::before {
  content: "\f11f";
}
.ag-theme-alpine .ag-icon-none::before {
  content: "\f120";
}
.ag-theme-alpine .ag-icon-not-allowed::before {
  content: "\f121";
}
.ag-theme-alpine .ag-icon-paste::before {
  content: "\f122";
}
.ag-theme-alpine .ag-icon-pin::before {
  content: "\f123";
}
.ag-theme-alpine .ag-icon-pivot::before {
  content: "\f124";
}
.ag-theme-alpine .ag-icon-previous::before {
  content: "\f125";
}
.ag-theme-alpine .ag-icon-right::before {
  content: "\f128";
}
.ag-theme-alpine .ag-icon-save::before {
  content: "\f129";
}
.ag-theme-alpine .ag-icon-small-down::before {
  content: "\f12a";
}
.ag-theme-alpine .ag-icon-small-left::before {
  content: "\f12b";
}
.ag-theme-alpine .ag-icon-small-right::before {
  content: "\f12c";
}
.ag-theme-alpine .ag-icon-small-up::before {
  content: "\f12d";
}
.ag-theme-alpine .ag-icon-tick::before {
  content: "\f12e";
}
.ag-theme-alpine .ag-icon-tree-closed::before {
  content: "\f12f";
}
.ag-theme-alpine .ag-icon-tree-indeterminate::before {
  content: "\f130";
}
.ag-theme-alpine .ag-icon-tree-open::before {
  content: "\f131";
}
.ag-theme-alpine .ag-icon-unlinked::before {
  content: "\f132";
}
.ag-theme-alpine .ag-icon-row-drag::before {
  content: "\f116";
}
.ag-theme-alpine .ag-left-arrow::before {
  content: "\f119";
}
.ag-theme-alpine .ag-right-arrow::before {
  content: "\f128";
}
.ag-theme-alpine .ag-root-wrapper, .ag-theme-alpine .ag-sticky-top {
  background-color: #fff;
  background-color: var(--ag-background-color, #fff);
}
.ag-theme-alpine [class^=ag-], .ag-theme-alpine [class^=ag-]:focus, .ag-theme-alpine [class^=ag-]:after, .ag-theme-alpine [class^=ag-]:before {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  outline: none;
}
.ag-theme-alpine [class^=ag-]::-ms-clear {
  display: none;
}
.ag-theme-alpine .ag-checkbox .ag-input-wrapper,
.ag-theme-alpine .ag-radio-button .ag-input-wrapper {
  overflow: visible;
}
.ag-theme-alpine .ag-range-field .ag-input-wrapper {
  height: 100%;
}
.ag-theme-alpine .ag-toggle-button {
  -webkit-box-flex: 0;
          flex: none;
  width: unset;
  min-width: unset;
}
.ag-theme-alpine .ag-ltr .ag-label-align-right .ag-label {
  margin-left: 6px;
}

.ag-theme-alpine .ag-rtl .ag-label-align-right .ag-label {
  margin-right: 6px;
}

.ag-theme-alpine input[class^=ag-] {
  margin: 0;
  background-color: #fff;
  background-color: var(--ag-background-color, #fff);
}
.ag-theme-alpine textarea[class^=ag-],
.ag-theme-alpine select[class^=ag-] {
  background-color: #fff;
  background-color: var(--ag-background-color, #fff);
}
.ag-theme-alpine input[class^=ag-]:not([type]),
.ag-theme-alpine input[class^=ag-][type=text],
.ag-theme-alpine input[class^=ag-][type=number],
.ag-theme-alpine input[class^=ag-][type=tel],
.ag-theme-alpine input[class^=ag-][type=date],
.ag-theme-alpine input[class^=ag-][type=datetime-local],
.ag-theme-alpine textarea[class^=ag-] {
  font-size: inherit;
  line-height: inherit;
  color: inherit;
  border-width: 1px;
  border-style: solid;
  border-color: #babfc7;
  border-color: var(--ag-input-border-color, var(--ag-border-color, #babfc7));
}
.ag-theme-alpine input[class^=ag-]:not([type]):disabled,
.ag-theme-alpine input[class^=ag-][type=text]:disabled,
.ag-theme-alpine input[class^=ag-][type=number]:disabled,
.ag-theme-alpine input[class^=ag-][type=tel]:disabled,
.ag-theme-alpine input[class^=ag-][type=date]:disabled,
.ag-theme-alpine input[class^=ag-][type=datetime-local]:disabled,
.ag-theme-alpine textarea[class^=ag-]:disabled {
  color: rgba(24, 29, 31, 0.5);
  color: var(--ag-disabled-foreground-color, rgba(24, 29, 31, 0.5));
  background-color: #f1f2f4;
  background-color: var(--ag-input-disabled-background-color, #f1f2f4);
  border-color: rgba(186, 191, 199, 0.3);
  border-color: var(--ag-input-disabled-border-color, rgba(186, 191, 199, 0.3));
}
.ag-theme-alpine input[class^=ag-]:not([type]):focus,
.ag-theme-alpine input[class^=ag-][type=text]:focus,
.ag-theme-alpine input[class^=ag-][type=number]:focus,
.ag-theme-alpine input[class^=ag-][type=tel]:focus,
.ag-theme-alpine input[class^=ag-][type=date]:focus,
.ag-theme-alpine input[class^=ag-][type=datetime-local]:focus,
.ag-theme-alpine textarea[class^=ag-]:focus {
  outline: none;
  -webkit-box-shadow: 0 0 2px 0.1rem rgba(33, 150, 243, 0.4);
          box-shadow: 0 0 2px 0.1rem rgba(33, 150, 243, 0.4);
  border-color: rgba(33, 150, 243, 0.4);
  border-color: var(--ag-input-focus-border-color, rgba(33, 150, 243, 0.4));
}
.ag-theme-alpine input[class^=ag-]:not([type]):invalid,
.ag-theme-alpine input[class^=ag-][type=text]:invalid,
.ag-theme-alpine input[class^=ag-][type=number]:invalid,
.ag-theme-alpine input[class^=ag-][type=tel]:invalid,
.ag-theme-alpine input[class^=ag-][type=date]:invalid,
.ag-theme-alpine input[class^=ag-][type=datetime-local]:invalid,
.ag-theme-alpine textarea[class^=ag-]:invalid {
  border-width: 2px;
  border-style: solid;
  border-color: #e02525;
  border-color: var(--ag-input-border-color-invalid, var(--ag-invalid-color, #e02525));
}
.ag-theme-alpine input[class^=ag-][type=number] {
  -moz-appearance: textfield;
}
.ag-theme-alpine input[class^=ag-][type=number]::-webkit-outer-spin-button, .ag-theme-alpine input[class^=ag-][type=number]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
.ag-theme-alpine input[class^=ag-][type=range] {
  padding: 0;
}
.ag-theme-alpine input[class^=ag-][type=button]:focus, .ag-theme-alpine button[class^=ag-]:focus {
  -webkit-box-shadow: 0 0 2px 0.1rem rgba(33, 150, 243, 0.4);
          box-shadow: 0 0 2px 0.1rem rgba(33, 150, 243, 0.4);
}
.ag-theme-alpine .ag-drag-handle {
  color: #181d1f;
  color: var(--ag-secondary-foreground-color, var(--ag-foreground-color, #181d1f));
}
.ag-theme-alpine .ag-list-item, .ag-theme-alpine .ag-virtual-list-item {
  height: 24px;
}
.ag-theme-alpine .ag-keyboard-focus .ag-virtual-list-item:focus {
  outline: none;
}
.ag-theme-alpine .ag-keyboard-focus .ag-virtual-list-item:focus::after {
  content: "";
  position: absolute;
  background-color: transparent;
  pointer-events: none;
  top: 4px;
  left: 4px;
  display: block;
  width: calc(100% - 8px);
  height: calc(100% - 8px);
  border: 1px solid;
  border-color: rgba(33, 150, 243, 0.4);
  border-color: var(--ag-input-focus-border-color, rgba(33, 150, 243, 0.4));
}
.ag-theme-alpine .ag-select-list {
  background-color: #fff;
  background-color: var(--ag-background-color, #fff);
  overflow-y: auto;
  overflow-x: hidden;
}
.ag-theme-alpine .ag-list-item {
  display: -webkit-box;
  display: flex;
  -webkit-box-align: center;
          align-items: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.ag-theme-alpine .ag-list-item.ag-active-item {
  background-color: rgba(33, 150, 243, 0.1);
  background-color: var(--ag-row-hover-color, rgba(33, 150, 243, 0.1));
}
.ag-theme-alpine .ag-select-list-item {
  padding-left: 4px;
  padding-right: 4px;
  cursor: default;
  -moz-user-select: none;
  -webkit-user-select: none;
  user-select: none;
}
.ag-theme-alpine .ag-select-list-item span {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.ag-theme-alpine .ag-select .ag-picker-field-wrapper {
  background-color: #fff;
  background-color: var(--ag-background-color, #fff);
  min-height: 24px;
  cursor: default;
}
.ag-theme-alpine .ag-select.ag-disabled .ag-picker-field-wrapper:focus {
  -webkit-box-shadow: none;
          box-shadow: none;
}
.ag-theme-alpine .ag-select:not(.ag-cell-editor) {
  height: 24px;
}
.ag-theme-alpine .ag-select .ag-picker-field-display {
  margin: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.ag-theme-alpine .ag-select .ag-picker-field-icon {
  display: -webkit-box;
  display: flex;
  -webkit-box-align: center;
          align-items: center;
}
.ag-theme-alpine .ag-select.ag-disabled {
  opacity: 0.5;
}
.ag-theme-alpine .ag-rich-select {
  background-color: #f8f8f8;
  background-color: var(--ag-control-panel-background-color, #f8f8f8);
}
.ag-theme-alpine .ag-rich-select-list {
  width: 100%;
  min-width: 200px;
  height: 273px;
}
.ag-theme-alpine .ag-rich-select-value {
  padding: 0 6px 0 18px;
  height: 42px;
  border-bottom: solid 1px;
  border-bottom-color: #dde2eb;
  border-bottom-color: var(--ag-secondary-border-color, #dde2eb);
}
.ag-theme-alpine .ag-rich-select-virtual-list-item {
  cursor: default;
  height: 24px;
}
.ag-keyboard-focus .ag-theme-alpine .ag-rich-select-virtual-list-item:focus::after {
  content: none;
}
.ag-theme-alpine .ag-rich-select-virtual-list-item:hover {
  background-color: rgba(33, 150, 243, 0.1);
  background-color: var(--ag-row-hover-color, rgba(33, 150, 243, 0.1));
}
.ag-theme-alpine .ag-rich-select-row {
  padding-left: 18px;
}
.ag-theme-alpine .ag-rich-select-row-selected {
  background-color: rgba(33, 150, 243, 0.3);
  background-color: var(--ag-selected-row-background-color, rgba(33, 150, 243, 0.3));
}
.ag-theme-alpine .ag-row-drag,
.ag-theme-alpine .ag-selection-checkbox,
.ag-theme-alpine .ag-group-expanded,
.ag-theme-alpine .ag-group-contracted {
  color: #181d1f;
  color: var(--ag-secondary-foreground-color, var(--ag-foreground-color, #181d1f));
}
.ag-theme-alpine .ag-ltr .ag-row-drag, .ag-theme-alpine .ag-ltr .ag-selection-checkbox, .ag-theme-alpine .ag-ltr .ag-group-expanded, .ag-theme-alpine .ag-ltr .ag-group-contracted {
  margin-right: 12px;
}

.ag-theme-alpine .ag-rtl .ag-row-drag, .ag-theme-alpine .ag-rtl .ag-selection-checkbox, .ag-theme-alpine .ag-rtl .ag-group-expanded, .ag-theme-alpine .ag-rtl .ag-group-contracted {
  margin-left: 12px;
}

.ag-theme-alpine .ag-cell-wrapper > *:not(.ag-cell-value):not(.ag-group-value) {
  --ag-internal-calculated-line-height: var(--ag-line-height, 40px);
  --ag-internal-padded-row-height: 40px;
  height: min(var(--ag-internal-calculated-line-height), var(--ag-internal-padded-row-height));
  display: -webkit-box;
  display: flex;
  -webkit-box-align: center;
          align-items: center;
  -webkit-box-flex: 0;
          flex: none;
}
.ag-theme-alpine .ag-group-expanded,
.ag-theme-alpine .ag-group-contracted {
  cursor: pointer;
}
.ag-theme-alpine .ag-group-title-bar-icon {
  cursor: pointer;
  -webkit-box-flex: 0;
          flex: none;
  color: #181d1f;
  color: var(--ag-secondary-foreground-color, var(--ag-foreground-color, #181d1f));
}
.ag-theme-alpine .ag-ltr .ag-group-child-count {
  margin-left: 2px;
}

.ag-theme-alpine .ag-rtl .ag-group-child-count {
  margin-right: 2px;
}

.ag-theme-alpine .ag-group-title-bar {
  background-color: #fff;
  background-color: var(--ag-subheader-background-color, #fff);
  padding: 6px;
}
.ag-theme-alpine .ag-group-toolbar {
  padding: 6px;
}
.ag-theme-alpine .ag-disabled-group-title-bar, .ag-theme-alpine .ag-disabled-group-container {
  opacity: 0.5;
}
.ag-theme-alpine .group-item {
  margin: 3px 0;
}
.ag-theme-alpine .ag-label {
  white-space: nowrap;
}
.ag-theme-alpine .ag-ltr .ag-label {
  margin-right: 6px;
}

.ag-theme-alpine .ag-rtl .ag-label {
  margin-left: 6px;
}

.ag-theme-alpine .ag-label-align-top .ag-label {
  margin-bottom: 3px;
}
.ag-theme-alpine .ag-angle-select[disabled] {
  color: rgba(24, 29, 31, 0.5);
  color: var(--ag-disabled-foreground-color, rgba(24, 29, 31, 0.5));
  pointer-events: none;
}
.ag-theme-alpine .ag-angle-select[disabled] .ag-angle-select-field {
  opacity: 0.4;
}
.ag-theme-alpine .ag-ltr .ag-slider-field, .ag-theme-alpine .ag-ltr .ag-angle-select-field {
  margin-right: 12px;
}

.ag-theme-alpine .ag-rtl .ag-slider-field, .ag-theme-alpine .ag-rtl .ag-angle-select-field {
  margin-left: 12px;
}

.ag-theme-alpine .ag-angle-select-parent-circle {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  border: solid 1px;
  border-color: #babfc7;
  border-color: var(--ag-border-color, #babfc7);
  background-color: #fff;
  background-color: var(--ag-background-color, #fff);
}
.ag-theme-alpine .ag-angle-select-child-circle {
  top: 4px;
  left: 12px;
  width: 6px;
  height: 6px;
  margin-left: -3px;
  margin-top: -4px;
  border-radius: 3px;
  background-color: #181d1f;
  background-color: var(--ag-secondary-foreground-color, var(--ag-foreground-color, #181d1f));
}
.ag-theme-alpine .ag-picker-field-wrapper {
  border: 1px solid;
  border-color: #babfc7;
  border-color: var(--ag-border-color, #babfc7);
  border-radius: 5px;
}
.ag-theme-alpine .ag-picker-field-wrapper:focus {
  -webkit-box-shadow: 0 0 2px 0.1rem rgba(33, 150, 243, 0.4);
          box-shadow: 0 0 2px 0.1rem rgba(33, 150, 243, 0.4);
}
.ag-theme-alpine .ag-picker-field-button {
  background-color: #fff;
  background-color: var(--ag-background-color, #fff);
  color: #181d1f;
  color: var(--ag-secondary-foreground-color, var(--ag-foreground-color, #181d1f));
}
.ag-theme-alpine .ag-dialog.ag-color-dialog {
  border-radius: 5px;
}
.ag-theme-alpine .ag-color-picker .ag-picker-field-display {
  height: 16px;
}
.ag-theme-alpine .ag-color-panel {
  padding: 6px;
}
.ag-theme-alpine .ag-spectrum-color {
  background-color: rgb(255, 0, 0);
  border-radius: 2px;
}
.ag-theme-alpine .ag-spectrum-tools {
  padding: 10px;
}
.ag-theme-alpine .ag-spectrum-sat {
  background-image: -webkit-gradient(linear, left top, right top, from(white), to(rgba(204, 154, 129, 0)));
  background-image: linear-gradient(to right, white, rgba(204, 154, 129, 0));
}
.ag-theme-alpine .ag-spectrum-val {
  background-image: -webkit-gradient(linear, left bottom, left top, from(black), to(rgba(204, 154, 129, 0)));
  background-image: linear-gradient(to top, black, rgba(204, 154, 129, 0));
}
.ag-theme-alpine .ag-spectrum-dragger {
  border-radius: 12px;
  height: 12px;
  width: 12px;
  border: 1px solid white;
  background: black;
  -webkit-box-shadow: 0 0 2px 0px rgba(0, 0, 0, 0.24);
          box-shadow: 0 0 2px 0px rgba(0, 0, 0, 0.24);
}
.ag-theme-alpine .ag-spectrum-hue-background {
  border-radius: 2px;
}
.ag-theme-alpine .ag-spectrum-alpha-background {
  border-radius: 2px;
}
.ag-theme-alpine .ag-spectrum-tool {
  margin-bottom: 10px;
  height: 11px;
  border-radius: 2px;
}
.ag-theme-alpine .ag-spectrum-slider {
  margin-top: -12px;
  width: 13px;
  height: 13px;
  border-radius: 13px;
  background-color: rgb(248, 248, 248);
  -webkit-box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.37);
          box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.37);
}
.ag-theme-alpine .ag-recent-color {
  margin: 0 3px;
}
.ag-theme-alpine .ag-recent-color:first-child {
  margin-left: 0;
}
.ag-theme-alpine .ag-recent-color:last-child {
  margin-right: 0;
}
.ag-theme-alpine.ag-dnd-ghost {
  border: solid 1px;
  border-color: #babfc7;
  border-color: var(--ag-border-color, #babfc7);
  background: #fff;
  background: var(--ag-background-color, #fff);
  border-radius: 3px;
  -webkit-box-shadow: 0 1px 4px 1px rgba(186, 191, 199, 0.4);
          box-shadow: 0 1px 4px 1px rgba(186, 191, 199, 0.4);
  padding: 6px;
  overflow: hidden;
  text-overflow: ellipsis;
  border: solid 1px;
  border-color: #dde2eb;
  border-color: var(--ag-secondary-border-color, #dde2eb);
  color: #181d1f;
  color: var(--ag-secondary-foreground-color, var(--ag-foreground-color, #181d1f));
  height: 48px !important;
  line-height: 48px;
  margin: 0;
  padding: 0 12px;
  -webkit-transform: translateY(12px);
          transform: translateY(12px);
}
.ag-theme-alpine .ag-dnd-ghost-icon {
  margin-right: 6px;
  color: #181d1f;
  color: var(--ag-foreground-color, #181d1f);
}
.ag-theme-alpine .ag-popup-child:not(.ag-tooltip-custom) {
  -webkit-box-shadow: 0 1px 4px 1px rgba(186, 191, 199, 0.4);
          box-shadow: 0 1px 4px 1px rgba(186, 191, 199, 0.4);
}
.ag-dragging-range-handle .ag-theme-alpine .ag-dialog, .ag-dragging-fill-handle .ag-theme-alpine .ag-dialog {
  opacity: 0.7;
  pointer-events: none;
}
.ag-theme-alpine .ag-dialog {
  border-radius: 3px;
  border: solid 1px;
  border-color: #babfc7;
  border-color: var(--ag-border-color, #babfc7);
}
.ag-theme-alpine .ag-panel {
  background-color: #fff;
  background-color: var(--ag-background-color, #fff);
}
.ag-theme-alpine .ag-panel-title-bar {
  background-color: #f8f8f8;
  background-color: var(--ag-header-background-color, #f8f8f8);
  color: #181d1f;
  color: var(--ag-header-foreground-color, var(--ag-secondary-foreground-color, var(--ag-foreground-color, #181d1f)));
  height: 48px;
  padding: 6px 18px;
  border-bottom: solid 1px;
  border-bottom-color: #babfc7;
  border-bottom-color: var(--ag-border-color, #babfc7);
}
.ag-theme-alpine .ag-ltr .ag-panel-title-bar-button {
  margin-left: 6px;
}

.ag-theme-alpine .ag-rtl .ag-panel-title-bar-button {
  margin-right: 6px;
}

.ag-theme-alpine .ag-tooltip {
  background-color: #f8f8f8;
  background-color: var(--ag-header-background-color, #f8f8f8);
  color: #181d1f;
  color: var(--ag-foreground-color, #181d1f);
  padding: 6px;
  border: solid 1px;
  border-color: #babfc7;
  border-color: var(--ag-border-color, #babfc7);
  border-radius: 3px;
  -webkit-transition: opacity 1s;
  transition: opacity 1s;
  white-space: normal;
}
.ag-theme-alpine .ag-tooltip.ag-tooltip-hiding {
  opacity: 0;
}
.ag-theme-alpine .ag-tooltip-custom {
  -webkit-transition: opacity 1s;
  transition: opacity 1s;
}
.ag-theme-alpine .ag-tooltip-custom.ag-tooltip-hiding {
  opacity: 0;
}
.ag-theme-alpine .ag-ltr .ag-column-select-indent-1 {
  padding-left: 16px;
}

.ag-theme-alpine .ag-rtl .ag-column-select-indent-1 {
  padding-right: 16px;
}

.ag-theme-alpine .ag-ltr .ag-column-select-indent-2 {
  padding-left: 32px;
}

.ag-theme-alpine .ag-rtl .ag-column-select-indent-2 {
  padding-right: 32px;
}

.ag-theme-alpine .ag-ltr .ag-column-select-indent-3 {
  padding-left: 48px;
}

.ag-theme-alpine .ag-rtl .ag-column-select-indent-3 {
  padding-right: 48px;
}

.ag-theme-alpine .ag-ltr .ag-column-select-indent-4 {
  padding-left: 64px;
}

.ag-theme-alpine .ag-rtl .ag-column-select-indent-4 {
  padding-right: 64px;
}

.ag-theme-alpine .ag-ltr .ag-column-select-indent-5 {
  padding-left: 80px;
}

.ag-theme-alpine .ag-rtl .ag-column-select-indent-5 {
  padding-right: 80px;
}

.ag-theme-alpine .ag-ltr .ag-column-select-indent-6 {
  padding-left: 96px;
}

.ag-theme-alpine .ag-rtl .ag-column-select-indent-6 {
  padding-right: 96px;
}

.ag-theme-alpine .ag-ltr .ag-column-select-indent-7 {
  padding-left: 112px;
}

.ag-theme-alpine .ag-rtl .ag-column-select-indent-7 {
  padding-right: 112px;
}

.ag-theme-alpine .ag-ltr .ag-column-select-indent-8 {
  padding-left: 128px;
}

.ag-theme-alpine .ag-rtl .ag-column-select-indent-8 {
  padding-right: 128px;
}

.ag-theme-alpine .ag-ltr .ag-column-select-indent-9 {
  padding-left: 144px;
}

.ag-theme-alpine .ag-rtl .ag-column-select-indent-9 {
  padding-right: 144px;
}

.ag-theme-alpine .ag-column-select-header-icon {
  cursor: pointer;
}
.ag-theme-alpine .ag-keyboard-focus .ag-column-select-header-icon:focus {
  outline: none;
}
.ag-theme-alpine .ag-keyboard-focus .ag-column-select-header-icon:focus::after {
  content: "";
  position: absolute;
  background-color: transparent;
  pointer-events: none;
  top: 0px;
  left: 0px;
  display: block;
  width: calc(100% - 0px);
  height: calc(100% - 0px);
  border: 1px solid;
  border-color: rgba(33, 150, 243, 0.4);
  border-color: var(--ag-input-focus-border-color, rgba(33, 150, 243, 0.4));
}
.ag-theme-alpine .ag-ltr .ag-column-group-icons:not(:last-child), .ag-theme-alpine .ag-ltr .ag-column-select-header-icon:not(:last-child), .ag-theme-alpine .ag-ltr .ag-column-select-header-checkbox:not(:last-child), .ag-theme-alpine .ag-ltr .ag-column-select-header-filter-wrapper:not(:last-child), .ag-theme-alpine .ag-ltr .ag-column-select-checkbox:not(:last-child), .ag-theme-alpine .ag-ltr .ag-column-select-column-drag-handle:not(:last-child), .ag-theme-alpine .ag-ltr .ag-column-select-column-group-drag-handle:not(:last-child), .ag-theme-alpine .ag-ltr .ag-column-select-column-label:not(:last-child) {
  margin-right: 12px;
}

.ag-theme-alpine .ag-rtl .ag-column-group-icons:not(:last-child), .ag-theme-alpine .ag-rtl .ag-column-select-header-icon:not(:last-child), .ag-theme-alpine .ag-rtl .ag-column-select-header-checkbox:not(:last-child), .ag-theme-alpine .ag-rtl .ag-column-select-header-filter-wrapper:not(:last-child), .ag-theme-alpine .ag-rtl .ag-column-select-checkbox:not(:last-child), .ag-theme-alpine .ag-rtl .ag-column-select-column-drag-handle:not(:last-child), .ag-theme-alpine .ag-rtl .ag-column-select-column-group-drag-handle:not(:last-child), .ag-theme-alpine .ag-rtl .ag-column-select-column-label:not(:last-child) {
  margin-left: 12px;
}

.ag-theme-alpine .ag-keyboard-focus .ag-column-select-virtual-list-item:focus {
  outline: none;
}
.ag-theme-alpine .ag-keyboard-focus .ag-column-select-virtual-list-item:focus::after {
  content: "";
  position: absolute;
  background-color: transparent;
  pointer-events: none;
  top: 1px;
  left: 1px;
  display: block;
  width: calc(100% - 2px);
  height: calc(100% - 2px);
  border: 1px solid;
  border-color: rgba(33, 150, 243, 0.4);
  border-color: var(--ag-input-focus-border-color, rgba(33, 150, 243, 0.4));
}
.ag-theme-alpine .ag-column-select-column-group:not(:last-child),
.ag-theme-alpine .ag-column-select-column:not(:last-child) {
  margin-bottom: 9px;
}
.ag-theme-alpine .ag-column-select-column-readonly,
.ag-theme-alpine .ag-column-select-column-group-readonly {
  color: rgba(24, 29, 31, 0.5);
  color: var(--ag-disabled-foreground-color, rgba(24, 29, 31, 0.5));
  pointer-events: none;
}
.ag-theme-alpine .ag-ltr .ag-column-select-add-group-indent {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-column-select-add-group-indent {
  margin-right: 28px;
}

.ag-theme-alpine .ag-column-select-virtual-list-viewport {
  padding: 6px 0px;
}
.ag-theme-alpine .ag-column-select-virtual-list-item {
  padding: 0 12px;
}
.ag-theme-alpine .ag-rtl {
  text-align: right;
}
.ag-theme-alpine .ag-root-wrapper {
  border: solid 1px;
  border-color: #babfc7;
  border-color: var(--ag-border-color, #babfc7);
}
.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-1 {
  padding-left: 46px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-1 {
  padding-right: 46px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-1 {
  padding-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-1 {
  padding-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-1 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-1 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-2 {
  padding-left: 74px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-2 {
  padding-right: 74px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-2 {
  padding-left: 56px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-2 {
  padding-right: 56px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-2 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-2 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-3 {
  padding-left: 102px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-3 {
  padding-right: 102px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-3 {
  padding-left: 84px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-3 {
  padding-right: 84px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-3 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-3 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-4 {
  padding-left: 130px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-4 {
  padding-right: 130px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-4 {
  padding-left: 112px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-4 {
  padding-right: 112px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-4 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-4 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-5 {
  padding-left: 158px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-5 {
  padding-right: 158px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-5 {
  padding-left: 140px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-5 {
  padding-right: 140px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-5 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-5 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-6 {
  padding-left: 186px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-6 {
  padding-right: 186px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-6 {
  padding-left: 168px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-6 {
  padding-right: 168px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-6 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-6 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-7 {
  padding-left: 214px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-7 {
  padding-right: 214px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-7 {
  padding-left: 196px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-7 {
  padding-right: 196px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-7 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-7 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-8 {
  padding-left: 242px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-8 {
  padding-right: 242px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-8 {
  padding-left: 224px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-8 {
  padding-right: 224px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-8 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-8 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-9 {
  padding-left: 270px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-9 {
  padding-right: 270px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-9 {
  padding-left: 252px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-9 {
  padding-right: 252px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-9 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-9 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-10 {
  padding-left: 298px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-10 {
  padding-right: 298px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-10 {
  padding-left: 280px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-10 {
  padding-right: 280px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-10 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-10 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-11 {
  padding-left: 326px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-11 {
  padding-right: 326px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-11 {
  padding-left: 308px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-11 {
  padding-right: 308px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-11 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-11 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-12 {
  padding-left: 354px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-12 {
  padding-right: 354px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-12 {
  padding-left: 336px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-12 {
  padding-right: 336px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-12 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-12 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-13 {
  padding-left: 382px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-13 {
  padding-right: 382px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-13 {
  padding-left: 364px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-13 {
  padding-right: 364px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-13 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-13 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-14 {
  padding-left: 410px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-14 {
  padding-right: 410px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-14 {
  padding-left: 392px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-14 {
  padding-right: 392px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-14 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-14 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-15 {
  padding-left: 438px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-15 {
  padding-right: 438px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-15 {
  padding-left: 420px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-15 {
  padding-right: 420px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-15 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-15 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-16 {
  padding-left: 466px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-16 {
  padding-right: 466px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-16 {
  padding-left: 448px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-16 {
  padding-right: 448px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-16 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-16 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-17 {
  padding-left: 494px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-17 {
  padding-right: 494px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-17 {
  padding-left: 476px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-17 {
  padding-right: 476px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-17 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-17 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-18 {
  padding-left: 522px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-18 {
  padding-right: 522px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-18 {
  padding-left: 504px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-18 {
  padding-right: 504px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-18 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-18 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-19 {
  padding-left: 550px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-19 {
  padding-right: 550px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-19 {
  padding-left: 532px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-19 {
  padding-right: 532px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-19 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-19 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-20 {
  padding-left: 578px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-20 {
  padding-right: 578px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-20 {
  padding-left: 560px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-20 {
  padding-right: 560px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-20 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-20 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-21 {
  padding-left: 606px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-21 {
  padding-right: 606px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-21 {
  padding-left: 588px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-21 {
  padding-right: 588px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-21 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-21 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-22 {
  padding-left: 634px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-22 {
  padding-right: 634px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-22 {
  padding-left: 616px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-22 {
  padding-right: 616px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-22 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-22 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-23 {
  padding-left: 662px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-23 {
  padding-right: 662px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-23 {
  padding-left: 644px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-23 {
  padding-right: 644px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-23 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-23 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-24 {
  padding-left: 690px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-24 {
  padding-right: 690px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-24 {
  padding-left: 672px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-24 {
  padding-right: 672px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-24 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-24 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-25 {
  padding-left: 718px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-25 {
  padding-right: 718px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-25 {
  padding-left: 700px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-25 {
  padding-right: 700px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-25 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-25 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-26 {
  padding-left: 746px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-26 {
  padding-right: 746px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-26 {
  padding-left: 728px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-26 {
  padding-right: 728px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-26 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-26 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-27 {
  padding-left: 774px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-27 {
  padding-right: 774px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-27 {
  padding-left: 756px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-27 {
  padding-right: 756px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-27 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-27 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-28 {
  padding-left: 802px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-28 {
  padding-right: 802px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-28 {
  padding-left: 784px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-28 {
  padding-right: 784px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-28 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-28 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-29 {
  padding-left: 830px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-29 {
  padding-right: 830px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-29 {
  padding-left: 812px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-29 {
  padding-right: 812px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-29 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-29 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-30 {
  padding-left: 858px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-30 {
  padding-right: 858px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-30 {
  padding-left: 840px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-30 {
  padding-right: 840px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-30 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-30 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-31 {
  padding-left: 886px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-31 {
  padding-right: 886px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-31 {
  padding-left: 868px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-31 {
  padding-right: 868px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-31 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-31 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-32 {
  padding-left: 914px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-32 {
  padding-right: 914px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-32 {
  padding-left: 896px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-32 {
  padding-right: 896px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-32 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-32 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-33 {
  padding-left: 942px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-33 {
  padding-right: 942px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-33 {
  padding-left: 924px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-33 {
  padding-right: 924px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-33 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-33 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-34 {
  padding-left: 970px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-34 {
  padding-right: 970px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-34 {
  padding-left: 952px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-34 {
  padding-right: 952px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-34 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-34 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-35 {
  padding-left: 998px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-35 {
  padding-right: 998px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-35 {
  padding-left: 980px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-35 {
  padding-right: 980px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-35 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-35 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-36 {
  padding-left: 1026px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-36 {
  padding-right: 1026px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-36 {
  padding-left: 1008px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-36 {
  padding-right: 1008px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-36 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-36 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-37 {
  padding-left: 1054px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-37 {
  padding-right: 1054px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-37 {
  padding-left: 1036px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-37 {
  padding-right: 1036px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-37 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-37 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-38 {
  padding-left: 1082px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-38 {
  padding-right: 1082px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-38 {
  padding-left: 1064px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-38 {
  padding-right: 1064px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-38 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-38 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-39 {
  padding-left: 1110px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-39 {
  padding-right: 1110px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-39 {
  padding-left: 1092px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-39 {
  padding-right: 1092px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-39 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-39 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-40 {
  padding-left: 1138px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-40 {
  padding-right: 1138px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-40 {
  padding-left: 1120px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-40 {
  padding-right: 1120px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-40 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-40 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-41 {
  padding-left: 1166px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-41 {
  padding-right: 1166px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-41 {
  padding-left: 1148px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-41 {
  padding-right: 1148px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-41 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-41 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-42 {
  padding-left: 1194px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-42 {
  padding-right: 1194px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-42 {
  padding-left: 1176px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-42 {
  padding-right: 1176px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-42 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-42 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-43 {
  padding-left: 1222px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-43 {
  padding-right: 1222px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-43 {
  padding-left: 1204px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-43 {
  padding-right: 1204px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-43 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-43 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-44 {
  padding-left: 1250px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-44 {
  padding-right: 1250px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-44 {
  padding-left: 1232px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-44 {
  padding-right: 1232px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-44 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-44 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-45 {
  padding-left: 1278px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-45 {
  padding-right: 1278px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-45 {
  padding-left: 1260px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-45 {
  padding-right: 1260px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-45 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-45 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-46 {
  padding-left: 1306px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-46 {
  padding-right: 1306px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-46 {
  padding-left: 1288px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-46 {
  padding-right: 1288px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-46 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-46 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-47 {
  padding-left: 1334px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-47 {
  padding-right: 1334px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-47 {
  padding-left: 1316px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-47 {
  padding-right: 1316px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-47 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-47 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-48 {
  padding-left: 1362px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-48 {
  padding-right: 1362px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-48 {
  padding-left: 1344px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-48 {
  padding-right: 1344px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-48 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-48 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-49 {
  padding-left: 1390px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-49 {
  padding-right: 1390px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-49 {
  padding-left: 1372px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-49 {
  padding-right: 1372px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-49 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-49 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-50 {
  padding-left: 1418px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-50 {
  padding-right: 1418px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-50 {
  padding-left: 1400px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-50 {
  padding-right: 1400px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-50 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-50 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-51 {
  padding-left: 1446px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-51 {
  padding-right: 1446px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-51 {
  padding-left: 1428px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-51 {
  padding-right: 1428px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-51 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-51 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-52 {
  padding-left: 1474px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-52 {
  padding-right: 1474px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-52 {
  padding-left: 1456px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-52 {
  padding-right: 1456px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-52 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-52 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-53 {
  padding-left: 1502px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-53 {
  padding-right: 1502px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-53 {
  padding-left: 1484px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-53 {
  padding-right: 1484px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-53 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-53 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-54 {
  padding-left: 1530px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-54 {
  padding-right: 1530px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-54 {
  padding-left: 1512px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-54 {
  padding-right: 1512px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-54 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-54 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-55 {
  padding-left: 1558px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-55 {
  padding-right: 1558px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-55 {
  padding-left: 1540px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-55 {
  padding-right: 1540px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-55 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-55 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-56 {
  padding-left: 1586px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-56 {
  padding-right: 1586px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-56 {
  padding-left: 1568px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-56 {
  padding-right: 1568px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-56 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-56 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-57 {
  padding-left: 1614px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-57 {
  padding-right: 1614px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-57 {
  padding-left: 1596px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-57 {
  padding-right: 1596px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-57 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-57 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-58 {
  padding-left: 1642px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-58 {
  padding-right: 1642px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-58 {
  padding-left: 1624px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-58 {
  padding-right: 1624px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-58 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-58 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-59 {
  padding-left: 1670px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-59 {
  padding-right: 1670px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-59 {
  padding-left: 1652px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-59 {
  padding-right: 1652px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-59 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-59 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-60 {
  padding-left: 1698px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-60 {
  padding-right: 1698px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-60 {
  padding-left: 1680px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-60 {
  padding-right: 1680px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-60 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-60 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-61 {
  padding-left: 1726px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-61 {
  padding-right: 1726px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-61 {
  padding-left: 1708px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-61 {
  padding-right: 1708px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-61 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-61 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-62 {
  padding-left: 1754px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-62 {
  padding-right: 1754px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-62 {
  padding-left: 1736px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-62 {
  padding-right: 1736px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-62 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-62 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-63 {
  padding-left: 1782px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-63 {
  padding-right: 1782px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-63 {
  padding-left: 1764px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-63 {
  padding-right: 1764px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-63 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-63 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-64 {
  padding-left: 1810px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-64 {
  padding-right: 1810px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-64 {
  padding-left: 1792px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-64 {
  padding-right: 1792px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-64 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-64 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-65 {
  padding-left: 1838px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-65 {
  padding-right: 1838px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-65 {
  padding-left: 1820px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-65 {
  padding-right: 1820px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-65 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-65 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-66 {
  padding-left: 1866px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-66 {
  padding-right: 1866px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-66 {
  padding-left: 1848px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-66 {
  padding-right: 1848px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-66 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-66 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-67 {
  padding-left: 1894px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-67 {
  padding-right: 1894px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-67 {
  padding-left: 1876px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-67 {
  padding-right: 1876px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-67 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-67 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-68 {
  padding-left: 1922px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-68 {
  padding-right: 1922px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-68 {
  padding-left: 1904px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-68 {
  padding-right: 1904px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-68 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-68 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-69 {
  padding-left: 1950px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-69 {
  padding-right: 1950px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-69 {
  padding-left: 1932px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-69 {
  padding-right: 1932px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-69 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-69 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-70 {
  padding-left: 1978px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-70 {
  padding-right: 1978px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-70 {
  padding-left: 1960px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-70 {
  padding-right: 1960px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-70 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-70 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-71 {
  padding-left: 2006px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-71 {
  padding-right: 2006px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-71 {
  padding-left: 1988px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-71 {
  padding-right: 1988px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-71 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-71 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-72 {
  padding-left: 2034px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-72 {
  padding-right: 2034px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-72 {
  padding-left: 2016px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-72 {
  padding-right: 2016px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-72 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-72 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-73 {
  padding-left: 2062px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-73 {
  padding-right: 2062px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-73 {
  padding-left: 2044px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-73 {
  padding-right: 2044px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-73 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-73 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-74 {
  padding-left: 2090px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-74 {
  padding-right: 2090px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-74 {
  padding-left: 2072px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-74 {
  padding-right: 2072px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-74 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-74 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-75 {
  padding-left: 2118px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-75 {
  padding-right: 2118px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-75 {
  padding-left: 2100px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-75 {
  padding-right: 2100px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-75 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-75 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-76 {
  padding-left: 2146px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-76 {
  padding-right: 2146px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-76 {
  padding-left: 2128px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-76 {
  padding-right: 2128px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-76 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-76 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-77 {
  padding-left: 2174px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-77 {
  padding-right: 2174px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-77 {
  padding-left: 2156px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-77 {
  padding-right: 2156px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-77 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-77 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-78 {
  padding-left: 2202px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-78 {
  padding-right: 2202px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-78 {
  padding-left: 2184px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-78 {
  padding-right: 2184px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-78 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-78 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-79 {
  padding-left: 2230px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-79 {
  padding-right: 2230px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-79 {
  padding-left: 2212px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-79 {
  padding-right: 2212px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-79 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-79 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-80 {
  padding-left: 2258px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-80 {
  padding-right: 2258px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-80 {
  padding-left: 2240px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-80 {
  padding-right: 2240px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-80 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-80 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-81 {
  padding-left: 2286px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-81 {
  padding-right: 2286px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-81 {
  padding-left: 2268px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-81 {
  padding-right: 2268px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-81 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-81 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-82 {
  padding-left: 2314px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-82 {
  padding-right: 2314px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-82 {
  padding-left: 2296px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-82 {
  padding-right: 2296px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-82 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-82 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-83 {
  padding-left: 2342px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-83 {
  padding-right: 2342px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-83 {
  padding-left: 2324px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-83 {
  padding-right: 2324px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-83 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-83 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-84 {
  padding-left: 2370px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-84 {
  padding-right: 2370px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-84 {
  padding-left: 2352px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-84 {
  padding-right: 2352px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-84 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-84 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-85 {
  padding-left: 2398px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-85 {
  padding-right: 2398px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-85 {
  padding-left: 2380px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-85 {
  padding-right: 2380px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-85 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-85 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-86 {
  padding-left: 2426px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-86 {
  padding-right: 2426px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-86 {
  padding-left: 2408px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-86 {
  padding-right: 2408px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-86 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-86 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-87 {
  padding-left: 2454px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-87 {
  padding-right: 2454px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-87 {
  padding-left: 2436px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-87 {
  padding-right: 2436px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-87 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-87 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-88 {
  padding-left: 2482px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-88 {
  padding-right: 2482px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-88 {
  padding-left: 2464px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-88 {
  padding-right: 2464px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-88 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-88 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-89 {
  padding-left: 2510px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-89 {
  padding-right: 2510px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-89 {
  padding-left: 2492px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-89 {
  padding-right: 2492px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-89 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-89 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-90 {
  padding-left: 2538px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-90 {
  padding-right: 2538px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-90 {
  padding-left: 2520px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-90 {
  padding-right: 2520px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-90 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-90 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-91 {
  padding-left: 2566px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-91 {
  padding-right: 2566px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-91 {
  padding-left: 2548px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-91 {
  padding-right: 2548px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-91 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-91 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-92 {
  padding-left: 2594px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-92 {
  padding-right: 2594px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-92 {
  padding-left: 2576px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-92 {
  padding-right: 2576px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-92 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-92 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-93 {
  padding-left: 2622px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-93 {
  padding-right: 2622px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-93 {
  padding-left: 2604px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-93 {
  padding-right: 2604px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-93 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-93 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-94 {
  padding-left: 2650px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-94 {
  padding-right: 2650px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-94 {
  padding-left: 2632px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-94 {
  padding-right: 2632px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-94 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-94 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-95 {
  padding-left: 2678px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-95 {
  padding-right: 2678px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-95 {
  padding-left: 2660px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-95 {
  padding-right: 2660px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-95 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-95 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-96 {
  padding-left: 2706px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-96 {
  padding-right: 2706px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-96 {
  padding-left: 2688px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-96 {
  padding-right: 2688px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-96 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-96 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-97 {
  padding-left: 2734px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-97 {
  padding-right: 2734px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-97 {
  padding-left: 2716px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-97 {
  padding-right: 2716px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-97 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-97 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-98 {
  padding-left: 2762px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-98 {
  padding-right: 2762px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-98 {
  padding-left: 2744px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-98 {
  padding-right: 2744px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-98 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-98 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row > .ag-cell-wrapper.ag-row-group-indent-99 {
  padding-left: 2790px;
}

.ag-theme-alpine .ag-rtl .ag-row > .ag-cell-wrapper.ag-row-group-indent-99 {
  padding-right: 2790px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-indent-99 {
  padding-left: 2772px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-indent-99 {
  padding-right: 2772px;
}

.ag-theme-alpine .ag-ltr .ag-row-level-99 .ag-pivot-leaf-group {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-level-99 .ag-pivot-leaf-group {
  margin-right: 28px;
}

.ag-theme-alpine .ag-ltr .ag-row-group-leaf-indent {
  margin-left: 28px;
}

.ag-theme-alpine .ag-rtl .ag-row-group-leaf-indent {
  margin-right: 28px;
}

.ag-theme-alpine .ag-value-change-delta {
  padding-right: 2px;
}
.ag-theme-alpine .ag-value-change-delta-up {
  color: #43a047;
  color: var(--ag-value-change-delta-up-color, #43a047);
}
.ag-theme-alpine .ag-value-change-delta-down {
  color: #e53935;
  color: var(--ag-value-change-delta-down-color, #e53935);
}
.ag-theme-alpine .ag-value-change-value {
  background-color: transparent;
  border-radius: 1px;
  padding-left: 1px;
  padding-right: 1px;
  -webkit-transition: background-color 1s;
  transition: background-color 1s;
}
.ag-theme-alpine .ag-value-change-value-highlight {
  background-color: rgba(22, 160, 133, 0.5);
  background-color: var(--ag-value-change-value-highlight-background-color, rgba(22, 160, 133, 0.5));
  -webkit-transition: background-color 0.1s;
  transition: background-color 0.1s;
}
.ag-theme-alpine .ag-cell-data-changed {
  background-color: rgba(22, 160, 133, 0.5) !important;
  background-color: var(--ag-value-change-value-highlight-background-color, rgba(22, 160, 133, 0.5)) !important;
}
.ag-theme-alpine .ag-cell-data-changed-animation {
  background-color: transparent;
}
.ag-theme-alpine .ag-cell-highlight {
  background-color: #2196f3 !important;
  background-color: var(--ag-range-selection-highlight-color, var(--ag-range-selection-border-color, #2196f3)) !important;
}
.ag-theme-alpine .ag-row {
  height: 42px;
  background-color: #fff;
  background-color: var(--ag-background-color, #fff);
  color: #181d1f;
  color: var(--ag-data-color, var(--ag-foreground-color, #181d1f));
  border-width: 1px;
  border-color: #dde2eb;
  border-color: var(--ag-row-border-color, var(--ag-secondary-border-color, #dde2eb));
  border-bottom-style: solid;
}
.ag-theme-alpine .ag-row-highlight-above::after, .ag-theme-alpine .ag-row-highlight-below::after {
  content: "";
  position: absolute;
  width: calc(100% - 1px);
  height: 1px;
  background-color: #2196f3;
  background-color: var(--ag-range-selection-border-color, #2196f3);
  left: 1px;
}
.ag-theme-alpine .ag-row-highlight-above::after {
  top: -1px;
}
.ag-theme-alpine .ag-row-highlight-above.ag-row-first::after {
  top: 0;
}
.ag-theme-alpine .ag-row-highlight-below::after {
  bottom: 0px;
}
.ag-theme-alpine .ag-row-odd {
  background-color: #fcfcfc;
  background-color: var(--ag-odd-row-background-color, #fcfcfc);
}
.ag-theme-alpine .ag-body-horizontal-scroll:not(.ag-scrollbar-invisible) .ag-horizontal-left-spacer:not(.ag-scroller-corner) {
  border-right: solid 1px;
  border-right-color: #babfc7;
  border-right-color: var(--ag-border-color, #babfc7);
}
.ag-theme-alpine .ag-body-horizontal-scroll:not(.ag-scrollbar-invisible) .ag-horizontal-right-spacer:not(.ag-scroller-corner) {
  border-left: solid 1px;
  border-left-color: #babfc7;
  border-left-color: var(--ag-border-color, #babfc7);
}
.ag-theme-alpine .ag-row-selected::before {
  content: "";
  background-color: rgba(33, 150, 243, 0.3);
  background-color: var(--ag-selected-row-background-color, rgba(33, 150, 243, 0.3));
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
.ag-theme-alpine .ag-row-hover:not(.ag-full-width-row)::before,
.ag-theme-alpine .ag-row-hover.ag-full-width-row.ag-row-group::before {
  content: "";
  background-color: rgba(33, 150, 243, 0.1);
  background-color: var(--ag-row-hover-color, rgba(33, 150, 243, 0.1));
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}
.ag-theme-alpine .ag-row-hover.ag-row-selected::before {
  background-color: rgba(33, 150, 243, 0.1);
  background-color: var(--ag-row-hover-color, rgba(33, 150, 243, 0.1));
  background-image: -webkit-gradient(linear, left top, left bottom, from(rgba(33, 150, 243, 0.3)), to(rgba(33, 150, 243, 0.3)));
  background-image: linear-gradient(rgba(33, 150, 243, 0.3), rgba(33, 150, 243, 0.3));
}
.ag-theme-alpine .ag-row-hover.ag-full-width-row.ag-row-group > * {
  position: relative;
}
.ag-theme-alpine .ag-column-hover {
  background-color: rgba(33, 150, 243, 0.1);
  background-color: var(--ag-column-hover-color, rgba(33, 150, 243, 0.1));
}
.ag-theme-alpine .ag-ltr .ag-right-aligned-cell {
  text-align: right;
}

.ag-theme-alpine .ag-rtl .ag-right-aligned-cell {
  text-align: left;
}

.ag-theme-alpine .ag-ltr .ag-right-aligned-cell .ag-cell-value, .ag-theme-alpine .ag-ltr .ag-right-aligned-cell .ag-group-value {
  margin-left: auto;
}

.ag-theme-alpine .ag-rtl .ag-right-aligned-cell .ag-cell-value, .ag-theme-alpine .ag-rtl .ag-right-aligned-cell .ag-group-value {
  margin-right: auto;
}

.ag-theme-alpine .ag-cell, .ag-theme-alpine .ag-full-width-row .ag-cell-wrapper.ag-row-group {
  --ag-internal-calculated-line-height: var(--ag-line-height, 40px);
  --ag-internal-padded-row-height: 40px;
  border: 1px solid transparent;
  line-height: min(var(--ag-internal-calculated-line-height), var(--ag-internal-padded-row-height));
  padding-left: 17px;
  padding-right: 17px;
  -webkit-font-smoothing: subpixel-antialiased;
}
.ag-theme-alpine .ag-row > .ag-cell-wrapper {
  padding-left: 17px;
  padding-right: 17px;
}
.ag-theme-alpine .ag-row-dragging {
  cursor: move;
  opacity: 0.5;
}
.ag-theme-alpine .ag-cell-inline-editing {
  border: solid 1px;
  border-color: #babfc7;
  border-color: var(--ag-border-color, #babfc7);
  background: #fff;
  background: var(--ag-background-color, #fff);
  border-radius: 3px;
  -webkit-box-shadow: 0 1px 4px 1px rgba(186, 191, 199, 0.4);
          box-shadow: 0 1px 4px 1px rgba(186, 191, 199, 0.4);
  padding: 6px;
  padding: 0;
  height: 42px;
  background-color: #f8f8f8;
  background-color: var(--ag-control-panel-background-color, #f8f8f8);
}
.ag-theme-alpine .ag-popup-editor {
  border: solid 1px;
  border-color: #babfc7;
  border-color: var(--ag-border-color, #babfc7);
  background: #fff;
  background: var(--ag-background-color, #fff);
  border-radius: 3px;
  -webkit-box-shadow: 0 1px 4px 1px rgba(186, 191, 199, 0.4);
          box-shadow: 0 1px 4px 1px rgba(186, 191, 199, 0.4);
  padding: 6px;
  background-color: #f8f8f8;
  background-color: var(--ag-control-panel-background-color, #f8f8f8);
  padding: 0;
}
.ag-theme-alpine .ag-large-text-input {
  height: auto;
  padding: 18px;
}
.ag-theme-alpine .ag-details-row {
  padding: 30px;
  background-color: #fff;
  background-color: var(--ag-background-color, #fff);
}
.ag-theme-alpine .ag-layout-auto-height .ag-center-cols-clipper, .ag-theme-alpine .ag-layout-auto-height .ag-center-cols-container, .ag-theme-alpine .ag-layout-print .ag-center-cols-clipper, .ag-theme-alpine .ag-layout-print .ag-center-cols-container {
  min-height: 50px;
}
.ag-theme-alpine .ag-overlay-loading-wrapper {
  background-color: rgba(255, 255, 255, 0.66);
  background-color: var(--ag-modal-overlay-background-color, rgba(255, 255, 255, 0.66));
}
.ag-theme-alpine .ag-overlay-loading-center {
  border: solid 1px;
  border-color: #babfc7;
  border-color: var(--ag-border-color, #babfc7);
  background: #fff;
  background: var(--ag-background-color, #fff);
  border-radius: 3px;
  -webkit-box-shadow: 0 1px 4px 1px rgba(186, 191, 199, 0.4);
          box-shadow: 0 1px 4px 1px rgba(186, 191, 199, 0.4);
  padding: 6px;
}
.ag-theme-alpine .ag-overlay-no-rows-wrapper.ag-layout-auto-height {
  padding-top: 30px;
}
.ag-theme-alpine .ag-loading {
  display: -webkit-box;
  display: flex;
  height: 100%;
  -webkit-box-align: center;
          align-items: center;
}
.ag-theme-alpine .ag-ltr .ag-loading {
  padding-left: 18px;
}

.ag-theme-alpine .ag-rtl .ag-loading {
  padding-right: 18px;
}

.ag-theme-alpine .ag-ltr .ag-loading-icon {
  padding-right: 12px;
}

.ag-theme-alpine .ag-rtl .ag-loading-icon {
  padding-left: 12px;
}

.ag-theme-alpine .ag-icon-loading {
  -webkit-animation-name: spin;
          animation-name: spin;
  -webkit-animation-duration: 1000ms;
          animation-duration: 1000ms;
  -webkit-animation-iteration-count: infinite;
          animation-iteration-count: infinite;
  -webkit-animation-timing-function: linear;
          animation-timing-function: linear;
}
@-webkit-keyframes spin {
  from {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
  }
}
@keyframes spin {
  from {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
  }
}
.ag-theme-alpine .ag-floating-top {
  border-bottom: solid 1px;
  border-bottom-color: #babfc7;
  border-bottom-color: var(--ag-border-color, #babfc7);
}
.ag-theme-alpine .ag-floating-bottom {
  border-top: solid 1px;
  border-top-color: #babfc7;
  border-top-color: var(--ag-border-color, #babfc7);
}
.ag-theme-alpine .ag-ltr .ag-cell {
  border-right: solid transparent;
}

.ag-theme-alpine .ag-rtl .ag-cell {
  border-left: solid transparent;
}

.ag-theme-alpine .ag-ltr .ag-cell {
  border-right-width: 1px;
}

.ag-theme-alpine .ag-rtl .ag-cell {
  border-left-width: 1px;
}

.ag-theme-alpine .ag-cell.ag-cell-first-right-pinned:not(.ag-cell-range-left):not(.ag-cell-range-single-cell) {
  border-left: solid 1px;
  border-left-color: #babfc7;
  border-left-color: var(--ag-border-color, #babfc7);
}
.ag-theme-alpine .ag-cell.ag-cell-last-left-pinned:not(.ag-cell-range-right):not(.ag-cell-range-single-cell) {
  border-right: solid 1px;
  border-right-color: #babfc7;
  border-right-color: var(--ag-border-color, #babfc7);
}
.ag-theme-alpine .ag-cell-range-selected:not(.ag-cell-focus),
.ag-theme-alpine .ag-body-viewport:not(.ag-has-focus) .ag-cell-range-single-cell:not(.ag-cell-inline-editing) {
  background-color: rgba(33, 150, 243, 0.2);
  background-color: var(--ag-range-selection-background-color, rgba(33, 150, 243, 0.2));
}
.ag-theme-alpine .ag-cell-range-selected:not(.ag-cell-focus).ag-cell-range-chart,
.ag-theme-alpine .ag-body-viewport:not(.ag-has-focus) .ag-cell-range-single-cell:not(.ag-cell-inline-editing).ag-cell-range-chart {
  background-color: rgba(0, 88, 255, 0.1) !important;
  background-color: var(--ag-range-selection-chart-background-color, rgba(0, 88, 255, 0.1)) !important;
}
.ag-theme-alpine .ag-cell-range-selected:not(.ag-cell-focus).ag-cell-range-chart.ag-cell-range-chart-category,
.ag-theme-alpine .ag-body-viewport:not(.ag-has-focus) .ag-cell-range-single-cell:not(.ag-cell-inline-editing).ag-cell-range-chart.ag-cell-range-chart-category {
  background-color: rgba(0, 255, 132, 0.1) !important;
  background-color: var(--ag-range-selection-chart-category-background-color, rgba(0, 255, 132, 0.1)) !important;
}
.ag-theme-alpine .ag-cell-range-selected-1:not(.ag-cell-focus),
.ag-theme-alpine .ag-root:not(.ag-context-menu-open) .ag-body-viewport:not(.ag-has-focus) .ag-cell-range-selected-1:not(.ag-cell-inline-editing) {
  background-color: rgba(33, 150, 243, 0.2);
  background-color: var(--ag-range-selection-background-color-1, var(--ag-range-selection-background-color, rgba(33, 150, 243, 0.2)));
}
.ag-theme-alpine .ag-cell-range-selected-2:not(.ag-cell-focus),
.ag-theme-alpine .ag-body-viewport:not(.ag-has-focus) .ag-cell-range-selected-2 {
  background-color: rgba(33, 150, 243, 0.36);
  background-color: var(--ag-range-selection-background-color-2, rgba(33, 150, 243, 0.36));
}
.ag-theme-alpine .ag-cell-range-selected-3:not(.ag-cell-focus),
.ag-theme-alpine .ag-body-viewport:not(.ag-has-focus) .ag-cell-range-selected-3 {
  background-color: rgba(33, 150, 243, 0.488);
  background-color: var(--ag-range-selection-background-color-3, rgba(33, 150, 243, 0.488));
}
.ag-theme-alpine .ag-cell-range-selected-4:not(.ag-cell-focus),
.ag-theme-alpine .ag-body-viewport:not(.ag-has-focus) .ag-cell-range-selected-4 {
  background-color: rgba(33, 150, 243, 0.5904);
  background-color: var(--ag-range-selection-background-color-4, rgba(33, 150, 243, 0.5904));
}
.ag-theme-alpine .ag-cell.ag-cell-range-selected:not(.ag-cell-range-single-cell).ag-cell-range-top {
  border-top-color: #2196f3;
  border-top-color: var(--ag-range-selection-border-color, #2196f3);
}
.ag-theme-alpine .ag-cell.ag-cell-range-selected:not(.ag-cell-range-single-cell).ag-cell-range-right {
  border-right-color: #2196f3;
  border-right-color: var(--ag-range-selection-border-color, #2196f3);
}
.ag-theme-alpine .ag-cell.ag-cell-range-selected:not(.ag-cell-range-single-cell).ag-cell-range-bottom {
  border-bottom-color: #2196f3;
  border-bottom-color: var(--ag-range-selection-border-color, #2196f3);
}
.ag-theme-alpine .ag-cell.ag-cell-range-selected:not(.ag-cell-range-single-cell).ag-cell-range-left {
  border-left-color: #2196f3;
  border-left-color: var(--ag-range-selection-border-color, #2196f3);
}
.ag-theme-alpine .ag-ltr .ag-cell-focus:not(.ag-cell-range-selected):focus-within,
.ag-theme-alpine .ag-ltr .ag-context-menu-open .ag-cell-focus:not(.ag-cell-range-selected),
.ag-theme-alpine .ag-ltr .ag-full-width-row.ag-row-focus:focus .ag-cell-wrapper.ag-row-group,
.ag-theme-alpine .ag-ltr .ag-cell-range-single-cell,
.ag-theme-alpine .ag-ltr .ag-cell-range-single-cell.ag-cell-range-handle, .ag-theme-alpine .ag-rtl .ag-cell-focus:not(.ag-cell-range-selected):focus-within,
.ag-theme-alpine .ag-rtl .ag-context-menu-open .ag-cell-focus:not(.ag-cell-range-selected),
.ag-theme-alpine .ag-rtl .ag-full-width-row.ag-row-focus:focus .ag-cell-wrapper.ag-row-group,
.ag-theme-alpine .ag-rtl .ag-cell-range-single-cell,
.ag-theme-alpine .ag-rtl .ag-cell-range-single-cell.ag-cell-range-handle {
  border: 1px solid;
  border-color: #2196f3;
  border-color: var(--ag-range-selection-border-color, #2196f3);
  outline: initial;
}
.ag-theme-alpine .ag-cell.ag-selection-fill-top,
.ag-theme-alpine .ag-cell.ag-selection-fill-top.ag-cell-range-selected {
  border-top: 1px dashed;
  border-top-color: #2196f3;
  border-top-color: var(--ag-range-selection-border-color, #2196f3);
}
.ag-theme-alpine .ag-ltr .ag-cell.ag-selection-fill-right, .ag-theme-alpine .ag-ltr .ag-cell.ag-selection-fill-right.ag-cell-range-selected {
  border-right: 1px dashed !important;
  border-right-color: #2196f3 !important;
  border-right-color: var(--ag-range-selection-border-color, #2196f3) !important;
}

.ag-theme-alpine .ag-rtl .ag-cell.ag-selection-fill-right, .ag-theme-alpine .ag-rtl .ag-cell.ag-selection-fill-right.ag-cell-range-selected {
  border-left: 1px dashed !important;
  border-left-color: #2196f3 !important;
  border-left-color: var(--ag-range-selection-border-color, #2196f3) !important;
}

.ag-theme-alpine .ag-cell.ag-selection-fill-bottom,
.ag-theme-alpine .ag-cell.ag-selection-fill-bottom.ag-cell-range-selected {
  border-bottom: 1px dashed;
  border-bottom-color: #2196f3;
  border-bottom-color: var(--ag-range-selection-border-color, #2196f3);
}
.ag-theme-alpine .ag-ltr .ag-cell.ag-selection-fill-left, .ag-theme-alpine .ag-ltr .ag-cell.ag-selection-fill-left.ag-cell-range-selected {
  border-left: 1px dashed !important;
  border-left-color: #2196f3 !important;
  border-left-color: var(--ag-range-selection-border-color, #2196f3) !important;
}

.ag-theme-alpine .ag-rtl .ag-cell.ag-selection-fill-left, .ag-theme-alpine .ag-rtl .ag-cell.ag-selection-fill-left.ag-cell-range-selected {
  border-right: 1px dashed !important;
  border-right-color: #2196f3 !important;
  border-right-color: var(--ag-range-selection-border-color, #2196f3) !important;
}

.ag-theme-alpine .ag-range-handle, .ag-theme-alpine .ag-fill-handle {
  position: absolute;
  width: 6px;
  height: 6px;
  bottom: -1px;
  background-color: #2196f3;
  background-color: var(--ag-range-selection-border-color, #2196f3);
}
.ag-theme-alpine .ag-ltr .ag-range-handle, .ag-theme-alpine .ag-ltr .ag-fill-handle {
  right: -1px;
}

.ag-theme-alpine .ag-rtl .ag-range-handle, .ag-theme-alpine .ag-rtl .ag-fill-handle {
  left: -1px;
}

.ag-theme-alpine .ag-fill-handle {
  cursor: cell;
}
.ag-theme-alpine .ag-range-handle {
  cursor: nwse-resize;
}
.ag-theme-alpine .ag-cell-inline-editing {
  border-color: rgba(33, 150, 243, 0.4) !important;
  border-color: var(--ag-input-focus-border-color, rgba(33, 150, 243, 0.4)) !important;
}
.ag-theme-alpine .ag-menu {
  border: solid 1px;
  border-color: #babfc7;
  border-color: var(--ag-border-color, #babfc7);
  background: #fff;
  background: var(--ag-background-color, #fff);
  border-radius: 3px;
  -webkit-box-shadow: 0 1px 4px 1px rgba(186, 191, 199, 0.4);
          box-shadow: 0 1px 4px 1px rgba(186, 191, 199, 0.4);
  padding: 6px;
  padding: 0;
}
.ag-theme-alpine .ag-menu-list {
  cursor: default;
  padding: 6px 0;
}
.ag-theme-alpine .ag-menu-separator {
  height: 13px;
}
.ag-theme-alpine .ag-menu-separator-part::after {
  content: "";
  display: block;
  border-top: solid 1px;
  border-top-color: #babfc7;
  border-top-color: var(--ag-border-color, #babfc7);
}
.ag-theme-alpine .ag-menu-option-active, .ag-theme-alpine .ag-compact-menu-option-active {
  background-color: rgba(33, 150, 243, 0.1);
  background-color: var(--ag-row-hover-color, rgba(33, 150, 243, 0.1));
}
.ag-theme-alpine .ag-menu-option-part, .ag-theme-alpine .ag-compact-menu-option-part {
  line-height: 16px;
  padding: 8px 0;
}
.ag-theme-alpine .ag-menu-option-disabled, .ag-theme-alpine .ag-compact-menu-option-disabled {
  opacity: 0.5;
}
.ag-theme-alpine .ag-menu-option-icon, .ag-theme-alpine .ag-compact-menu-option-icon {
  width: 16px;
}
.ag-theme-alpine .ag-ltr .ag-menu-option-icon, .ag-theme-alpine .ag-ltr .ag-compact-menu-option-icon {
  padding-left: 12px;
}

.ag-theme-alpine .ag-rtl .ag-menu-option-icon, .ag-theme-alpine .ag-rtl .ag-compact-menu-option-icon {
  padding-right: 12px;
}

.ag-theme-alpine .ag-menu-option-text, .ag-theme-alpine .ag-compact-menu-option-text {
  padding-left: 12px;
  padding-right: 12px;
}
.ag-theme-alpine .ag-ltr .ag-menu-option-shortcut, .ag-theme-alpine .ag-ltr .ag-compact-menu-option-shortcut {
  padding-right: 6px;
}

.ag-theme-alpine .ag-rtl .ag-menu-option-shortcut, .ag-theme-alpine .ag-rtl .ag-compact-menu-option-shortcut {
  padding-left: 6px;
}

.ag-theme-alpine .ag-menu-option-popup-pointer, .ag-theme-alpine .ag-compact-menu-option-popup-pointer {
  padding-right: 6px;
}
.ag-theme-alpine .ag-tabs {
  min-width: 240px;
}
.ag-theme-alpine .ag-tabs-header {
  width: 100%;
  display: -webkit-box;
  display: flex;
}
.ag-theme-alpine .ag-tab {
  border-bottom: 2px solid transparent;
  display: -webkit-box;
  display: flex;
  -webkit-box-flex: 0;
          flex: none;
  -webkit-box-align: center;
          align-items: center;
  -webkit-box-pack: center;
          justify-content: center;
  cursor: pointer;
  -webkit-box-flex: 1;
          flex: 1 1 auto;
  -webkit-transition: border-bottom 0.3s;
  transition: border-bottom 0.3s;
}
.ag-theme-alpine .ag-keyboard-focus .ag-tab:focus {
  outline: none;
}
.ag-theme-alpine .ag-keyboard-focus .ag-tab:focus::after {
  content: "";
  position: absolute;
  background-color: transparent;
  pointer-events: none;
  top: 4px;
  left: 4px;
  display: block;
  width: calc(100% - 8px);
  height: calc(100% - 8px);
  border: 1px solid;
  border-color: rgba(33, 150, 243, 0.4);
  border-color: var(--ag-input-focus-border-color, rgba(33, 150, 243, 0.4));
}
.ag-theme-alpine .ag-tab-selected {
  border-bottom-color: #2196f3;
  border-bottom-color: var(--ag-selected-tab-underline-color, var(--ag-alpine-active-color, #2196f3));
}
.ag-theme-alpine .ag-menu-header {
  color: #181d1f;
  color: var(--ag-secondary-foreground-color, var(--ag-foreground-color, #181d1f));
}
.ag-theme-alpine .ag-filter-separator {
  border-top: solid 1px;
  border-top-color: #babfc7;
  border-top-color: var(--ag-border-color, #babfc7);
}
.ag-theme-alpine .ag-menu:not(.ag-tabs) .ag-filter-select {
  min-width: 155px;
}
.ag-theme-alpine .ag-tabs .ag-filter-select {
  min-width: 214px;
}
.ag-theme-alpine .ag-filter-select .ag-picker-field-wrapper {
  width: 0;
}
.ag-theme-alpine .ag-filter-condition-operator {
  height: 17px;
}
.ag-theme-alpine .ag-ltr .ag-filter-condition-operator-or {
  margin-left: 12px;
}

.ag-theme-alpine .ag-rtl .ag-filter-condition-operator-or {
  margin-right: 12px;
}

.ag-theme-alpine .ag-set-filter-select-all {
  padding-top: 12px;
}
.ag-theme-alpine .ag-set-filter-list, .ag-theme-alpine .ag-filter-no-matches {
  height: 144px;
}
.ag-theme-alpine .ag-set-filter-filter {
  margin-top: 12px;
  margin-left: 12px;
  margin-right: 12px;
}
.ag-theme-alpine .ag-filter-to {
  margin-top: 9px;
}
.ag-theme-alpine .ag-mini-filter {
  margin: 12px 12px;
}
.ag-theme-alpine .ag-set-filter-item {
  margin: 0px 12px;
}
.ag-theme-alpine .ag-ltr .ag-set-filter-item-value {
  margin-left: 12px;
}

.ag-theme-alpine .ag-rtl .ag-set-filter-item-value {
  margin-right: 12px;
}

.ag-theme-alpine .ag-filter-apply-panel {
  padding: 12px 12px;
  border-top: solid 1px;
  border-top-color: #dde2eb;
  border-top-color: var(--ag-secondary-border-color, #dde2eb);
}
.ag-theme-alpine .ag-filter-apply-panel-button {
  line-height: 1.5;
}
.ag-theme-alpine .ag-ltr .ag-filter-apply-panel-button {
  margin-left: 12px;
}

.ag-theme-alpine .ag-rtl .ag-filter-apply-panel-button {
  margin-right: 12px;
}

.ag-theme-alpine .ag-simple-filter-body-wrapper {
  padding: 12px 12px;
  padding-bottom: 3px;
}
.ag-theme-alpine .ag-simple-filter-body-wrapper > * {
  margin-bottom: 9px;
}
.ag-theme-alpine .ag-filter-no-matches {
  padding: 12px 12px;
}
.ag-theme-alpine .ag-multi-filter-menu-item {
  margin: 6px 0;
}
.ag-theme-alpine .ag-multi-filter-group-title-bar {
  padding: 12px 6px;
  background-color: transparent;
}
.ag-theme-alpine .ag-keyboard-focus .ag-multi-filter-group-title-bar:focus {
  outline: none;
}
.ag-theme-alpine .ag-keyboard-focus .ag-multi-filter-group-title-bar:focus::after {
  content: "";
  position: absolute;
  background-color: transparent;
  pointer-events: none;
  top: 4px;
  left: 4px;
  display: block;
  width: calc(100% - 8px);
  height: calc(100% - 8px);
  border: 1px solid;
  border-color: rgba(33, 150, 243, 0.4);
  border-color: var(--ag-input-focus-border-color, rgba(33, 150, 243, 0.4));
}
.ag-theme-alpine .ag-side-bar {
  position: relative;
}
.ag-theme-alpine .ag-tool-panel-wrapper {
  width: 250px;
  background-color: #f8f8f8;
  background-color: var(--ag-control-panel-background-color, #f8f8f8);
}
.ag-theme-alpine .ag-side-buttons {
  padding-top: 24px;
  width: 20px;
  position: relative;
  color: #181d1f;
  color: var(--ag-foreground-color, #181d1f);
  overflow: hidden;
}
.ag-theme-alpine button.ag-side-button-button {
  color: inherit;
  font-family: inherit;
  font-size: inherit;
  font-weight: inherit;
  line-height: inherit;
  background: transparent;
  padding: 12px 0 12px 0;
  width: 100%;
  margin: 0;
  min-height: 108px;
  background-position-y: center;
  background-position-x: center;
  background-repeat: no-repeat;
  border: none;
}
.ag-theme-alpine button.ag-side-button-button:focus {
  -webkit-box-shadow: none;
          box-shadow: none;
}
.ag-theme-alpine .ag-keyboard-focus .ag-side-button-button:focus {
  outline: none;
}
.ag-theme-alpine .ag-keyboard-focus .ag-side-button-button:focus::after {
  content: "";
  position: absolute;
  background-color: transparent;
  pointer-events: none;
  top: 4px;
  left: 4px;
  display: block;
  width: calc(100% - 8px);
  height: calc(100% - 8px);
  border: 1px solid;
  border-color: rgba(33, 150, 243, 0.4);
  border-color: var(--ag-input-focus-border-color, rgba(33, 150, 243, 0.4));
}
.ag-theme-alpine .ag-side-button-icon-wrapper {
  margin-bottom: 3px;
}
.ag-theme-alpine .ag-ltr .ag-side-bar-left,
.ag-theme-alpine .ag-rtl .ag-side-bar-right {
  border-right: solid 1px;
  border-right-color: #babfc7;
  border-right-color: var(--ag-border-color, #babfc7);
}
.ag-theme-alpine .ag-ltr .ag-side-bar-left .ag-tool-panel-wrapper,
.ag-theme-alpine .ag-rtl .ag-side-bar-right .ag-tool-panel-wrapper {
  border-left: solid 1px;
  border-left-color: #babfc7;
  border-left-color: var(--ag-border-color, #babfc7);
}
.ag-theme-alpine .ag-ltr .ag-side-bar-left .ag-side-button-button,
.ag-theme-alpine .ag-rtl .ag-side-bar-right .ag-side-button-button {
  border-right: 2px solid transparent;
  -webkit-transition: border-right 0.3s;
  transition: border-right 0.3s;
}
.ag-theme-alpine .ag-ltr .ag-side-bar-left .ag-selected .ag-side-button-button,
.ag-theme-alpine .ag-rtl .ag-side-bar-right .ag-selected .ag-side-button-button {
  border-right-color: #2196f3;
  border-right-color: var(--ag-selected-tab-underline-color, var(--ag-alpine-active-color, #2196f3));
}
.ag-theme-alpine .ag-rtl .ag-side-bar-left,
.ag-theme-alpine .ag-ltr .ag-side-bar-right {
  border-left: solid 1px;
  border-left-color: #babfc7;
  border-left-color: var(--ag-border-color, #babfc7);
}
.ag-theme-alpine .ag-rtl .ag-side-bar-left .ag-tool-panel-wrapper,
.ag-theme-alpine .ag-ltr .ag-side-bar-right .ag-tool-panel-wrapper {
  border-right: solid 1px;
  border-right-color: #babfc7;
  border-right-color: var(--ag-border-color, #babfc7);
}
.ag-theme-alpine .ag-rtl .ag-side-bar-left .ag-side-button-button,
.ag-theme-alpine .ag-ltr .ag-side-bar-right .ag-side-button-button {
  border-left: 2px solid transparent;
  -webkit-transition: border-left 0.3s;
  transition: border-left 0.3s;
}
.ag-theme-alpine .ag-rtl .ag-side-bar-left .ag-selected .ag-side-button-button,
.ag-theme-alpine .ag-ltr .ag-side-bar-right .ag-selected .ag-side-button-button {
  border-left-color: #2196f3;
  border-left-color: var(--ag-selected-tab-underline-color, var(--ag-alpine-active-color, #2196f3));
}
.ag-theme-alpine .ag-filter-toolpanel-header {
  height: 36px;
}
.ag-theme-alpine .ag-ltr .ag-filter-toolpanel-header, .ag-theme-alpine .ag-ltr .ag-filter-toolpanel-search {
  padding-left: 6px;
}

.ag-theme-alpine .ag-rtl .ag-filter-toolpanel-header, .ag-theme-alpine .ag-rtl .ag-filter-toolpanel-search {
  padding-right: 6px;
}

.ag-theme-alpine .ag-keyboard-focus .ag-filter-toolpanel-header:focus {
  outline: none;
}
.ag-theme-alpine .ag-keyboard-focus .ag-filter-toolpanel-header:focus::after {
  content: "";
  position: absolute;
  background-color: transparent;
  pointer-events: none;
  top: 4px;
  left: 4px;
  display: block;
  width: calc(100% - 8px);
  height: calc(100% - 8px);
  border: 1px solid;
  border-color: rgba(33, 150, 243, 0.4);
  border-color: var(--ag-input-focus-border-color, rgba(33, 150, 243, 0.4));
}
.ag-theme-alpine .ag-filter-toolpanel-group.ag-has-filter > .ag-group-title-bar .ag-group-title::after {
  font-family: "agGridAlpine";
  font-size: 16px;
  line-height: 16px;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  content: "\f114";
  position: absolute;
}
.ag-theme-alpine .ag-ltr .ag-filter-toolpanel-group.ag-has-filter > .ag-group-title-bar .ag-group-title::after {
  padding-left: 6px;
}

.ag-theme-alpine .ag-rtl .ag-filter-toolpanel-group.ag-has-filter > .ag-group-title-bar .ag-group-title::after {
  padding-right: 6px;
}

.ag-theme-alpine .ag-filter-toolpanel-group-level-0-header {
  height: 48px;
}
.ag-theme-alpine .ag-filter-toolpanel-group-item {
  margin-top: 3px;
  margin-bottom: 3px;
}
.ag-theme-alpine .ag-filter-toolpanel-search {
  height: 48px;
}
.ag-theme-alpine .ag-filter-toolpanel-search-input {
  -webkit-box-flex: 1;
          flex-grow: 1;
  height: 24px;
}
.ag-theme-alpine .ag-ltr .ag-filter-toolpanel-search-input {
  margin-right: 6px;
}

.ag-theme-alpine .ag-rtl .ag-filter-toolpanel-search-input {
  margin-left: 6px;
}

.ag-theme-alpine .ag-filter-toolpanel-group-level-0 {
  border-top: solid 1px;
  border-top-color: #dde2eb;
  border-top-color: var(--ag-secondary-border-color, #dde2eb);
}
.ag-theme-alpine .ag-ltr .ag-filter-toolpanel-expand, .ag-theme-alpine .ag-ltr .ag-filter-toolpanel-group-title-bar-icon {
  margin-right: 6px;
}

.ag-theme-alpine .ag-rtl .ag-filter-toolpanel-expand, .ag-theme-alpine .ag-rtl .ag-filter-toolpanel-group-title-bar-icon {
  margin-left: 6px;
}

.ag-theme-alpine .ag-filter-toolpanel-group-level-1 .ag-filter-toolpanel-group-level-1-header.ag-filter-toolpanel-group-title-bar {
  background-color: transparent;
}
.ag-theme-alpine .ag-ltr .ag-filter-toolpanel-group-level-1 .ag-filter-toolpanel-group-level-2-header {
  padding-left: 22px;
}

.ag-theme-alpine .ag-rtl .ag-filter-toolpanel-group-level-1 .ag-filter-toolpanel-group-level-2-header {
  padding-right: 22px;
}

.ag-theme-alpine .ag-filter-toolpanel-group-level-2 .ag-filter-toolpanel-group-level-2-header.ag-filter-toolpanel-group-title-bar {
  background-color: transparent;
}
.ag-theme-alpine .ag-ltr .ag-filter-toolpanel-group-level-2 .ag-filter-toolpanel-group-level-3-header {
  padding-left: 38px;
}

.ag-theme-alpine .ag-rtl .ag-filter-toolpanel-group-level-2 .ag-filter-toolpanel-group-level-3-header {
  padding-right: 38px;
}

.ag-theme-alpine .ag-filter-toolpanel-group-level-3 .ag-filter-toolpanel-group-level-3-header.ag-filter-toolpanel-group-title-bar {
  background-color: transparent;
}
.ag-theme-alpine .ag-ltr .ag-filter-toolpanel-group-level-3 .ag-filter-toolpanel-group-level-4-header {
  padding-left: 54px;
}

.ag-theme-alpine .ag-rtl .ag-filter-toolpanel-group-level-3 .ag-filter-toolpanel-group-level-4-header {
  padding-right: 54px;
}

.ag-theme-alpine .ag-filter-toolpanel-group-level-4 .ag-filter-toolpanel-group-level-4-header.ag-filter-toolpanel-group-title-bar {
  background-color: transparent;
}
.ag-theme-alpine .ag-ltr .ag-filter-toolpanel-group-level-4 .ag-filter-toolpanel-group-level-5-header {
  padding-left: 70px;
}

.ag-theme-alpine .ag-rtl .ag-filter-toolpanel-group-level-4 .ag-filter-toolpanel-group-level-5-header {
  padding-right: 70px;
}

.ag-theme-alpine .ag-filter-toolpanel-group-level-5 .ag-filter-toolpanel-group-level-5-header.ag-filter-toolpanel-group-title-bar {
  background-color: transparent;
}
.ag-theme-alpine .ag-ltr .ag-filter-toolpanel-group-level-5 .ag-filter-toolpanel-group-level-6-header {
  padding-left: 86px;
}

.ag-theme-alpine .ag-rtl .ag-filter-toolpanel-group-level-5 .ag-filter-toolpanel-group-level-6-header {
  padding-right: 86px;
}

.ag-theme-alpine .ag-filter-toolpanel-group-level-6 .ag-filter-toolpanel-group-level-6-header.ag-filter-toolpanel-group-title-bar {
  background-color: transparent;
}
.ag-theme-alpine .ag-ltr .ag-filter-toolpanel-group-level-6 .ag-filter-toolpanel-group-level-7-header {
  padding-left: 102px;
}

.ag-theme-alpine .ag-rtl .ag-filter-toolpanel-group-level-6 .ag-filter-toolpanel-group-level-7-header {
  padding-right: 102px;
}

.ag-theme-alpine .ag-filter-toolpanel-group-level-7 .ag-filter-toolpanel-group-level-7-header.ag-filter-toolpanel-group-title-bar {
  background-color: transparent;
}
.ag-theme-alpine .ag-ltr .ag-filter-toolpanel-group-level-7 .ag-filter-toolpanel-group-level-8-header {
  padding-left: 118px;
}

.ag-theme-alpine .ag-rtl .ag-filter-toolpanel-group-level-7 .ag-filter-toolpanel-group-level-8-header {
  padding-right: 118px;
}

.ag-theme-alpine .ag-filter-toolpanel-group-level-8 .ag-filter-toolpanel-group-level-8-header.ag-filter-toolpanel-group-title-bar {
  background-color: transparent;
}
.ag-theme-alpine .ag-ltr .ag-filter-toolpanel-group-level-8 .ag-filter-toolpanel-group-level-9-header {
  padding-left: 134px;
}

.ag-theme-alpine .ag-rtl .ag-filter-toolpanel-group-level-8 .ag-filter-toolpanel-group-level-9-header {
  padding-right: 134px;
}

.ag-theme-alpine .ag-filter-toolpanel-group-level-9 .ag-filter-toolpanel-group-level-9-header.ag-filter-toolpanel-group-title-bar {
  background-color: transparent;
}
.ag-theme-alpine .ag-ltr .ag-filter-toolpanel-group-level-9 .ag-filter-toolpanel-group-level-10-header {
  padding-left: 150px;
}

.ag-theme-alpine .ag-rtl .ag-filter-toolpanel-group-level-9 .ag-filter-toolpanel-group-level-10-header {
  padding-right: 150px;
}

.ag-theme-alpine .ag-filter-toolpanel-group-level-10 .ag-filter-toolpanel-group-level-10-header.ag-filter-toolpanel-group-title-bar {
  background-color: transparent;
}
.ag-theme-alpine .ag-ltr .ag-filter-toolpanel-group-level-10 .ag-filter-toolpanel-group-level-11-header {
  padding-left: 166px;
}

.ag-theme-alpine .ag-rtl .ag-filter-toolpanel-group-level-10 .ag-filter-toolpanel-group-level-11-header {
  padding-right: 166px;
}

.ag-theme-alpine .ag-filter-toolpanel-instance-header.ag-filter-toolpanel-group-level-1-header {
  padding-left: 6px;
}
.ag-theme-alpine .ag-filter-toolpanel-instance-filter {
  border-top: solid 1px;
  border-top-color: #babfc7;
  border-top-color: var(--ag-border-color, #babfc7);
  border-bottom: solid 1px;
  border-bottom-color: #babfc7;
  border-bottom-color: var(--ag-border-color, #babfc7);
  margin-top: 6px;
}
.ag-theme-alpine .ag-ltr .ag-filter-toolpanel-instance-header-icon {
  margin-left: 6px;
}

.ag-theme-alpine .ag-rtl .ag-filter-toolpanel-instance-header-icon {
  margin-right: 6px;
}

.ag-theme-alpine .ag-pivot-mode-panel {
  min-height: 48px;
  height: 48px;
  display: -webkit-box;
  display: flex;
}
.ag-theme-alpine .ag-pivot-mode-select {
  display: -webkit-box;
  display: flex;
  -webkit-box-align: center;
          align-items: center;
}
.ag-theme-alpine .ag-ltr .ag-pivot-mode-select {
  margin-left: 12px;
}

.ag-theme-alpine .ag-rtl .ag-pivot-mode-select {
  margin-right: 12px;
}

.ag-theme-alpine .ag-keyboard-focus .ag-column-select-header:focus {
  outline: none;
}
.ag-theme-alpine .ag-keyboard-focus .ag-column-select-header:focus::after {
  content: "";
  position: absolute;
  background-color: transparent;
  pointer-events: none;
  top: 4px;
  left: 4px;
  display: block;
  width: calc(100% - 8px);
  height: calc(100% - 8px);
  border: 1px solid;
  border-color: rgba(33, 150, 243, 0.4);
  border-color: var(--ag-input-focus-border-color, rgba(33, 150, 243, 0.4));
}
.ag-theme-alpine .ag-column-select-header {
  height: 48px;
  -webkit-box-align: center;
          align-items: center;
  padding: 0 12px;
  border-bottom: solid 1px;
  border-bottom-color: #dde2eb;
  border-bottom-color: var(--ag-secondary-border-color, #dde2eb);
}
.ag-theme-alpine .ag-column-panel-column-select {
  border-bottom: solid 1px;
  border-bottom-color: #dde2eb;
  border-bottom-color: var(--ag-secondary-border-color, #dde2eb);
  border-top: solid 1px;
  border-top-color: #dde2eb;
  border-top-color: var(--ag-secondary-border-color, #dde2eb);
}
.ag-theme-alpine .ag-column-group-icons,
.ag-theme-alpine .ag-column-select-header-icon {
  color: #181d1f;
  color: var(--ag-secondary-foreground-color, var(--ag-foreground-color, #181d1f));
}
.ag-theme-alpine .ag-column-select-list .ag-list-item-hovered::after {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  height: 1px;
  background-color: #2196f3;
  background-color: var(--ag-range-selection-border-color, #2196f3);
}
.ag-theme-alpine .ag-column-select-list .ag-item-highlight-top::after {
  top: 0;
}
.ag-theme-alpine .ag-column-select-list .ag-item-highlight-bottom::after {
  bottom: 0;
}
.ag-theme-alpine .ag-header {
  background-color: #f8f8f8;
  background-color: var(--ag-header-background-color, #f8f8f8);
  border-bottom: solid 1px;
  border-bottom-color: #babfc7;
  border-bottom-color: var(--ag-border-color, #babfc7);
}
.ag-theme-alpine .ag-header-row {
  color: #181d1f;
  color: var(--ag-header-foreground-color, var(--ag-secondary-foreground-color, var(--ag-foreground-color, #181d1f)));
  height: 48px;
}
.ag-theme-alpine .ag-pinned-right-header {
  border-left: solid 1px;
  border-left-color: #babfc7;
  border-left-color: var(--ag-border-color, #babfc7);
}
.ag-theme-alpine .ag-pinned-left-header {
  border-right: solid 1px;
  border-right-color: #babfc7;
  border-right-color: var(--ag-border-color, #babfc7);
}
.ag-theme-alpine .ag-ltr .ag-header-cell:not(.ag-right-aligned-header) .ag-header-label-icon {
  margin-left: 6px;
}

.ag-theme-alpine .ag-rtl .ag-header-cell:not(.ag-right-aligned-header) .ag-header-label-icon {
  margin-right: 6px;
}

.ag-theme-alpine .ag-ltr .ag-header-cell.ag-right-aligned-header .ag-header-label-icon {
  margin-right: 6px;
}

.ag-theme-alpine .ag-rtl .ag-header-cell.ag-right-aligned-header .ag-header-label-icon {
  margin-left: 6px;
}

.ag-theme-alpine .ag-header-cell,
.ag-theme-alpine .ag-header-group-cell {
  padding-left: 18px;
  padding-right: 18px;
}
.ag-theme-alpine .ag-header-cell.ag-header-cell-moving,
.ag-theme-alpine .ag-header-group-cell.ag-header-cell-moving {
  background-color: #fff;
  background-color: var(--ag-header-cell-moving-background-color, var(--ag-background-color, #fff));
}
.ag-theme-alpine .ag-keyboard-focus .ag-header-cell:focus {
  outline: none;
}
.ag-theme-alpine .ag-keyboard-focus .ag-header-cell:focus::after {
  content: "";
  position: absolute;
  background-color: transparent;
  pointer-events: none;
  top: 4px;
  left: 4px;
  display: block;
  width: calc(100% - 8px);
  height: calc(100% - 8px);
  border: 1px solid;
  border-color: rgba(33, 150, 243, 0.4);
  border-color: var(--ag-input-focus-border-color, rgba(33, 150, 243, 0.4));
}
.ag-theme-alpine .ag-keyboard-focus .ag-header-group-cell:focus {
  outline: none;
}
.ag-theme-alpine .ag-keyboard-focus .ag-header-group-cell:focus::after {
  content: "";
  position: absolute;
  background-color: transparent;
  pointer-events: none;
  top: 4px;
  left: 4px;
  display: block;
  width: calc(100% - 8px);
  height: calc(100% - 8px);
  border: 1px solid;
  border-color: rgba(33, 150, 243, 0.4);
  border-color: var(--ag-input-focus-border-color, rgba(33, 150, 243, 0.4));
}
.ag-theme-alpine .ag-header-icon {
  color: #181d1f;
  color: var(--ag-secondary-foreground-color, var(--ag-foreground-color, #181d1f));
}
.ag-theme-alpine .ag-header-expand-icon {
  cursor: pointer;
}
.ag-theme-alpine .ag-ltr .ag-header-expand-icon {
  padding-left: 4px;
}

.ag-theme-alpine .ag-rtl .ag-header-expand-icon {
  padding-right: 4px;
}

.ag-theme-alpine .ag-header-row:not(:first-child) .ag-header-cell,
.ag-theme-alpine .ag-header-row:not(:first-child) .ag-header-group-cell.ag-header-group-cell-with-group {
  border-top: solid 1px;
  border-top-color: #babfc7;
  border-top-color: var(--ag-border-color, #babfc7);
}
.ag-theme-alpine .ag-header-cell-resize {
  display: -webkit-box;
  display: flex;
  -webkit-box-align: center;
          align-items: center;
}
.ag-theme-alpine .ag-header-cell-resize::after {
  content: "";
  position: absolute;
  z-index: 1;
  display: block;
  left: calc(50% - 1px);
  width: 2px;
  height: 30%;
  top: calc(50% - 15%);
  background-color: rgba(186, 191, 199, 0.5);
  background-color: var(--ag-header-column-resize-handle-color, rgba(186, 191, 199, 0.5));
}
.ag-theme-alpine .ag-pinned-right-header .ag-header-cell-resize::after {
  left: calc(50% - 2px);
}
.ag-theme-alpine .ag-ltr .ag-header-select-all {
  margin-right: 18px;
}

.ag-theme-alpine .ag-rtl .ag-header-select-all {
  margin-left: 18px;
}

.ag-theme-alpine .ag-ltr .ag-floating-filter-button {
  margin-left: 18px;
}

.ag-theme-alpine .ag-rtl .ag-floating-filter-button {
  margin-right: 18px;
}

.ag-theme-alpine .ag-floating-filter-button-button {
  color: inherit;
  font-family: inherit;
  font-size: inherit;
  font-weight: inherit;
  line-height: inherit;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  background: transparent;
  border: none;
  height: 16px;
  padding: 0;
  width: 16px;
}
.ag-theme-alpine .ag-filter-loading {
  background-color: #f8f8f8;
  background-color: var(--ag-control-panel-background-color, #f8f8f8);
  height: 100%;
  padding: 12px 12px;
  position: absolute;
  width: 100%;
  z-index: 1;
}
.ag-theme-alpine .ag-paging-panel {
  border-top: 1px solid;
  border-top-color: #babfc7;
  border-top-color: var(--ag-border-color, #babfc7);
  color: #181d1f;
  color: var(--ag-secondary-foreground-color, var(--ag-foreground-color, #181d1f));
  height: 48px;
}
.ag-theme-alpine .ag-paging-panel > * {
  margin: 0 18px;
}
.ag-theme-alpine .ag-paging-button {
  cursor: pointer;
}
.ag-theme-alpine .ag-paging-button.ag-disabled {
  cursor: default;
  color: rgba(24, 29, 31, 0.5);
  color: var(--ag-disabled-foreground-color, rgba(24, 29, 31, 0.5));
}
.ag-theme-alpine .ag-keyboard-focus .ag-paging-button:focus {
  outline: none;
}
.ag-theme-alpine .ag-keyboard-focus .ag-paging-button:focus::after {
  content: "";
  position: absolute;
  background-color: transparent;
  pointer-events: none;
  top: 0px;
  left: 0px;
  display: block;
  width: calc(100% - 0px);
  height: calc(100% - 0px);
  border: 1px solid;
  border-color: rgba(33, 150, 243, 0.4);
  border-color: var(--ag-input-focus-border-color, rgba(33, 150, 243, 0.4));
}
.ag-theme-alpine .ag-paging-button, .ag-theme-alpine .ag-paging-description {
  margin: 0 6px;
}
.ag-theme-alpine .ag-status-bar {
  border-top: solid 1px;
  border-top-color: #babfc7;
  border-top-color: var(--ag-border-color, #babfc7);
  color: rgba(24, 29, 31, 0.5);
  color: var(--ag-disabled-foreground-color, rgba(24, 29, 31, 0.5));
  padding-right: 24px;
  padding-left: 24px;
  line-height: 1.5;
}
.ag-theme-alpine .ag-status-name-value-value {
  color: #181d1f;
  color: var(--ag-foreground-color, #181d1f);
}
.ag-theme-alpine .ag-status-bar-center {
  text-align: center;
}
.ag-theme-alpine .ag-status-name-value {
  margin-left: 6px;
  margin-right: 6px;
  padding-top: 12px;
  padding-bottom: 12px;
}
.ag-theme-alpine .ag-column-drop-cell {
  background: rgba(24, 29, 31, 0.07);
  background: var(--ag-chip-background-color, rgba(24, 29, 31, 0.07));
  border-radius: 24px;
  height: 24px;
  padding: 0 3px;
  border: 1px solid transparent;
  cursor: pointer;
}
.ag-theme-alpine .ag-keyboard-focus .ag-column-drop-cell:focus {
  outline: none;
}
.ag-theme-alpine .ag-keyboard-focus .ag-column-drop-cell:focus::after {
  content: "";
  position: absolute;
  background-color: transparent;
  pointer-events: none;
  top: 2px;
  left: 2px;
  display: block;
  width: calc(100% - 4px);
  height: calc(100% - 4px);
  border: 1px solid;
  border-color: rgba(33, 150, 243, 0.4);
  border-color: var(--ag-input-focus-border-color, rgba(33, 150, 243, 0.4));
}
.ag-theme-alpine .ag-column-drop-cell-text {
  margin: 0 6px;
}
.ag-theme-alpine .ag-column-drop-cell-button {
  min-width: 24px;
  margin: 0 3px;
  color: #181d1f;
  color: var(--ag-secondary-foreground-color, var(--ag-foreground-color, #181d1f));
}
.ag-theme-alpine .ag-column-drop-cell-drag-handle {
  margin-left: 12px;
}
.ag-theme-alpine .ag-column-drop-cell-ghost {
  opacity: 0.5;
}
.ag-theme-alpine .ag-column-drop-horizontal {
  background-color: #f8f8f8;
  background-color: var(--ag-control-panel-background-color, #f8f8f8);
  color: #181d1f;
  color: var(--ag-secondary-foreground-color, var(--ag-foreground-color, #181d1f));
  height: 42px;
  border-bottom: solid 1px;
  border-bottom-color: #babfc7;
  border-bottom-color: var(--ag-border-color, #babfc7);
}
.ag-theme-alpine .ag-ltr .ag-column-drop-horizontal {
  padding-left: 18px;
}

.ag-theme-alpine .ag-rtl .ag-column-drop-horizontal {
  padding-right: 18px;
}

.ag-theme-alpine .ag-ltr .ag-column-drop-horizontal-half-width:not(:last-child) {
  border-right: solid 1px;
  border-right-color: #babfc7;
  border-right-color: var(--ag-border-color, #babfc7);
}

.ag-theme-alpine .ag-rtl .ag-column-drop-horizontal-half-width:not(:last-child) {
  border-left: solid 1px;
  border-left-color: #babfc7;
  border-left-color: var(--ag-border-color, #babfc7);
}

.ag-theme-alpine .ag-column-drop-horizontal-cell-separator {
  margin: 0 6px;
  color: #181d1f;
  color: var(--ag-secondary-foreground-color, var(--ag-foreground-color, #181d1f));
}
.ag-theme-alpine .ag-column-drop-horizontal-empty-message {
  color: rgba(24, 29, 31, 0.5);
  color: var(--ag-disabled-foreground-color, rgba(24, 29, 31, 0.5));
}
.ag-theme-alpine .ag-ltr .ag-column-drop-horizontal-icon {
  margin-right: 18px;
}

.ag-theme-alpine .ag-rtl .ag-column-drop-horizontal-icon {
  margin-left: 18px;
}

.ag-theme-alpine .ag-column-drop-vertical-list {
  padding-bottom: 6px;
  padding-right: 6px;
  padding-left: 6px;
}
.ag-theme-alpine .ag-column-drop-vertical-cell {
  margin-top: 6px;
}
.ag-theme-alpine .ag-column-drop-vertical {
  min-height: 50px;
  border-bottom: solid 1px;
  border-bottom-color: #dde2eb;
  border-bottom-color: var(--ag-secondary-border-color, #dde2eb);
}
.ag-theme-alpine .ag-column-drop-vertical.ag-last-column-drop {
  border-bottom: none;
}
.ag-theme-alpine .ag-column-drop-vertical-icon {
  margin-left: 6px;
  margin-right: 6px;
}
.ag-theme-alpine .ag-column-drop-vertical-empty-message {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  overflow: hidden;
  color: rgba(24, 29, 31, 0.5);
  color: var(--ag-disabled-foreground-color, rgba(24, 29, 31, 0.5));
  margin-top: 6px;
}
.ag-theme-alpine .ag-select-agg-func-popup {
  border: solid 1px;
  border-color: #babfc7;
  border-color: var(--ag-border-color, #babfc7);
  background: #fff;
  background: var(--ag-background-color, #fff);
  border-radius: 3px;
  -webkit-box-shadow: 0 1px 4px 1px rgba(186, 191, 199, 0.4);
          box-shadow: 0 1px 4px 1px rgba(186, 191, 199, 0.4);
  padding: 6px;
  background: #fff;
  background: var(--ag-background-color, #fff);
  height: 105px;
  padding: 0;
}
.ag-theme-alpine .ag-select-agg-func-virtual-list-item {
  cursor: default;
  padding-left: 12px;
}
.ag-theme-alpine .ag-select-agg-func-virtual-list-item:hover {
  background-color: rgba(33, 150, 243, 0.3);
  background-color: var(--ag-selected-row-background-color, rgba(33, 150, 243, 0.3));
}
.ag-theme-alpine .ag-keyboard-focus .ag-select-agg-func-virtual-list-item:focus {
  outline: none;
}
.ag-theme-alpine .ag-keyboard-focus .ag-select-agg-func-virtual-list-item:focus::after {
  content: "";
  position: absolute;
  background-color: transparent;
  pointer-events: none;
  top: 1px;
  left: 1px;
  display: block;
  width: calc(100% - 2px);
  height: calc(100% - 2px);
  border: 1px solid;
  border-color: rgba(33, 150, 243, 0.4);
  border-color: var(--ag-input-focus-border-color, rgba(33, 150, 243, 0.4));
}
.ag-theme-alpine .ag-sort-indicator-container {
  display: -webkit-box;
  display: flex;
}
.ag-theme-alpine .ag-ltr .ag-sort-indicator-icon {
  padding-left: 6px;
}

.ag-theme-alpine .ag-rtl .ag-sort-indicator-icon {
  padding-right: 6px;
}

.ag-theme-alpine .ag-chart-menu {
  border-radius: 3px;
  background: #fff;
  background: var(--ag-background-color, #fff);
}
.ag-theme-alpine .ag-chart-menu-icon {
  opacity: 0.5;
  line-height: 24px;
  font-size: 24px;
  width: 24px;
  height: 24px;
  margin: 2px 0;
  cursor: pointer;
  border-radius: 3px;
  color: #181d1f;
  color: var(--ag-secondary-foreground-color, var(--ag-foreground-color, #181d1f));
}
.ag-theme-alpine .ag-chart-menu-icon:hover {
  opacity: 1;
}
.ag-theme-alpine .ag-chart-menu-close {
  background: #fff;
  background: var(--ag-background-color, #fff);
}
.ag-theme-alpine .ag-chart-menu-close .ag-icon {
  background: none;
  border: 1px solid #dde2eb;
  border-right: none;
}
.ag-theme-alpine .ag-chart-menu-close .ag-icon:hover {
  background: #f8f8f8;
  background: var(--ag-header-background-color, #f8f8f8);
}
.ag-theme-alpine .ag-chart-mini-thumbnail {
  border: 1px solid;
  border-color: #dde2eb;
  border-color: var(--ag-secondary-border-color, #dde2eb);
  border-radius: 5px;
  margin: 5px;
}
.ag-theme-alpine .ag-chart-mini-thumbnail:nth-last-child(3), .ag-theme-alpine .ag-chart-mini-thumbnail:nth-last-child(3) ~ .ag-chart-mini-thumbnail {
  margin-left: auto;
  margin-right: auto;
}
.ag-theme-alpine .ag-ltr .ag-chart-mini-thumbnail:first-child {
  margin-left: 0;
}

.ag-theme-alpine .ag-rtl .ag-chart-mini-thumbnail:first-child {
  margin-right: 0;
}

.ag-theme-alpine .ag-ltr .ag-chart-mini-thumbnail:last-child {
  margin-right: 0;
}

.ag-theme-alpine .ag-rtl .ag-chart-mini-thumbnail:last-child {
  margin-left: 0;
}

.ag-theme-alpine .ag-chart-mini-thumbnail.ag-selected {
  border-color: #2196f3;
  border-color: var(--ag-minichart-selected-chart-color, var(--ag-checkbox-checked-color, var(--ag-alpine-active-color, #2196f3)));
}
.ag-theme-alpine .ag-chart-settings-card-item {
  background: #181d1f;
  background: var(--ag-foreground-color, #181d1f);
  width: 8px;
  height: 8px;
  border-radius: 4px;
}
.ag-theme-alpine .ag-chart-settings-card-item.ag-selected {
  background-color: #2196f3;
  background-color: var(--ag-minichart-selected-page-color, var(--ag-checkbox-checked-color, var(--ag-alpine-active-color, #2196f3)));
}
.ag-theme-alpine .ag-chart-data-column-drag-handle {
  margin-left: 6px;
}
.ag-theme-alpine .ag-charts-settings-group-title-bar,
.ag-theme-alpine .ag-charts-data-group-title-bar,
.ag-theme-alpine .ag-charts-format-top-level-group-title-bar {
  border-top: solid 1px;
  border-top-color: #dde2eb;
  border-top-color: var(--ag-secondary-border-color, #dde2eb);
}
.ag-theme-alpine .ag-charts-settings-group-container {
  padding: 6px;
}
.ag-theme-alpine .ag-charts-data-group-container {
  padding: 6px 12px;
}
.ag-theme-alpine .ag-charts-data-group-container .ag-charts-data-group-item:not(.ag-charts-format-sub-level-group) {
  height: 24px;
}
.ag-theme-alpine .ag-charts-data-group-container .ag-list-item-hovered::after {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  height: 1px;
  background-color: #2196f3;
  background-color: var(--ag-range-selection-border-color, #2196f3);
}
.ag-theme-alpine .ag-charts-data-group-container .ag-item-highlight-top::after {
  top: 0;
}
.ag-theme-alpine .ag-charts-data-group-container .ag-item-highlight-bottom::after {
  bottom: 0;
}
.ag-theme-alpine .ag-charts-format-top-level-group-container {
  margin-left: 12px;
  padding: 6px;
}
.ag-theme-alpine .ag-charts-format-top-level-group-item {
  margin: 6px 0;
}
.ag-theme-alpine .ag-charts-format-sub-level-group-container {
  padding: 12px 12px;
  padding-bottom: 3px;
}
.ag-theme-alpine .ag-charts-format-sub-level-group-container > * {
  margin-bottom: 9px;
}
.ag-theme-alpine .ag-charts-group-container.ag-group-container-horizontal {
  padding: 6px;
}
.ag-theme-alpine .ag-chart-data-section,
.ag-theme-alpine .ag-chart-format-section {
  display: -webkit-box;
  display: flex;
  margin: 0;
}
.ag-theme-alpine .ag-chart-menu-panel {
  background-color: #f8f8f8;
  background-color: var(--ag-control-panel-background-color, #f8f8f8);
}
.ag-theme-alpine .ag-ltr .ag-chart-menu-panel {
  border-left: solid 1px;
  border-left-color: #babfc7;
  border-left-color: var(--ag-border-color, #babfc7);
}

.ag-theme-alpine .ag-rtl .ag-chart-menu-panel {
  border-right: solid 1px;
  border-right-color: #babfc7;
  border-right-color: var(--ag-border-color, #babfc7);
}

.ag-theme-alpine .ag-date-time-list-page-title {
  -webkit-box-flex: 1;
          flex-grow: 1;
  text-align: center;
}
.ag-theme-alpine .ag-date-time-list-page-column-label {
  text-align: center;
}
.ag-theme-alpine .ag-date-time-list-page-entry {
  text-align: center;
}
.ag-theme-alpine .ag-checkbox-input-wrapper {
  font-family: "agGridAlpine";
  font-size: 16px;
  line-height: 16px;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  width: 16px;
  height: 16px;
  background-color: #fff;
  background-color: var(--ag-checkbox-background-color, var(--ag-background-color, #fff));
  border-radius: 3px;
  display: inline-block;
  vertical-align: middle;
  -webkit-box-flex: 0;
          flex: none;
}
.ag-theme-alpine .ag-checkbox-input-wrapper input, .ag-theme-alpine .ag-checkbox-input-wrapper input {
  -webkit-appearance: none;
  opacity: 0;
  width: 100%;
  height: 100%;
}
.ag-theme-alpine .ag-checkbox-input-wrapper:focus-within, .ag-theme-alpine .ag-checkbox-input-wrapper:active {
  outline: none;
  -webkit-box-shadow: 0 0 2px 0.1rem rgba(33, 150, 243, 0.4);
          box-shadow: 0 0 2px 0.1rem rgba(33, 150, 243, 0.4);
}
.ag-theme-alpine .ag-checkbox-input-wrapper.ag-disabled {
  opacity: 0.5;
}
.ag-theme-alpine .ag-checkbox-input-wrapper::after {
  content: "\f108";
  color: #999;
  color: var(--ag-checkbox-unchecked-color, #999);
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
}
.ag-theme-alpine .ag-checkbox-input-wrapper.ag-checked::after {
  content: "\f106";
  color: #2196f3;
  color: var(--ag-checkbox-checked-color, var(--ag-alpine-active-color, #2196f3));
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
}
.ag-theme-alpine .ag-checkbox-input-wrapper.ag-indeterminate::after {
  content: "\f107";
  color: #999;
  color: var(--ag-checkbox-indeterminate-color, var(--ag-checkbox-unchecked-color, #999));
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
}
.ag-theme-alpine .ag-toggle-button-input-wrapper {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  width: 28px;
  height: 18px;
  background-color: #999;
  background-color: var(--ag-toggle-button-off-background-color, var(--ag-checkbox-unchecked-color, #999));
  border-radius: 9px;
  position: relative;
  -webkit-box-flex: 0;
          flex: none;
  border: 1px solid;
  border-color: #999;
  border-color: var(--ag-toggle-button-off-border-color, var(--ag-checkbox-unchecked-color, #999));
}
.ag-theme-alpine .ag-toggle-button-input-wrapper input {
  opacity: 0;
  height: 100%;
  width: 100%;
}
.ag-theme-alpine .ag-toggle-button-input-wrapper:focus-within {
  outline: none;
  -webkit-box-shadow: 0 0 2px 0.1rem rgba(33, 150, 243, 0.4);
          box-shadow: 0 0 2px 0.1rem rgba(33, 150, 243, 0.4);
}
.ag-theme-alpine .ag-toggle-button-input-wrapper.ag-disabled {
  opacity: 0.5;
}
.ag-theme-alpine .ag-toggle-button-input-wrapper.ag-checked {
  background-color: #2196f3;
  background-color: var(--ag-toggle-button-on-background-color, var(--ag-checkbox-checked-color, var(--ag-alpine-active-color, #2196f3)));
  border-color: #2196f3;
  border-color: var(--ag-toggle-button-on-border-color, var(--ag-checkbox-checked-color, var(--ag-alpine-active-color, #2196f3)));
}
.ag-theme-alpine .ag-toggle-button-input-wrapper::before {
  content: " ";
  position: absolute;
  top: -1px;
  left: -1px;
  display: block;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  height: 18px;
  width: 18px;
  background-color: #fff;
  background-color: var(--ag-toggle-button-switch-background-color, var(--ag-background-color, #fff));
  border-radius: 9px;
  -webkit-transition: left 100ms;
  transition: left 100ms;
  border: 1px solid;
  border-color: #999;
  border-color: var(--ag-toggle-button-switch-border-color, var(--ag-toggle-button-off-border-color, var(--ag-checkbox-unchecked-color, #999)));
}
.ag-theme-alpine .ag-toggle-button-input-wrapper.ag-checked::before {
  left: calc(100% - 18px );
  border-color: #2196f3;
  border-color: var(--ag-toggle-button-on-border-color, var(--ag-checkbox-checked-color, var(--ag-alpine-active-color, #2196f3)));
}
.ag-theme-alpine .ag-radio-button-input-wrapper {
  font-family: "agGridAlpine";
  font-size: 16px;
  line-height: 16px;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  width: 16px;
  height: 16px;
  background-color: #fff;
  background-color: var(--ag-checkbox-background-color, var(--ag-background-color, #fff));
  border-radius: 3px;
  display: inline-block;
  vertical-align: middle;
  -webkit-box-flex: 0;
          flex: none;
  border-radius: 16px;
}
.ag-theme-alpine .ag-radio-button-input-wrapper input, .ag-theme-alpine .ag-radio-button-input-wrapper input {
  -webkit-appearance: none;
  opacity: 0;
  width: 100%;
  height: 100%;
}
.ag-theme-alpine .ag-radio-button-input-wrapper:focus-within, .ag-theme-alpine .ag-radio-button-input-wrapper:active {
  outline: none;
  -webkit-box-shadow: 0 0 2px 0.1rem rgba(33, 150, 243, 0.4);
          box-shadow: 0 0 2px 0.1rem rgba(33, 150, 243, 0.4);
}
.ag-theme-alpine .ag-radio-button-input-wrapper.ag-disabled {
  opacity: 0.5;
}
.ag-theme-alpine .ag-radio-button-input-wrapper::after {
  content: "\f126";
  color: #999;
  color: var(--ag-checkbox-unchecked-color, #999);
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
}
.ag-theme-alpine .ag-radio-button-input-wrapper.ag-checked::after {
  content: "\f127";
  color: #2196f3;
  color: var(--ag-checkbox-checked-color, var(--ag-alpine-active-color, #2196f3));
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
}
.ag-theme-alpine input[class^=ag-][type=range] {
  -webkit-appearance: none;
  width: 100%;
  height: 100%;
  background: none;
  overflow: visible;
}
.ag-theme-alpine input[class^=ag-][type=range]::-webkit-slider-runnable-track {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 3px;
  background-color: #babfc7;
  background-color: var(--ag-border-color, #babfc7);
  border-radius: 3px;
  border-radius: 3px;
}
.ag-theme-alpine input[class^=ag-][type=range]::-moz-range-track {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 3px;
  background-color: #babfc7;
  background-color: var(--ag-border-color, #babfc7);
  border-radius: 3px;
  border-radius: 3px;
}
.ag-theme-alpine input[class^=ag-][type=range]::-ms-track {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 3px;
  background-color: #babfc7;
  background-color: var(--ag-border-color, #babfc7);
  border-radius: 3px;
  border-radius: 3px;
  color: transparent;
  width: calc(100% - 2px);
}
.ag-theme-alpine input[class^=ag-][type=range]::-webkit-slider-thumb {
  margin: 0;
  padding: 0;
  -webkit-appearance: none;
  width: 16px;
  height: 16px;
  background-color: #fff;
  background-color: var(--ag-background-color, #fff);
  border: 1px solid;
  border-color: #999;
  border-color: var(--ag-checkbox-unchecked-color, #999);
  border-radius: 16px;
  -webkit-transform: translateY(-6.5px);
          transform: translateY(-6.5px);
}
.ag-theme-alpine input[class^=ag-][type=range]::-ms-thumb {
  margin: 0;
  padding: 0;
  -webkit-appearance: none;
  width: 16px;
  height: 16px;
  background-color: #fff;
  background-color: var(--ag-background-color, #fff);
  border: 1px solid;
  border-color: #999;
  border-color: var(--ag-checkbox-unchecked-color, #999);
  border-radius: 16px;
}
.ag-theme-alpine input[class^=ag-][type=range]::-moz-ag-range-thumb {
  margin: 0;
  padding: 0;
  -webkit-appearance: none;
  width: 16px;
  height: 16px;
  background-color: #fff;
  background-color: var(--ag-background-color, #fff);
  border: 1px solid;
  border-color: #999;
  border-color: var(--ag-checkbox-unchecked-color, #999);
  border-radius: 16px;
}
.ag-theme-alpine input[class^=ag-][type=range]:focus {
  outline: none;
}
.ag-theme-alpine input[class^=ag-][type=range]:focus::-webkit-slider-thumb {
  -webkit-box-shadow: 0 0 2px 0.1rem rgba(33, 150, 243, 0.4);
          box-shadow: 0 0 2px 0.1rem rgba(33, 150, 243, 0.4);
  border-color: #2196f3;
  border-color: var(--ag-checkbox-checked-color, var(--ag-alpine-active-color, #2196f3));
}
.ag-theme-alpine input[class^=ag-][type=range]:focus::-ms-thumb {
  box-shadow: 0 0 2px 0.1rem rgba(33, 150, 243, 0.4);
  border-color: #2196f3;
  border-color: var(--ag-checkbox-checked-color, var(--ag-alpine-active-color, #2196f3));
}
.ag-theme-alpine input[class^=ag-][type=range]:focus::-moz-ag-range-thumb {
  box-shadow: 0 0 2px 0.1rem rgba(33, 150, 243, 0.4);
  border-color: #2196f3;
  border-color: var(--ag-checkbox-checked-color, var(--ag-alpine-active-color, #2196f3));
}
.ag-theme-alpine input[class^=ag-][type=range]:active::-webkit-slider-runnable-track {
  background-color: rgba(33, 150, 243, 0.4);
  background-color: var(--ag-input-focus-border-color, rgba(33, 150, 243, 0.4));
}
.ag-theme-alpine input[class^=ag-][type=range]:active::-moz-ag-range-track {
  background-color: rgba(33, 150, 243, 0.4);
  background-color: var(--ag-input-focus-border-color, rgba(33, 150, 243, 0.4));
}
.ag-theme-alpine input[class^=ag-][type=range]:active::-ms-track {
  background-color: rgba(33, 150, 243, 0.4);
  background-color: var(--ag-input-focus-border-color, rgba(33, 150, 243, 0.4));
}
.ag-theme-alpine input[class^=ag-][type=range]:disabled {
  opacity: 0.5;
}
.ag-theme-alpine .ag-filter-toolpanel-header,
.ag-theme-alpine .ag-filter-toolpanel-search,
.ag-theme-alpine .ag-status-bar,
.ag-theme-alpine .ag-header-row,
.ag-theme-alpine .ag-panel-title-bar-title,
.ag-theme-alpine .ag-multi-filter-group-title-bar {
  font-weight: 700;
  color: #181d1f;
  color: var(--ag-header-foreground-color, var(--ag-secondary-foreground-color, var(--ag-foreground-color, #181d1f)));
}
.ag-theme-alpine .ag-rtl .ag-pinned-left-header .ag-header-row::before, .ag-theme-alpine .ag-ltr .ag-pinned-right-header .ag-header-row::after {
  content: "";
  position: absolute;
  height: calc(100% - 20px);
  top: 10px;
  width: 1px;
  background-color: #babfc7;
  background-color: var(--ag-border-color, #babfc7);
}
.ag-theme-alpine .ag-ltr .ag-pinned-right-header .ag-header-row::after {
  right: 0;
}
.ag-theme-alpine .ag-rtl .ag-pinned-left-header .ag-header-row::before {
  left: 0;
}
.ag-theme-alpine .ag-row {
  font-size: 14px;
}
.ag-theme-alpine input[class^=ag-]:not([type]),
.ag-theme-alpine input[class^=ag-][type=text],
.ag-theme-alpine input[class^=ag-][type=number],
.ag-theme-alpine input[class^=ag-][type=tel],
.ag-theme-alpine input[class^=ag-][type=date],
.ag-theme-alpine input[class^=ag-][type=datetime-local],
.ag-theme-alpine textarea[class^=ag-] {
  min-height: 24px;
  border-radius: 3px;
}
.ag-theme-alpine .ag-ltr input[class^=ag-]:not([type]),
.ag-theme-alpine .ag-ltr input[class^=ag-][type=text],
.ag-theme-alpine .ag-ltr input[class^=ag-][type=number],
.ag-theme-alpine .ag-ltr input[class^=ag-][type=tel],
.ag-theme-alpine .ag-ltr input[class^=ag-][type=date],
.ag-theme-alpine .ag-ltr input[class^=ag-][type=datetime-local],
.ag-theme-alpine .ag-ltr textarea[class^=ag-] {
  padding-left: 6px;
}

.ag-theme-alpine .ag-rtl input[class^=ag-]:not([type]),
.ag-theme-alpine .ag-rtl input[class^=ag-][type=text],
.ag-theme-alpine .ag-rtl input[class^=ag-][type=number],
.ag-theme-alpine .ag-rtl input[class^=ag-][type=tel],
.ag-theme-alpine .ag-rtl input[class^=ag-][type=date],
.ag-theme-alpine .ag-rtl input[class^=ag-][type=datetime-local],
.ag-theme-alpine .ag-rtl textarea[class^=ag-] {
  padding-right: 6px;
}

.ag-theme-alpine .ag-tab {
  padding: 9px;
  -webkit-transition: color 0.4s;
  transition: color 0.4s;
}
.ag-theme-alpine .ag-tab-selected {
  color: #2196f3;
  color: var(--ag-alpine-active-color, #2196f3);
}
.ag-theme-alpine .ag-menu {
  background-color: #f8f8f8;
  background-color: var(--ag-control-panel-background-color, #f8f8f8);
}
.ag-theme-alpine .ag-menu-header {
  background-color: #f8f8f8;
  background-color: var(--ag-control-panel-background-color, #f8f8f8);
  padding-top: 1px;
}
.ag-theme-alpine .ag-tabs-header {
  border-bottom: solid 1px;
  border-bottom-color: #babfc7;
  border-bottom-color: var(--ag-border-color, #babfc7);
}
.ag-theme-alpine .ag-charts-settings-group-title-bar,
.ag-theme-alpine .ag-charts-data-group-title-bar,
.ag-theme-alpine .ag-charts-format-top-level-group-title-bar {
  padding: 6px 12px;
  line-height: 20px;
}
.ag-theme-alpine .ag-chart-mini-thumbnail {
  background-color: #fff;
  background-color: var(--ag-background-color, #fff);
}
.ag-theme-alpine .ag-chart-settings-nav-bar {
  border-top: solid 1px;
  border-top-color: #dde2eb;
  border-top-color: var(--ag-secondary-border-color, #dde2eb);
}
.ag-theme-alpine .ag-ltr .ag-group-title-bar-icon {
  margin-right: 6px;
}

.ag-theme-alpine .ag-rtl .ag-group-title-bar-icon {
  margin-left: 6px;
}

.ag-theme-alpine .ag-charts-format-top-level-group-toolbar {
  margin-top: 6px;
}
.ag-theme-alpine .ag-ltr .ag-charts-format-top-level-group-toolbar {
  padding-left: 20px;
}

.ag-theme-alpine .ag-rtl .ag-charts-format-top-level-group-toolbar {
  padding-right: 20px;
}

.ag-theme-alpine .ag-charts-format-sub-level-group {
  border-left: dashed 1px;
  border-left-color: #babfc7;
  border-left-color: var(--ag-border-color, #babfc7);
  padding-left: 6px;
  margin-bottom: 12px;
}
.ag-theme-alpine .ag-charts-format-sub-level-group-title-bar {
  padding-top: 0;
  padding-bottom: 0;
  background: none;
  font-weight: 700;
}
.ag-theme-alpine .ag-charts-format-sub-level-group-container {
  padding-bottom: 0;
}
.ag-theme-alpine .ag-charts-format-sub-level-group-item:last-child {
  margin-bottom: 0;
}
.ag-theme-alpine .ag-dnd-ghost {
  font-size: 12px;
  font-weight: 700;
}
.ag-theme-alpine .ag-side-buttons {
  width: 30px;
}
.ag-theme-alpine .ag-standard-button {
  -moz-appearance: none;
       appearance: none;
  -webkit-appearance: none;
  border-radius: 3px;
  border: 1px solid;
  border-color: #2196f3;
  border-color: var(--ag-alpine-active-color, #2196f3);
  color: #2196f3;
  color: var(--ag-alpine-active-color, #2196f3);
  background-color: #fff;
  background-color: var(--ag-background-color, #fff);
  font-weight: 600;
  padding: 6px 12px;
}
.ag-theme-alpine .ag-standard-button:hover {
  border-color: #2196f3;
  border-color: var(--ag-alpine-active-color, #2196f3);
  background-color: rgba(33, 150, 243, 0.1);
  background-color: var(--ag-row-hover-color, rgba(33, 150, 243, 0.1));
}
.ag-theme-alpine .ag-standard-button:active {
  border-color: #2196f3;
  border-color: var(--ag-alpine-active-color, #2196f3);
  background-color: #2196f3;
  background-color: var(--ag-alpine-active-color, #2196f3);
  color: #fff;
  color: var(--ag-background-color, #fff);
}
.ag-theme-alpine .ag-standard-button:disabled {
  color: rgba(24, 29, 31, 0.5);
  color: var(--ag-disabled-foreground-color, rgba(24, 29, 31, 0.5));
  background-color: #f1f2f4;
  background-color: var(--ag-input-disabled-background-color, #f1f2f4);
  border-color: rgba(186, 191, 199, 0.3);
  border-color: var(--ag-input-disabled-border-color, rgba(186, 191, 199, 0.3));
}
.ag-theme-alpine .ag-column-drop-vertical {
  min-height: 75px;
}
.ag-theme-alpine .ag-column-drop-vertical-title-bar {
  padding: 12px;
  padding-bottom: 0px;
}
.ag-theme-alpine .ag-column-drop-vertical-empty-message {
  display: -webkit-box;
  display: flex;
  -webkit-box-align: center;
          align-items: center;
  border: dashed 1px;
  border-color: #babfc7;
  border-color: var(--ag-border-color, #babfc7);
  margin: 12px;
  padding: 12px;
}
.ag-theme-alpine .ag-column-drop-empty-message {
  color: #181d1f;
  color: var(--ag-foreground-color, #181d1f);
  opacity: 0.75;
}
.ag-theme-alpine .ag-status-bar {
  font-weight: normal;
}
.ag-theme-alpine .ag-status-name-value-value {
  font-weight: 700;
}
.ag-theme-alpine .ag-paging-number, .ag-theme-alpine .ag-paging-row-summary-panel-number {
  font-weight: 700;
}
.ag-theme-alpine .ag-column-drop-cell-button {
  opacity: 0.5;
}
.ag-theme-alpine .ag-column-drop-cell-button:hover {
  opacity: 0.75;
}
.ag-theme-alpine .ag-header-cell-menu-button:hover,
.ag-theme-alpine .ag-side-button-button:hover,
.ag-theme-alpine .ag-tab:hover,
.ag-theme-alpine .ag-panel-title-bar-button:hover,
.ag-theme-alpine .ag-header-expand-icon:hover,
.ag-theme-alpine .ag-column-group-icons:hover,
.ag-theme-alpine .ag-group-expanded .ag-icon:hover,
.ag-theme-alpine .ag-group-contracted .ag-icon:hover,
.ag-theme-alpine .ag-chart-settings-prev:hover,
.ag-theme-alpine .ag-chart-settings-next:hover,
.ag-theme-alpine .ag-group-title-bar-icon:hover,
.ag-theme-alpine .ag-column-select-header-icon:hover,
.ag-theme-alpine .ag-floating-filter-button-button:hover,
.ag-theme-alpine .ag-filter-toolpanel-expand:hover,
.ag-theme-alpine .ag-chart-menu-icon:hover {
  color: #2196f3;
  color: var(--ag-alpine-active-color, #2196f3);
}
.ag-theme-alpine .ag-chart-settings-card-item.ag-not-selected:hover {
  opacity: 0.35;
}
.ag-theme-alpine .ag-ltr .ag-panel-title-bar-button {
  margin-left: 12px;
  margin-right: 6px;
}

.ag-theme-alpine .ag-rtl .ag-panel-title-bar-button {
  margin-right: 12px;
  margin-left: 6px;
}

.ag-theme-alpine .ag-filter-toolpanel-group-container {
  padding-left: 6px;
}
.ag-theme-alpine .ag-filter-toolpanel-instance-filter {
  border: none;
  background-color: #f8f8f8;
  background-color: var(--ag-control-panel-background-color, #f8f8f8);
  border-left: dashed 1px;
  border-left-color: #babfc7;
  border-left-color: var(--ag-border-color, #babfc7);
  margin-left: 8px;
  padding-left: 8px;
  margin-right: 12px;
}
.ag-theme-alpine .ag-set-filter-list {
  padding-top: 3px;
  padding-bottom: 3px;
}
.ag-theme-alpine .ag-layout-auto-height .ag-center-cols-clipper, .ag-theme-alpine .ag-layout-auto-height .ag-center-cols-container, .ag-theme-alpine .ag-layout-print .ag-center-cols-clipper, .ag-theme-alpine .ag-layout-print .ag-center-cols-container {
  min-height: 150px;
}
.ag-theme-alpine .ag-overlay-no-rows-wrapper.ag-layout-auto-height {
  padding-top: 60px;
}
.ag-theme-alpine .ag-date-time-list-page-entry-is-current {
  background-color: #2196f3;
  background-color: var(--ag-alpine-active-color, #2196f3);
}
.chart_chartDiv {

}
.chart_contentDiv {
    position: relative;
    border: 1px solid #e1e1e1;
    padding: 20px 10px;
    border-radius: 4px;
    margin-bottom: 5px;
}
.chart_titleSpan {
    width: 95px;
    line-height: 28px;
    margin-right: 5px;
    text-align: center;
    letter-spacing: -1.2px;
    color: #333;
    font-size: 16px;
    font-family: '맑은 고딕';
    font-weight: bold;
}
.chart_titleInput {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    padding-left: 15px;
    height: 40px;
    border-radius: 4px;
    border: 1px solid #e9e9e9;
    color: #555;
    font-size: 16px;
    font-family: '맑은 고딕';
    font-weight: normal;
    resize: none;
    width :260px;
    margin-left: 5px;
    margin-bottom: 10px;
}

.chart_typeSpan {
    width: 95px;
    line-height: 28px;
    margin-right: 5px;
    text-align: center;
    letter-spacing: -1.2px;
    color: #333;
    font-size: 16px;
    font-family: '맑은 고딕';
    font-weight: bold;
}
.chart_typeSelectBox {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    padding-left: 15px;
    height: 40px;
    border-radius: 4px;
    border: 1px solid #e9e9e9;
    color: #555;
    font-size: 16px;
    font-family: '맑은 고딕';
    font-weight: normal;
    resize: none;
    width: 260px;
    margin-left: 5px;
    margin-bottom: 10px;
}
.chart_typeOption {

}
.chart_category {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    padding-left: 15px;
    height: 40px;
    border-radius: 4px;
    border: 1px solid #e9e9e9;
    color: #555;
    font-size: 16px;
    font-family: '맑은 고딕';
    font-weight: normal;
    resize: none;
    width: 260px;
    margin-left: 5px;
    margin-bottom: 10px;
}
.chart_categoryOption {

}
.chart_categorySelectSpan {
    width: 95px;
    line-height: 28px;
    margin-right: 5px;
    text-align: center;
    letter-spacing: -1.2px;
    color: #333;
    font-size: 16px;
    font-family: '맑은 고딕';
    font-weight: bold;
}
.chart_fieldSpan {
    width: 95px;
    line-height: 28px;
    margin-right: 5px;
    text-align: center;
    letter-spacing: -1.2px;
    color: #333;
    font-size: 16px;
    font-family: '맑은 고딕';
    font-weight: bold;
}
.chart_fieldSelectBox {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    padding-left: 15px;
    height: 40px;
    border-radius: 4px;
    border: 1px solid #e9e9e9;
    color: #555;
    font-size: 16px;
    font-family: '맑은 고딕';
    font-weight: normal;
    resize: none;
    width: 260px;
    margin-left: 5px;
    margin-bottom: 10px;
}
.chart_fieldOption {

}
.chart_aggregateUnitSpan{
    width: 95px;
    line-height: 28px;
    margin-right: 5px;
    text-align: center;
    letter-spacing: -1.2px;
    color: #333;
    font-size: 16px;
    font-family: '맑은 고딕';
    font-weight: bold;
}
.chart_aggregateUnitSelectBox {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    padding-left: 15px;
    height: 40px;
    border-radius: 4px;
    border: 1px solid #e9e9e9;
    color: #555;
    font-size: 16px;
    font-family: '맑은 고딕';
    font-weight: normal;
    resize: none;
    width: 260px;
    margin-left: 5px;
    margin-bottom: 10px;
}
.chart_aggregateUnitOption {

}
.chart_applyBtn {
    position: relative;
    display: flex;
    align-items: center;
    height: 40px;
    padding: 0 15px;
    border-radius: 4px;
    box-sizing: border-box;
    font-family: '맑은 고딕';
    font-weight: normal;
    font-size: 16px;
    color: #fff;
    vertical-align: top;
    letter-spacing: -1px;
    transition: .4s;
    background: #436aeb;
    padding: 0 30px;
    cursor: pointer;
    margin-right: 5px;
    float: right;
}
.chart_chartShowDiv {
    width : 95%;
    height: 600px;
}
.chart_applyBtnSpan{

}
.chart_btnBox{
    overflow: hidden;
}.featureAdd_featureAddContent {}

.featureAdd_contentDiv {
    position: relative;
    border: 1px solid #e1e1e1;
    padding: 20px 10px;
    border-radius: 4px;
    margin-bottom: 5px;
    max-height: 560px;
    overflow-y: auto;
    overflow-x: hidden;
}

.featureAdd_titleSpan {
    width: 95px;
    line-height: 28px;
    margin-right: 5px;
    text-align: center;
    letter-spacing: -1.2px;
    color: #333;
    font-size: 16px;
    font-family: '맑은 고딕';
    font-weight: bold;
}

.featureAdd_titleInput {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    padding-left: 15px;
    height: 40px;
    border-radius: 4px;
    border: 1px solid #e9e9e9;
    color: #555;
    font-size: 16px;
    font-family: '맑은 고딕';
    font-weight: normal;
    resize: none;
    width: 280px;
    margin-left: 5px;
    margin-bottom: 10px;
}

.featureAdd_addBtn {
    position: relative;
    display: flex;
    align-items: center;
    height: 40px;
    padding: 0 15px;
    border-radius: 4px;
    box-sizing: border-box;
    font-family: '맑은 고딕';
    font-weight: normal;
    font-size: 16px;
    color: #fff;
    vertical-align: top;
    letter-spacing: -1px;
    transition: .4s;
    background: #436aeb;
    padding: 0 30px;
    cursor: pointer;
    margin-right: 5px;
}

.featureAdd_addBtnSpan {}

.featureAdd_footerDiv {
    display: flex;
    justify-content: flex-end;
}
.featureAdd_valueDatetime{
    padding: 0;
    width: 280px;
    margin: 0;
    margin-left: 5px;
}
.featureAdd_valueDatetime input{
    width: 56px;
}
.featureAdd_valueDate{
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    height: 40px;
    color: #555;
    font-size: 16px;
    font-family: '맑은 고딕';
    font-weight: normal;
    resize: none;
    width: 280px;
    margin-left: 5px;
}
.featureAdd_valueDate input{
    width:100%;
}
.featureAdd_valueSelectBox {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    padding-left: 15px;
    height: 40px;
    border-radius: 4px;
    border: 1px solid #e9e9e9;
    color: #555;
    font-size: 16px;
    font-family: '맑은 고딕';
    font-weight: normal;
    resize: none;
    width: 280px;
    margin-left: 5px;
}

.featureAdd_valueOption {}

.featureAdd_closeBtn {
    position: relative;
    display: flex;
    align-items: center;
    height: 40px;
    padding: 0 15px;
    border-radius: 4px;
    box-sizing: border-box;
    font-family: '맑은 고딕';
    font-weight: normal;
    font-size: 16px;
    color: #fff;
    vertical-align: top;
    letter-spacing: -1px;
    transition: .4s;
    background: gray;
}

.featureAdd_closeBtnSpan {}

.featureAdd_hidden {
    display: none;
    margin: 0;
    padding: 0;
    width: 0;
    height: 0;
    overflow: hidden;
    font-size: 0;
    line-height: 0;
    visibility: hidden;
}.grid_firstBtnGrp , .grid_secondBtnGrp, .grid_thirdBtnGrp,  .grid_forthBtnGrp, .grid_customBtnGrp{
    display: flex;
}
.grid_thirdBtnGrp{
    padding-right: 5px;
}

.grid_attributeBtn, .grid_chartBtn, .grid_exportBtn, .grid_cqlfilterBtn ,.grid_searchAreaBtn, .grid_modifyBtn, .grid_deleteColBtn , .grid_addFeatureBtn ,.grid_modifyCompleteBtn, .grid_customBtn{
    margin-right: 5px;
    word-break: keep-all;
    background: #333;
    display: flex;
    align-items: center;
    height: 40px;
    padding: 0 15px;
    border-radius: 4px;
    box-sizing: border-box;
    font-family: '맑은 고딕';
    font-weight: normal;
    font-size: 16px;
    color: #fff;
    vertical-align: top;
    letter-spacing: -1px;
    transition: .4s;
}
.grid_deleteColBtn{
    
}
.grid_btnUl {
    position:absolute;
    z-index: 100;
    display: flex;
    flex-direction: column;
    transform: translateX(-40%);
    align-items: center;
    opacity: 1;
    visibility: visible;
    margin-top: 5px;
    display:none;
}

.grid_btnUl button:hover span{
    
}

.grid_btnLi{
    list-style: none;
}

.grid_exportItem{

}
.grid_exportGEOJSON{
    display: block;
    margin-right: 10px;
    height: 25px;
    line-height: 25px;
    color: #fff;
    border-radius: 4px;
    font-size: 15px;
    font-family: "Pretendard";
    background: #2fb4be;
}
.grid_exportKML{
    display: block;
    margin-right: 10px;
    height: 25px;
    line-height: 25px;
    padding: 0 10px;
    color: #fff;
    border-radius: 4px;
    font-size: 15px;
    font-family: "Pretendard";
    background: #c25c5c;
}
.grid_exportSHP{
    display: block;
    margin-right: 10px;
    height: 25px;
    line-height: 25px;
    padding: 0 10px;
    color: #fff;
    border-radius: 4px;
    font-size: 15px;
    font-family: "Pretendard";
    background: #5c99c2;
}
.grid_exportCSV{
    display: block;
    margin-right: 10px;
    height: 25px;
    line-height: 25px;
    padding: 0 10px;
    color: #fff;
    border-radius: 4px;
    font-size: 15px;
    font-family: "Pretendard";
    background: #73bc60;
}

.grid_searchAreaSelectBox {
    height : 40px;
    margin-left: 30px;
    width: 100px;
}

.grid_searchAreaOption {

}
.grid_showAllBtn{
    margin-right: 5px;
    word-break: keep-all;
    background: #333;
    display: flex;
    align-items: center;
    height: 40px;
    padding: 0 15px;
    border-radius: 4px;
    box-sizing: border-box;
    font-family: '맑은 고딕';
    font-weight: normal;
    font-size: 16px;
    color: #fff;
    vertical-align: top;
    letter-spacing: -1px;
    transition: .4s;
}
.grid_showAllSpan{
    
}

.grid_modifySelectBox{
    width:130px;
}
.grid_modifyOption{

}

.grid_addFeatureBtnSpan{

}

.grid_clearFilterBtn , .grid_clearSelectionBtn{
    display: flex;
    align-items: center;
    height: 40px;
    padding: 0 15px;
    box-sizing: border-box;
    font-family: '맑은 고딕';
    font-weight: normal;
    font-size: 16px;
    vertical-align: top;
    letter-spacing: -1px;
    transition: .4s;
    border-radius: 50px;
    margin-right: 5px;
    word-break: keep-all;
    background: #fff;
    color: #555;
    border: 1px solid #e9e9e9;
}
.grid_saveGridInfoBtn{
    display: flex;
    align-items: center;
    height: 40px;
    padding: 0 15px;
    box-sizing: border-box;
    font-family: '맑은 고딕';
    font-weight: normal;
    font-size: 16px;
    vertical-align: top;
    letter-spacing: -1px;
    transition: .4s;
    border-radius: 50px;
    margin-right: 5px;
    word-break: keep-all;
    background: #333;
    color: white;
    border: 1px solid #e9e9e9;
}
.grid_editModeCheckBox{
    position: relative;
    display: inline-block;
    min-width: 30px;
    min-height: 30px;
    line-height: 22px;
    text-indent: 30px;
    color: #555;
    font-size: 16px;
    font-family: '맑은 고딕';
}
.grid_headerGrp{
    display: flex;
    padding: 20px;
    justify-content: space-between;
    /*width: 1500px;*/
}
.grid_saveGridInfoBtnSpan{
    
}

.grid_editModeSpan{
    margin-right: 15px;
    margin-top: 10px;
    font-size : 16px;
}
.grid_tableDiv{
    /*padding: 0 20px 20px;*/
}
.grid_attributeBtnSpan{

}

.grid_chartBtnSpan{

}
.grid_exportBtnSpan{

}
.grid_exportBtnSpan{

}
.grid_exportBtnSpan{

}
.grid_exportBtnSpan{

}
.grid_cqlfilterBtnSpan{

}
.grid_searchAreaBtnSpan{

}
.grid_modifySpan{

}
.grid_modifyCompleteSpan{

}
.grid_deleteColBtnSpan{

}
.grid_clearFilterBtnSpan{

}
.grid_clearSelectionBtnSpan{

}
.grid_customBtnSpan{
    
}

/* 230216 위젯 예제 개선 */
.grid_GridAppContent {
    border: 1px solid #e1e1e1;
    border-radius: 4px;
    padding: 20px 10px;
    overflow: auto;
}

.grid_sort_asc::after {
    content: "\f103";
    padding-left: 5px;
    font-family: "agGridAlpine";
    
}
.grid_sort_desc::after
{
    content: "\f10f";
    padding-left: 5px;
    font-family: "agGridAlpine";
}
.paging_pageArea span {
  margin: 5px;
  color: black;
  font-weight: normal;
}

.paging_pageArea span.paging_hilight {
  cursor: pointer;
  font-weight: bold;
}

.paging_pageArea span.paging_hilight.paging_navigation {
  color: gray
}

.paging_pageArea span.paging_hilight.paging_page {
  color: green
}

.paging_resultArea {
  max-height: 200px;
  display: block;
  color: #333;
  letter-spacing: -0.045em !important;
}

.paging_scroll {
  overflow-y: auto;
  overflow-x: hidden;
}

.paging_scroll::-webkit-scrollbar {
  width: 14px;
  height: 14px;
}

.paging_scroll::-webkit-scrollbar-button {
  display: none;
}

.paging_scroll::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 5px;
}

.paging_scroll::-webkit-scrollbar-track {
  background-color: #e9e9e9;
}

.paging_item {
  padding: 10px 0;
  font-size: 14px;
  font-family: "Pretendard";
  font-weight: normal;
  border-bottom: 1px solid #e9e9e9;
}

.paging_item:hover {
  background-color: #dadada63;
}

.paging_pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 10px;
  font-size: 16px;
  font-family: '맑은 고딕';
  margin: 0;
  font: inherit;
}

.paging_pagination.paging_sm {
  font-size: 14px;
}

.paging_pagination button {
  display: inline-block;
  border: none;
  background-color: transparent;
  cursor: pointer;
}

.paging_pagination button,
.paging_pagination .paging_pageNum {
  display: inline-block;
  min-width: 40px;
  height: 40px;
  line-height: 40px;
  text-align: center;
}

.paging_pagination .paging_btnPagi {
  background-repeat: no-repeat;
  background-position: center;
}

.paging_pagination.paging_sm button,
.paging_pagination.paging_sm .paging_pageNum {
  min-width: 20px;
  height: 20px;
  line-height: 20px;
}

.paging_pagination .paging_btnPagi.paging_first {
  background-image: url(./images/widget/common/btn-pagi-first.png);
}

.paging_pagination .paging_btnPagi.paging_prev {
  background-image: url(./images/widget/common/btn-pagi-prev.png);
}

.paging_pagination .paging_btnPagi.paging_next {
  background-image: url(./images/widget/common/btn-pagi-next.png);

}

.paging_pagination .paging_btnPagi.paging_last {
  background-image: url(./images/widget/common/btn-pagi-last.png);
}

.paging_pagination .paging_pageNum:hover,
.paging_pagination .paging_pageNum.paging_active {
  color: #436aeb;
  font-weight: bold;
}

.paging_pagination .paging_pageNum {
  color: #333;
}

.paging_message{
  position: absolute;
  right: 130px;
}
.paging_pageSize{
  position: absolute;
  right: 20px;
  width:100px;
}.legend_frame {
  position: absolute;
  bottom: 20px;
  left: 20px;
}

.legend_item {
  background: #f7f7f7;
  border: 1px solid #ccc !important;
  border-radius: 3px;
}

.legend_title {
  background: #f7f7f7;
  border: 1px solid #ccc;
  border-radius: 3px;
  padding: 10px;
  font-weight: bold;
  font-size: 15px;
  width: 220px;
  padding-right: 20px;
}


.legend_activeBtn {
  position: absolute;
  right: 10px;
}

.legend_area {
  display: none;
  margin: 0 15px;
  padding: 10px 0;
  list-style: none;
}

.legend_area.legend_active {
  display: block;
}

.legend_area>li {
  display: flex;
  line-height: 24px;
  margin-bottom: 5px;
  font-size: 16px;
  font-family: '맑은 고딕';
  font-weight: normal;
  color: #555;
  list-style: none;
}

.legend_conditionIcon {
  display: flex;
  width: 22px;
  height: 22px;
  margin-right: 10px;
}

.legend_condition {
  font-size: 15px;
  font-family: "Pretendard";
  font-weight: normal;
  color: #555;
  width: 150px;
}.webLayerForm_webLayerUploadBox table tr td:nth-child(4) {
  width: 66%;
}

.webLayerForm_section {
  padding-bottom: 20px;
  border-bottom: 0;
}

.webLayerForm_section:last-of-type {
  margin-bottom: 0;
  padding-bottom: 0;
}

.webLayerForm_section .webLayerForm_titSec strong {
  position: relative;
  font-size: 18px;
  color: #2e2e2e;
  text-indent: 10px;
}

.webLayerForm_section .webLayerForm_titSec strong:before {
  position: absolute;
  left: 0;
  top: 6px;
  width: 3px;
  height: 14px;
  background: #d6d6d6;
  display: block;
  content: '';
}

.webLayerForm_titSec {
  display: flex;
  align-items: center;
  margin-top: 10px;
}

.webLayerForm_mb20 {
  margin-bottom: 20px;
}

.webLayerForm_red {
  color: #fc3e3e;
}

.webLayerForm_table table {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
}

.webLayerForm_table table tbody tr th,
.webLayerForm_table table tbody tr td {
  font-size: 16px;
}

.webLayerForm_table {
  max-height: 342px;
  overflow-y: auto;
}

.webLayerForm_table table th {
  background: #f9f9f9;
  font-weight: bold;
  width: 200px;
  word-break: keep-all;
}

.webLayerForm_table table th,
.webLayerForm_table table td {
  height: 40px;
  color: #555;
  border-bottom: 1px solid #e9e9e9;
  padding: 8px;
}

.webLayerForm_table table td input[type="text"],
.webLayerForm_layerUpload select,
.webLayerForm_layerUpload .webLayerForm_layerNameBox input,
.webLayerForm_sourceSridBox input {
  width: 100%;
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  padding-left: 15px;
  height: 40px;
  border-radius: 4px;
  border: 1px solid #e9e9e9;
  color: #555;
  font-size: 16px;
  font-weight: normal;
  resize: none;
}

.webLayerForm_table table tbody tr td {
  color: #333;
  background: #fff;
}

.webLayerForm_checkbox input {
  display: none;
  border: none !important;
}

.webLayerForm_checkbox label:before {
  position: absolute;
  left: 0;
  top: 50%;
  display: block;
  content: '';
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  border-radius: 2px;
  border: 1px solid #cbced2;
  background-color: #fff;
  background-position: center;
  background-repeat: no-repeat;
  transition: .4s;
  background-size: 0px 0px;
}

.webLayerForm_checkbox input[type="checkbox"]:checked+label:before {
  background-image: url(./images/widget/common/ico-checkbox.png);
  background-size: 11px 10px;
}

.webLayerForm_checkbox label {
  position: relative;
  display: inline-block;
  height: 22px;
  line-height: 22px;
  text-indent: 30px;
  color: #555;
  font-size: 16px;
}

.webLayerForm_hidden {
  display: none;
  margin: 0;
  padding: 0;
  width: 0;
  height: 0;
  overflow: hidden;
  font-size: 0;
  line-height: 0;
  visibility: hidden;
}

.webLayerForm_btnParamDelete {
  width: 40px;
  background: url(./images/widget/common/ico-tr-remove.png) no-repeat center;
  border: 1px solid #e9e9e9;
  color: #555;
  height: 40px;
  margin-left: 10px;
}

.webLayerForm_btnParamDelete span {
  display: none;
}

.webLayerForm_table table .webLayerForm_variable {
  display: block;
  width: 100%;
  height: 40px;
  line-height: 40px;
  padding-left: 0;
  border-radius: 4px;
  background: #fff;
  color: #333333;
  font-weight: bold;
  text-align: center;
  font-size: 16px;
  border: 1px solid #555555;
  text-indent: 0;
}

.webLayerForm_paramBox .webLayerForm_table {
  overflow-y: scroll;
  max-height: 122px;
}

.webLayerForm_btnUploadWebLayer {
  margin-left: 0;
  margin-right: 0;
  padding: 0 30px;
  background: #436aeb;
  height: 40px;
  display: flex;
  align-items: center;
  border-radius: 4px;
  box-sizing: border-box;
  font-weight: normal;
  font-size: 16px;
  color: #fff;
  vertical-align: top;
  letter-spacing: -1px;
  cursor: pointer;
  border: 0;
}

.webLayerForm_btnArea {
  margin-top: 20px;
  justify-content: flex-end;
  display: flex;
}

.webLayerForm_resolutionsBtnArea {
  margin-bottom: 10px;
}

.webLayerForm_btnParamDelete span {
  display: none;
}

.webLayerForm_table {
  margin-top: 10px;
}

.webLayerForm_paramBox table tr th {
  width: 40%;
}

.webLayerForm_paramBox table tr td:nth-child(2) {
  width: 58%;
}

.webLayerForm_resolutions_error_ext {
  position: relative;
  float: right;
  right: 126px;
}

.webLayerForm_paramKey_error_txt {
  font-weight: normal;
}

.webLayerForm_table table td input[type="text"].webLayerForm_paramVal {
  width: 89%;
  vertical-align: top;
}

.webLayerForm_icoAdd {
  position: relative;
  width: 25px;
  height: 25px;
  border-radius: 50%;
  background: #fff url(./images/widget/common/ico-add.png) no-repeat center;
  display: block;
  margin-right: 5px;
}

.webLayerForm_btnParamAdd {
  margin-left: auto;
  background: #f9f9f9;
  border: 1px solid #e9e9e9;
  color: #555;
  height: 40px;
  display: flex;
  align-items: center;
  padding: 0 15px;
  border-radius: 4px;
  box-sizing: border-box;
  font-weight: normal;
  font-size: 16px;
  vertical-align: top;
  letter-spacing: -1px;
  cursor: pointer;
}

.webLayerForm_requiredDataTable {}

.webLayerForm_basic {}

.webLayerForm_btnResolutionDelete {
  vertical-align: bottom;
}

.webLayerForm_resolutionsDiv {
  margin-bottom: 5px;
}



.webLayerForm_table table td input.webLayerForm_metrixIdInput {
  width: 40%;
  margin-right: 10px;
}

.webLayerForm_metrixIdInput::placeholder,
.webLayerForm_resolutionsInput::placeholder {
  color: #c8c8c8;
}

.webLayerForm_table table td input.webLayerForm_resolutionsInput {
  width: 48%;
}

.webLayerForm_layerUpload table td input.webLayerForm_originInput {
  display: flex;
}

.webLayerForm_selectBox {
  width: 100%;
}.layerDetail_inner{
    background: #fff;
    width: auto;
    padding: 9px 11px;
    height: 100%;
}

  .layerDetail_tabContWrap > .layerDetail_tabNav .layerDetail_tabList li {
    flex: 1;
    text-align: center;
    list-style: none;
  }
  
  .layerDetail_tabContWrap > .layerDetail_tabNav .layerDetail_tabList {
    display: flex;
  }
  .layerDetail_tabContWrap > .layerDetail_tabNav .layerDetail_tabList ul{
    list-style: none;
  }
  
  .layerDetail_tabContWrap.layerDetail_type01 .layerDetail_tabCont {
    border: 1px solid #eee;
    border-top: 0;
    display: block;
  }

  ul.layerDetail_tabList{
    margin: 0;
    padding: 0;
    border: 0;
    font: inherit;
  }
  .layerDetail_tabContWrap.layerDetail_type01 > .layerDetail_tabNav .layerDetail_tabList li {
    margin-right: 1px;
    height: 36px;
    line-height: 36px;
    color: #666;
    font-size: 16px;
    font-family: "Pretendard Bold";
    border-radius: 5px 5px 0 0;
    background-color: #eee;
    cursor: pointer;
}
.layerDetail_tabContWrap > .layerDetail_tabNav .layerDetail_tabList li.layerDetail_active {
  color: #fff;
  background-color: #8FAADC;
}
.layerDetail_widget{
  position: absolute;
  width: 400px;
  height: auto;
  top: 0px;
  left: 400px;
  background-color: white;
  z-index: 9999;
}

.layerDetail_inner.layerDetail_layer{
  padding: 30px 11px 9px 11px;
}

.style_choice {
  display: flex;
  margin-bottom: 18px;
  flex-wrap: wrap;
  align-items: flex-end;
}

.style_choice>label {
  width: 95px;
  line-height: 28px;
  margin-right: 5px;
  text-align: center;
  letter-spacing: -1.2px;
  color: #333;
  font-size: 15px;
  font-family: "Pretendard Bold";
}

.style_choice .style_pick {
  flex: 1;
}

.style_choice .style_pick select {
  width: 100%;
  border: 1px solid #e9e9e9;
  border-radius: 4px;
  height: 28px;
  font-size: 15px;
  color: #555;
  font-family: "Pretendard";
  margin-top: 5px;
}

.style_choice .style_pick .style_checkbox {
  margin-top: 10px;
  text-align: right;
}

.style_choice .style_pick .style_tabContWrap {
  margin-top: 10px;
}

.style_tabContWrap.style_type02 .style_tabCont {
  border: 1px solid #eee;
  border-top: 0;
  padding: 5px 0px;
}

.style_tabContWrap.style_type02>.style_tabNav .style_tabList {
  margin: 0;
  padding: 0;
  border: 0;
  font: inherit;
}

.style_tabContWrap.style_type02>.style_tabNav .style_tabList li {
  height: 28px;
  line-height: 28px;
  color: #666;
  font-size: 15px;
  font-family: "Pretendard Bold";
  border-radius: 5px 5px 0 0;
  background-color: #eee;
  cursor: pointer;
}

.style_tabContWrap.style_type02>.style_tabNav .style_tabList li.style_active {
  color: #fff;
  background-color: #8FAADC;
}

.style_tabContWrap>.style_tabNav .style_tabList li {
  flex: 1;
  text-align: center;
  list-style: none;
}

.style_tabContWrap>.style_tabNav .style_tabList {
  display: flex;
}

.style_tabContWrap>.style_tabNav .style_tabList ul {
  list-style: none;
}

.style_tabContWrap.style_type01 .style_tabCont {
  border: 1px solid #eee;
  border-top: 0;
  display: block;
}

.style_inner {
  background: #fff;
  width: auto;
  padding: 9px 11px;
  height: 100%;
}

.style_checkbox input[type="checkbox"] {
  display: none;
}

.style_choice .style_pick .style_checkbox label {
  font-size: 15px;
  color: #555;
  font-family: "Pretendard";
  font-weight: normal;
}

.style_choice .style_pick>.style_inner {

  border: 1px solid #e9e9e9;
}

.style_checkbox label {
  position: relative;
  display: inline-block;
  min-width: 22px;
  min-height: 22px;
  line-height: 22px;
  text-indent: 30px;
  color: #555;
  font-size: 16px;
  font-family: "Pretendard";
}

.style_checkbox label:before {
  position: absolute;
  left: 0;
  top: 50%;
  display: block;
  content: '';
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  border-radius: 2px;
  border: 1px solid #cbced2;
  background-color: #fff;
  background-position: center;
  background-repeat: no-repeat;
  transition: .4s;
  background-size: 0px 0px;
}

.style_checkbox input[type="checkbox"]:checked+label:before {
  background-image: url(./images/widget/common/ico-checkbox.png);
  background-size: 11px 10px;
}

.style_choice .style_pick .style_list {
  /* margin-top: 10px; */
  border: 1px solid #e9e9e9;
  border-radius: 4px;
}

.style_choice .style_pick .style_list ul {
  padding: 10px 0;
  list-style: none;
}

.style_choice .style_pick .style_list li {
  padding-left: 10px;
  line-height: 25px;
  margin-bottom: 5px;
  font-size: 16px;
  font-family: '맑은 고딕';
  font-weight: normal;
  color: #555;
  list-style: none;
}

.style_choice.style_ab {
  align-items: flex-end;
}

.style_choice>label.style_wide {
  letter-spacing: 3px;
  line-height: 98px;
}

.style_scroll {
  max-height: 100px;
  overflow-y: auto;
  overflow-x: hidden;
  margin: 0;
}

.style_scroll::-webkit-scrollbar {
  width: 14px;
  height: 14px;
}

.style_scroll::-webkit-scrollbar-button {
  display: none;
}

.style_scroll::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 5px;
}

.style_scroll::-webkit-scrollbar-track {
  background-color: #e9e9e9;
}

.style_symbolEditor {}

.style_btnArea {
  display: flex;
  padding: 5px 0 5px 0;
}

.style_btnArea.style_flexCenter {
  justify-content: center;
}

.style_btnArea.style_flexCenter .style_btn:first-of-type {
  margin-left: 0;
}

.style_btnArea.style_flexCenter .style_btn:last-of-type {
  margin-right: 0;
}

.style_btnArea.style_flexCenter .style_btn {
  margin-left: 5px;
}

.style_btnArea.style_flexRight {
  justify-content: flex-end;
}

.style_btnArea.style_flexRight .style_btn:first-of-type {
  margin-left: 0;
}

.style_btnArea.style_flexRight .style_btn {
  margin-left: 5px;
}

.style_btn.style_lg {
  padding: 0 30px;
}


.style_btn.style_hilight1 {
  background: #2F5597;
}

.style_btn.style_hilight2 {
  background: #8faadc;
}

.style_btn.style_normal1 {
  background-color: #d7dadf;
  color: #333;
}

.style_btn.style_normal2 {
  background-color: #fff;
  color: #2f5597;
  border: 1px solid #2f5597;
}


.style_btnArea .style_btn {
  margin-right: 5px;
}

.style_btn.style_mini {
  height: 26px;
}

.style_btn {
  position: relative;
  display: flex;
  align-items: center;
  height: 36px;
  padding: 0 15px;
  border-radius: 4px;
  box-sizing: border-box;
  font-family: "Pretendard";
  font-weight: normal;
  font-size: 14px;
  color: #fff;
  vertical-align: top;
  letter-spacing: 0px;
  transition: .4s;
}
/*
.tabContWrap.type02.large .choice.type02 > label {
  width: 80px;
} */

.style_comboBox {
  position: relative;
  border-radius: 4px;
  border: 1px solid #e9e9e9;
  font-size: 16px;
  color: #555;
  text-indent: 11px;
  font-family: '맑은 고딕';
  font-weight: normal;
  background: #fff url(./images/widget/common/ico-combo-show.png) no-repeat right 10px center;
  box-sizing: border-box;
}

.style_comboBox>span {
  display: block;
}

.style_comboBox .style_comboList {
  position: absolute;
  display: none;
  left: 0;
  top: calc(100% + 1px);
  width: 100%;
  border-radius: 4px;
  border: 1px solid #e9e9e9;
  background: #fff;
  box-sizing: border-box;
  list-style: none;
}

.style_color {
  display: inline-block;
  width: 26px;
  height: 26px;
  margin-right: 10px;
  border: 1px solid #e9e9e9;
  vertical-align: middle;
  cursor: pointer;
  position: relative;
  margin: 6px 0;
}

.style_colorPicker.style_on {
  display: block;
  position: absolute;
  top: 30px;
  z-index: 10;
}

.style_colorPicker.style_on.style_center {
  left: -97.5px;
}

.style_colorPicker.style_on.style_right {
  left: -195px;
}

.style_colorPicker.style_on>div {
  border-radius: unset !important;
  box-shadow: rgb(150 150 150 / 15%) 0px 0px 0px 1px, rgb(0 0 0 / 15%) 0px 8px 16px !important;
}

.style_colorPicker.style_on>div:after {
  position: absolute;
  top: -6px;
  display: block;
  content: '';
  border: 6px solid transparent;
  border-left: 6px solid #ffffff;
  border-bottom: 6px solid #ffffff;
  transform: translateX(-50%) rotate(135deg);
}

.style_colorPicker.style_on.style_center>div:after {
  left: 50%;
}

.style_colorPicker.style_on.style_right>div:after {
  left: 95%;
}

.style_colorPicker.style_off {
  display: none
}

.style_choice .style_pick input {
  width: 100%;
  border: 1px solid #e9e9e9;
  border-radius: 4px;
  height: 28px;
  font-size: 16px;
  color: #888;
  font-family: "Pretendard";

}

.style_choice .style_pick input[type="number"] {
  width: 49px;
  margin-right: 6px;
}

.style_choice .style_pick>span {
  font-family: "Pretendard";
  font-weight: normal;
  font-size: 16px;
  color: #888;
}

.style_noMarginBottom {
  /* margin-bottom: 5px; */
}

.style_iconList {
  min-height: 70px;
  max-height: 100px;
  border: 1px solid #eee;
  margin: 5px 0 5px 0;
  overflow-y: auto;
}

.style_icon {
  width: 20px;
  height: 20px;
  border: 1px solid #e0e0e0;
  display: inline-block;
  border-radius: 20%;
  cursor: pointer;
  margin: 5px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.style_icon.style_selected {
  border: 1px solid #2F5597;
}

.style_info {
  font-family: '맑은 고딕';
  font-weight: normal;
  color: #7e7e7e;
  font-size: 11px !important;
}

.style_divPopupFrame {
  position: relative;
}

.style_divPopup {
  position: absolute;
  height: auto;
  background-color: #fff;
  border: 1px solid #333;
  text-align: left;
  z-index: 5;
}

.style_divPopup.style_addImage {
  width: 240px;
  top: 0px;
  left: -48px;
}

.style_preview .style_divPopup {
  top: 136px;
  /* top: 35px; */
  left: -109px;
  width: 300px;
}

.style_patternPickerArea .style_divPopup {
  position: absolute;
  top: 33px;
  width: 250px;
}

.style_normal .style_patternPickerArea .style_divPopup {
  left: -50px;
}

.style_multi .style_patternPickerArea .style_divPopup {
  left: -225px;
}

.style_divPopup .style_divPopupHeader {
  background-color: #333;
  height: auto;
  color: #fff;
  padding: 5px 10px;
  font-weight: bold;
}

.style_divPopup .style_divPopupHeader buttton.style_closeBtn {
  display: block;
  float: right;
  width: 20px;
  height: 20px;
  background: url(./images/widget/toc/ico-toc-close.png);
  background-size: contain;
  border: none;
  background-color: transparent;
  cursor: pointer;
}

.style_divPopup .style_divPopupHeader buttton.style_closeBtn span {
  display: none;
}

.style_flex {
  display: flex;
}

.style_paddingLeft {
  padding-left: 5px;
}

.style_marginRight {
  margin-right: 5px;
}

.style_marginBottom {
  margin-bottom: 5px;
}

.style_marginBottom10 {
  margin-bottom: 10px;
}

.style_widthMinus100 {
  width: calc(100% - 100px) !important;
}

.style_widthMinus33 {
  width: calc(100% - 33px) !important;
}



.style_alignItem {
  width: 20px;
  height: 20px;
  border: 1px solid #e0e0e0;
  display: inline-block;
  border-radius: 20%;
  cursor: pointer;
  margin: 5px;
}

.style_alignItem.style_selected {
  background-color: #2F5597;
}


/*패턴픽커*/
.style_patternPickerArea {
  position: relative;
}

.style_patternPickerArea .style_patternPicker {
  color: #888;
  display: inline-block;
  width: 26px;
  height: 26px;
  margin-right: 10px;
  border: 1px solid #e9e9e9;
  vertical-align: middle;
  cursor: pointer;
  position: relative;
  margin: 0 0;
}

.style_patternPickerArea .style_patternPicker>img {
  width: 100%;
  height: 100%;
}

.style_patternPickerArea .style_patternItem {
  width: 20px;
  height: 20px;
  display: inline-block;
  border: 1px solid #e9e9e9;
  cursor: pointer;
  margin: 3px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.style_patternPickerArea .style_patternItem.style_selected {
  border: 2px solid #2F5597;
}


/*미리보기 영역 start*****************************************************/

.style_preview {
  position: relative;
}

.style_previewItem {
  display: flex;
}

.style_previewItem .style_previewItem_icon {
  display: flex;
  width: fit-content;
  height: fit-content;
  margin-right: 10px;
  /*border: 1px solid #e9e9e9;*/
  vertical-align: middle;
}

.style_previewItem .style_previewItem_icon.style_catEdit:hover {
  border: 1px solid #e9e9e9;
  border-radius: 15%;
  cursor: pointer;
}

.style_previewItem .style_previewItem_name {
  font-size: 15px;
  font-family: "Pretendard";
  font-weight: normal;
  color: #555;
  width: 100%;
}

.style_previewItem .style_previewItem_name input {
  height: 30px;
  max-width: 60px !important;
  text-align: right;
}

.style_previewItem .style_previewItem_name span {
  margin-left: 0px;
  font-size: 14px;
}

.style_previewItem .style_previewItem_editingPop {
  position: absolute;
  background: gray;
  margin-left: 5px;
  z-index: 10;
}

/*개별 스타일 수정 영역*/
.style_divPopup .style_choice label {
  height: 30px;
  line-height: 30px;
}

.style_divPopup .style_choice .style_pick select {
  height: 30px;
  padding-right: 30px;
}

.style_divPopup .style_choice .style_pick span.style_color,
.style_divPopup .style_choice .style_pick .style_patternPickerArea span {
  height: 30px;
  width: 30px;
  padding-right: 0px;
}

.style_btn span {
  height: auto !important;
}

/*미리보기 영역 end*****************************************************/

.style_tabList li.style_disabled {
  color: #ccc !important;
}

/*
.lineStyle .choice.type02 > label {
  width: 80px;
  line-height: 28px;
  margin-right: 10px;
  color: #555;
  text-align: right;
} */

.style_previewItem .style_previewItem_icon.style_svgIcon {
  width: 22px;
  height: 22px;
}

.style_fileSelect {
  justify-content: space-between;
  display: flex;
}

.style_fileSelect .style_fileHidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}

.style_fileSelect .style_fileLocal {
  flex: 1;
  height: 30px !important;
  margin-right: 5px;
  padding: 0;
  color: #555;
  border: 1px solid #e1e1e1;
  border-radius: 4px;
  background: #fafafa;
  box-sizing: border-box;
  text-indent: 7px;
  text-overflow: ellipsis;
  white-space: nowrap;
  word-break: break-all;
}

.style_fileSelect .style_btnFileSelect {
  height: 30px;
}

.style_btn.style_lg {
  padding: 0 30px;
}

.style_pick.style_fileSelect {
  margin-bottom: 20px;
}

/*heatmap관련 start*/
.style_colorListWrap {
  height: fit-content;
  width: auto;
}

.style_scrollX {
  /*overflow-x: auto;*/
  height: fit-content;
  /*overflow-y: clip;*/
}

.style_heatmapGroup {
  /* width: max-content !important;
  height: auto !important;*/
}

.style_heatmapColor {
  position: relative;
  margin-right: 10px;
  margin-bottom: 10px;
  display: inline-block;
}

.style_heatmapColor .style_color {
  margin: 0px 0px !important;
}

.style_btnHeatmapClose {
  position: absolute;
  right: -8px;
  top: -8px;
  width: 16px;
  height: 16px;
  background: url(./images/widget/toc/ico-heatmap-close.png) no-repeat center;
  border: none;
}

.style_hidden {
  display: block;
  margin: 0;
  padding: 0;
  width: 0;
  height: 0;
  overflow: hidden;
  font-size: 0;
  line-height: 0;
  visibility: hidden;
}

.style_heatmapAdd {
  position: relative;
  width: 28px;
  height: 28px;
  border: 1px solid #e9e9e9;
  vertical-align: top;
  display: inline-block;
  background-color: transparent;
  cursor: pointer;
  font-family: inherit;
  font-size: inherit;
}

.style_heatmapAdd:before {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  display: block;
  content: '';
  width: 2px;
  height: 16px;
  background: #555;
}

.style_heatmapAdd:after {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  display: block;
  content: '';
  width: 16px;
  height: 2px;
  background: #555;
}

/*heatmap관련 end*/

.style_symbolEditor .style_full {
  width: 100%;
  margin-top: 15px;
}

.style_divPopup .style_full .style_tabContWrap {
  margin-top: 0px;
}

.style_symbolEditor .style_full label,
.style_divPopup .style_full label {
  width: 27%;
}

.style_symbolEditor .style_full .style_pick label {
  width: 27%;
}

.style_symbolEditor .style_full .style_pick.style_fileSelect label {
  width: unset;
}

/* 채우기 없음 체크박스관련 */
.style_noFill {}

.style_choice .style_type02 div .style_noFill {
  width: auto;
  height: auto;
}

#style_noFillLabel {
  text-align: left;
  font-size: 11px;
  padding-left: 0;
}

.style_noFillLabel {}

.style_noFillBox {}

.style_textAlign {
  align-items: center;
}

.style_border0 {
  border: 0;
}

.style_choice .style_pick>span.style_tipTxt {
  font-size: 11px;
  color: #afafaf;
  font-weight: normal;
  float: right;
  word-break: keep-all;
}

.style_btnRangeSync {
  width: 135px;
  height: 25px;
  background-color: #8FAADC;
  border: 1px solid #e9e9e9;
  border-radius: 4px;
  color: white;
}

.style_marginTop10 {
  margin-top: 10px;
}

.style_miniBox {
  display: flex;
  justify-content: space-between;
  line-height: 22px;
}

.style_choice .style_pick input.style_miniInput {
  width: 37%;
}

.style_miniBtn {
  width: 31px;
  padding: 3px;
  display: inline;
}

.style_choice .style_pick .style_marginBottom10 {
  margin-bottom: 10px;
}

.style_alignItemsCenter {
  align-items: center;
}

.style_symbolEditor .style_full .style_pick .style_flex.style_multi label {
  width: 0%;
}

.style_symbolEditor .style_full .style_pick .style_flex.style_multi label.style_btnFileSelect {
  width: unset;
}

.style_modify {
  background: url(./images/widget/toc/ico-edit.png) no-repeat center;
  float: right;
  width: 25px;
  height: 25px;
}

.style_txtHidden {
  visibility: hidden;
}

.style_pick .style_divPopup .style_lowerBound {
  width: 36%;
}

.style_pick .style_divPopup .style_upperBound {
  width: 36%;
  margin-left: 20px;
}

.style_divPopup .style_divPopupHeader .style_closeUnitCondition {
  float: right;
  margin-top: 5px;
}

.style_pick .style_divPopup .style_applyUnitCondition {
  color: #fff;
  width: 54px;
  height: 26px;
  font-size: 14px;
  margin-right: 5px;
  background-color: #2F5597;
}

.style_errorTxt {
  display: block;
}

.style_choiceType {}

.style_multiType {}

.style_choice.style_type02 {
  margin-bottom: 10px;
  align-items: center;
}

.style_choice.style_type02>label {
  width: 78px;
  /*line-height: 100%;*/
  margin-right: 10px;
  color: #555;
  text-align: center;
}

.style_choice.style_type02 .style_pick select,
.style_choice.style_type02 .style_pick .style_comboBox {
  height: 28px;
  line-height: 28px;
  padding-right: 30px;
}

.style_choice.style_type02 .style_pick input {
  height: 28px;
}

.style_choice.style_type02 .style_color {
  margin: 0 0;
}
.sliderBar_sliderArea {
  display: flex;
  position: relative;
}

.sliderBar_sliderArea .sliderBar_sliderBar {
  position: relative;
  margin-right: 10px;
}

.sliderBar_sliderArea.sliderBar_horizonal .sliderBar_sliderBar {
  height: 28px;
  flex: 1;
}

.sliderBar_sliderArea.sliderBar_vertical .sliderBar_sliderBar {
  width: 28px;
}

.sliderBar_sliderArea .sliderBar_sliderBar:after {
  position: absolute;
  display: block;
  content: '';
  background: #e1e1e1;
}

.sliderBar_sliderArea.sliderBar_horizonal .sliderBar_sliderBar:after {
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 100%;
  height: 2px;
}

.sliderBar_sliderArea.sliderBar_vertical .sliderBar_sliderBar:after {
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  height: 100%;
  min-height: 100px;
  width: 2px;
}

.sliderBar_sliderArea .sliderBar_sliderBar_bar {
  background-color: #333333;
  position: absolute;
  z-index: 1;
  border: 0;
  border-bottom-right-radius: 3px;
  border-bottom-left-radius: 3px;
  border-top-right-radius: 3px;
  border-top-left-radius: 3px;
}

.sliderBar_sliderArea.sliderBar_horizonal .sliderBar_sliderBar_bar {
  height: 2px;
  top: 50%;
  transform: translateY(-50%);
}

.sliderBar_sliderArea.sliderBar_vertical .sliderBar_sliderBar_bar {
  width: 2px;
  left: 50%;
  transform: translateX(-50%);
  bottom: 0px;
}

.sliderBar_sliderArea .sliderBar_sliderBar_btn {
  position: absolute;
  z-index: 1;
  width: 10px;
  height: 10px !important;
  padding: 0px !important;

  background: #555;
  border-radius: 50%;

}

.sliderBar_sliderArea.sliderBar_horizonal .sliderBar_sliderBar_btn {
  top: 50%;
  transform: translate(-5px, -50%);
}

.sliderBar_sliderArea.sliderBar_vertical .sliderBar_sliderBar_btn {
  left: 50%;
  transform: translate(-5px, 50%);
}

.sliderBar_sliderArea .sliderBar_sliderBar .sliderBar_sliderBar_btn {
  cursor: pointer;
}

.sliderBar_sliderArea:active,
.sliderBar_sliderArea .sliderBar_sliderBar:active,
.sliderBar_sliderArea .sliderBar_sliderBar .sliderBar_sliderBar_btn:active {
  cursor: pointer;
}

.sliderBar_sliderArea.sliderBar_vertical .sliderBar_inputBox {
  position: absolute;
  bottom: 0px;
  left: 30px;
}

.sliderBar_hidden {
  display: none;
  margin: 0;
  padding: 0;
  width: 0;
  height: 0;
  overflow: hidden;
  font-size: 0;
  line-height: 0;
  visibility: hidden;
}

.range_widget {
  user-select: none;
}

.range_widget.range_horizon {
  width: 100%;
}

.range_widget.range_vertical {
  height: 100%;
}

.range_widget table {
  border-collapse: collapse;
  border-spacing: 0;
}

.range_widget table.range_horizon {
  width: 100%;
}

.range_widget table.range_vertical {
  height: 100%;
}

.range_widget table tr {
  margin: 0;
  padding: 0;
  border: 0;
  font: inherit;
}

.range_widget table th {
  height: 30px;
  padding-bottom: 10px;
}

.range_widget table th .range_balloon {
  position: relative;
  color: #fff;
  background: #474747;
  padding: 0 2px;
  margin: 0;
  border: 0;
  font: inherit;
  font-size: 12px;
}

.range_widget table th .range_balloon:after {
  position: absolute;
  left: 50%;
  bottom: -3px;
  display: block;
  content: '';
  border: 3px solid transparent;
  border-left: 3px solid #474747;
  border-bottom: 3px solid #474747;
  transform: translateX(-50%) rotate(-45deg);
}

.range_widget table td.range_header {
  padding-left: 10px;
}

.range_widget table td.range_header .range_balloon {
  position: absolute;
  color: #fff;
  background: #474747;
  padding: 5px 0;
  margin: 0;
  border: 0;
  font: inherit;
  font-size: 13px;
  writing-mode: vertical-lr;
}

.range_widget table td.range_header .range_balloon:after {
  position: absolute;
  left: 0px;
  bottom: 50%;
  display: block;
  content: '';
  border: 3px solid transparent;
  border-left: 3px solid #474747;
  border-bottom: 3px solid #474747;
  transform: translateX(-50%) rotate(45deg);
}

.range_widget table td {
  position: relative;
  margin: 0;
  padding: 0;
  border: 0;
  font: inherit;
}

.range_widget table.range_horizon td {
  height: 16px;
}

.range_widget table.range_vertical td {
  width: 16px;
}

.range_widget table.range_horizon td:not(.range_left):after {
  position: absolute;
  left: 0;
  top: 4px;
  display: block;
  content: '';
  width: 1px;
  height: 8px;
  border-left: 1px solid #aaa;
}

.range_widget table.range_horizon td:not(.range_right):last-of-type:before {
  position: absolute;
  right: 0;
  top: 4px;
  display: block;
  content: '';
  width: 1px;
  height: 8px;
  border-right: 1px solid #aaa;
}

.range_widget table.range_vertical td:not(.range_top, .range_header):after {
  position: absolute;
  top: 0;
  left: 4px;
  display: block;
  content: '';
  height: 1px;
  width: 8px;
  border-top: 1px solid #aaa;
}

.range_widget table.range_vertical tr:last-of-type td:not(.range_bottom, .range_header):before {
  position: absolute;
  top: 0;
  left: 4px;
  display: block;
  content: '';
  height: 1px;
  width: 8px;
  border-bottom: 1px solid #aaa;
}

.range_widget table td.range_active {
  background-color: #eee;
}

.range_widget table td.range_active.range_dragTarget {
  background-color: #474747;
}

.range_widget table td.range_active.range_dragTarget:hover {
  background-color: #ff4b00;
  cursor: pointer;
}

.range_left {
  border-left: 2px solid #474747 !important;
}

.range_right {
  border-right: 2px solid #474747 !important;
}

.range_top {
  border-top: 2px solid #474747 !important;
}

.range_bottom {
  border-bottom: 2px solid #474747 !important;
}.popup_choice {
  display: flex;
  margin-bottom: 30px;
  flex-wrap: wrap;
}

.popup_choice .popup_pick {
  flex: 1;
}

.popup_choice>label {
  width: 95px;
  line-height: 28px;
  margin-right: 5px;
  text-align: center;
  letter-spacing: -1.2px;
  color: #333;
  font-size: 16px;
  font-family: 'Pretendard-Regular';
  font-weight: bold;
}

.popup_choice .popup_pick select {
  width: 100%;
  border: 1px solid #e9e9e9;
  border-radius: 4px;
  height: 28px;
  font-size: 16px;
  color: #555;
  font-family: 'Pretendard-Regular';
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  padding-left: 15px;
  font-weight: normal;
  resize: none;
  -webkit-appearance: none;
  background: #fff url(./images/widget/common/ico-select.png) no-repeat right 11px center;
}

.popup_popupSj {
  margin-bottom: 10px;
}

.popup_choice .popup_pick input[type="text"],
.popup_choice .popup_pick input[type="number"] {
  width: 100%;
  border: 1px solid #e9e9e9;
  border-radius: 4px;
  height: 28px;
  font-size: 16px;
  color: #888;
  font-family: 'Pretendard-Regular';
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  padding-left: 15px;
  font-weight: normal;
  resize: none;
}

.popup_btnArea {
  margin-top: 20px;
}

.popup_btnArea.popup_flex.popup_sb {
  justify-content: space-between;
}

.popup_btnArea.popup_flex {
  display: flex;
}

.popup_flex {
  position: relative;
  display: flex;
}


.popup_btn {
  position: relative;
  display: flex;
  align-items: center;
  height: 40px;
  padding: 0 15px;
  border-radius: 4px;
  box-sizing: border-box;
  font-family: 'Pretendard-Regular';
  font-weight: normal;
  font-size: 16px;
  color: #fff;
  vertical-align: top;
  letter-spacing: -1px;
  transition: .4s;
  cursor: pointer;
  border: none;
}

.popup_btnApply {
  padding: 0 30px;
  background: #ff4b00;
  margin-right: 5px;
}

.popup_btnAttributeEdit {
  background: #555;
}

.popup_validBox {
  margin-top: 5px;
  height: 20px;
  width: 100%;
  margin-left: 16px;
}

.popup_popupSettings {}.layerSearch_layerSearch {
  display: flex;
  color: #333;
  letter-spacing: -0.045em !important;
  padding: 0 !important;
  height: 688px;
}

.layerSearch_hidden {
  display: none;
  margin: 0;
  padding: 0;
  width: 0;
  height: 0;
  overflow: hidden;
  font-size: 0;
  line-height: 0;
  visibility: hidden;
}

.layerSearch_dep {
  overflow-y: auto;
  max-height: 688px;
  /* 230809 왼쪽 탭을 접었다 폈다 하는 형태로 수정하기 위해 주석처리함
  min-height: 520px;
  */
  background-color: #eee;
}

.layerSearch_layerMenu {
  width: 180px;
  display: flex;
  flex-direction: column;
  background: #F4F5F6;
}

.layerSearch_sortSelect {
  width: 160px;
  height: 30px;
  float: right;
}

.layerSearch_sortDt {
  width: 100%;
}

.layerSearch_layerMenu dl dt {
  height: 42px;
  line-height: 42px;
  text-indent: 24px;
  color: #fff;
  font-size: 16px;
  font-family: '맑은 고딕';
  font-weight: bold;
  background: #444;
}

.layerSearch_layerMenu dl dd.layerSearch_selected {
  color: #436aeb;
  background: #fff;
}

.layerSearch_layerMenu dl dd ul li.layerSearch_selected {
  color: #436aeb;
  background: #fff;
}

.layerSearch_layerMenu dl,
.layerSearch_layerMenu dd,
.layerSearch_layerMenu dt,
.layerSearch_layerMenu ul,
.layerSearch_layerMenu li .layerSearch_resultArea dl,
.layerSearch_resultArea dl dt,
.layerSearch_resultArea .layerSearch_layerTypeTxt,
.layerSearch_resultArea .layerSearch_layerCountTxt,
.layerSearch_layerListBox,
.layerSearch_layerListBox table,
.layerSearch_layerListBox table thead,
.layerSearch_layerListBox table tr,
.layerSearch_layerListBox table th,
.layerSearch_layerListBox table tbody {
  margin: 0 !important;
  padding: 0;
  border: 0;
  font: inherit;
}


.layerSearch_layerMenu dl dd {
  position: relative;
  background: #eee;
  line-height: 42px;
  font-size: 16px;
  font-family: '맑은 고딕';
  font-weight: bold;
  text-indent: 32px;
  cursor: pointer;
}

.layerSearch_layerMenu .layerSearch_dep dd:before {
  position: absolute;
  left: 20px;
  top: 21px;
  display: block;
  content: '';
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: #c5c5c5;
}

.layerSearch_layerContent {
  max-height: 650px;
  flex: 1;
  padding: 30px;
  overflow-y: auto;
  overflow-x: hidden;
}

.layerSearch_layerMenu dl dd ul li {
  color: #555;
  font-size: 14px;
  font-family: '맑은 고딕';
  font-weight: normal;
  line-height: 30px;
  text-indent: 46px;
  list-style: none;
  display: flex;
}

.layerSearch_layerMenu dl dd ul li span {
  overflow: hidden;
  line-height: 30px;
  width: 261px;
  white-space: normal;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  height: 30px;
}

.layerSearch_filter {}

.layerSearch_layerMenu dl.layerSearch_filter dd {
  display: flex;
  align-items: center;
  padding-left: 20px;
  text-indent: 0;
}

.layerSearch_layerMenu dl.layerSearch_filter dd em {
  margin-left: 5px;
  font-style: normal;
  font-size: 12px;
  font-weight: normal;
}

.layerSearch_layerMenu dl dd.layerSearch_selected {
  color: #436aeb;
  background: #fff;
}

.layerSearch_searchArea {
  margin: 0;
  padding: 0;
  border: 0;
  font: inherit;
}

.layerSearch_inputBox {
  display: flex;
}

.layerSearch_inputBox .layerSearch_search {
  width: 100%;
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  padding-left: 15px;
  height: 40px;
  border-radius: 4px;
  border: 1px solid #e9e9e9;
  color: #555;
  font-size: 16px;
  font-family: '맑은 고딕';
  font-weight: normal;
  resize: none;
}

.layerSearch_btnSearch {
  margin-left: 10px;
  width: 49px;
  background: #333 url(./images/widget/popup/ico-search.png) no-repeat center;
  display: flex;
  align-items: center;
  height: 40px;
  padding: 0 15px;
  border-radius: 4px;
  box-sizing: border-box;
  font-family: '맑은 고딕';
  font-weight: normal;
  font-size: 16px;
  color: #fff;
  vertical-align: top;
  letter-spacing: -1px;
  transition: .4s;
  cursor: pointer;
  font-size: 0;
}

.layerSearch_validBox {
  margin-top: 5px;
  height: 3px;
}

.layerSearch_resultArea {
  margin: 19px 0;
  letter-spacing: -0.045em !important;
  color: #333;
  align-items: center;
  justify-content: space-between;
  padding: 0;
  border: 0;
}

.layerSearch_noCont {
  min-width: 70%;
}

.layerSearch_layerTypeTxt {}

.layerSearch_layerCountTxt {}

.layerSearch_btnSearch span {
  display: block;
  margin: 0;
  padding: 0;
  width: 0;
  height: 0;
  overflow: hidden;
  font-size: 0;
  line-height: 0;
  visibility: hidden;
}

.layerSearch_resultArea dl {
  display: flex;
}

.layerSearch_resultArea dl dt {
  position: relative;
  margin-right: 20px;
  letter-spacing: -0.045em !important;
  color: #333;
}

.layerSearch_resultArea dl dt strong {
  color: #555;
  font-size: 16px;
  font-family: '맑은 고딕';
  font-weight: normal;
}

.layerSearch_resultArea .layerSearch_layerTypeTxt {
  color: #ff8a61;
}

.layerSearch_resultArea .layerSearch_layerCountTxt {
  color: #283ebf;
}

.layerSearch_layerListWrap {}

.layerSearch_layerListBox {
  max-height: 489px;
  /*
  overflow-y: auto;
  overflow-x: hidden;
  */
  /* 230317 위젯 예제 개선 */
  overflow: auto;
  width: 100%;
  border-top: 1px solid #e9e9e9;
  border-bottom: 1px solid #e9e9e9;
  text-align: center;
}

.layerSearch_layerListBox table {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  text-align: center;
  color: #333;
  letter-spacing: -0.045em !important;
  table-layout: fixed;
}

.layerSearch_layerListBox table th:nth-child(1),
.layerSearch_layerListBox table tbody tr td:nth-child(1) {
  width: 48px;
}

.layerSearch_layerListBox table th:nth-child(2),
.layerSearch_layerListBox table tbody tr td:nth-child(2) {
  /* width: 100px; */
  /* 서비스 컬럼이 빠지면서 아래와 같이 수정 */
  width: 35%;
}

.layerSearch_layerListBox table th:nth-child(3),
.layerSearch_layerListBox table tbody tr td:nth-child(3) {
  /*width: 225px;*/
  /* 서비스 컬럼이 빠지면서 아래와 같이 수정 */
  width: auto;
}

.layerSearch_layerListBox table th:nth-child(4),
.layerSearch_layerListBox table tbody tr td:nth-child(4) {
  /* 230317 위젯 예제 개선 */
  /* width: auto; */
  /* 서비스 컬럼이 빠지면서 아래와 같이 수정 */
  width: 48px;
}

.layerSearch_layerListBox table th:nth-child(5),
.layerSearch_layerListBox table tbody tr td:nth-child(5) {
  width: 48px;
}

.layerSearch_layerListBox table th:nth-child(6),
.layerSearch_layerListBox table tbody tr td:nth-child(6) {
  width: 48px;
}

.layerSearch_layerListBox table thead tr:first-child th {
  position: sticky;
  top: 0;
  z-index: 10;
}

.layerSearch_layerListBox table thead tr th {
  font-size: 16px;
  font-family: '맑은 고딕';
  font-weight: bold;
}

.layerSearch_layerListBox table th {
  background: #f9f9f9;
  font-weight: bold;
}

.layerSearch_layerListBox table th,
.layerSearch_layerListBox table td {
  height: 40px;
  color: #555;
  border-bottom: 1px solid #e9e9e9;
  padding: 8px;
}

.layerSearch_layerListBox.layerSearch_hover table tbody tr td {
  transition: .4s;
}

.layerSearch_layerListBox table tbody tr td {
  color: #333;
  background: #fff;
}

.layerSearch_layerListBox table tbody tr th,
.layerSearch_layerListBox table tbody tr td {
  font-size: 16px;
  font-family: '맑은 고딕';
}

.layerSearch_layerListBox table th,
.layerSearch_layerListBox table td {
  height: 40px;
  color: #555;
  border-bottom: 1px solid #e9e9e9;
  padding: 8px;
}

.layerSearch_layerDcTxt {
  text-align: left;
}

.layerSearch_layerListBox table td p {
  margin: 0;
}

.layerSearch_iconWfs,
.layerSearch_iconWms,
.layerSearch_iconWmts {
  border: none;
  width: 56px;
  height: 40px;
  display: block;
  background-position: center;
  background-repeat: no-repeat;
  margin-left: 14px;
}

.layerSearch_iconWfs {
  background-image: url(./images/widget/common/ico-WFS.png);
}

.layerSearch_iconWms {
  background-image: url(./images/widget/common/ico-WMS.png);
}

.layerSearch_iconWmts {
  background-image: url(./images/widget/common/ico-WMTS.png);
}

.layerSearch_dep::-webkit-scrollbar,
.layerSearch_layerListBox::-webkit-scrollbar {
  width: 14px;
  height: 14px;
}

.layerSearch_dep::-webkit-scrollbar-button,
.layerSearch_layerListBox::-webkit-scrollbar-button {
  display: none;
}

.layerSearch_dep::-webkit-scrollbar-thumb,
.layerSearch_layerListBox::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 5px;
}

.layerSearch_dep::-webkit-scrollbar-track,
.layerSearch_layerListBox::-webkit-scrollbar-track {
  background-color: #e9e9e9;
}

.layerSearch_btnAddLayer:hover {
  background: #fff url(./images/widget/common/ico-in-add-hover.png) no-repeat center;
  border: 1px solid #333;
}

.layerSearch_btnAddLayer {
  background: #fff url(./images/widget/common/ico-in-add.png) no-repeat center;
  position: relative;
  width: 40px;
  padding: 0;
  border: 1px solid #e9e9e9;
  display: flex;
  align-items: center;
  height: 40px;
  padding: 0 15px;
  border-radius: 4px;
  box-sizing: border-box;
  font-family: '맑은 고딕';
  font-weight: normal;
  font-size: 16px;
  color: #fff;
  vertical-align: top;
  letter-spacing: -1px;
  transition: .4s;
  cursor: pointer;
}

.layerSearch_btnAddLayer span,
.layerSearch_btnRemoveLayer span {
  display: none;
}

.layerSearch_btnRemoveLayer {
  background: #fff url(./images/widget/common/ico-in-remove.png) no-repeat center;
  position: relative;
  width: 40px;
  padding: 0;
  border: 1px solid #e9e9e9;
  display: flex;
  align-items: center;
  height: 40px;
  padding: 0 15px;
  border-radius: 4px;
  box-sizing: border-box;
  font-family: '맑은 고딕';
  font-weight: normal;
  font-size: 16px;
  color: #fff;
  vertical-align: top;
  letter-spacing: -1px;
  transition: .4s;
  cursor: pointer;
}

.layerSearch_btnRemoveLayer:hover {
  background: #fff url(./images/widget/common/ico-in-remove-hover.png) no-repeat center;
}

.layerSearch_btnRemoveLayer:hover {
  border: 1px solid #333;
}

.layerSearch_layerType,
.layerSearch_layerType0,
.layerSearch_layerType1,
.layerSearch_layerType2,
.layerSearch_layerType3,
.layerSearch_layerType4,
.layerSearch_layerType5,
.layerSearch_layerType6 {
  width: 13px;
  height: 13px;
  display: block;
  background-position: center;
  background-repeat: no-repeat;
}

.layerSearch_layerTypeBox {}

.layerSearch_layerType0 {
  background-image: url(./images/widget/ico/ico-layer-t.png);
}

.layerSearch_layerType1 {
  background-image: url(./images/widget/ico/ico-layer-dot.png);
}

.layerSearch_layerType2 {
  background-image: url(./images/widget/ico/ico-layer-line.png);
}

.layerSearch_layerType3 {
  background-image: url(./images/widget/ico/ico-layer-plane.png);
}

.layerSearch_layerType4,
.layerSearch_layerType6 {
  background-image: url(./images/widget/ico/ico-layer-g.png);
}

.layerSearch_layerType {
  background-image: url(./images/widget/ico/ico-layer-all.png);
}

.layerSearch_layerType5 {
  background-image: url(./images/widget/ico/ico-layer-group.png);
}

.layerSearch_layerType6 {}

.layerSearch_layerTypeBox {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  margin: 0 auto;
  border: 1px solid #e9e9e9;
  border-radius: 4px;
  background: #fff;
}

.layerSearch_filter i {
  margin: 0 13px 0 0;
}

.layerSearch_subDc {
  font-size: 14px;
  color: #878787;
}

.layerSearch_lyrTxt {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  line-height: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
}

/* 230808 레이어검색 위젯 개선 */
.layerSearch_navListBtn {
  cursor: pointer;
  width: 14px;
  height: 26px;
  margin-left: 100px;
  vertical-align: middle;
  background: url(./images/widget/toc/ico-toc-hide_dark.png) no-repeat center;
}

.layerSearch_layerTypeListBtn {
  cursor: pointer;
  width: 14px;
  height: 26px;
  margin-left: 100px;
  vertical-align: middle;
  background: url(./images/widget/toc/ico-toc-hide_dark.png) no-repeat center;
}

.layerSearch_listHide {
  display: none;
}

.layerSearch_listShow {
  display: block;
}

.layerSearch_listFoldBtn {
  transform: rotate(90deg);
}

.layerSearch_listUnfoldBtn {
  transform: rotate(-90deg);
}

.layerSearch_btnArea{

}
.layerSearch_tableBox{

}

.layerSearch_colBox{
  
}

.layerSearch_row{
}

.layerSearch_label{
}.fileDrop_fileDropTxt {
  position: relative;
  padding-top: 20px;
  text-align: center;
  font-size: 25px;
  font-family: '맑은 고딕';
  color: #555;
  display: block;
}

.fileDrop_fileDropTxt:after {
  position: absolute;
  left: 50%;
  top: 120px;
  display: block;
  content: '';
  transform: translateX(-50%);
  width: 82px;
  height: 82px;
  border-radius: 50%;
  border: 1px solid #e9e9e9;
  background: #fff url(./images/widget/common/ico-upload-plus.png) no-repeat center;
  box-sizing: border-box;

  top: 67px;
  width: 54px;
  height: 53px;
  border-radius: 50%;
  background: #fff url(./images/widget/common/ico-upload-plus.png) no-repeat center;
  box-sizing: border-box;
}

.fileDrop_hidden {
  display: none;
  margin: 0;
  padding: 0;
  width: 0;
  height: 0;
  overflow: hidden;
  font-size: 0;
  line-height: 0;
  visibility: hidden;
}/* .layerUpload  */

.layerUpload_layerUpload {
  color: #333;
  font-size: 15px;
  font-family: '맑은 고딕';
}

.layerUpload_section {
  padding-bottom: 20px;
  border-bottom: 0;
}

.layerUpload_section:last-of-type {
  margin-bottom: 0;
  padding-bottom: 0;
}

.layerUpload_titSec {
  display: flex;
  align-items: center;
  margin-top: 10px;
}

.layerUpload_mb20 {
  margin-bottom: 20px;
}

.layerUpload_red {
  color: #fc3e3e;
}

.layerUpload_bold {
  font-weight: bold;
}

.layerUpload_dec {
  text-align: right;
  color: #436aeb;
  text-indent: 21px;
  font-size: 14px;
  line-height: 30px;
}

.layerUpload_section .layerUpload_titSec strong {
  position: relative;
  font-size: 18px;
  color: #2e2e2e;
  text-indent: 10px;
}

.layerUpload_table table {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
}

.layerUpload_table table tbody tr th,
.layerUpload_table table tbody tr td {
  font-size: 16px;
}

.layerUpload_table {
  max-height: 342px;
  overflow-y: auto;
}

.layerUpload_table table th {
  background: #f9f9f9;
  font-weight: bold;
  width: 200px;
  word-break: keep-all;
}

.layerUpload_table table th,
.layerUpload_table table td {
  height: 40px;
  color: #555;
  border-bottom: 1px solid #e9e9e9;
  padding: 8px;
}

.layerUpload_table table td input[type="text"],
.layerUpload_layerUpload select,
.layerUpload_layerUpload .layerUpload_layerNameBox input,
.layerUpload_sourceSridBox input {
  width: 100%;
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  padding-left: 15px;
  height: 40px;
  border-radius: 4px;
  border: 1px solid #e9e9e9;
  color: #555;
  font-size: 16px;
  font-weight: normal;
  resize: none;
}

.layerUpload_table table tbody tr td {
  color: #333;
  background: #fff;
}

.layerUpload_checkbox input {
  display: none;
  border: none !important;
}

.layerUpload_checkbox label:before {
  position: absolute;
  left: 0;
  top: 50%;
  display: block;
  content: '';
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  border-radius: 2px;
  border: 1px solid #cbced2;
  background-color: #fff;
  background-position: center;
  background-repeat: no-repeat;
  transition: .4s;
  background-size: 0px 0px;
}

.layerUpload_checkbox input[type="checkbox"]:checked+label:before {
  background-image: url(./images/widget/common/ico-checkbox.png);
  background-size: 11px 10px;
}

.layerUpload_layerUpload select:disabled {
  background-color: #f6f6f6;
}

.layerUpload_checkbox label {
  position: relative;
  display: inline-block;
  height: 22px;
  line-height: 22px;
  text-indent: 30px;
  color: #555;
  font-size: 16px;
}

.layerUpload_btnParamAdd {
  margin-left: auto;
  background: #f9f9f9;
  border: 1px solid #e9e9e9;
  color: #555;
  height: 40px;
  display: flex;
  align-items: center;
  padding: 0 15px;
  border-radius: 4px;
  box-sizing: border-box;
  font-weight: normal;
  font-size: 16px;
  vertical-align: top;
  letter-spacing: -1px;
  cursor: pointer;
}

.layerUpload_table table .layerUpload_variable {
  display: block;
  width: 100%;
  height: 40px;
  line-height: 40px;
  padding-left: 0;
  border-radius: 4px;
  background: #fff;
  color: #333333;
  font-weight: bold;
  text-align: center;
  font-size: 16px;
  border: 1px solid #555555;
  text-indent: 0;
}

.layerUpload_paramBox .layerUpload_table {
  overflow-y: scroll;
  max-height: 122px;
}

.layerUpload_paramBox .layerUpload_table.layerUpload_createLayerTable {
  max-height: 250px;
}

.layerUpload_btnUploadCustomLayer,
.layerUpload_btnUploadFileLayer,
.layerUpload_btnUploadGeocoding {
  margin-left: 0;
  margin-right: 0;
  padding: 0 30px;
  background: #436aeb;
  height: 40px;
  display: flex;
  align-items: center;
  border-radius: 4px;
  box-sizing: border-box;
  font-weight: normal;
  font-size: 16px;
  color: #fff;
  vertical-align: top;
  letter-spacing: -1px;
  cursor: pointer;
  border: 0;
}

.layerUpload_btnArea {
  margin-top: 20px;
  justify-content: flex-end;
  display: flex;
}

.layerUpload_sridSelectBox,
.layerUpload_charsetSelectBox {
  width: 100%;
}

.layerUpload_section .layerUpload_titSec strong:before {
  position: absolute;
  left: 0;
  top: 6px;
  width: 3px;
  height: 14px;
  background: #d6d6d6;
  display: block;
  content: '';
}

.layerUpload_stepBox {
  display: flex;
}

.layerUpload_stepBox strong {
  width: 15%;
  height: 30px;
  line-height: 30px;
  padding-left: 0;
  color: #333333;
  font-weight: bold;
}

.layerUpload_descriptionTxt {
  color: #436aeb;
  text-indent: 34px;
  font-size: 14px;
  line-height: 20px;
  display: block;
  float: right;
}

.layerUpload_fieldDesTxt {
  position: relative;
}

.layerUpload_titNum {
  width: 18px;
  height: 18px;
  line-height: 18px;
  margin-right: 6px;
  border-radius: 50px;
  background: #444;
  color: #fff;
  text-align: center;
}

.layerUpload_fileSelectBox,
.layerUpload_fieldTypeBox,
.layerUpload_separatorSelectBox,
.layerUpload_fieldBox,
.layerUpload_xyFieldBox,
.layerUpload_centerPointBox,
.layerUpload_layerNameBox .layerUpload_preserveOriginCoordBox {
  margin-bottom: 10px;
}

.layerUpload_radioGroup {
  justify-content: space-around;
  display: flex;
}

.layerUpload_radio label:before {
  position: absolute;
  left: 0;
  top: 50%;
  display: block;
  content: '';
  transform: translateY(-50%);
  width: 23px;
  height: 23px;
  border-radius: 50%;
  border: 1px solid #e9e9e9;
  background-position: center;
  background-repeat: no-repeat;
  background-image: url(./images/widget/common/ico-radio.png);
}

.layerUpload_hidden {
  display: none;
  margin: 0;
  padding: 0;
  width: 0;
  height: 0;
  overflow: hidden;
  font-size: 0;
  line-height: 0;
  visibility: hidden;
}



.layerUpload_radio label {
  position: relative;
  display: inline-block;
  height: 23px;
  line-height: 23px;
  text-indent: 30px;
  font-size: 17px;
  font-weight: bold;
}

.layerUpload_radio input[type="radio"] {
  display: none;
}

.layerUpload_layerUpload input[type="radio"] {
  border: none !important;
}

.layerUpload_layerUpload input,
.layerUpload_layerUpload button,
.layerUpload_layerUpload a,
.layerUpload_layerUpload select,
.layerUpload_layerUpload option {
  font-family: inherit;
  font-size: inherit;
}

.layerUpload_radio input[type="radio"]:checked+label:before {
  background-color: #333;
  background-image: url(./images/widget/common/ico-radio-check.png);
}

.layerUpload_layerUpload .layerUpload_xfieldBox,
.layerUpload_layerUpload .layerUpload_yfieldBox {
  text-indent: 21px;
  font-size: 14px;
  line-height: 30px;
  width: 389px;
}

.layerUpload_xyFieldBox span {
  margin-right: 10px;
}

.layerUpload_yfieldTxt {
  margin-left: 10px;
}

.layerUpload_btnParamDelete {
  width: 40px;
  background: url(./images/widget/common/ico-tr-remove.png) no-repeat center;
  border: 1px solid #e9e9e9;
  color: #555;
  height: 40px;
}

.layerUpload_requiredDataTable {}

.layerUpload_table::-webkit-scrollbar,
.layerUpload_layerListBox::-webkit-scrollbar {
  width: 14px;
  height: 14px;
}

.layerUpload_table::-webkit-scrollbar-button,
.layerUpload_layerListBox::-webkit-scrollbar-button {
  display: none;
}

.layerUpload_table::-webkit-scrollbar-thumb,
.layerUpload_layerListBox::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 5px;
}

.layerUpload_table::-webkit-scrollbar-track,
.layerUpload_layerListBox::-webkit-scrollbar-track {
  background-color: #e9e9e9;
}

.layerUpload_resolutionsBtnArea {
  margin-bottom: 10px;
}

.layerUpload_btnParamDelete span {
  display: none;
}

.layerUpload_lyrNmBox {
  width: 100%;
}

.layerUpload_createLayerTable table colgroup col:nth-child(1) {
  width: 80%;
}

.layerUpload_createLayerTable table colgroup col:nth-child(2) {
  width: 35%;
}

.layerUpload_table {
  margin-top: 10px;
}

.layerUpload_paramBox table tr th {
  width: 40%;
}

.layerUpload_paramBox table tr td:nth-child(2) {
  width: 58%;
}

.layerUpload_resolutions_error_ext {
  position: relative;
  float: right;
  right: 126px;
}

.layerUpload_paramKey_error_txt {
  font-weight: normal;
}

.layerUpload_table table td input[type="text"].layerUpload_paramVal {
  width: 89%;
  vertical-align: top;
}

.layerUpload_layerUpload button.layerUpload_btnParamDelete {
  margin-left: 10px;
}

.layerUpload_content>div {
  padding-top: 30px;
}

.layerUpload_title:before {
  position: absolute;
  width: 3px;
  height: 14px;
  background: #0A2D3D;
  display: block;
  content: '';
}

.layerUpload_title>span {
  position: relative;
  font-size: 18px;
  color: #2e2e2e;
  text-indent: 10px;
  font-weight: bold;
}

.layerUpload_title {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
}

.layerUpload_xfieldTxt {}

.layerUpload_webLayerUploadBox {}

.layerUpload_paramBox {}



.layerUpload_fileLayerUploadBox {}

.layerUpload_geocodingUploadBox {}

.layerUpload_onefieldBox {}

.layerUpload_onefieldSelectBox {}

.layerUpload_centerPointSelectBox {}

.layerUpload_basic {}

.layerUpload_check {}
.tab_tabList{
  display: flex;
  margin: 0;
  padding: 0;
}

.tab_tabList li.tab_selected {
  color: #ffffff;
  background-color: #444444;
}

.tab_tabList li {
  height: 45px;
  line-height: 42px;
  border-radius: 4px;
  font-size: 17px;
  color: #555;
  margin-right: 1px;
  letter-spacing: -0.77px;
  cursor: pointer;
  background-color: #eeeeee;
  font-family: '맑은 고딕';
  font-weight: bold;
  list-style: none;
}
.tab_tabList li {
  flex: 1;
  text-align: center;
}
.tab_content > div {
  padding-top: 30px;
}


.addressSearch_frame {
  width: 100%;
  position: relative;
  margin-right: 18px;
  margin: 0;
  padding: 0;
  border: 0;
  font: inherit;
}

.addressSearch_hidden {
  display: block;
  margin: 0;
  padding: 0;
  width: 0;
  height: 0;
  overflow: hidden;
  font-size: 0;
  line-height: 0;
  visibility: hidden;
}

.addressSearch_frame input,
.addressSearch_frame select {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

.addressSearch_frame select.addressSearch_type {
  padding-right: 30px;
  width: 130px;
  height: 38px;
  margin-right: 1px;
  padding-left: 9px;
  text-indent: 0;
  color: #555;
  font-size: 14px;
  border: 0;
  box-shadow: 0.5px 0.9px 4px 0 rgb(0 0 0 / 27%);
  border-radius: 3px;
}

.addressSearch_frame select.addressSearch_type option {
  font-family: inherit;
  font-size: inherit;
  font-weight: normal;
  display: block;
  white-space: nowrap;
  min-height: 1.2em;
  padding: 0px 2px 1px;
}

.addressSearch_frame .addressSearch_inputBox {
  position: relative;
  width: 230px;
  box-shadow: 0.5px 0.9px 4px 0 rgb(0 0 0 / 27%);
  display: flex;
}

.addressSearch_inputWrap {
  padding: 0 45px 0 15px;
  background: white;
  border-radius: 2px;
  display: flex;
}

.addressSearch_frame .addressSearch_inputBox input {
  width: 100%;
  height: 38px;
  border: 0;
  padding: 0 5px 0 5px;
  border-radius: 3px;
}

.addressSearch_frame .addressSearch_param {
  width: 100%;
  height: 38px;
  border: 0;
  padding: 0 10px 0 10px;
  border-radius: 3px;
}

.addressSearch_param.addressSearch_keyword {}

.addressSearch_inputBox .addressSearch_param.addressSearch_lng,
.addressSearch_inputBox .addressSearch_param.addressSearch_lat {
  max-width: 100px;
}

.addressSearch_btnSearch {
  position: absolute;
  right: 10px;
  top: 50%;
  width: 21px;
  height: 22px;
  transform: translateY(-50%);
  background: url(./images/widget/common/ico-search.png) no-repeat;
  display: inline-block;
  margin-right: 5px;
  border: 0;
}

.addressSearch_btnSearch span {
  display: none;
}

.addressSearch_result {
  right: 0;
  top: 40px;
  width: 100%;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0.5px 0.9px 4px 0 rgb(0 0 0 / 27%);
  display: block;
}

.addressSearch_inner {
  padding: 10px;
}

.addressSearch_searchArea {
  display: flex;
}

/*검색결과 header 영역*/
.addressSearch_head {
  border-bottom: 1px solid #ccc;
  font-size: 14px;
  font-family: "Pretendard";
  word-break: keep-all;
}

.addressSearch_head .addressSearch_inner {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  word-break: break-all;
}

.addressSearch_val {}

.addressSearch_btnAddrClose {
  width: 17px;
  height: 17px;
  background: url(./images/widget/common/ico-tr-remove.png) no-repeat center;
}

.addressSearch_hidden {
  display: block;
  margin: 0;
  padding: 0;
  width: 0;
  height: 0;
  overflow: hidden;
  font-size: 0;
  line-height: 0;
  visibility: hidden;
}

.addressSearch_noSelect .addressSearch_inputBox {
  width: 100%;
}

.addressSearch_noSelect .addressSearch_inputWrap {
  width: 100%;
}

.addressSearch_noSelect .addressSearch_inputWrap div {
  width: 100%;
}

.addressSearch_noSelect .addressSearch_inputWrap div.addressSearch_coord {
  width: 50%;
}

.addressSearch_noSelect .addressSearch_inputBox .addressSearch_param.addressSearch_lng,
.addressSearch_noSelect .addressSearch_inputBox .addressSearch_param.addressSearch_lat {
  max-width: 100%;
}

.addressSearch_btnReset {
  position: absolute;
  right: 10px;
  top: 50%;
  width: 21px;
  height: 22px;
  transform: translateY(-50%);
  background: url(./images/widget/common/ico-tr-remove.png) center no-repeat;
  display: inline-block;
  margin-right: 5px;
  border: 0;
}

.addressSearch_btnReset span {
  display: none;
}
.administrativeDistrictSearch_location{
  display: flex;
}
.administrativeDistrictSearch_location li, .administrativeDistrictSearch_location ul{
  list-style: none;
}

.administrativeDistrictSearch_location > ul >li ul{
  max-height: 200px;
  overflow-y: scroll;
}

.administrativeDistrictSearch_location > ul{
  display: flex;
}

.administrativeDistrictSearch_location > ul > li:not(.administrativeDistrictSearch_last):after {
  position: absolute;
  right: -13px;
  top: 14px;
  content: '';
  display: block;
  width: 6px;
  height: 9px;
  margin: 0 8px;
  background: url(./images/widget/common/ico-location-arrow.png) no-repeat;
}
.administrativeDistrictSearch_location > ul > li{
  position: relative;
  line-height: 38px;
  color: #787878;
  font-family: '맑은 고딕';
  text-align: center;
  font-size: 14px;
}
.administrativeDistrictSearch_location > ul > li > span{
  padding: 0 20px;
  cursor: pointer;
}
.administrativeDistrictSearch_location > ul > li > span.administrativeDistrictSearch_selected{
	font-weight: bold;
}
.administrativeDistrictSearch_location > ul > li .administrativeDistrictSearch_dep2.administrativeDistrictSearch_active {
  display: block;
}
.administrativeDistrictSearch_location > ul > li .administrativeDistrictSearch_dep2 {
  display: none;
  position: absolute;
  left: 0;
  top: 38px;
  background: #fff;
  width: 130px;
  box-shadow: 0.5px 0.9px 4px 0 rgb(0 0 0 / 27%);
  border-radius: 4px;
  overflow: hidden;
}
.administrativeDistrictSearch_location > ul > li .administrativeDistrictSearch_dep2 ul li{
  text-align: left;
  text-indent: 12px;
  line-height: 25px;
  cursor: default;
  font-weight: normal;
}
.administrativeDistrictSearch_location > ul > li .administrativeDistrictSearch_dep2 ul li:hover {
	background: #e4edff;
}
.administrativeDistrictSearch_ctpv{

}
.administrativeDistrictSearch_sgg{

}
.administrativeDistrictSearch_emd{

}
.administrativeDistrictSearch_li{

}
.administrativeDistrictSearch_type{

}
.administrativeDistrictSearch_value{
}
/* 스크롤테마 */
/* line 1035, scss/common.scss */
.administrativeDistrictSearch_cScroll {
	overflow-y: auto;
	overflow-x: hidden;
	scrollbar-width: thin;
	scrollbar-color: #303030 transparent;
	scrollbar-arrow-color: #fff;
	scrollbar-3dlight-color: #fff;
	scrollbar-darkshadow-color: #fff;
	scrollbar-face-color: #303030;
	scrollbar-hightlight-color: #fff;
	scrollbar-shadow-color: #fff;
	scrollbar-track-color: #fff;
	scrollbar-base-color: #efefef;
}

/* line 1038, scss/common.scss */
.administrativeDistrictSearch_cScroll .administrativeDistrictSearch_innerScroll {
	width: calc(100% + 8px);
}

/* line 1040, scss/common.scss */
.administrativeDistrictSearch_cScroll::-webkit-scrollbar {
	width: 14px;
	height: 14px;
}

/* line 1041, scss/common.scss */
.administrativeDistrictSearch_cScroll::-webkit-scrollbar-button {
	display: none;
}

/* line 1042, scss/common.scss */
.administrativeDistrictSearch_cScroll::-webkit-scrollbar-track {
	background-color: #e9e9e9;
}

/* line 1043, scss/common.scss */
.administrativeDistrictSearch_cScroll::-webkit-scrollbar-thumb {
	background: #ccc;
	border-radius: 5px;
}

/* line 1044, scss/common.scss */
.administrativeDistrictSearch_cScroll::-webkit-scrollbar-thumb:hover {
	background: #b6b6b6;
}

/* line 1045, scss/common.scss */
.administrativeDistrictSearch_cScroll::-webkit-scrollbar-thumb:active {
	background: #808080;
}

/* line 1046, scss/common.scss */
.administrativeDistrictSearch_cScroll.administrativeDistrictSearch_cScrollX {
	overflow-y: hidden;
	overflow-x: scroll;
}

/* line 1047, scss/common.scss */
.administrativeDistrictSearch_cScroll.administrativeDistrictSearch_cScrollX .administrativeDistrictSearch_innerScroll {
	width: auto;
}

/* line 1049, scss/common.scss */
.administrativeDistrictSearch_cScroll.administrativeDistrictSearch_cScrollXY {
	overflow-y: scroll;
	overflow-x: scroll;
}

/* line 1052, scss/common.scss */
.administrativeDistrictSearch_cScroll.administrativeDistrictSearch_thin::-webkit-scrollbar {
	width: 6px;
	height: 6px;
}


/*****hhj추가 css****************************************************/
.spatialAnalysis_widget {
	height: 100%;
}

button.spatialAnalysis_btnRemove,
button.spatialAnalysis_btnAdd {
	width: 20px;
	height: 40px;
	background-position: center;
	background-repeat: no-repeat;
	border: 1px solid #e9e9e9;
}

button.spatialAnalysis_btnRemove {
	margin-left: 5px;
	background-image: url(./images/widget/popup/ico-remove.png);
}

button.spatialAnalysis_btnAdd {
	background-image: url(./images/widget/common/ico-add.png);
}

.spatialAnalysis_listItem {
	width: calc(100% - 25px);
}

.spatialAnalysis_detailFrame {}

.spatialAnalysis_inputParameters {}

.spatialAnalysis_inputArea {}

.spatialAnalysis_resultArea {
	background: #ecf1ff;
	border-radius: 10px;
	padding: 17px;
	margin-top: 40px;
}

.spatialAnalysis_resultArea .spatialAnalysis_titSec {
	display: block;
	margin-bottom: 18px;
	font-size: 18px;
	color: #333;
	font-family: 'Pretendard Bold';
	font-weight: bold;
}

.spatialAnalysis_resultArea input {
	background-color: white;
}

.spatialAnalysis_resultLayerName {
	width: 100%;
}

.spatialAnalysis_resultLayerDescription {
	width: 100%;
}

/*common.css에서 분리*/

/* accordion */
.spatialAnalysis_accordion .spatialAnalysis_toolList>li {
	line-height: 58px;
	text-indent: 21px;
	font-family: "Pretendard Bold";
	color: #333;
	font-size: 17px;
}

.spatialAnalysis_accordion .spatialAnalysis_toolList>li>span {
	display: block;
	border-bottom: 1px solid #c9d7f1;
	cursor: pointer;
}

.spatialAnalysis_accordion .spatialAnalysis_toolList>li .spatialAnalysis_handle {
	width: 19px;
	height: 19px;
	margin-right: 7px;
	background: url(./images/widget/toc/ico-handle.png) no-repeat center;
	cursor: pointer;
	opacity: 0;
}

.spatialAnalysis_accordion .spatialAnalysis_toolList>li .spatialAnalysis_btnTip {
	margin: 0 0 0 auto;
	color: #555;
	border-color: #555;
}

.spatialAnalysis_accordion .spatialAnalysis_toolList>li .spatialAnalysis_innerList {
	display: none;
	background: #fff;
	border-bottom: 1px solid #c9d7f1;
}

.spatialAnalysis_accordion .spatialAnalysis_toolList>li .spatialAnalysis_innerList li {
	display: flex;
	align-items: center;
	color: #333;
	text-indent: 46px;
	line-height: 45px;
}

.spatialAnalysis_accordion .spatialAnalysis_toolList>li .spatialAnalysis_innerList li:hover {
	color: #436aeb;
}

.spatialAnalysis_accordion .spatialAnalysis_toolList>li .spatialAnalysis_innerList li:hover .spatialAnalysis_handle {
	opacity: 1;
}

.spatialAnalysis_accordion .spatialAnalysis_toolList>li .spatialAnalysis_innerList li span {
	cursor: pointer;
}

.spatialAnalysis_accordion .spatialAnalysis_toolList>li.spatialAnalysis_hasDep {
	position: relative;
}

.spatialAnalysis_accordion .spatialAnalysis_toolList>li.spatialAnalysis_hasDep:before,
.spatialAnalysis_accordion .spatialAnalysis_toolList>li.spatialAnalysis_hasDep:after {
	position: absolute;
	top: 28px;
	display: block;
	width: 12px;
	height: 2px;
	content: '';
	background: #333;
	transition: .4s;
}

.spatialAnalysis_accordion .spatialAnalysis_toolList>li.spatialAnalysis_hasDep:after {
	right: 23px;
	transform: rotate(-45deg);
}

.spatialAnalysis_accordion .spatialAnalysis_toolList>li.spatialAnalysis_hasDep:before {
	right: 31px;
	transform: rotate(45deg);
}

.spatialAnalysis_accordion .spatialAnalysis_toolList>li.spatialAnalysis_hasDep.spatialAnalysis_active:before {
	background: #8da6cf;
	transform: rotate(-45deg);
}

.spatialAnalysis_accordion .spatialAnalysis_toolList>li.spatialAnalysis_hasDep.spatialAnalysis_active:after {
	background: #8da6cf;
	transform: rotate(45deg);
}

.spatialAnalysis_accordion.spatialAnalysis_library dl {
	margin-bottom: 10px;
	font-size: 16px;
	color: #333;
	font-family: '맑은 고딕';
}

.spatialAnalysis_accordion.spatialAnalysis_library dl dt {
	height: 36px;
	line-height: 36px;
	text-indent: 10px;
	font-weight: bold;
	background: #e4edff;
	border-radius: 4px;
}

.spatialAnalysis_accordion.spatialAnalysis_library dl dd {
	position: relative;
	height: 36px;
	line-height: 36px;
	text-indent: 23px;
}

.spatialAnalysis_accordion.spatialAnalysis_library dl dd:before {
	position: absolute;
	left: 10px;
	top: 50%;
	display: block;
	content: '';
	transform: translateY(-50%);
	width: 2px;
	height: 2px;
	border-radius: 50%;
	background: #333;
}

.spatialAnalysis_accordion.spatialAnalysis_library dl:last-of-type {
	margin-bottom: 0;
}

ul.spatialAnalysis_toolList {
	list-style: none;
}

ul.spatialAnalysis_toolList li {
	list-style: none;
}

ul.spatialAnalysis_toolList,
ul.spatialAnalysis_innerList {
	margin: 0;
	padding: 0;
	border: 0;
	font: inherit;
}

/*, span, object, h1, h2, h3, h4, h5, h6, p, blockquote, a, button, abbr, address, img, q, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td, article, aside, footer, header, section, summary */

/* section */
/* line 269, scss/common.scss */
.spatialAnalysis_section {
	padding-bottom: 20px;
	margin-bottom: 20px;
	border-bottom: 1px solid #e9e9e9;
}

/* line 270, scss/common.scss */
.spatialAnalysis_section.spatialAnalysis_type02 {
	border-bottom: 0;
}

/* line 271, scss/common.scss */
.spatialAnalysis_section.spatialAnalysis_type02 .spatialAnalysis_titSec {
	margin-bottom: 20px;
}

/* line 272, scss/common.scss */
.spatialAnalysis_section.spatialAnalysis_type02 .spatialAnalysis_titSec strong {
	position: relative;
	font-size: 18px;
	color: #2e2e2e;
	text-indent: 10px;
}

/* line 273, scss/common.scss */
.spatialAnalysis_section.spatialAnalysis_type02 .spatialAnalysis_titSec strong:before {
	position: absolute;
	left: 0;
	top: 6px;
	width: 3px;
	height: 14px;
	background: #d6d6d6;
	display: block;
	content: '';
}

/* line 279, scss/common.scss */
.spatialAnalysis_section.spatialAnalysis_type03 .spatialAnalysis_titSec .spatialAnalysis_titNum {
	background: #555;
}

/* line 280, scss/common.scss */
.spatialAnalysis_section.spatialAnalysis_type03 .spatialAnalysis_titSec strong {
	color: #555;
}

/* line 283, scss/common.scss */
.spatialAnalysis_section.spatialAnalysis_noLine {
	padding-bottom: 0;
	border-bottom: 0;
}

/* line 285, scss/common.scss */
.spatialAnalysis_section .spatialAnalysis_titSec {
	display: flex;
	margin-bottom: 10px;
	align-items: flex-start;
}

/* line 286, scss/common.scss */
.spatialAnalysis_section .spatialAnalysis_titSec .spatialAnalysis_titNum {
	width: 18px;
	height: 18px;
	line-height: 18px;
	margin-right: 6px;
	margin-top: 2px;
	border-radius: 50px;
	font-size: 13px;
	background: #444;
	color: #fff;
	text-align: center;
}

/* line 288, scss/common.scss */
.spatialAnalysis_section .spatialAnalysis_titSec .spatialAnalysis_btn {
	margin-left: auto;
}

/* line 289, scss/common.scss */
.spatialAnalysis_section .spatialAnalysis_titSec strong {
	color: #333;
	font-family: "Pretendard Bold";
	font-size: 16px;
	letter-spacing: -1px;
}

/* line 291, scss/common.scss */
.spatialAnalysis_section:last-of-type {
	margin-bottom: 0;
	padding-bottom: 0;
}

/* line 292, scss/common.scss */
.spatialAnalysis_section .spatialAnalysis_btn {
	height: 40px;
}

/* line 293, scss/common.scss */
.spatialAnalysis_section .spatialAnalysis_flex {
	display: flex;
	justify-content: space-between;
	align-items: center;
}


.spatialAnalysis_section span {
	color: #333;
	font-size: 15px;
	font-family: "Pretendard Bold";
}

.spatialAnalysis_section label span {
	font-family: "Pretendard";
}

/* checkbox */
span .spatialAnalysis_checkbox {
	margin-top: 15px;
}

/* line 985, scss/common.scss */
.spatialAnalysis_checkbox input[type="checkbox"] {
	display: none;
}

/* line 987, scss/common.scss */
.spatialAnalysis_checkbox input[type="checkbox"]:checked+label:before {
	background-image: url(./images/widget/common/ico-checkbox.png);
	background-size: 11px 10px;
}

/* line 987, scss/common.scss */
.spatialAnalysis_checkbox.spatialAnalysis_disabled input[type="checkbox"]:checked+label:before {
	background-color: #dedede75;
}

/* line 990, scss/common.scss */
.spatialAnalysis_checkbox label {
	position: relative;
	display: inline-block;
	min-width: 22px;
	min-height: 22px;
	line-height: 22px;
	text-indent: 30px;
	color: #555;
	font-size: 16px;
	font-family: "Pretendard";
}

/* line 990, scss/common.scss */
.spatialAnalysis_checkbox.spatialAnalysis_disabled label {
	color: #bbb;
}

/* line 991, scss/common.scss */
.spatialAnalysis_checkbox label:before {
	position: absolute;
	left: 0;
	top: 50%;
	display: block;
	content: '';
	transform: translateY(-50%);
	width: 20px;
	height: 20px;
	border-radius: 2px;
	border: 1px solid #cbced2;
	background-color: #fff;
	background-position: center;
	background-repeat: no-repeat;
	transition: .4s;
	background-size: 0px 0px;
}

/* line 995, scss/common.scss */
.spatialAnalysis_checkbox.spatialAnalysis_sm label {
	font-size: 14px;
	letter-spacing: -1px;
}

/* line 996, scss/common.scss */
.spatialAnalysis_checkbox.spatialAnalysis_sm label:before {
	width: 18px;
	height: 18px;
	box-sizing: border-box;
}

.spatialAnalysis_marginTop10 {
	margin-top: 10px;
}

.spatialAnalysis_innerParam {}

/* 스크롤테마 */
/* line 1035, scss/common.scss */
.spatialAnalysis_cScroll {
	overflow-y: auto;
	overflow-x: hidden;
	scrollbar-width: thin;
	scrollbar-color: #303030 transparent;
	scrollbar-arrow-color: #fff;
	scrollbar-3dlight-color: #fff;
	scrollbar-darkshadow-color: #fff;
	scrollbar-face-color: #303030;
	scrollbar-hightlight-color: #fff;
	scrollbar-shadow-color: #fff;
	scrollbar-track-color: #fff;
	scrollbar-base-color: #efefef;
}

/* line 1038, scss/common.scss */
.spatialAnalysis_cScroll .spatialAnalysis_innerScroll {
	width: calc(100% + 8px);
}

/* line 1040, scss/common.scss */
.spatialAnalysis_cScroll::-webkit-scrollbar {
	width: 14px;
	height: 14px;
}

/* line 1041, scss/common.scss */
.spatialAnalysis_cScroll::-webkit-scrollbar-button {
	display: none;
}

/* line 1042, scss/common.scss */
.spatialAnalysis_cScroll::-webkit-scrollbar-track {
	background-color: #e9e9e9;
}

/* line 1043, scss/common.scss */
.spatialAnalysis_cScroll::-webkit-scrollbar-thumb {
	background: #ccc;
	border-radius: 5px;
}

/* line 1044, scss/common.scss */
.spatialAnalysis_cScroll::-webkit-scrollbar-thumb:hover {
	background: #b6b6b6;
}

/* line 1045, scss/common.scss */
.spatialAnalysis_cScroll::-webkit-scrollbar-thumb:active {
	background: #808080;
}

/* line 1046, scss/common.scss */
.spatialAnalysis_cScroll.spatialAnalysis_cScrollX {
	overflow-y: hidden;
	overflow-x: scroll;
}

/* line 1047, scss/common.scss */
.spatialAnalysis_cScroll.spatialAnalysis_cScrollX .spatialAnalysis_innerScroll {
	width: auto;
}

/* line 1049, scss/common.scss */
.spatialAnalysis_cScroll.spatialAnalysis_cScrollXY {
	overflow-y: scroll;
	overflow-x: scroll;
}

/* line 1052, scss/common.scss */
.spatialAnalysis_cScroll.spatialAnalysis_thin::-webkit-scrollbar {
	width: 6px;
	height: 6px;
}

/* line 68, scss/common.scss */
.spatialAnalysis_hidden {
	display: block;
	margin: 0;
	padding: 0;
	width: 0;
	height: 0;
	overflow: hidden;
	font-size: 0;
	line-height: 0;
	visibility: hidden;
}

/*****toc.css 에서 분리****************************************************/

.spatialAnalysis_cont {
	max-height: 100%;
}

.spatialAnalysis_cont .spatialAnalysis_inner {
	width: auto;
	padding: 9px 11px;
}

.spatialAnalysis_cont .spatialAnalysis_inner.spatialAnalysis_type02 {
	padding: 20px;
}

.spatialAnalysis_cont input[type="text"],
.spatialAnalysis_cont input[type="number"],
.spatialAnalysis_cont select,
.spatialAnalysis_cont textarea {
	padding: 0;
	margin: 0;
	box-sizing: border-box;
	padding-left: 15px;
	height: 40px;
	border-radius: 4px;
	border: 1px solid #e9e9e9;
	color: #555;
	font-size: 15px;
	font-family: "Pretendard";
	font-weight: normal;
	resize: none;
}


/* 버튼 */
/* line 885, scss/common.scss */
.spatialAnalysis_btnArea {
	display: flex;
}

/* line 886, scss/common.scss */
.spatialAnalysis_btnArea .spatialAnalysis_btn {
	margin-right: 5px;
}

/* line 887, scss/common.scss */
.spatialAnalysis_btnArea .spatialAnalysis_btn:first-of-type {
	margin-right: 0;
}

/* line 889, scss/common.scss */
.spatialAnalysis_btnArea.spatialAnalysis_flexRight {
	justify-content: flex-end;
}

/* line 890, scss/common.scss */
.spatialAnalysis_btnArea.spatialAnalysis_flexRight .spatialAnalysis_btn {
	margin-left: 5px;
}

/* line 891, scss/common.scss */
.spatialAnalysis_btnArea.spatialAnalysis_flexRight .spatialAnalysis_btn:first-of-type {
	margin-left: 0;
}

/* line 894, scss/common.scss */
.spatialAnalysis_btnArea.spatialAnalysis_flexCenter {
	justify-content: center;
}

/* line 926, scss/common.scss */
.spatialAnalysis_btn.spatialAnalysis_lg {
	padding: 0 30px;
}

/* line 912, scss/common.scss */
.spatialAnalysis_btn.spatialAnalysis_hilight {
	background: #2F5597;
}

.spatialAnalysis_marginTop40 {
	margin-top: 40px;
}

.spatialAnalysis_btn {
	position: relative;
	display: flex;
	align-items: center;
	height: 36px;
	padding: 0 15px;
	border-radius: 4px;
	box-sizing: border-box;
	font-family: "Pretendard";
	font-weight: normal;
	font-size: 14px;
	color: #fff;
	vertical-align: top;
	letter-spacing: 0px;
	transition: .4s;
}

/* line 898, scss/common.scss */
.spatialAnalysis_btn.spatialAnalysis_white {
	background-color: #fff;
	color: #2f5597;
	border: 1px solid #2f5597;
}

.spatialAnalysis_btn.spatialAnalysis_black {
	background-color: #d7dadf;
	color: #333;
}

/* radio */
/* line 1005, scss/common.scss */
.spatialAnalysis_radioGroup.spatialAnalysis_col .spatialAnalysis_radio {
	height: 40px;
	line-height: 40px;
}

/* line 1007, scss/common.scss */
.spatialAnalysis_radioGroup.spatialAnalysis_row {
	display: flex;
	justify-content: space-between;
}

/* line 1012, scss/common.scss */
.spatialAnalysis_radio input[type="radio"] {
	display: none;
}

/* line 1014, scss/common.scss */
.spatialAnalysis_radio input[type="radio"]:checked+label:before {
	background-color: #333;
	background-image: url(./images/widget/common/ico-radio-check.png);
}

/* line 1017, scss/common.scss */
.spatialAnalysis_radio label {
	position: relative;
	display: inline-block;
	height: 23px;
	line-height: 23px;
	text-indent: 30px;
	color: #333;
	font-size: 17px;
	font-family: "Pretendard";
}

/* line 1018, scss/common.scss */
.spatialAnalysis_radio label:before {
	position: absolute;
	left: 0;
	top: 50%;
	display: block;
	content: '';
	transform: translateY(-50%);
	width: 23px;
	height: 23px;
	border-radius: 50%;
	border: 1px solid #e9e9e9;
	background-position: center;
	background-repeat: no-repeat;
	background-image: url(./images/widget/common/ico-radio.png);
}

/* line 1024, scss/common.scss */
.spatialAnalysis_radio.spatialAnalysis_type02 input[type="radio"]:checked+label:before {
	background: #fff;
}

/* line 1025, scss/common.scss */
.spatialAnalysis_radio.spatialAnalysis_type02 input[type="radio"]:checked+label:after {
	position: absolute;
	left: 3px;
	top: 50%;
	transform: translateY(-50%);
	display: block;
	content: '';
	width: 8px;
	height: 8px;
	border-radius: 50px;
	background: #333;
}

/* line 1028, scss/common.scss */
.spatialAnalysis_radio.spatialAnalysis_type02 label {
	text-indent: 24px;
	font-size: 16px;
	font-weight: normal;
	align-items: center;
	display: flex;
}

/* line 1029, scss/common.scss */
.spatialAnalysis_radio.spatialAnalysis_type02 label:before {
	width: 14px;
	height: 14px;
	border-radius: 50px;
	border: 1px solid #c2c2c2;
	box-sizing: border-box;
	background: #fff;
}

.spatialAnalysis_inner.spatialAnalysis_flexRight {
	padding: 10px 20px;
	display: flex;
	justify-content: flex-end;
}

.spatialAnalysis_btn.spatialAnalysis_searchPoint,
.spatialAnalysis_btn.spatialAnalysis_selectPoint,
.spatialAnalysis_btn.spatialAnalysis_selectPolygon,
.spatialAnalysis_btn.spatialAnalysis_drawFeature,
.spatialAnalysis_btn.spatialAnalysis_selectFeature {
	width: 20px;
	height: 40px;
	background-position: center;
	background-repeat: no-repeat;
	border: 1px solid #e9e9e9;
	margin-left: 5px;
}

.spatialAnalysis_btn.spatialAnalysis_searchPoint span,
.spatialAnalysis_btn.spatialAnalysis_selectPoint span,
.spatialAnalysis_btn.spatialAnalysis_selectPolygon span,
.spatialAnalysis_btn.spatialAnalysis_drawFeature span,
.spatialAnalysis_btn.spatialAnalysis_selectFeature span {
	display: none;
}

.spatialAnalysis_btn.spatialAnalysis_searchPoint {
	background: url(./images/widget/common/ico-search.png) no-repeat center;
}

.spatialAnalysis_btn.spatialAnalysis_selectPoint {
	background: url(./images/widget/ico/ico-location.png) no-repeat center;
}

.spatialAnalysis_btn.spatialAnalysis_drawFeature {
	background: url(./images/widget/ico/ico-draw.png) no-repeat center;
}

.spatialAnalysis_keyword.spatialAnalysis_drawFeature {
	width: calc(100% - 20px);
}

.spatialAnalysis_btn.spatialAnalysis_selectFeature {
	background: url(./images/widget/ico/ico-draw.png) no-repeat center;
}

.spatialAnalysis_keyword.spatialAnalysis_selectFeature {
	width: calc(100% - 20px);
}

.spatialAnalysis_btn.spatialAnalysis_selectPolygon {
	background: url(./images/widget/ico/ico-draw.png) no-repeat center;
}

.spatialAnalysis_selectPolygon_keyword {
	width: calc(100% - 20px);
}

.spatialAnalysis_frame {}

.spatialAnalysis_selectPoint {
	display: flex;
}

.spatialAnalysis_selectPoint input[type='text'] {
	width: 100%;
}

.spatialAnalysis_searchResultArea {}

.spatialAnalysis_wkt {}

.spatialAnalysis_keyword {}


.spatialAnalysis_pagination {
	display: flex;
	justify-content: center;
	align-items: center;
	margin-top: 10px;
	font-size: 16px;
	font-family: '맑은 고딕';
	margin: 0;
	font: inherit;
}

.spatialAnalysis_pagination.spatialAnalysis_sm {
	font-size: 14px;
}

.spatialAnalysis_pagination button {
	display: inline-block;
	border: none;
	background-color: transparent;
	cursor: pointer;
}

.spatialAnalysis_pagination button,
.spatialAnalysis_pagination a {
	display: inline-block;
	min-width: 40px;
	height: 40px;
	line-height: 40px;
	text-align: center;
}

.spatialAnalysis_pagination .spatialAnalysis_btnPagi {
	background-repeat: no-repeat;
	background-position: center;
}

.spatialAnalysis_pagination.spatialAnalysis_sm button,
.spatialAnalysis_pagination.spatialAnalysis_sm a {
	min-width: 20px;
	height: 20px;
	line-height: 20px;
}

.spatialAnalysis_pagination .spatialAnalysis_btnPagi.spatialAnalysis_first {
	background-image: url(./images/widget/common/btn-pagi-first.png);
}

.spatialAnalysis_pagination .spatialAnalysis_btnPagi.spatialAnalysis_prev {
	background-image: url(./images/widget/common/btn-pagi-prev.png);
}

.spatialAnalysis_pagination .spatialAnalysis_btnPagi.spatialAnalysis_next {
	background-image: url(./images/widget/common/btn-pagi-next.png);

}

.spatialAnalysis_pagination .spatialAnalysis_btnPagi.spatialAnalysis_last {
	background-image: url(./images/widget/common/btn-pagi-last.png);
}

.spatialAnalysis_pagination a:hover,
.spatialAnalysis_pagination a.spatialAnalysis_active {
	color: #436aeb;
	font-weight: bold;
}

.spatialAnalysis_pagination a {
	color: #333;
}

.spatialAnalysis_horizonGroup {
	width: 100%;
}

.spatialAnalysis_description {
	font-size: 12px;
	color: #afafaf;
	font-weight: normal;
	padding-bottom: 10px;
	padding-left: 25px;
}

.spatialAnalysis_list {
	margin: 10px 0px;
	border: 1px solid #e9e9e9;
	border-radius: 4px;
}

.spatialAnalysis_list ul {
	height: 130px;
	padding: 10px 10px;
}

.spatialAnalysis_list ul,
.spatialAnalysis_list li {
	list-style: none;
}

.spatialAnalysis_list li {
	padding: 5px 0px;
}

.spatialAnalysis_flex {
	display: flex;
}

.spatialAnalysis_selectAdministrativeDistrict_select {
	width: 50%;
}


.spatialAnalysis_fileSelect {
	justify-content: space-between;
	display: flex;
}

.spatialAnalysis_fileSelect input[type="file"] {
	position: absolute;
	width: 1px;
	height: 1px;
	padding: 0;
	margin: -1px;
	overflow: hidden;
	clip: rect(0, 0, 0, 0);
	border: 0;
}

.spatialAnalysis_fileSelect .spatialAnalysis_fileLocal {
	flex: 1;
	margin-right: 5px;
	padding: 0;
	color: #555;
	border: 1px solid #e1e1e1;
	border-radius: 4px;
	background: #fafafa;
	box-sizing: border-box;
	text-indent: 7px;
	text-overflow: ellipsis;
	white-space: nowrap;
	word-break: break-all;
}

.spatialAnalysis_horizonGroup .spatialAnalysis_multiField .spatialAnalysis_checkbox label {
	font-size: 12px;
}

.spatialAnalysis_menuWrap {
	position: relative;
}

.spatialAnalysis_menuWrap .spatialAnalysis_menu {
	position: absolute;
	width: 50px;
	background-color: white;
	border: 1px solid #eee;
	font-size: 14px;
	text-align: center;
	right: 0px;
}

.spatialAnalysis_menuWrap .spatialAnalysis_menu li:hover {
	background-color: #b4bfe4;
	cursor: default;
}

/*직접선택 팝업 start*/
.spatialAnalysis_head {

	cursor: default;
}

.spatialAnalysis_title {}

.spatialAnalysis_close {}

.spatialAnalysis_body {

	cursor: default;
}

.spatialAnalysis_featureList {}

.spatialAnalysis_featureItem {}

.spatialAnalysis_featureItem_no {}

.spatialAnalysis_featureItem_lyrNm {}

.spatialAnalysis_selectFeatureBtn {}

/*직접선택 팝업 end*/


/*data-name 값을 툴팁으로 띄우기*/
.spatialAnalysis_hasTooltip[data-name]:hover {
	position: relative;
}

.spatialAnalysis_hasTooltip[data-name]:hover::after {
	content: attr(data-name);

	position: absolute;
	bottom: 100%;
	left: 0;
	background-color: #555;
	color: #fff;
	font-size: 10px;

	z-index: 1;
}

.spatialAnalysis_submitBtn {}

section span.spatialAnalysis_errorTxt {
	font-size: 13px;
	color: red;
}

.spatialAnalysis_tooltipBtn {
	width: 19px;
	height: 19px;
	margin-left: 10px;
	border-radius: 50px;
	border: 1px solid #7eb7ff;
	box-sizing: border-box;
	color: #7eb7ff;
}

.spatialAnalysis_tooltipBtn span:not(.spatialAnalysis_hidden) {
	font-size: 16px;
	display: block;
	text-indent: -1px;
	line-height: 7px;
}

.spatialAnalysis_tooltip_img {
	width: 850px;
	height: 430px;
	background-repeat: no-repeat;
	background-position: center;
	background-size: contain;
}

.spatialAnalysis_tooltip_txt {
	font-size: 16px;
	margin-top: 10px;
	line-height: 23px;
}

.spatialAnalysis_tooltip_img_extrc {
	background-image: url(./images/widget/analysis/extrc.png);
}

.spatialAnalysis_tooltip_img_ag {
	background-image: url(./images/widget/analysis/ag.png);
}

.spatialAnalysis_tooltip_img_join {
	background-image: url(./images/widget/analysis/join.png);
}

.spatialAnalysis_tooltip_img_nrby {
	background-image: url(./images/widget/analysis/nrby.png);
}

.spatialAnalysis_tooltip_img_range {
	background-image: url(./images/widget/analysis/range.png);
}

.spatialAnalysis_tooltip_img_center {
	background-image: url(./images/widget/analysis/center.png);
}

.spatialAnalysis_tooltip_img_searchLegacy {
	background-image: url(./images/widget/analysis/searchLegacy.png);
}

.spatialAnalysis_tooltip_img_searchNew {
	background-image: url(./images/widget/analysis/searchNew.png);
}

.spatialAnalysis_tooltip_img_searchCenter {
	background-image: url(./images/widget/analysis/searchCenter.png);
}

.spatialAnalysis_tooltip_img_searchSimilar {
	background-image: url(./images/widget/analysis/searchSimilar.png);
}

.spatialAnalysis_tooltip_img_density {
	background-image: url(./images/widget/analysis/density.png);
}

.spatialAnalysis_tooltip_img_hotspot {
	background-image: url(./images/widget/analysis/hotspot.png);
}

.spatialAnalysis_tooltip_img_gatherPoints {
	background-image: url(./images/widget/analysis/gatherPoints.png);
}

.spatialAnalysis_tooltip_img_interpolatePoints {
	background-image: url(./images/widget/analysis/interpolatePoints.png);
}

.spatialAnalysis_tooltip_img_searchOutliers {
	background-image: url(./images/widget/analysis/searchOutliers.png);
}

.spatialAnalysis_tooltip_img_connectDestination {
	background-image: url(./images/widget/analysis/connectDestination.png);
}

.spatialAnalysis_tooltip_img_buffer {
	background-image: url(./images/widget/analysis/buffer.png);
}

.spatialAnalysis_tooltip_img_drivingArea {
	background-image: url(./images/widget/analysis/drivingArea.png);
}

.spatialAnalysis_tooltip_img_findNearestPoint {
	background-image: url(./images/widget/analysis/findNearestPoint.png);
}

.spatialAnalysis_tooltip_img_findPath {
	background-image: url(./images/widget/analysis/findPath.png);
}

.spatialAnalysis_tooltip_img_dsslve {
	background-image: url(./images/widget/analysis/dsslve.png);
}

.spatialAnalysis_tooltip_img_dvsion {
	background-image: url(./images/widget/analysis/dvsion.png);
}

.spatialAnalysis_tooltip_img_merge {
	background-image: url(./images/widget/analysis/merge.png);
}

.spatialAnalysis_tooltip_img_erase {
	background-image: url(./images/widget/analysis/erase.png);
}

.spatialAnalysis_tooltip_img_intsct {
	background-image: url(./images/widget/analysis/intsct.png);
}

.spatialAnalysis_tooltip_img_union {
	background-image: url(./images/widget/analysis/union.png);
}

.spatialAnalysis_tooltip_img_clustering {
	background-image: url(./images/widget/analysis/clustering.png);
}

.spatialAnalysis_tooltip_img_ar {
	background-image: url(./images/widget/analysis/ar.png);
}

.spatialAnalysis_tooltip_img_lt {
	background-image: url(./images/widget/analysis/lt.png);
}

.spatialAnalysis_tooltip_img_file {
	background-image: url(./images/widget/analysis/file.png);
}

.spatialAnalysis_tooltip_img_single {
	background-image: url(./images/widget/analysis/single.png);
}

.spatialAnalysis_serviceTitBox {
	line-height: 36px;
	width: 70px;
	font-size: 14px;
}

.spatialAnalysis_serviceTypeBox {
	width: 160px;
}

.spatialAnalysis_serviceTypeRadioBox {
	width: 50%;
	padding-top: 6px;
	margin-left: 10px;
}

.spatialAnalysis_customDisabled {
	opacity: 0.7;
	color: rgb(170, 170, 170);
	background-color: #f7f7f7;
}

.spatialAnalysis_wd100p {
	width: 100%;
}

.spatialAnalysis_srid {}.geocodingGrid_geocodingGrid {}

.geocodingGrid_geocodingGridTextBox {
  font-family: '맑은 고딕';
  font-weight: normal;
  font-size: 16px;
  color: #555;
  float: left;
  display: flex;
}

.geocodingGrid_geocodingGridHeaderBox {
  margin: 10px 10px 10px 10px;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}

.geocodingGrid_geocodingGridTextBox span {
  margin-right: 10px;
}

.geocodingGrid_geocodingGridTextBox span:nth-child(1) {
  margin-left: 12px;
}

.geocodingGrid_countBox {
  line-height: 36px;
}

.geocodingGrid_geocodingGrid button {
  position: relative;
  align-items: center;
  height: 36px;
  padding: 0 15px;
  border-radius: 4px;
  box-sizing: border-box;
  font-family: "Pretendard";
  font-weight: normal;
  font-size: 14px;
  color: #fff;
  vertical-align: top;
  letter-spacing: 0px;
  transition: .4s;
  background-color: #8faadc;
  line-height: 17px;
}

.geocodingGrid_geocodingGrid button.geocodingGrid_btnRemoveSelect {
  background-color: #d7dadf;
  color: #333;
}

.geocodingGrid_btnPublishPointLayer,
.geocodingGrid_btnPublishPolygonLayer,
.geocodingGrid_btnDownloadCsv,
.geocodingGrid_btnFilterClear,
.geocodingGrid_btnFilterUnMatch,
.geocodingGrid_btnRemoveSelect {
  margin-right: 5px;
  word-break: keep-all;
  background: #333;
}

.geocodingGrid_btnPublishPointLayer,
.geocodingGrid_btnPublishPolygonLayer,
.geocodingGrid_btnDownloadCsv {
  width: 95px;
}

.geocodingGrid_btnFilterClear,
.geocodingGrid_btnFilterUnMatch,
.geocodingGrid_btnRemoveSelect {
  width: 75px;
}

.geocodingGrid_btnArea {
  display: flex;
  justify-content: flex-end;
}

.geocodingGrid_svcTySeCodeSelectBox {
  width: 100px;
  height: 36px;
  margin-right: 5px;
  text-align: justify;
}

.geocodingGrid_publishBox {
  margin-left: 20px;
}

.geocodingGrid_hidden {
  display: none;
  margin: 0;
  padding: 0;
  width: 0;
  height: 0;
  overflow: hidden;
  font-size: 0;
  line-height: 0;
  visibility: hidden;
}

.geocodingGrid_downListBox button {
  background-color: #8faadc;
  border-radius: 4px;
  font-size: 14px;
  font-family: "Pretendard";
  color: #fff;
  line-height: 25px;
  width: 65px;
  border: 1px solid #f3f3f3;
  justify-content: center;
  background-repeat: no-repeat;
  background-position: center;
}

.geocodingGrid_downListBox {
  position: absolute;
  z-index: 100;
  transform: translateX(20%);
  align-items: center;
  opacity: 1;
  visibility: visible;
  margin-top: 5px;
}

button.geocodingGrid_btnCsv {
  display: block;
  margin-right: 10px;
  height: 25px;
  line-height: 25px;
  padding: 0 10px;
  color: #fff;
  border-radius: 4px;
  font-size: 15px;
  font-family: "Pretendard";
  background: #73bc60;
}

button.geocodingGrid_btnExcel {
  display: block;
  margin-right: 10px;
  height: 25px;
  line-height: 25px;
  padding: 0 10px;
  color: #fff;
  border-radius: 4px;
  font-size: 15px;
  font-family: "Pretendard";
  background: #4c863d;
}.homeControl_homeControlContent {

}
.homeControl_moveHomeBtn {

}
.homeControl_moveHomeSpan{
    
}
.clearControl_clearControlContent {

}
.clearControl_clearBtn {

}
.clearControl_clearBtnSpan{
    
}

.printControl_printControlContent {

}
.printControl_printBtn {

}
.printControl_printBtnSpan {

}

.downloadControl_downloadControlContent {

}
.downloadControl_downloadPngBtn {

}
.downloadControl_downloadPDFBtn {

}
.downloadControl_pngBtnSpan{

}
.downloadControl_PDFBtnSpan{
    
}
.downloadControl_toolBtn{

}
.downloadControl_downloadToolSpan{

}
.downloadControl_downloadGrpDiv{
    
}.fullScreenControl_fullSceenControlContent {

}
.fullScreenControl_fullScreenBtn {

}
.fullScreenControl_fullScreenBtnSpan{
    
}
.mousePositionControl_mousePositionControlContent {

}
.scaleControl_scaleControlContent {
    
}
.scaleControl_scaleInput {
    width: 70px;
    text-align : center;
    height: 30px;
    margin-right: 4px;
    font-size:12px;
}
.scaleControl_scaleInfoInput {
    height: 30px;
    width: 50px;
    font-size:12px;
}
.scaleControl_applyBtn {
    width: 30px;
    height: 30px;
    padding: 0;
    justify-content: center;
    background: #c2c2c2;
    position: relative;
    align-items: center;
    border-radius: 4px;
    box-sizing: border-box;
    font-family: "Pretendard";
    font-weight: normal;
    font-size: 14px;
    color: #fff;
    vertical-align: top;
    letter-spacing: 0px;
    transition: .4s;
    margin-left:5px;
}
.scaleControl_displayDiv {
    width :  100px;
    background-color: white ;
    border: #e6e6ea 1px solid;
    text-align : center;
}
.scaleControl_showSpan {
    
}
.scaleControl_applyBtnSpan{
    
}
.scaleControl_scaleBar{
    display: block;
    position: relative;
    width: 100px;
    height: 2px;
    background: #444;
    margin: 13px 5px 13px 0px;;
}
.scaleControl_scaleBarSpanSecond{
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    display: block;
    width: 1px;
    height: 14px;
    background: #444;
}
.scaleControl_scaleBarSpanFirst{
    position: absolute;
    left: 0;
    top: 50%;
    transform: translate(-50%, -50%);
    display: block;
    width: 1px;
    height: 14px;
    background: #444;
}
.scaleControl_scaleBarSpanThird{
    position: absolute;
    left: 100%;
    top: 50%;
    transform: translate(-50%, -50%);
    display: block;
    width: 1px;
    height: 14px;
    background: #444;
}

.moveControl_moveControlContent {

}
.moveControl_preBtn {

}
.moveControl_nextBtn{

}
.moveControl_preBtnSpan{

}
.moveControl_nextBtnSpan{

}
.drawControl_pointBtn {

}
.drawControl_polygonBtn {

}
.drawControl_boxBtn {

}
.drawControl_lineBtn {

}
.drawControl_textBtn {

}
.drawControl_curveBtn {

}
.drawControl_circleBtn {

}
.drawControl_bufferBtn {

}

.drawControl_toolBtn {

}
.drawControl_pointSpan{

}
.drawControl_polygonSpan{

}
.drawControl_boxSpan{

}
.drawControl_lineSpan{

}
.drawControl_textSpan{

}
.drawControl_curveSpan{

}
.drawControl_circleSpan{

}
.drawControl_bufferSpan{

}
.drawControl_drawToolSpan{
    
}
.drawControl_drawControlContent{
    
}
.drawControl_drawGrpDiv{

}
.drawControl_strokeColorSpan{
    width: 26px;
    display: inline-block;
    height: 26px;
    border: 1px solid #e9e9e9;
    vertical-align: middle;
    cursor: pointer;
    position: relative;
}
.drawControl_strokeColorDiv{

}
.drawControl_fillColorDiv{

}
.drawControl_patternFillColorDiv{

}
.drawControl_fillColorSpan, .drawControl_patternFillColorSpan, .drawControl_patternPickerSpan, .drawControl_iconPickerSpan{
    width: 26px;
    display: inline-block;
    height: 26px;
    border: 1px solid #e9e9e9;
    vertical-align: middle;
    cursor: pointer;
    position: relative;
}
.drawControl_patternPickerSpan img {
    width: 100%;
    height: 100%;
}
.drawControl_patternListImg , .drawControl_iconListImg {
    width: 20px;
    height: 20px;
    display: inline-block;
    border: 1px solid #e9e9e9;
    cursor: pointer;
    margin: 3px;
}

.drawControl_patternListImg.drawControl_selected , .drawControl_iconListImg.drawControl_selected {
    border: 2px solid blue;
}
.drawControl_strokeWidthInput{

}
.drawControl_opacityInput{

}
.drawControl_patternOpacityInput{

}
.drawControl_fontSizeInput{

}
.drawControl_strokePatternSelect{

}
.drawControl_strokePatternOption{

}
.drawControl_radiusInput{

}
.drawControl_iconSizeInput{

}
.drawControl_fillTypeSelect{

}
.drawControl_fillTypeOption{

}
.drawControl_pointTypeOption{

}
.drawControl_pointTypeSelect{

}
.drawControl_iconGroupSelect{

}
.drawControl_iconGroupOption{

}
.drawControl_iconPickerImg{

}
.drawControl_iconPickerDisplayImg{
    
}
.drawControl_patternPickerDisplayImg{

}
.drawControl_startDrawBtn{

}
.drawControl_saveDrawBtn{
    
}.measureControl_areaBtn {

}
.measureControl_distanceBtn {

}
.measureControl_spotBtn {

}
.measureControl_circleBtn{

}
.measureControl_circleBtnSpan{
    
}
.measureControl_measureControlContent {

}
.measureControl_toolBtn {

}
.measureControl_areaBtnSpan{

}
.measureControl_distanceBtnSpan{

}
.measureControl_spotBtnSpan{

}
.measureControl_measureToolSpan{

}
.measureControl_measureGrpDiv{
    
}
.zoomControl_zoomControlContent {
    width: 58px;
    display: flex;
    flex-direction: column;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 0 4px 0 rgb(0 0 0 / 30%);
    align-items: center;
}

.zoomControl_inBtn {
    width: 100%;
    height: 37px;
    background-position: center;
    background-repeat: no-repeat;
    background-image: url(./images/widget/toolbar/ico-zoom-in.png);
    display: inline-block;
    border: none;
    background-color: transparent;
    cursor: pointer;
}

.zoomControl_outBtn {
    width: 100%;
    height: 37px;
    background-position: center;
    background-repeat: no-repeat;
    background-image: url(./images/widget/toolbar/ico-zoom-out.png);
    display: inline-block;
    border: none;
    background-color: transparent;
    cursor: pointer;
}

.zoomControl_sliderDiv {
    /* width : 25px; */
    height: 100px;
    background-color: white;
    position: relative;
    padding: 10px 0;
    border-top: 1px solid #e8e8e8;
    border-bottom: 1px solid #e8e8e8;
    /* text-align: center; */
}

.zoomControl_inBtnSpan {
    display: none;
}

.zoomControl_outBtnSpan {
    display: none;
}






.zoomControl_tooltip {
    display: none;
}

.zoomControl_zoomControlContent:hover .zoomControl_tooltip {
    display: unset;
    position: absolute;
    left: -53px;
    top: 37px;
}

.zoomControl_zoomControlContent:hover .zoomControl_tooltip>ul>li {
    position: absolute;
    left: 0;
    color: #fff;
    font-size: 10px;
    font-family: 'Pretendard';
    width: 45px;
    text-align: center;
    height: 17px;
    line-height: 17px;
    background: #474747;
}

.zoomControl_tooltip li.zoomControl_emd {}

.zoomControl_tooltip li.zoomControl_sig {}

.zoomControl_tooltip li.zoomControl_ctprvn {
    left: 10px;
    width: 35px;
}

.zoomControl_tooltip>ul>li:after {
    position: absolute;
    right: -2px;
    top: 50%;
    transform: translateY(-50%) rotate(-45deg);
    display: block;
    content: '';
    border: 3px solid #474747;
    border-left: 3px solid transparent;
    border-top: 3px solid transparent;
}.bookMarkControl_bookmarkBtn {
    width: 200px;
    font-size: 14px;
    font-family: '맑은 고딕';
    color: #333;
    background: #fff;
    text-align: left;
}

.bookMarkControl_hidden {
    display: none;
    margin: 0;
    padding: 0;
    width: 0;
    height: 0;
    overflow: hidden;
    font-size: 0;
    line-height: 0;
    visibility: hidden;
}

.bookMarkControl_bookMarkList {
    position: relative;
    margin-bottom: 10px;
    max-height: 300px;
    overflow-y: auto;
    overflow-x: hidden;
}

.bookMarkControl_bookMarkControlContent {
    position: absolute;
    border-radius: 4px;
    background: #fff;
    box-shadow: 0 3px 20px 0 rgb(0 0 0 / 16%)
}

.bookMarkControl_rowDiv {
    position: relative;
    display: flex;
}

.bookMarkControl_bookMarkInput {
    width: 200px;
    margin-right: 6px;
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    padding-left: 15px;
    height: 40px;
    border-radius: 4px;
    border: 1px solid #e9e9e9;
    color: #555;
    font-size: 16px;
    font-family: '맑은 고딕';
    font-weight: normal;
    resize: none;
    margin-bottom: 5px;
    float: left;
}

.bookMarkControl_addBtn {
    background: #fff url(./images/widget/common/ico-in-add.png) no-repeat center;
    position: relative;
    display: flex;
    align-items: center;
    height: 40px;
    padding: 0 15px;
    border-radius: 4px;
    box-sizing: border-box;
    font-family: '맑은 고딕';
    font-weight: normal;
    font-size: 0;
    color: #fff;
    vertical-align: top;
    letter-spacing: -1px;
    transition: .4s;
    width: 40px;
    border: 1px solid #e9e9e9;
    float: left;
}

.bookMarkControl_addBtn:hover {
    background: #fff url(./images/widget/common/ico-in-add-hover.png) no-repeat center;
    border: 1px solid #333;
}

.bookMarkControl_bookmarkDeleteBtn {
    position: relative;
    display: flex;
    align-items: center;
    height: 40px;
    width: 40px;
    padding: 0 15px;
    border-radius: 4px;
    box-sizing: border-box;
    font-family: '맑은 고딕';
    font-weight: normal;
    font-size: 16px;
    color: #fff;
    vertical-align: top;
    letter-spacing: -1px;
    transition: .4s;
    background: #fff url(./images/widget/common/ico-in-remove.png) no-repeat center;
    border: 1px solid #e9e9e9;
}

.bookMarkControl_bookmarkDeleteBtn:hover {
    background: #fff url(./images/widget/common/ico-in-remove-hover.png) no-repeat center;
    border: 1px solid #333;
}

.bookMarkControl_addBookMarkSpan {
    display: none;
}

.bookMarkControl_bookmarkDeleteSpan {
    display: none;
}

.bookMarkControl_bookmarkNmSpan {
    display: inline-block;
    width: 160px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.bookMarkControl_errorTxt {
    font-size: 12px;
    color: red;
    display: block;
}

.bookMarkControl_inputDiv {}.overViewMapControl_OverViewMapControlContent {

}
.overViewMapControl_openBtn {

}
.overViewMapControl_openBtnSpan{
    
}

.rotationControl_rotationControlContent {
    
}

.rotationControl_rotateBtn {
    position : relative;
    width: 40px;
    height: 40px;
}
.rotationControl_rotateSpan {
    background-size: contain;
    position: absolute;
    width: 40px;
    height: 40px;
    left: 0px;
    top: 0px;  
    background-repeat: no-repeat;
    background-position: center center;
    background-image: url(./images/widget/toolbar/ico-rotate-01.png);
}

.rotationControl_rotateSpan:hover {
    background-image: url(./images/widget/toolbar/ico-rotate-01-active.png);
}


.timeSliderControl_timeSliderControlContent {
    border: 1px solid #eee;
    border-radius: 4px;
    padding: 10px;
    width: 340px;
    height: 420px;
    padding: 20px;
    background-color: white;
}

.timeSliderControl_columnListDiv {
    flex-direction: column;
    display: flex;
}

.timeSliderControl_columnDiv {
    display: flex;
    margin-bottom: 15px;
}

.timeSliderControl_timeBoxDiv {
    margin-bottom: 15px;
}

.timeSliderControl_timeSliderBar {
    margin-right: 10px;
    -webkit-appearance: none;
    outline: none;
    width: 170px;
    height: 30px;
    border: 1px solid #e9e9e9;
    border-radius: 4px;
    background: transparent;
}

.timeSliderControl_timeSliderBar::-webkit-slider-thumb {
    -webkit-appearance: none;
}

.timeSliderControl_timeSliderBar::-ms-track {
    width: 100%;
    cursor: pointer;
    background: transparent;
    border-color: transparent;
    color: transparent;
}

.timeSliderControl_timeSliderBar::-webkit-slider-thumb {
    -webkit-appearance: none;
    background: #555;
    cursor: pointer;
    height: 36px;
    width: 12px;
    transform: translateY(6px);
    margin-top: -14px;
    box-shadow: 1px 1px 1px #000000, 0px 0px 1px #0d0d0d;
    border-radius: 3px;
}

.timeSliderControl_playBtn {
    border: 3px solid #555;
    margin-right: 10px;
    background-image: url(./images/widget/ico/ico-player-play.png);
    position: relative;
    width: 33px;
    height: 33px;
    border-radius: 50%;
    box-sizing: border-box;
    background-position: center;
    background-repeat: no-repeat;

}

.timeSliderControl_playBtnSpan,
.timeSliderControl_pauseBtnSpan,
.timeSliderControl_stopBtnSpan {
    display: none
}

.timeSliderControl_pauseBtn {
    border: 3px solid #555;
    margin-right: 10px;
    background-image: url(./images/widget/ico/ico-player-pause.png);
    position: relative;
    width: 33px;
    height: 33px;
    border-radius: 50%;
    box-sizing: border-box;
    background-position: center;
    background-repeat: no-repeat;
}

.timeSliderControl_pauseBtnSpan {
    display: none;
}

.timeSliderControl_stopBtn {
    border: 3px solid #436aeb;
    background-image: url(./images/widget/ico/ico-player-stop.png);
    position: relative;
    width: 33px;
    height: 33px;
    border-radius: 50%;
    box-sizing: border-box;
    background-position: center;
    background-repeat: no-repeat;
}

.timeSliderControl_layerSelectSpan,
.timeSliderControl_columnSelectSpan,
.timeSliderControl_dateTypeSelectSpan,
.timeSliderControl_outputDateSpan,
.timeSliderControl_intervalSpan,
.timeSliderControl_intervalInput,
.timeSliderControl_accumSpan {
    width: 120px;
    line-height: 28px;
    margin-right: 5px;
    text-align: center;
    letter-spacing: -1.2px;
    color: #333;
    font-size: 16px;
    font-family: '맑은 고딕';
    font-weight: bold;
    text-align: left;
}

.timeSliderControl_layerSelectBox,
.timeSliderControl_columnSelectBox,
.timeSliderControl_dateTypeSelectBox {
    width: 210px;
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    padding-left: 15px;
    height: 40px;
    border-radius: 4px;
    border: 1px solid #e9e9e9;
    color: #555;
    font-size: 16px;
    font-family: '맑은 고딕';
    font-weight: normal;
    resize: none;
}

.timeSliderControl_outputDateSelectBox {
    width: 140px;
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    padding-left: 15px;
    height: 40px;
    border-radius: 4px;
    border: 1px solid #e9e9e9;
    color: #555;
    font-size: 16px;
    font-family: '맑은 고딕';
    font-weight: normal;
    resize: none;

}

.timeSliderControl_layerSelectOption {}

.timeSliderControl_columnSelectOption {}

.timeSliderControl_dateTypeSelectOption {}

.timeSliderControl_outputDateInput {
    width: 60px;
}

.timeSliderControl_outputDateOption {}

.timeSliderControl_accumCheckbox {
    position: relative;
    display: inline-block;
    min-width: 22px;
    min-height: 22px;
    line-height: 22px;
    text-indent: 30px;
    color: #555;
    font-size: 16px;
    font-family: '맑은 고딕';
}

.timeSliderControl_btnDiv {
    display: flex;
    justify-content: flex-end;
}

.timeSliderControl_createBtn {
    background: #436aeb;
    position: relative;
    display: flex;
    align-items: center;
    height: 40px;
    padding: 0 15px;
    border-radius: 4px;
    box-sizing: border-box;
    font-family: '맑은 고딕';
    font-weight: normal;
    font-size: 16px;
    color: #fff;
    vertical-align: top;
    letter-spacing: -1px;
    transition: .4s;
}

.timeSliderControl_createBtnSpan {}

.timeSliderControl_resetBtn {
    background: #436aeb;
    position: relative;
    display: flex;
    align-items: center;
    height: 40px;
    padding: 0 15px;
    border-radius: 4px;
    box-sizing: border-box;
    font-family: '맑은 고딕';
    font-weight: normal;
    font-size: 16px;
    color: #fff;
    vertical-align: top;
    letter-spacing: -1px;
    transition: .4s;
}

.timeSliderControl_resetBtnSpan {}

.timeSliderControl_currentTimeSpan {}

.timeSliderControl_hidden {
    display: none;
    margin: 0;
    padding: 0;
    width: 0;
    height: 0;
    overflow: hidden;
    font-size: 0;
    line-height: 0;
    visibility: hidden;
}.pnuGetter_PNUGetterContent {

}
.pnuGetter_searchBtn {

}
.pnuGetter_searchSpan{

}

.swiperControl_swiperControlContent {
    border: 1px solid #eee;
    border-radius: 4px;
    padding: 10px;
    width: 405px;
    height: auto;
    padding: 20px;
    background-color: white;
    max-height: 590px !important;
}

.swiperControl_leftSideSpan,
.swiperControl_rightSideSpan {
    display: block;
    font-size: 16px;
    font-family: '맑은 고딕';
    font-weight: bold;
    margin-bottom: 10px;
    padding-top: 0px !important;
    color: #555;
}

.swiperControl_hidden {
    display: none;
    margin: 0;
    padding: 0;
    width: 0;
    height: 0;
    overflow: hidden;
    font-size: 0;
    line-height: 0;
    visibility: hidden;
}

.swiperControl_barDiv {
    padding-bottom: 20px;
    margin-bottom: 20px;
    border-bottom: 1px solid #e9e9e9;
}

.swiperControl_headerDiv {
    display: flex;

}

.swiperControl_leftSideDiv {
    display: flex;
    flex-direction: column;
}

.swiperControl_rightSideDiv {
    display: flex;
    flex-direction: column;
    margin-left: 10px;
}

.swiperControl_colDiv {
    display: flex;
    align-items: center;
}

.swiperControl_footerDiv {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
}

.swiperControl_basemapSelect {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    padding-left: 15px;
    height: 40px;
    border-radius: 4px;
    border: 1px solid #e9e9e9;
    color: #555;
    font-size: 15px;
    font-family: "Pretendard";
    font-weight: normal;
    resize: none;
    width: 200px;
}

.swiperControl_basemapOption {}

.swiperControl_layerSelect {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    padding-left: 15px;
    height: 40px;
    border-radius: 4px;
    border: 1px solid #e9e9e9;
    color: #555;
    font-size: 15px;
    font-family: "Pretendard";
    font-weight: normal;
    resize: none;
}

.swiperControl_layerOption {}

.swiperControl_createBtn,
.swiperControl_deleteBtn{
    width: 67px !important;
    height: 40px !important;
    color: #fff;
    font-size: 14px;
    font-family: 'Pretendard';
    transition: .4s;
    border-radius: 4px;
    margin-right: 5px;
}

.swiperControl_createBtn {
    background-color: #2f5597;
}

 .swiperControl_deleteBtn{
    background-color: #555;
}
.swiperControl_deleteBtnSpan,
.swiperControl_createBtnSpan {
    padding: 0 !important;
}

.swiperControl_swiperBar {
    margin-right: 10px;
    flex: 1;
    -webkit-appearance: none;
    outline: none;
    height: 2px;
    border-radius: 4px;
    background: #e1e1e1;
    font-family: inherit;
    font-size: inherit;
    width: calc(100% - 150px);
}

.swiperControl_swiperBar::-ms-track {
    width: 100%;
    cursor: pointer;
    background: transparent;
    border-color: transparent;
    color: transparent;
}

.swiperControl_swiperBar::-webkit-slider-thumb {
    -webkit-appearance: none;
    background: #555;
    cursor: pointer;
    height: 15px;
    width: 15px;
    box-shadow: 1px 1px 1px #000000, 0px 0px 1px #0d0d0d;
    border-radius: 50%;
}

.swiperControl_strictModeBtn {}

.swiperControl_strictModeSpan {}

.swiperControl_swiperBarLabel {
    position: relative;
    font-size: 18px;
    color: #2e2e2e;
    text-indent: 10px;
    font-family: "Pretendard Bold";
    margin-right: 20px;
}

.swiperControl_swiperBtn{}
.swiperControl_swiperDiv{
    position: absolute;
    display: block;
    z-index: 1;
}
/*.swiperDiv .left {right: 69px;}*/
/*.swiperDiv .right {left: 69px;}*/
.swiperControl_left {right: 69px;}
.swiperControl_right {left: 69px;}
/*모바일 테마용 css start*/
.basemap_mobile{

}
button.basemap_mobile{
    width: 44px;
    height: 44px;
}

button.basemap_tool.basemap_mobile.basemap_on{
    background-image: url(./images/widget/mobile/widget-basemap.png);
    border: 1.5px solid #32a1ff !important;
}
button.basemap_tool.basemap_mobile{
    border-radius: 5px;
    display: block;
    background-repeat: no-repeat;
    background-position: center;
    background-size: 24px;
    background-color: #f6f6f8;
    background-image: url(./images/widget/mobile/widget-basemap-gray.png);
}
button.basemap_tool.basemap_mobile span{
    position: absolute;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0;
}
.basemap_mobile.basemap_toolbox{
    overflow-x: hidden;
    z-index:1;
    width: 100%;
    height: 100%;
    display: block;
}
.basemap_mobile.basemap_toolbox ul.basemap_basemapArea{
    padding-top: 10px;
    grid-gap: 10px;
    flex-wrap: wrap;
    padding: 20px;
}
.basemap_mobile.basemap_toolbox ul.basemap_basemapArea li.basemap_active{
    border-color: #32a1ff;
}
.basemap_mobile.basemap_toolbox ul.basemap_basemapArea li{
    width: 30%;
    aspect-ratio: 1/1;
    border: 1.5px solid #fff;
    border-radius: 5px;
    text-align: center;
    padding: 10px 0;
    box-shadow: 2px 3px 3px rgba(0 0 0 / .16);
    background-color: #fff;
    display: list-item;
    list-style: none;
}
.basemap_mobile.basemap_toolbox ul.basemap_basemapArea li button.basemap_layer{
    width: 100%;
    height: 100%;
    border-radius: 5px;
}
.basemap_mobile.basemap_toolbox ul.basemap_basemapArea li.basemap_active button.basemap_layer.basemap_active img{
    border-color: #32a1ff;
}
.basemap_mobile.basemap_toolbox ul.basemap_basemapArea li button.basemap_layer img{
    border: 1.5px solid #fff;
    width: 95%;
    aspect-ratio: 4 / 3;
    border-radius: 5px;
}
.basemap_mobile.basemap_toolbox ul.basemap_basemapArea li button.basemap_layer span{
    margin-top: 12px;
    font-size: 16px;
    font-weight: bold;
    color: #333333;
    display: inline-block;
    white-space: nowrap;
}
.basemap_mobile.basemap_toolbox ul.basemap_basemapArea li.basemap_active button.basemap_layer.basemap_active span{
    color: #32a1ff;
}

/*모바일 테마용 css end*/


.basemap_menu{

}
.basemap_gallary{

}
.basemap_basemapArea button img{
    border-width : 1px;
    border-radius: 10%;
}
/*
.mobile button.active img {
    display: block;
    position: relative;
    z-index: 0;
}

.mobile button.active img::after {
    content: "V";
    background: rgba(45, 88, 35, 0.7);
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 10;
}

.mobile button.active img.active > * {
    z-index: 10;
}*/



button#basemap_widget:not(.basemap_mobile),
ul.basemap_basemapGroup button {
    border: none;
}

.basemap_hidden {
    display: block;
    margin: 0;
    padding: 0;
    width: 0;
    height: 0;
    overflow: hidden;
    font-size: 0;
    line-height: 0;
    visibility: hidden;
}


ul.basemap_basemapGroup,
ul.basemap_basemapGroup li,
ul.basemap_basemapArea,
ul.basemap_basemapArea li {
    list-style: none;
    list-style-position: initial;
    list-style-image: initial;
    list-style-type: none;
    align-items: baseline;
    display: grid;
}

ul.basemap_basemapArea li {
    display: grid;
}

.basemap_toolbox {
    position: absolute;
    top: 0;
}

.basemap_toolbox.basemap_left {
    right: 100%;
    margin-right: 10px;
}

.basemap_toolbox.basemap_right {
    left: 100%;
    margin-left: 10px;
}

.basemap_toolbox.basemap_top {
    bottom: 100%;
    margin-right: 10px;
}

.basemap_toolbox.basemap_bottom {
    top: 100%;
    margin-left: 10px;
}

ul.basemap_basemapGroup,
ul.basemap_basemapArea {
    display: flex;
}

ul.basemap_basemapArea {
    top: inherit;
    margin-top: 5px;
}

.basemap_basemapGroup button {
    width: max-content;
}

button.basemap_widget span {
    /* display : none; */

}

.basemap_tool {}

.basemap_group {}

.basemap_layer {}

.basemap_active {}

.basemap_basemapArea {}

.basemap_basemapGroup {}

.basemap_toolbox:not(.basemap_mobile) button.basemap_layer>img {
    width: 40px;
    height: 30px;
}

.basemap_hoverSelect {
    padding-top: 0;
}

.basemap_hoverSelect.basemap_selected {
    border: 2px solid blue;
}

.basemap_hoverDiv {
    background-color: white;
}

.basemap_noneMapBox {
    width: 100%;
    height: 100%;
    display: -webkit-inline-box;
}.swiper_swiperControlContent {
    border: 1px solid #eee;
    border-radius: 4px;
    padding: 10px;
    width: 405px;
    height: auto;
    padding: 20px;
    background-color: white;
    max-height: 590px !important;
}

.swiper_leftSideSpan,
.swiper_rightSideSpan {
    display: block;
    font-size: 16px;
    font-family: '맑은 고딕';
    font-weight: bold;
    margin-bottom: 10px;
    padding-top: 0px !important;
    color: #555;
}

.swiper_hidden {
    display: none;
    margin: 0;
    padding: 0;
    width: 0;
    height: 0;
    overflow: hidden;
    font-size: 0;
    line-height: 0;
    visibility: hidden;
}

.swiper_barDiv {
    padding-bottom: 20px;
    margin-bottom: 20px;
    border-bottom: 1px solid #e9e9e9;
}

.swiper_headerDiv {
    display: flex;

}

.swiper_leftSideDiv {
    display: flex;
    flex-direction: column;
}

.swiper_rightSideDiv {
    display: flex;
    flex-direction: column;
    margin-left: 10px;
}

.swiper_colDiv {
    display: flex;
    align-items: center;
}

.swiper_footerDiv {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
}

.swiper_basemapSelect {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    padding-left: 15px;
    height: 40px;
    border-radius: 4px;
    border: 1px solid #e9e9e9;
    color: #555;
    font-size: 15px;
    font-family: "Pretendard";
    font-weight: normal;
    resize: none;
    width: 200px;
}

.swiper_basemapOption {}

.swiper_layerSelect {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    padding-left: 15px;
    height: 40px;
    border-radius: 4px;
    border: 1px solid #e9e9e9;
    color: #555;
    font-size: 15px;
    font-family: "Pretendard";
    font-weight: normal;
    resize: none;
}

.swiper_layerOption {}

.swiper_createBtn,
.swiper_deleteBtn{
    width: 67px !important;
    height: 40px !important;
    color: #fff;
    font-size: 14px;
    font-family: 'Pretendard';
    transition: .4s;
    border-radius: 4px;
    margin-right: 5px;
}

.swiper_createBtn {
    background-color: #2f5597;
}

 .swiper_deleteBtn{
    background-color: #555;
}
.swiper_deleteBtnSpan,
.swiper_createBtnSpan {
    padding: 0 !important;
}

.swiper_swiperBar {
    margin-right: 10px;
    flex: 1;
    -webkit-appearance: none;
    outline: none;
    height: 2px;
    border-radius: 4px;
    background: #e1e1e1;
    font-family: inherit;
    font-size: inherit;
    width: calc(100% - 150px);
}

.swiper_swiperBar::-ms-track {
    width: 100%;
    cursor: pointer;
    background: transparent;
    border-color: transparent;
    color: transparent;
}

.swiper_swiperBar::-webkit-slider-thumb {
    -webkit-appearance: none;
    background: #555;
    cursor: pointer;
    height: 15px;
    width: 15px;
    box-shadow: 1px 1px 1px #000000, 0px 0px 1px #0d0d0d;
    border-radius: 50%;
}

.swiper_strictModeBtn {}

.swiper_strictModeSpan {}

.swiper_swiperBarLabel {
    position: relative;
    font-size: 18px;
    color: #2e2e2e;
    text-indent: 10px;
    font-family: "Pretendard Bold";
    margin-right: 20px;
}

.swiper_swiperBtn{}
.swiper_swiperDiv{
    position: absolute;
    display: block;
    z-index: 1;
}
/*.swiperDiv .left {right: 69px;}*/
/*.swiperDiv .right {left: 69px;}*/
.swiper_left {}
.swiper_right {}


.swiper_valueBar{
    position: fixed;
    background-color: black;
    width : 1px;
}

.swiper_valueBar .swiper_valueBarButton{
    position: relative;
    top : calc(50% - 21px);
    height: 42px;
    left: -10px;
    width : 20px;
    border-radius: 5px;
    border: 1px solid #000;
    box-sizing: border-box;
    background-color:#FFF;
    cursor: pointer;
    transform: unset !important;
    /* 드래그 방지 */
    -ms-user-select: none;
    -moz-user-select: -moz-none;
    -khtml-user-select: none;
    user-select: none;
}

.swiper_swipeArea {
    position: fixed;
    pointer-events: none;
    overflow: hidden;

}
.swiper_swipeArea .swiper_swiperBtn {
    

}

.swiper_miniTOCFrame {
    position: absolute;
    top : 10px;
    left : 10px;
    width: fit-content;
    pointer-events: all;
}


.swiper_tocArea.swiper_hide {
    display: none;
}

.swiper_tocOnOffText {}

.swiper_miniTOCFrame button.swiper_on {}

ul.swiper_widget {
    position: absolute;
    top: 10px;
    right: 10px;
    pointer-events: all;
}
ul.swiper_widget li{
    position: relative;
}
.swiper_basemap{
    
}.featureAttributeForm_popup.featureAttributeForm_poi {
  overflow: visible;
  position: inherit;
}
.featureAttributeForm_popup {
  z-index: 200;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 3px 20px 0 rgba(0, 0, 0, 0.1);
  width: 370px;
}
/* .popup.poi .head {
  height: 29px;
} */

.featureAttributeForm_popup .featureAttributeForm_head {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #666;
  background: #5779FF;
}
.featureAttributeForm_popup.featureAttributeForm_poi .featureAttributeForm_popupComboBox {
  position: relative;
  border-radius: 4px;
  font-size: 16px;
  color: #555;
  font-family: '맑은 고딕';
  font-weight: normal;
  box-sizing: border-box;
  margin-left: 0;
  background: transparent url(./images/widget/ico/ico-form-combo.png) no-repeat right center;
  max-width: 260px;
  min-width: 187px;
  border: none;
  height: 24px;
  cursor: pointer;
}

.featureAttributeForm_popup.featureAttributeForm_poi .featureAttributeForm_head .featureAttributeForm_popupTitPop {
  display: block;
  height: 40px;
  line-height: 40px;
  cursor: pointer;
}
.featureAttributeForm_popup .featureAttributeForm_head .featureAttributeForm_popupTitPop {
  margin-left: 20px;
  line-height: 50px;
  color: #fff;
  font-size: 19px;
  font-family: '맑은 고딕';
  font-weight: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.featureAttributeForm_popupComboBox > span {
  margin-left: 0px;
  padding-left: 0;
  display: flex;
  align-items: center;
  padding: 0 27px 0 10px;
  height: 30px;
}
/* .popupComboBox.type02 .comboList {
  padding: 5px 0;
  min-width: 100px;
  border: 1px solid #436aeb;
  border-radius: 0;
  background: #fff;
  box-sizing: border-box;
  width: 220px;
} */

.featureAttributeForm_popupComboBox .featureAttributeForm_comboList {
  position: absolute;
  border: 1px solid #e9e9e9;
  border-radius: 0;
  box-sizing: border-box;
  width: 220px;
  padding: 5px 0;
  background-color: #fff;
  display: block;
  z-index: 10;
}

ol, ul, li {
  list-style: none;
  margin: 0;
  padding: 0;
}
.featureAttributeForm_popupComboBox .featureAttributeForm_comboList li span {
  display: block;
  cursor: pointer;
  padding: 0 10px;
  word-break: keep-all;
}

.featureAttributeForm_popup .featureAttributeForm_head .featureAttributeForm_btnGroup {
  display: flex;
  align-items: center;
  margin-right: 13px;
}

.featureAttributeForm_popup.featureAttributeForm_poi .featureAttributeForm_head .featureAttributeForm_btnGroup .featureAttributeForm_popupBtnClosed {
  width: 20px;
  height: 20px;
  background-image: url(./images/widget/common/ico-pop-close.png);
  background: url(./images/widget/popup/ico-pop-close.png) no-repeat center;
  cursor: pointer;
}
.featureAttributeForm_popup.featureAttributeForm_poi .featureAttributeForm_cont .featureAttributeForm_titSec .featureAttributeForm_btnGroup .featureAttributeForm_popupBtnOnPrev:hover {
  background-image: url(./images/widget/popup/ico-opt-prev-hover.png);
  cursor: pointer;
}
.featureAttributeForm_popup.featureAttributeForm_poi .featureAttributeForm_cont .featureAttributeForm_titSec .featureAttributeForm_btnGroup .featureAttributeForm_popupBtnOnNext:hover {
  cursor: pointer;
  background-image: url(./images/widget/popup/ico-opt-next-hover.png);
}

.featureAttributeForm_hidden {
  display: block;
  margin: 0;
  padding: 0;
  width: 0;
  height: 0;
  overflow: hidden;
  font-size: 0;
  line-height: 0;
  visibility: hidden;
}
.featureAttributeForm_popup .featureAttributeForm_cont {
  background: #fff;
}
.featureAttributeForm_popup.featureAttributeForm_poi .featureAttributeForm_cont .featureAttributeForm_inner {
  padding: 20px;
}
/* .popup .cont .inner {
  padding: 30px;
  max-height: 590px !important;
} */
.featureAttributeForm_section:last-of-type {
  margin-bottom: 0;
  padding-bottom: 0;
}
.featureAttributeForm_section {
  border-bottom: 0;
}
.featureAttributeForm_section {
  padding-bottom: 20px;
  margin-bottom: 20px;
  border-bottom: 1px solid #e9e9e9;
}

.featureAttributeForm_popup.featureAttributeForm_poi .featureAttributeForm_cont .featureAttributeForm_titSec {
  align-items: center;
  justify-content: space-between;
}

.featureAttributeForm_section .featureAttributeForm_titSec {
  display: flex;
  margin-bottom: 10px;
}

.featureAttributeForm_section .featureAttributeForm_titSec strong {
  position: relative;
    font-size: 18px;
    color: #2e2e2e;
    text-indent: 10px;
    font-family: "Pretendard Bold";
    letter-spacing: -1px;
}
.featureAttributeForm_popup.featureAttributeForm_poi .featureAttributeForm_cont .featureAttributeForm_titSec .featureAttributeForm_btnGroup {
  display: block;
}
.featureAttributeForm_popup.featureAttributeForm_poi .featureAttributeForm_cont .featureAttributeForm_titSec .featureAttributeForm_btnGroup .featureAttributeForm_popupBtnOnPrev {
  background-image: url(./images/widget/popup/ico-opt-prev.png);
  margin-right: 4px;
}
.featureAttributeForm_popup.featureAttributeForm_poi .featureAttributeForm_cont .featureAttributeForm_titSec .featureAttributeForm_btnGroup .featureAttributeForm_popupBtnOnNext {
  background-image: url(./images/widget/popup/ico-opt-next.png);
}
.featureAttributeForm_popup.featureAttributeForm_poi .featureAttributeForm_cont .featureAttributeForm_titSec .featureAttributeForm_btnGroup .featureAttributeForm_popupBtnOnPrev, .featureAttributeForm_popup.featureAttributeForm_poi .featureAttributeForm_cont .featureAttributeForm_titSec .featureAttributeForm_btnGroup .featureAttributeForm_popupBtnOnNext {
  width: 27px;
  height: 27px;
  background-repeat: no-repeat;
  background-position: center;
}
.featureAttributeForm_popup button{
  border: 0;
  background: #fff;
}

.featureAttributeForm_table {
  width: 100%;
  border-top: 1px solid #e9e9e9;
  border-bottom: 1px solid #e9e9e9;
  height: 209px;
  overflow-y: auto;
  overflow-x: hidden;
  text-align: center;
  
}

.featureAttributeForm_table table {
  width: 100%;
}
table {
  border-collapse: collapse;
  border-spacing: 0;
}

.featureAttributeForm_table::-webkit-scrollbar-thumb{
  background: #ccc;
  border-radius: 5px;
}

.featureAttributeForm_table::-webkit-scrollbar-track{
  background-color: #e9e9e9;
}

.featureAttributeForm_table::-webkit-scrollbar-button{
  display: none;
}
.featureAttributeForm_table::-webkit-scrollbar{
  width: 14px;
  height: 14px;
}

caption, hr {
  display: none;
}
.featureAttributeForm_popup.featureAttributeForm_poi .featureAttributeForm_cont .featureAttributeForm_table table tbody th, .featureAttributeForm_popup.featureAttributeForm_poi .featureAttributeForm_cont .featureAttributeForm_table table tbody td {
  height: 25px;
}
.featureAttributeForm_table table tbody tr th, .featureAttributeForm_table table tbody tr td {
  font-size: 16px;
  font-family: 'Pretendard Bold';
}

.featureAttributeForm_table table th {
  background: #f9f9f9;
  font-weight: bold;
}
.featureAttributeForm_table table th, .featureAttributeForm_table table td {
  height: 30px;
  color: #555;
  border-bottom: 1px solid #e9e9e9;
  padding: 8px;
}


.featureAttributeForm_tableGroup span, .featureAttributeForm_btnGroup > span{
  display: flex;
    width: 95px;
    line-height: 28px;
    margin-right: 5px;
    text-align: center;
    letter-spacing: -1.2px;
    color: #5d7df5;
    margin-top: 10px;
    font-size: 16px;
    font-family: "Pretendard Bold";
}
.featureAttributeForm_table table tbody tr td input{
  background: #fff;
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  padding-left: 15px;
  height: 25px;
  border-radius: 4px;
  border: 1px solid #e9e9e9;
  color: #555;
  font-size: 15px;
  font-family: "Pretendard";
  font-weight: normal;
  resize: none;
}

.featureAttributeForm_table table tbody tr th{
  width : 100px;
  word-break: break-all;
}

.featureAttributeForm_table table tbody tr td select{
  width: 184px;
  background: #fff;
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  padding-left: 15px;
  height: 25px;
  border-radius: 4px;
  border: 1px solid #e9e9e9;
  color: #555;
  font-size: 15px;
  font-family: "Pretendard";
  font-weight: normal;
  resize: none;
}

.featureAttributeForm_popupComboBox .featureAttributeForm_comboList li {
  background: #fff;
  font-size: 19px;
  color: #333;
}
.featureAttributeForm_popupComboBox .featureAttributeForm_comboList li:hover {
  background: #e4edff;
}

.featureAttributeForm_btnChangeLayer{


}
.featureAttributeForm_tableGroup{

}

.featureAttributeForm_popup .featureAttributeForm_btnMove, .featureAttributeForm_popup .featureAttributeForm_btnEdit, .featureAttributeForm_popup .featureAttributeForm_btnTransform{
  width: 100px;
  background-color: #e1e1e1;
  word-break: keep-all;
  margin-right: 10px;
  height: 45px;
  border-radius: 5px;
  color: #555;
  font-family: "Pretendard";
  cursor: pointer;
}


.featureAttributeForm_popup .featureAttributeForm_btnMove.featureAttributeForm_active, .featureAttributeForm_popup .featureAttributeForm_btnEdit.featureAttributeForm_active, .featureAttributeForm_popup .featureAttributeForm_btnTransform.featureAttributeForm_active{
  background-color: #5d7df5;
  color:#fff;
}

.featureAttributeForm_popup .featureAttributeForm_btnDelete, .featureAttributeForm_popup .featureAttributeForm_btnSave{
  background-color: #ff5700;
  align-items: center;
  height: 40px;
  border-radius: 4px;
  box-sizing: border-box;
  font-family: "Pretendard";
  font-weight: normal;
  font-size: 14px;
  color: #fff;
  vertical-align: top;
  letter-spacing: 0px;
  transition: .4s;
  width: 80px;
  cursor: pointer;
  text-align: center;
  margin-left: 10px;
}

.featureAttributeForm_btnGroup2{
  margin-top:10px;
  margin-bottom:10px;
  justify-content: flex-end;
  display: flex;
}

.featureAttributeForm_section .featureAttributeForm_btnGroup{
  margin-top: 10px;
  margin-bottom: 10px;
}button#divideMap_widget , ul.divideMap_divideMapArea button{
    border : none;
}
ul.divideMap_divideMapArea, ul.divideMap_divideMapArea li{
    list-style : none;
    list-style-position: initial;
    list-style-image: initial;
    list-style-type: none;
}

.divideMap_toolbox{
    position: absolute;
    top: 0;
}
.divideMap_toolbox.divideMap_left{
    right: 100%;
    margin-right: 10px;
}
.divideMap_toolbox.divideMap_right{
    left: 100%;
    margin-left: 10px;
}

ul.divideMap_divideMapArea{
    display: flex;
}
.divideMap_divideMapArea button{
    width : max-content;
}


button.divideMap_widget span{
    /* display : none; */

}
.divideMap_tool{

}
.divideMap_divType{

}
.divideMap_dualMap{

}
.divideMap_threepleMap{
    
}
.divideMap_quadMap{
    
}
.divideMap_miniTOCFrame{

}
.divideMap_scale{
    
}
.divideMap_toc{
    /* position: absolute;
    top: 30px;
    left: 5px;
    width: 200px; */
    /* background-color: white; */
}
.divideMap_tocArea.divideMap_hide{
    display : none;
}
.divideMap_miniTOCFrame button.divideMap_on{
    
}
.divideMap_tocOnOffText{
    
}
.divideMap_active{
    
}

.divideMap_basemap{
    /* position: absolute;
    top: 5px;
    left: 5px; */
}.cctvControl_cctvControlContent {}

.cctvControl_cctvBtn {}

.cctvControl_cctvGrpDiv {}

.cctvControl_cctvGrpDiv {}

.cctvControl_itsBtn {}

.cctvControl_itsSpan {}

.cctvControl_exBtn {}

.cctvControl_exSpan {}

.cctvControl_videoDiv {}

.cctvControl_clearBtn {}

.cctvControl_clearBtnSpan {}

.cctvControl_headerSpan {
    margin-left: 8px;
    line-height: 30px;
    color: #fff;
    font-size: 16px;
    font-family: "Pretendard";
    font-weight: normal;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.cctvControl_headerCloseBtn {}

.cctvControl_headerCloseSpan {}

.cctvControl_cctvHeader {
    height: 30px;
    background: #5779FF;
    display: flex;
    justify-content: space-between;
}

.cctvControl_cctvBody {
    width: 360px;
    height: auto;
    position: relative;
    border: 1px solid #7b98bc;
}

.cctvControl_cctvBody video {
    width: 100%;
    height: auto;
    max-width: 100%;
    max-height: 100%;
    display: block;
    margin: 0;
    transform: translateX(-50%);
    position: relative;
    left: 50%;
}.currentViewControl_CurrentViewControlContent {

}
.currentViewControl_moveCurrentViewSpan {

}
.currentViewControl_moveCurrentView{
    
}
.roadView_roadViewContent{

}
.roadView_roadViewModal{
    width:'100%';
    height: 500px;
}
.roadView_roadViewArea_on{

}
.roadView_roadViewArea_off{

}
.roadView_roadViewBtn_on{

}
.roadView_roadViewBtn_off{

}
.roadView_roadViewSpan{

}
.roadView_roadViewWarnSpan{

}

/* 동동이 */
.roadView_map_wrap {overflow:hidden;height:330px}
/* 지도위에 로드뷰의 위치와 각도를 표시하기 위한 map walker 아이콘의 스타일 */
.roadView_MapWalker {position:absolute;margin:-26px 0 0 -51px}
.roadView_MapWalker .roadView_figure {position:absolute;width:25px;left:38px;top:-2px;
    height:39px;background:url(./images/widget/roadView/dongdong.png) -298px -114px no-repeat;}
.roadView_MapWalker .roadView_angleBack {width:102px;height:52px;background: url(./images/widget/roadView/dongdong.png) -834px -2px no-repeat;}
.roadView_MapWalker.roadView_m0 .roadView_figure {background-position: -298px -114px;}
.roadView_MapWalker.roadView_m1 .roadView_figure {background-position: -335px -114px;}
.roadView_MapWalker.roadView_m2 .roadView_figure {background-position: -372px -114px;}
.roadView_MapWalker.roadView_m3 .roadView_figure {background-position: -409px -114px;}
.roadView_MapWalker.roadView_m4 .roadView_figure {background-position: -446px -114px;}
.roadView_MapWalker.roadView_m5 .roadView_figure {background-position: -483px -114px;}
.roadView_MapWalker.roadView_m6 .roadView_figure {background-position: -520px -114px;}
.roadView_MapWalker.roadView_m7 .roadView_figure {background-position: -557px -114px;}
.roadView_MapWalker.roadView_m8 .roadView_figure {background-position: -2px -114px;}
.roadView_MapWalker.roadView_m9 .roadView_figure {background-position: -39px -114px;}
.roadView_MapWalker.roadView_m10 .roadView_figure {background-position: -76px -114px;}
.roadView_MapWalker.roadView_m11 .roadView_figure {background-position: -113px -114px;}
.roadView_MapWalker.roadView_m12 .roadView_figure {background-position: -150px -114px;}
.roadView_MapWalker.roadView_m13 .roadView_figure {background-position: -187px -114px;}
.roadView_MapWalker.roadView_m14 .roadView_figure {background-position: -224px -114px;}
.roadView_MapWalker.roadView_m15 .roadView_figure {background-position: -261px -114px;}
.roadView_MapWalker.roadView_m0 .roadView_angleBack {background-position: -834px -2px;}
.roadView_MapWalker.roadView_m1 .roadView_angleBack {background-position: -938px -2px;}
.roadView_MapWalker.roadView_m2 .roadView_angleBack {background-position: -1042px -2px;}
.roadView_MapWalker.roadView_m3 .roadView_angleBack {background-position: -1146px -2px;}
.roadView_MapWalker.roadView_m4 .roadView_angleBack {background-position: -1250px -2px;}
.roadView_MapWalker.roadView_m5 .roadView_angleBack {background-position: -1354px -2px;}
.roadView_MapWalker.roadView_m6 .roadView_angleBack {background-position: -1458px -2px;}
.roadView_MapWalker.roadView_m7 .roadView_angleBack {background-position: -1562px -2px;}
.roadView_MapWalker.roadView_m8 .roadView_angleBack {background-position: -2px -2px;}
.roadView_MapWalker.roadView_m9 .roadView_angleBack {background-position: -106px -2px;}
.roadView_MapWalker.roadView_m10 .roadView_angleBack {background-position: -210px -2px;}
.roadView_MapWalker.roadView_m11 .roadView_angleBack {background-position: -314px -2px;}
.roadView_MapWalker.roadView_m12 .roadView_angleBack {background-position: -418px -2px;}
.roadView_MapWalker.roadView_m13 .roadView_angleBack {background-position: -522px -2px;}
.roadView_MapWalker.roadView_m14 .roadView_angleBack {background-position: -626px -2px;}
.roadView_MapWalker.roadView_m15 .roadView_angleBack {background-position: -730px -2px;}
.limsControl_limsBtn{

}
.limsControl_limsBtnSpan{

}
.limsControl_limsControlContent{

}
.limsControl_limsLayerListDiv{

}
.limsControl_limsBaseBtnSpan{

}
.limsControl_limsAirBtnSpan{

}
.limsControl_limsBaseBtn.limsControl_on , .limsControl_limsAirBtn.limsControl_on{
}
.limsControl_limsAirBtn{
}
.limsControl_limsBaseBtn{
}
.limsControl_onoffBtn{

}
.limsControl_onoffBtnSpan{

}

.limsControl_limsBaseBtn{
    background-image: url(./images/widget/limsLsmd/widget-lsmd-normal-gray.png);
}
.limsControl_limsBaseBtn.limsControl_on{
    background-image: url(./images/widget/limsLsmd/widget-lsmd-normal.png);
}
.limsControl_onoffBtn{
    background-image: url(./images/widget/limsLsmd/LX맵_gray.png);
}
.limsControl_onoffBtn:hover{
}
.limsControl_limsControlContent:hover .limsControl_onoffBtn{
    background-image: url(./images/widget/limsLsmd/LX맵_on.png);
}
.limsControl_limsControlContent:hover .limsControl_onoffBtn > span{
    color: #ffffff;
}
.limsControl_limsBaseBtn:hover, .limsControl_limsBaseBtn:hover {
    background-image: url(./images/widget/limsLsmd/widget-lsmd-normal.png);
}
.limsControl_limsLayerListDiv {
    position: absolute;
    top: 0;
    right: 0;
    padding: 0 10px;
    display: flex;
    flex-direction: row;
    border-radius: 3px;
    transition: .2s;
    background: #fff;
    box-shadow: 0.6px 0.8px 4px 0 rgb(0 0 0 / 35%);
    visibility: visible;
    opacity: 1;
    right: 68px;
}
.lsmdControl_lsmdBtn{

}
.lsmdControl_lsmdBtnSpan{

}
.lsmdControl_lsmdControlContent{

}
.lsmdControl_lsmdLayerListDiv{

}
.lsmdControl_lsmdBaseBtnSpan{

}
.lsmdControl_lsmdAirBtnSpan{

}
.lsmdControl_lsmdBaseBtn.lsmdControl_on , .lsmdControl_lsmdAirBtn.lsmdControl_on{
}
.lsmdControl_lsmdBaseBtn{
}
.lsmdControl_onoffBtn{

}
.lsmdControl_onoffBtnSpan{

}
.lsmdControl_lsmdBaseBtn{
    background-image: url(./images/widget/limsLsmd/widget-lsmd-normal-gray.png);
}
.lsmdControl_lsmdBaseBtn.lsmdControl_on{
    background-image: url(./images/widget/limsLsmd/widget-lsmd-normal.png);
}
.lsmdControl_onoffBtn{
    background-image: url(./images/widget/limsLsmd/widget-lsmd-gray.png);
}
.lsmdControl_onoffBtn:hover{
}
.lsmdControl_lsmdControlContent:hover .lsmdControl_onoffBtn{
    background-image: url(./images/widget/limsLsmd/widget-lsmd-white.png);
}
.lsmdControl_lsmdControlContent:hover .lsmdControl_onoffBtn > span{
    color: #ffffff;
}
.lsmdControl_lsmdBaseBtn:hover, .lsmdControl_limsBaseBtn:hover {
    background-image: url(./images/widget/limsLsmd/widget-lsmd-normal.png);
}
.lsmdControl_lsmdLayerListDiv {
    position: absolute;
    top: 0;
    right: 0;
    padding: 0 10px;
    display: flex;
    flex-direction: row;
    border-radius: 3px;
    transition: .2s;
    background: #fff;
    box-shadow: 0.6px 0.8px 4px 0 rgb(0 0 0 / 35%);
    visibility: visible;
    opacity: 1;
    right: 68px;
}
/* 일필지조회 위젯 상단 공통 부분 (도로명, 지번 출력 부분) */
.oneParcel_topArea {
    background-color: #EBF5FC;
    padding: 30px 20px 80px 20px !important;
}

.oneParcel_addrArea {
    position: static;
    border-radius: 0;
    box-shadow: none;
    background: transparent;
}

.oneParcel_addrArea .oneParcel_addrItem {
    margin-bottom: 10px;
    font-size: 14px;
    font-family: "pretendard";
    font-weight: normal;
}

.oneParcel_addrItem {
    font-size: 14px;
    font-family: 'pretendard';
    font-weight: normal;
    margin: 0;
    padding: 0;
    border: 0;
    font: inherit;
    display: block;
}

.oneParcel_addrItem:last-of-type {
    margin-bottom: 0;
}

.oneParcel_roadAddr,
.oneParcel_jibunAddr {
    align-items: center;
}

.oneParcel_roadAddr {
    margin-bottom: 10px;
}

.oneParcel_addrArea .oneParcel_addrItem .oneParcel_roadAddr,
.oneParcel_addrArea .oneParcel_addrItem .oneParcel_jibunAddr {
    display: flex;
    align-items: normal;
}

.oneParcel_addrItem .oneParcel_roadAddr,
.oneParcel_addrItem .oneParcel_jibunAddr {
    display: flex;
    margin: 0;
    padding: 0;
    border: 0;
    font: inherit;
}

.oneParcel_addrTag {
    margin-right: 5px;
}

.oneParcel_addrItem .oneParcel_roadTag,
.oneParcel_addrItem .oneParcel_jibunTag {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 18px;
}

.oneParcel_addrArea .oneParcel_addrItem .oneParcel_roadTag,
.oneParcel_addrArea .oneParcel_addrItem .oneParcel_jibunTag {
    display: inline-block;
    width: 55px;
    border-radius: 3px;
    text-align: center;
    color: #fff;
    font-size: 12px;
}

.oneParcel_roadTag {
    background: #283ebf;
}

.oneParcel_addrItem .oneParcel_roadTag,
.oneParcel_addrItem .oneParcel_jibunTag {
    display: inline-block;
    width: 55px;
    border-radius: 3px;
    text-align: center;
    color: #fff;
    font-size: 12px;
    height: fit-content;
    margin-right: 5px;
}

.oneParcel_addrArea .oneParcel_addrItem .oneParcel_addrDesc.oneParcel_bold {
    font-size: 20px;
    font-family: 'pretendard Bold';
    color: #333;
}

.oneParcel_addrItem .oneParcel_addrDesc {
    flex: 1;
    word-break: keep-all;
}

.oneParcel_addrItem .oneParcel_jibunTag {
    background: #00b8a3;
}

/* 하단 컨텐츠 부분 */
.oneParcel_oneParcelInner {
    position: relative;
    padding: 0 !important;
    margin-top: -46px;
    overflow: hidden;
    max-height: initial !important;
}

.oneParcel_oneParcelInner.oneParcel_type02 {
    margin-top: 0;
    padding-top: 20px !important;
}

.oneParcel_oneParcelInner.oneParcel_type03 {
    margin-top: 0px;
}

.oneParcel_oneParcelInner.oneParcel_type02::before {
    content: '';
    position: absolute;
    top: 0;
    width: 100%;
    height: 66px;
    background-color: #EBF5FC;
}

.oneParcel_tabContWrap {
    position: relative;
}

.oneParcel_tabNav {
    padding: 0 20px;
}

.oneParcel_tabList {
    border: 0;
    background-color: transparent !important;
    border: 1px solid #ECECEC;
    background: #fff;
    border-radius: 50px;
    display: flex;
}

.oneParcel_tabList li {
    position: relative;
    height: 46px;
    line-height: 46px;
    color: #333;
    font-size: 16px;
    cursor: pointer;
    font-family: 'Pretendard';
    border-radius: 3px 3px 0 0;
    background-color: #CDD4DB;
    flex: 1;
    text-align: center;
}

.oneParcel_tabList li + li {
    margin-left: 5px;
}

.oneParcel_tabList li.oneParcel_active:before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translate(-50%);
    width: 100%;
    height: 4px;
    border-radius: 3px 3px 0 0;
    background-color: #283EBF;
}

.oneParcel_tabList li.oneParcel_active {
    color: #283EBF;
    font-family: 'Pretendard Bold';
    background-color: #fff;
}

.oneParcel_tabCont {
    max-height: 640px;
    padding-bottom: 20px;
    display: none;
}

.oneParcel_tabCont.oneParcel_active {
    display: block;
}

.oneParcel_oneParcelSection {
    padding-left: 20px;
    padding-right: 20px;
}

.oneParcel_oneParcelSection:first-of-type {
    padding-top: 20px;
}

.oneParcel_oneParcelSection+.oneParcel_oneParcelSection {
    margin-top: 20px;
}

.oneParcel_titSec {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
}

.oneParcel_titSec strong {
    position: relative;
    font-size: 18px;
    font-family: "Pretendard Bold";
    font-weight: normal;
    color: #2e2e2e;
    text-indent: 10px;
    letter-spacing: -1px;
}

.oneParcel_titSec strong:before {
    top: 3px;
    background: #7EB7FF;
    position: absolute;
    left: 0;
    width: 3px;
    height: 14px;
    display: block;
    content: '';
}

.oneParcel_titSec .oneParcel_btn {
    margin-left: auto;
}

.oneParcel_titSec.oneParcel_sb {
    justify-content: space-between;
}

.oneParcel_titSecType02 strong {
    font-size: 16px;
    color: #333;
}

.oneParcel_titSecType02 strong:before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background-color: #c5c5c5;
}

.oneParcel_oneParcelTable {
    border-top: 1px solid #E9E9E9;
    border-bottom: 1px solid #E9E9E9;
}

.oneParcel_oneParcelTable table thead {
    position: sticky;
    top: 0;
    z-index: 9999;
}

.oneParcel_oneParcelTable table thead tr:first-child th {
    position: static;
    border-bottom: 1px solid #E9E9E9;
}

.oneParcel_oneParcelTable table th,
.oneParcel_oneParcelTable table td {
    height: 40px;
    padding: 0 16px;
    font-family: 'Pretendard';
}

.oneParcel_oneParcelTable+.oneParcel_btnArea {
    margin-top: 10px;
}

.oneParcel_oneParcelTable.oneParcel_type02 {
    max-height: 500px;
}

.oneParcel_noDataArea {
    text-align: center;
}

.oneParcel_titSec+.oneParcel_inputWrap {
    margin-top: 10px;
    background-color: #EBF5FC;
    display: flex;
    padding: 15px;
}

.oneParcel_inputWrap .oneParcel_inputGroup {
    flex: 1;
    display: flex;
    align-items: center;
    column-gap: 5px;
}

.oneParcel_inputGroup select {
    flex: 1;
}

.oneParcel_inputWrap .oneParcel_inputGroup+.oneParcel_btnArea {
    margin-left: 10px;
    display: flex;
}

.oneParcel_btnArea {
    display: flex;
}

.oneParcel_btn {
    position: relative;
    display: flex;
    align-items: center;
    height: 36px;
    padding: 0 15px;
    border-radius: 4px;
    box-sizing: border-box;
    font-family: "Pretendard";
    font-weight: normal;
    font-size: 14px;
    color: #fff;
    vertical-align: top;
    letter-spacing: 0px;
    transition: .4s;
}

.oneParcel_btnArea .oneParcel_btn {
    min-width: 90px;
    width: auto;
    height: 40px;
    text-align: center;
    font-size: 16px;
    border-radius: 3px;
    justify-content: center;
}

.oneParcel_btnArea .oneParcel_btn span {
    display: flex;
    align-items: center;
}

.oneParcel_btnArea .oneParcel_gray {
    color: #fff;
    background-color: #555;
}

.oneParcel_btnArea .oneParcel_btn:first-of-type {
    margin-right: 0;
}

.oneParcel_btnArea .oneParcel_sky {
    color: #fff;
    background-color: #32A1FF;
}

.oneParcel_bgBox {
    height: 70px;
    width: 100%;
    /* .softBlue */
    background-color: #EBF5FC;
}

.oneParcel_center {
    display: flex;
    align-items: center;
    justify-content: center;
}

.oneParcel_txtCenter {
    text-align: center;
}

.oneParcel_btnArea .oneParcel_btn.oneParcel_openInNew span:after {
    display: inline-block;
    content: '';
    width: 12px;
    height: 12px;
    background: url(./images/widget/ico/ico-open-in-new.png) no-repeat center;
    margin-left: 5px;
}

.oneParcel_hidden {
    display: none;
}

.oneParcel_btn.oneParcel_btnSearch {
    /* .section .btn */
    height: 40px;
    width: 40px;
    padding: 0;
    border: 1px solid #e9e9e9;
    background: #fff url(./images/widget/ico/ico-search.png) no-repeat center;
}

.oneParcel_marginAuto {
    margin: 0 auto;
}

.oneParcel_flexCenter {
    justify-content: center;
}

.oneParcel_btnArea .oneParcel_btn.oneParcel_hvType01:hover {
    border-color: #333;
    color: #333;
}

.oneParcel_btnArea .oneParcel_btn.oneParcel_white {
    color: #333;
    background-color: #fff;
}

.oneParcel_btnArea .oneParcel_btn.oneParcel_wide2 {
    min-width: 120px;
}

.oneParcel_btnArea .oneParcel_btn.oneParcel_default {
    padding: 0;
    border: 1px solid #e9e9e9;
}

.oneParcel_btn.oneParcel_white:hover {
    background: #555;
    color: #fff;
    border: 1px solid transparent;
}

.oneParcel_btnArea .oneParcel_btn.oneParcel_more span:before {
    content: '';
    width: 14px;
    height: 14px;
    background: url(./images/widget/ico/ico-more.png) no-repeat center;
    margin-right: 5px;
}

td .oneParcel_value {
    display: block;
}

.oneParcel_btnArea .oneParcel_btn.oneParcel_transparent {
    color: #333;
    background-color: transparent;
    padding: 0;
}

.oneParcel_btnArea .oneParcel_btn.oneParcel_home span:before {
    content: '';
    width: 16px;
    height: 16px;
    background: url(./images/widget/ico/ico-home.png) no-repeat center;
    margin-right: 5px;
}

.oneParcel_alert .oneParcel_cont {
    padding: 10px 30px 20px 30px;
    background: #fff;
}

.oneParcel_alert .oneParcel_cont .oneParcel_type.oneParcel_warning {
    background-image: url(./images/widget/common/ico-type-alert.png);
}

.oneParcel_alert .oneParcel_cont .oneParcel_type {
    width: 47px;
    height: 47px;
    margin: 0 auto;
    background-repeat: no-repeat;
    background-position: center;
}

.oneParcel_alert .oneParcel_cont .oneParcel_titCont {
    display: block;
    margin-top: 10px;
    text-align: center;
    font-size: 22px;
    font-family: "Pretendard Bold";
}

.oneParcel_alert .oneParcel_cont .oneParcel_btnArea {
    display: flex;
    justify-content: center;
    margin-top: 29px;
}

.oneParcel_alert .oneParcel_cont .oneParcel_btnArea .oneParcel_btn:last-of-type {
    margin-right: 0;
}

.oneParcel_alert .oneParcel_cont .oneParcel_btnArea .oneParcel_btn {
    height: 45px;
    padding: 0 42px;
    font-size: 19px;
    font-weight: normal;
    margin-right: 6px;
}

.oneParcel_btn.oneParcel_blue {
    background-color: #32A1FF;
    border-radius: 0;
}

/* 버튼 영역 */
.oneParcel_oneParcelContent {

}
.oneParcel_toolBtn {
    min-height: 67px;
    /*
    font-family: inherit;
    font-size: inherit;
    */
}

.oneParcel_toolBtnOn {
    min-height: 67px;
    /*
    font-family: inherit;
    font-size: inherit;
    */
    color: white;
    background-color: #2f5597;
}
.oneParcel_oneParcelSpan {
    width: 58px;
}
.oneParcel_btnArea .oneParcel_btn.oneParcel_more span:hover:before {
    background: url(./images/widget/ico/ico-more-hover.png) no-repeat 50%;
}
.oneParcel_btnArea .oneParcel_btn.oneParcel_more span:before {
    background: url(./images/widget/ico/ico-more.png) no-repeat 50%;
}
button {
    display: inline-block;
    border: none;
    background-color: transparent;
    cursor: pointer;
}

.odf_toc_wrapper {
    width: 100%;
    height: 100%;
    display: table;
}

.odf_toc_target {
    float: left;
    width: calc(100% - 400px);
}

.odf_toc {
    width: 400px;
    height: 100%;
    float: left;
    background-color: white;
}

.odf_toc_content.selected {
    color: green;
    font-weight: bold;
}

.tooltip_bold {
    color: #7eb7ff;
    font-weight: bold;
}

/*
.odf_add_pattern_form{
    position: absolute;
    width: 290px;
    height: 100px;
    background-color: #FFFFFF;
    text-align: left;
}
.odf_add_pattern_form.input[type=text]{
    width: 100px;
}
.odf_add_pattern_form.input[type=file]{
    width: 150px;
}
.odf_color_picker_close_btn{
    position: absolute;
    z-index: 11;
    right: 0px;
}
.odf_icon_list{
    min-height: 60px;
    border: 1.5px solid #b7b4b4;
    border-radius: 10px;
    margin: 5px;
}*/
/*
.odf_icon{
    width: 20px;
    height: 20px;
    border: 1px solid #e0e0e0;
    display: inline-block;
    border-radius: 20%;
    cursor : pointer;
    margin: 5px;
}
.odf_icon.selected{
    border: 2px solid rgb(221, 54, 54);
}*/
/*
.odf_add_image_form{
    position: absolute;
    width: 290px;
    height: 100px;
    background-color: #e0e0e0;
    text-align: left;
}*/

/*
.oui_preview_item{
    display:inline-block;
}
.oui_manual_editing_pop{
    position: absolute;
    background: gray;
    margin-left: 5px;
    z-index: 10;
}
.oui_preview_item_name{
    margin-left: 5px;
}*/
/*로컬 toc에서만 필요한 css*/
/* .rstcustom__rowToolbar {
    margin-right: 81px !important;
}  */

.modal {
    background-color: #e8e8e8;
    display: none;
    position: absolute;
    z-index: 99999999;
}

.modal.open {
    display: block;
}

.lyrSearch-nav {
    float: left;
}

.lyrSearch-nav-cntnts {
    cursor: pointer;
}

.lyrSearch-nav-cntnts.selected {
    font-weight: bold;
}

.lyrType-nav-cntnts {
    cursor: pointer;
}

.lyrType-nav-cntnts.selected {
    font-weight: bold;
}

.lyrList-box-wrap {
    position: relative;
    margin-top: 20px;
    padding-top: 40px;
    border: 1px solid #ccc;
    float: left;
    width: 70%
}

.lyrList-box-wrap .lyrList-box {
    max-height: 420px;
    overflow: auto;
    overflow-x: hidden;
}

.lyrList-box-wrap .lyrList-box table {
    width: 100%;
    table-layout: fixed;
    border-spacing: 0;
    border-collapse: collapse;
}

.lyrList-box-wrap .lyrList-box table thead tr {
    position: absolute;
    top: 0;
    width: 70%;
}

.lyrList-box-wrap .lyrList-box table thead tr th {
    font-weight: normal;
    width: 120px;
    height: 40px;
    background: darksalmon;
    color: #fff;
}

/* 
.lyrList-box-wrap .lyrList-box table thead tr th:nth-child(1) {
    width: 60px;
} */

.lyrList-box-wrap .lyrList-box table td {
    text-align: center;
    height: 40px;
    border-top: 1px solid #ccc;
}

.lyrList-box-wrap .lyrList-box table tr {
    display: inline-table;
    width: 100%;
    table-layout: fixed;
}

.lyrList-box-wrap .lyrList-box table tbody tr {
    display: table-row;
}

.loadingPopup-box {
    width: 400px;
    height: 200px;
    background-color: green;
}

.modal-box-backgCtrl {
    width: 100%;
    height: 100%;
    background-color: slategray;
    z-index: 99999998;
    position: absolute;
    top: 0;
    opacity: 0.6;
}

.redFont {
    color: red;
}

.modal-header {
    background-color: cornflowerblue;
}


/* .oui_range_value{
    position: relative;
    display: table-cell;
} */

.oui_range_value {
    position: absolute;
    display: block;
}

.hidden {
    display: none;
    margin: 0;
    padding: 0;
    width: 0;
    height: 0;
    overflow: hidden;
    font-size: 0;
    line-height: 0;
    visibility: hidden;
}

/* 
div.oui_inner_icon{
	position: absolute;
    left: 3px;
    top: 8px;
    width: 25px !important;
    height: 25px !important;
    display: flex; 
} */
.oui_content_icon span.oui_inner_half_icon {
    width: 12.5px !important;
    border-radius: 0 !important;
    border-width: 1px ! important;

    position: absolute;
    left: 0px;
    top: 0px;
}

.oui_content_icon span.oui_inner_half_icon.left {
    border-top-left-radius: 12.5px !important;
    border-bottom-left-radius: 12.5px !important;

    left: 0px !important;
}

.oui_content_icon span.oui_inner_half_icon.right {
    border-top-right-radius: 12.5px !important;
    border-bottom-right-radius: 12.5px !important;

    left: 15px !important;
}


.oui_content_icon span.oui_inner_quad_icon {
    border-radius: 0 !important;
    width: 12.5px !important;
    height: 12.5px !important;
    border-width: 1px ! important;

    position: absolute;
    left: 0px;
    top: 0px;
}

.oui_content_icon span.oui_inner_quad_icon.left_top {
    border-top-left-radius: 100% !important;
    left: 0px !important;
    top: 0px !important;
}

.oui_content_icon span.oui_inner_quad_icon.right_top {
    border-top-right-radius: 100% !important;
    left: 12.5px !important;
    top: 0px !important;
}

.oui_content_icon span.oui_inner_quad_icon.left_bottom {
    border-bottom-left-radius: 100% !important;
    left: 0px !important;
    top: 12.5px !important;
}

.oui_content_icon span.oui_inner_quad_icon.right_bottom {
    border-bottom-right-radius: 100% !important;
    left: 12.5px !important;
    top: 12.5px !important;
}

/* .section {
    margin: 10px;
} */

/* 오버뷰맵 css*/
.ol-overviewmap:not(.odf-inner) .ol-overviewmap-map {
    position: relative;
}









.file-drop-target {
    height: 150px;
    margin-bottom: 30px;
    border: 1px solid #436aeb;
    border-radius: 4px;
}

.file-drop-dragging-over-target {
    background-color: #f6f9fe;
    ;
}

/* 
.popup-btn-onPrev{
    background-image: url(../css/images/ico/ico-popup-prev-1.png);
    display: inline-block;
    border: none;
    background-color: transparent;
    cursor: pointer;
    width: 15px;
    height: 15px;
    background-repeat: no-repeat;
    background-size: contain;
}

.popup-btn-onNext{
    background-image: url(../css/images/ico/ico-popup-next-1.png);
    display: inline-block;
    border: none;
    background-color: transparent;
    cursor: pointer;
    width: 15px;
    height: 15px;
    background-repeat: no-repeat;
    background-size: contain;
}

.popup-btn-closed{
    background-image: url(../css/images/popup/ico-pop-close.png);
    display: inline-block;
    border: none;
    background-color: transparent;
    cursor: pointer;
    width: 15px;
    height: 15px;
    background-repeat: no-repeat;
    background-size: contain;
} */

.popupCloseBox {
    float: right;
    margin-right: 5px;
}

.popupButtonBox {
    float: left;
    margin-left: 7px;
    width: 40px;
}

.popupTitleBox {
    width: 210px;
    float: left;
    color: #fff;
    font-size: 16px;
    font-family: '맑은 고딕';
    overflow: hidden;
    white-space: normal;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
}

/* .popup-btn-closed span, .popup-btn-onNext span, .popup-btn-onPrev span{
    display: none;
} */

.popup-box {
    width: 300px;
}

.popup-box-header {
    height: 24px;
    background-color: #436aeb;
    padding-top: 2px;
    border-radius: 5px 5px 0px 0px;
    border: #e9e9e9 1px solid;
    box-shadow: 2px 2px 7px #555;
}

.popup-box-content {
    border: #e9e9e9 1px solid;
    background: #f6f9fe;
    height: 250px;
    border-radius: 0px 0px 5px 5px;
    box-shadow: 2px 2px 7px #555;
    padding: 5px;
}

.popupSjIcon {
    background-image: url(./images/widget/ico/ico-layer-dot.png);
    width: 16px;
    height: 16px;
    display: flex;
    float: left;
    background-repeat: no-repeat;
    background-size: contain;
    margin-top: 2px;
}


.mapContainer {
    position: relative;
    display: flex;
    flex: 1;
    flex-direction: column;
}

.mapArea {
    position: relative;
    width: 100%;
    height: 100%;
    flex: 1 1;
    overflow: hidden;
}



.oui-popup .popupDiv.poi {
    overflow: visible;
    position: inherit;
    width: auto;
    max-width: 500px;
    min-width: 400px;
}

.oui-popup .popupDiv {
    z-index: 200;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 3px 20px 0 rgba(0, 0, 0, 0.1);
}

/* .oui-popup .popupDiv.poi .head {
    height: 29px;
} */

.oui-popup .popupDiv .head.blue {
    background: #5779FF;
}

.oui-popup .popupDiv .head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 38px;
}

.oui-popup .popupDiv.poi .head .popup-comboBox {
    max-width: 300px;
    height: 40px;
    position: relative;
    border-radius: 4px;
    font-size: 16px;
    color: #555;
    font-family: '맑은 고딕';
    font-weight: normal;
    box-sizing: border-box;
}

.oui-popup .popup-comboBox.type02 {
    margin-left: 0;
    background: transparent url(./images/widget/popup/ico-combobox-type02.png) no-repeat right center;
    border: none;
}

.oui-popup .popupDiv.poi .head .popup-titPop {
    display: block;
    height: 40px;
    line-height: 40px;
    cursor: pointer;
}

.oui-popup .popupDiv .head .popup-titPop {
    margin-left: 20px;
    line-height: 50px;
    color: #fff;
    font-size: 19px;
    font-family: '맑은 고딕';
    font-weight: normal;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.oui-popup .popup-comboBox.type02>span {
    margin-left: 0px;
    padding-left: 0;
}

.oui-popup .popup-comboBox>span {
    display: flex;
    align-items: center;
    padding: 0 27px 0 10px;
    height: 30px;
}

.oui-popup .comboList.active {
    display: block;
    z-index: 10;
}

.oui-popup .comboList {
    left: 0;
    top: 40px;
    position: absolute;
    display: none;
    border: 1px solid #e9e9e9;
    border-radius: 0;
    box-sizing: border-box;
    width: 220px;
    padding: 5px 0;
    background-color: #fff;
}

.oui-popup ol,
.oui-popup ul,
.oui-popup li {
    list-style: none;
    margin: 0;
    padding: 0;
}

.oui-popup .comboList li span {
    display: block;
    cursor: pointer;
    padding: 0 10px;
    word-break: keep-all;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.oui-popup .popupDiv .head .btnGroup {
    display: flex;
    align-items: center;
    margin-right: 0px;
}

.oui-popup .popupDiv.poi .head .btnGroup .popup-btn-minmax {
    cursor: pointer;
    margin-right: 10px;
    width: 45px;
    height: 38px;
    background: url(./images/widget/common/ico-option-hide.png) no-repeat center;
}

.oui-popup .popupDiv.poi .head .btnGroup .popup-btn-minmax.min {
    transform: rotate(180deg);
}


.oui-popup .popupDiv.poi .head .btnGroup .popup-btn-closed {
    width: 45px;
    height: 38px;
    background: url(./images/widget/popup/ico-pop-close.png) no-repeat center;
    cursor: pointer;
    background-size: 27px;
}

.oui-popup .popupDiv.poi .cont .titSec .btnGroup .popup-btn-onPrev:hover {
    background-image: url(./images/widget/popup/ico-opt-prev-hover.png);
}

.oui-popup .popupDiv.poi .cont .titSec .btnGroup .popup-btn-onNext:hover {
    background-image: url(./images/widget/popup/ico-opt-next-hover.png);
}

.oui-popup .hidden {
    display: block;
    margin: 0;
    padding: 0;
    width: 0;
    height: 0;
    overflow: hidden;
    font-size: 0;
    line-height: 0;
    visibility: hidden;
}

.oui-popup .popupDiv .cont {
    background: #fff;
    display: block;
}

.oui-popup .popupDiv.poi .cont .innerCont {
    padding: 20px;
}

/* .oui-popup .popupDiv .cont .inner {
    padding: 30px;
    max-height: 590px !important;
} */
.oui-popup .section:last-of-type {
    margin-bottom: 0;
    padding-bottom: 0;
}

.oui-popup .section.type02 {
    border-bottom: 0;
}

.oui-popup .section {
    border-bottom: 1px solid #e9e9e9;
}

.oui-popup .popupDiv.poi .cont .titSec {
    align-items: center;
    justify-content: space-between;
}

.oui-popup .section.type02 .titSec {
    margin-bottom: 20px;
}

.oui-popup .section .titSec {
    display: flex;
    margin-bottom: 10px;
}

.oui-popup .section.type02 .titSec strong {
    position: relative;
    font-size: 18px;
    color: #2e2e2e;
    text-indent: 10px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 380px;
}

.oui-popup .section .titSec strong {
    color: #333;
    font-family: '맑은 고딕';
    font-weight: bold;
    font-size: 16px;
    letter-spacing: -1px;
}

.oui-popup .popupDiv.poi .cont .titSec .btnGroup {
    display: block;
}

.oui-popup .popupDiv.poi .cont .titSec .btnGroup .popup-btn-onPrev {
    background-image: url(./images/widget/popup/ico-opt-prev.png);
    margin-right: 4px;
}

.oui-popup .popupDiv.poi .cont .titSec .btnGroup .popup-btn-onNext {
    background-image: url(./images/widget/popup/ico-opt-next.png);
}

.oui-popup .popupDiv.poi .cont .titSec .btnGroup .popup-btn-onPrev,
.popupDiv.poi .cont .titSec .btnGroup .popup-btn-onNext {
    width: 27px;
    height: 27px;
    background-repeat: no-repeat;
    background-position: center;
}

.oui-popup .popupDiv button {
    border: 0;
    background: #fff;
}

.oui-popup .table {
    width: 100%;
    border-top: 1px solid #e9e9e9;
    border-bottom: 1px solid #e9e9e9;
    max-height: 280px;
    overflow-y: auto;
    overflow-x: hidden;
    word-break: break-all;
}

.oui-popup .txt-center {
    text-align: center;
}

.oui-popup .table table {
    width: 100%;
}

.oui-popup table {
    border-collapse: collapse;
    border-spacing: 0;
}

.oui-popup .table::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 5px;
}

.oui-popup .table::-webkit-scrollbar-track {
    background-color: #e9e9e9;
}

.oui-popup .table::-webkit-scrollbar-button {
    display: none;
}

.oui-popup .table::-webkit-scrollbar {
    width: 14px;
    height: 14px;
}

.oui-popup caption,
.oui-popup hr {
    display: none;
}

.oui-popup .popupDiv.poi .cont .table table tbody th,
.oui-popup .popupDiv.poi .cont .table table tbody td {
    height: 25px;
}

.oui-popup .table table tbody tr th,
.oui-popup .table table tbody tr td {
    font-size: 16px;
    font-family: '맑은 고딕';
}

.oui-popup .table table th {
    background: #f9f9f9;
    font-weight: bold;
}

.oui-popup .table table th,
.oui-popup .table table td {
    height: 30px;
    color: #555;
    border-bottom: 1px solid #e9e9e9;
    padding: 8px;
}

.oui-popup .table table tbody tr td {
    color: #333;
    background: #fff;
}

.oui-popup .section.type02 .titSec strong:before {
    position: absolute;
    left: 0;
    top: 6px;
    width: 3px;
    height: 14px;
    background: #d6d6d6;
    display: block;
    content: '';
}

.oui-popup .comboList li {
    background: #fff;
    font-size: 19px;
    color: #333;
}

.oui-popup .comboList li:hover {
    background: #e4edff;
}

.oui-popup .popup-drag {
    width: 100%;
    height: 100%;
}




/* TOC 관련 CSS*/
/* 노드 */
.toc_tocContentBox .toc_children {
    margin-left: 20px;
}

.toc_tocContentBox .toc_children.toc_depth2 .rstcustom__rowContents {
    background-color: #dce6f2;
    margin-bottom: 10px;
}

.toc_tocContentBox .rstcustom__rowWrapper .toc_children.toc_layerContent.toc_depth2 {
    margin-left: 40px;
}

.toc_tocContentBox .rstcustom__rowWrapper .toc_children.toc_groupContent.toc_depth1 {
    margin-left: 20px;
}

.toc_tocContentBox .toc_children.toc_depth1 .rstcustom__rowContents {
    background-color: #eef6ff;
    margin-bottom: 10px;
}

.toc_tocContentBox .toc_btnUpdateTitle {
    margin-left: 10px;
    height: 23px;
    font-size: 14px;
    padding: 0 10px;
    border-radius: 50px;
    margin-right: 5px;
    background-color: #333;
    color: #fff;
}

.toc_tocContentBox .toc_btnCancelTitle {
    height: 23px;
    font-size: 14px;
    padding: 0 10px;
    border-radius: 50px;
    background: #e1e1e1;
    color: #333;
}

/* .toc_tocContentBox .toc_btnChangeTitle.toc_group {
    background-image: url(../css/images/toc/ico-group-edit.png);
}
.toc_tocContentBox .toc_btnLayerView {
    background-image: url(../css/images/toc/ico-group-view-hide.png);
} */
/* .toc_tocContentBox .btnLayerDelete {
    background-image: url(../css/images/toc/ico-group-remove-hide.png);
} */
.toc_tocContentBox .toc_groupContent .rstcustom__toolbarButton:nth-child(1) {
    margin-right: 4px;
}

.toc_tocContentBox .toc_hidden,
.rstcustom__toolbarButton>button {
    width: 23px;
    height: 23px;
}

.toc_tocContentBox .toc_layerContent .rstcustom__rowTitle .toc_layerText {
    overflow: hidden;
    line-height: 36px;
    width: 261px;
    white-space: normal;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    font-size: 16px;
    font-family: 'Pretendard';
    font-weight: normal;
    height: 30px;
    margin-top: -8px;
}

.toc_tocContentBox .toc_layerContent .rstcustom__rowContents {
    width: 100%;
    align-items: start;
    display: block;
}

.toc_tocContentBox .toc_layerContent .rstcustom__rowToolbar {
    justify-content: flex-end;
    margin-top: -3px;
}

.toc_tocContentBox .toc_groupContent .rstcustom__rowTitle span {
    overflow: hidden;
    line-height: 36px;
    width: 178px;
    white-space: normal;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    font-size: 16px;
    font-family: 'Pretendard';
    font-weight: normal;
    height: 30px;
    margin-top: -8px;
    position: absolute;
    left: 45px;
}

.toc_tocContentBox .toc_groupContent .rstcustom__rowTitle {
    display: block;
    margin-left: 15px;
    padding-left: 16px !important;
    font-size: 16px;
    font-family: 'Pretendard Bold';
    max-width: 185px;
    color: #555;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    line-height: 20px;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all;
}

.rstcustom__rowContents {
    box-shadow: none;
    border: 1px solid #eef1f8;
}

.rstcustom__nodeContent {
    padding-left: 2px !important;
    width: 100%;
}

/* 접기열기 */
.rstcustom__collapseButton,
.rstcustom__expandButton {
    padding-left: 30px;
    display: none;
}

.rstcustom__rowContents {
    padding: 0 10px 0 10px;
}

.rstcustom__rowWrapper div {
    padding-left: 2px !important;
}

.rstcustom__rowTitle {
    padding: 0px 0px 0px 18px;
}

.rstcustom__rowTitle div {
    display: flex;
    height: 25px;
}

.toc_editBox.toc_layer {
    margin-top: -35px;
    margin-right: -158px;
}

.rstcustom__toolbarButton button:hover span,
.toc_btnAllView:hover span,
.toc_btnAllRemove:hover span {
    display: block;
    z-index: 10;
}

.rstcustom__toolbarButton .toc_btnChangeTitle span,
.rstcustom__toolbarButton .toc_btnLayerView span,
.rstcustom__toolbarButton .toc_btnLabelView span,
.rstcustom__toolbarButton .toc_btnLayerGrid span,
.rstcustom__toolbarButton .toc_btnLayerDelete span,
.rstcustom__toolbarButton .toc_btnLayerDetail span,
.rstcustom__toolbarButton .toc_attributePopup span,
.rstcustom__toolbarButton .toc_btnWebLayerUpdate span,
.toc_btnAllView span,
.toc_btnAllRemove span,
.rstcustom__toolbarButton .toc_legend span {
    position: fixed;
    line-height: 27px;
    padding: 0 5px;
    display: none;
    background: #fff;
    border-radius: 5px;
    color: #555;
    border: 1px solid #eef1f8;
    font-size: 14px;
    margin-top: 18px;
    font-weight: normal;
    text-indent: initial;
}

.toc_btnAllView span {
    margin-left: -18px;
}

.toc_btnAllRemove span {
    margin-left: -21px;
}

.rstcustom__toolbarButton .toc_btnChangeTitle span,
.rstcustom__toolbarButton .toc_webLayerUpdatePop span {
    margin-left: -15px;
}

.toc_groupContent .rstcustom__toolbarButton .toc_btnChangeTitle span {
    margin-left: -19px;
}

.rstcustom__toolbarButton .toc_btnLayerView span {
    margin-left: -17px;
}

.rstcustom__toolbarButton .toc_legend span {
    margin-left: -15px;
}

.rstcustom__toolbarButton .toc_btnLabelView span {
    margin-left: -11px;
}

.rstcustom__toolbarButton .toc_btnLayerGrid span {
    margin-left: -24px;
}

.rstcustom__toolbarButton .toc_btnLayerDelete span {
    margin-left: -24px;
}

.toc_groupContent .rstcustom__toolbarButton .toc_btnLayerDelete span {
    margin-left: -19px;
}

.rstcustom__toolbarButton .toc_btnLayerDetail span {
    margin-left: -31px;
}

.rstcustom__highlight {
    background-color: transparent;
    /* background: rgba(255, 255, 255, 0); */
}

.rstcustom__rowWrapper:hover {
    opacity: 1
}

.toc_tocContentBox {
    padding: 8px;
    /* margin-top: -16px; */
}

.toc_tocContentList {
    margin-top: 10px;
}

.toc_tocContentBox .rstcustom__rowWrapper {
    padding: 0 2px 0 0;
}

.rstcustom__node {
    margin-bottom: 5px;
}

.toc_tocContentBox .toc_btnLabelView.off {
    background-image: url(./images/widget/toc/ico-bookmark.png);
}

.toc_tocContentBox .toc_btnLabelView.off {
    background-image: url(./images/widget/toc/ico-bookmark.png);
}

.toc_tocContentBox .toc_btnLayerGrid:hover,
.toc_tocContentBox .toc_btnLayerGrid.on {
    background-image: url(./images/widget/toc/ico-option-hover.png);
    background-position: center;
}

.toc_tocContentBox .toc_groupContent .toc_btnLayerDelete:hover,
.toc_tocContentBox .toc_layerContent .toc_btnLayerDelete.on {
    background-image: url(./images/widget/toc/ico-group-remove-show.png);
    background-position: center;
}

.toc_tocContentBox .toc_btnLayerDetail:hover,
.toc_tocContentBox .toc_btnLayerDetail.active {
    background-image: url(./images/widget/toc/ico-layer-more-hover.png);
    background-position: center;
}

.toc_tocContentBox .toc_layerContent .toc_btnLayerDelete:hover,
.toc_tocContentBox .toc_layerContent .toc_btnLayerDelete.on {
    background-image: url(./images/widget/toc/ico-remove-hover.png);
    background-position: center;
}

.toc_tocContentBox .toc_layerContent .toc_btnLayerView.on {
    background-image: url(./images/widget/toc/ico-view-hover.png);
    background-position: center;
}

.toc_tocContentBox .toc_groupContent .toc_btnLayerView.on {
    background-image: url(./images/widget/toc/ico-group-view-show.png);
    background-position: center;
}

.toc_tocContentBox .toc_btnAllView.active {
    background-image: url(./images/widget/toc/ico-toc-all-view-show.png);
}

.toc_tocContentBox .toc_layerContent>.rstcustom__rowContents>.rstcustom__rowToolbar>.rstcustom__toolbarButton>button {
    width: 30px;
    height: 30px;
    background-position: center;

    background-repeat: no-repeat;
}

i.ico-multi-polygon {
    background: url(./images/widget/toc/ico-layer-plane.png) no-repeat center;
    display: block;
}

i.ico-multi-line {
    background: url(./images/widget/toc/ico-layer-line.png) no-repeat center;
    display: block;
}

i.ico-multi-point {
    background: url(./images/widget/toc/ico-layer-dot.png) no-repeat center;
    display: block;
}

i.ico-hitmap {
    display: block;
    background: url(./images/widget/ico/ico-layer-hitmap.png) no-repeat center;
}

i.ico-geoTiff {
    display: block;
    background: url(./images/widget/ico/ico-layer-g.png) no-repeat center;
}

i.ico-layerGroup {
    display: block;
    background: url(./images/widget/ico/ico-layer-web2.png) no-repeat center;
}

i.ico-webLayer {
    display: block;
    background: url(./images/widget/ico/ico-layer-web.png) no-repeat center;
}

i.ico-layerType-point {
    display: block;
    background: url(./images/widget/ico/ico-layer-dot.png) no-repeat center;
}

i.ico-layerType-line {
    display: block;
    background: url(./images/widget/ico/ico-layer-line.png) no-repeat center;
}

i.ico-layerType-polygon {
    display: block;
    background: url(./images/widget/ico/ico-layer-plane.png) no-repeat center;
}

.errorTxt {
    font-size: 13px;
    color: red;
}

.divideMap_tocArea .toc_layerContent>.rstcustom__rowContents>.rstcustom__rowToolbar>.rstcustom__toolbarButton>button {
    width: 23px;
    height: 23px;
    background-position: center;
    background-repeat: no-repeat;
    background-color: #fff;
    border: 1px solid #E0E0E0;
    text-align: center;
    margin-top: 7px;
}