var javaCodeGenerator = {

		getTemplate : '\n\
			/* Java 샘플 코드 */\n\
\n\
			import java.io.InputStreamReader;\n\
			import java.net.HttpURLConnection;\n\
			import java.net.URL;\n\
			import java.net.URLEncoder;\n\
			import java.util.LinkedHashMap;\n\
			import java.util.Map;\n\
			import java.util.List;\n\
			import java.io.BufferedReader;\n\
			import java.io.File;\n\
			import java.io.FileWriter;\n\
			import java.io.IOException;\n\
\n\
			public class ApiExplorer {\n\
			    public static void main(String[] args) throws IOException {\n\
			        StringBuilder urlBuilder = new StringBuilder("::URL::");	 /*URL*/\n\
\n\
			        Map<String, Object> paramMap = new LinkedHashMap<>();\n\
::PARAMS::\n\
\n\
			        StringBuilder paramBuilder = new StringBuilder();\n\
			        for(Map.Entry<String, Object> param : paramMap.entrySet()) {\n\
			        	if(paramBuilder.length() != 0) {\n\
			        		paramBuilder.append("&");\n\
			        	} else {\n\
			        		paramBuilder.append("?");\n\
			        	}\n\
\n\
			        	paramBuilder.append(URLEncoder.encode(param.getKey(), "UTF-8"));\n\
			        	paramBuilder.append("=");\n\
			        	paramBuilder.append(URLEncoder.encode(String.valueOf(param.getValue()), "UTF-8"));\n\
			        }\n\
\n\
			        URL url = new URL(urlBuilder.toString() + paramBuilder.toString());\n\
			        HttpURLConnection conn = (HttpURLConnection) url.openConnection();\n\
			        conn.setRequestMethod("GET");\n\
			        conn.setRequestProperty("Content-type", "application/json");\n\
\n\
			        // Response Code\n\
			        int responseCode = conn.getResponseCode();\n\
			        System.out.println("Response code: " + responseCode);\n\
\n\
			     	// Response Header\n\
			        Map<String, List<String>> map = conn.getHeaderFields();\n\
			        System.out.println("Response Header...");\n\
			        for(Map.Entry<String, List<String>> entry : map.entrySet()) {\n\
			        	System.out.println("Key : " + entry.getKey() + ", Value : " + entry.getValue());\n\
			        }\n\
\n\
					// Response Body\n\
					BufferedReader rd;\n\
			        if(responseCode >= 200 && responseCode <= 300) {\n\
			            rd = new BufferedReader(new InputStreamReader(conn.getInputStream()));\n\
			        } else {\n\
			            rd = new BufferedReader(new InputStreamReader(conn.getErrorStream()));\n\
			        }\n\
			        StringBuilder sb = new StringBuilder();\n\
			        String line;\n\
			        while ((line = rd.readLine()) != null) {\n\
			            sb.append(line);\n\
			        }\n\
			        rd.close();\n\
			        conn.disconnect();\n\
					System.out.println("Response Body...");\n\
			        System.out.println(sb.toString());\n\
\n\
					// 결과 파일이 있는 경우 다운로드\n\
					if(map.containsKey("content-disposition")) {\n\
			        	String value = map.get("content-disposition").toString();\n\
			        	String fileName = value.replaceFirst("(?i)^.*filename=\\"?([^\\"]+)\\"?.*$", "$1");\n\
			        	String path = "c:\\\\temp\\\\";\n\
			        	String filePath = path + fileName;\n\
			        	File folder = new File(path);\n\
			        	if(!folder.exists()) {\n\
			        		folder.mkdir();\n\
			        	}\n\
\n\
			        	FileWriter fw = new FileWriter(filePath);\n\
			        	fw.write(sb.toString());\n\
			        	fw.close();\n\
			        }\n\
			    }\n\
			}',

			postTemplate : '\n\
			/* Java 샘플 코드 */\n\
				\n\
			import java.io.InputStreamReader;\n\
			import java.net.HttpURLConnection;\n\
			import java.net.URL;\n\
			import java.net.URLEncoder;\n\
			import java.util.LinkedHashMap;\n\
			import java.util.Map;\n\
			import java.util.List;\n\
			import java.io.BufferedReader;\n\
			import java.io.File;\n\
			import java.io.FileWriter;\n\
			import java.io.IOException;\n\
				\n\
			public class ApiExplorer {\n\
			    public static void main(String[] args) throws IOException {\n\
			        StringBuilder urlBuilder = new StringBuilder("::URL::");	 /*URL*/\n\
				\n\
			        Map<String, Object> paramMap = new LinkedHashMap<>();\n\
::PARAMS::\n\
				\n\
			        StringBuilder paramBuilder = new StringBuilder();\n\
			        for(Map.Entry<String, Object> param : paramMap.entrySet()) {\n\
			        	if(paramBuilder.length() != 0) {\n\
			        		paramBuilder.append("&");\n\
			        	}\n\
			        	\n\
			        	paramBuilder.append(URLEncoder.encode(param.getKey(), "UTF-8"));\n\
			        	paramBuilder.append("=");\n\
			        	paramBuilder.append(URLEncoder.encode(String.valueOf(param.getValue()), "UTF-8"));\n\
			        }\n\
			        byte[] postDataBytes = paramBuilder.toString().getBytes("UTF-8");\n\
				\n\
			        URL url = new URL(urlBuilder.toString());\n\
			        HttpURLConnection conn = (HttpURLConnection) url.openConnection();\n\
			        conn.setRequestMethod("POST");\n\
			        conn.setRequestProperty("Content-type", "application/x-www-form-urlencoded");\n\
			        conn.setRequestProperty("Content-Length", String.valueOf(postDataBytes.length));\n\
			        conn.setDoOutput(true);\n\
			        conn.getOutputStream().write(postDataBytes);\n\
\n\
			        // Response Code\n\
			        int responseCode = conn.getResponseCode();\n\
			        System.out.println("Response code: " + responseCode);\n\
\n\
			     	// Response Header\n\
			        Map<String, List<String>> map = conn.getHeaderFields();\n\
			        System.out.println("Response Header...");\n\
			        for(Map.Entry<String, List<String>> entry : map.entrySet()) {\n\
			        	System.out.println("Key : " + entry.getKey() + ", Value : " + entry.getValue());\n\
			        }\n\
\n\
					// Response Body\n\
			        BufferedReader rd;\n\
			        if(responseCode >= 200 && responseCode <= 300) {\n\
			            rd = new BufferedReader(new InputStreamReader(conn.getInputStream()));\n\
			        } else {\n\
			            rd = new BufferedReader(new InputStreamReader(conn.getErrorStream()));\n\
			        }\n\
			        StringBuilder sb = new StringBuilder();\n\
			        String line;\n\
			        while ((line = rd.readLine()) != null) {\n\
			            sb.append(line);\n\
			        }\n\
			        rd.close();\n\
			        conn.disconnect();\n\
					System.out.println("Response Body...");\n\
			        System.out.println(sb.toString());\n\
\n\
					// 결과 파일이 있는 경우 다운로드\n\
					if(map.containsKey("content-disposition")) {\n\
			        	String value = map.get("content-disposition").toString();\n\
			        	String fileName = value.replaceFirst("(?i)^.*filename=\\"?([^\\"]+)\\"?.*$", "$1");\n\
			        	String path = "c:\\\\temp\\\\";\n\
			        	String filePath = path + fileName;\n\
			        	File folder = new File(path);\n\
			        	if(!folder.exists()) {\n\
			        		folder.mkdir();\n\
			        	}\n\
\n\
			        	FileWriter fw = new FileWriter(filePath);\n\
			        	fw.write(sb.toString());\n\
			        	fw.close();\n\
				    }\n\
				}\n\
			}',

			putTemplate : '\n\
			/* Java 샘플 코드 */\n\
				\n\
			import java.io.InputStreamReader;\n\
			import java.net.HttpURLConnection;\n\
			import java.net.URL;\n\
			import java.net.URLEncoder;\n\
			import java.util.LinkedHashMap;\n\
			import java.util.Map;\n\
			import java.util.List;\n\
			import java.io.BufferedReader;\n\
			import java.io.File;\n\
			import java.io.FileWriter;\n\
			import java.io.IOException;\n\
				\n\
			public class ApiExplorer {\n\
			    public static void main(String[] args) throws IOException {\n\
			        StringBuilder urlBuilder = new StringBuilder("::URL::");	 /*URL*/\n\
				\n\
			        Map<String, Object> paramMap = new LinkedHashMap<>();\n\
::PARAMS::\n\
				\n\
			        StringBuilder paramBuilder = new StringBuilder();\n\
			        for(Map.Entry<String, Object> param : paramMap.entrySet()) {\n\
			        	if(paramBuilder.length() != 0) {\n\
			        		paramBuilder.append("&");\n\
			        	}\n\
				\n\
			        	paramBuilder.append(URLEncoder.encode(param.getKey(), "UTF-8"));\n\
			        	paramBuilder.append("=");\n\
			        	paramBuilder.append(URLEncoder.encode(String.valueOf(param.getValue()), "UTF-8"));\n\
			        }\n\
			        byte[] postDataBytes = paramBuilder.toString().getBytes("UTF-8");\n\
				\n\
			        URL url = new URL(urlBuilder.toString());\n\
			        HttpURLConnection conn = (HttpURLConnection) url.openConnection();\n\
			        conn.setRequestMethod("PUT");\n\
			        conn.setRequestProperty("Content-type", "application/x-www-form-urlencoded");\n\
			        conn.setRequestProperty("Content-Length", String.valueOf(postDataBytes.length));\n\
			        conn.setDoOutput(true);\n\
			        conn.getOutputStream().write(postDataBytes);\n\
\n\
			        // Response Code\n\
			        int responseCode = conn.getResponseCode();\n\
			        System.out.println("Response code: " + responseCode);\n\
\n\
			     	// Response Header\n\
			        Map<String, List<String>> map = conn.getHeaderFields();\n\
			        System.out.println("Response Header...");\n\
			        for(Map.Entry<String, List<String>> entry : map.entrySet()) {\n\
			        	System.out.println("Key : " + entry.getKey() + ", Value : " + entry.getValue());\n\
			        }\n\
\n\
					// Response Body\n\
			        BufferedReader rd;\n\
			        if(responseCode >= 200 && responseCode <= 300) {\n\
			            rd = new BufferedReader(new InputStreamReader(conn.getInputStream()));\n\
			        } else {\n\
			            rd = new BufferedReader(new InputStreamReader(conn.getErrorStream()));\n\
			        }\n\
			        StringBuilder sb = new StringBuilder();\n\
			        String line;\n\
			        while ((line = rd.readLine()) != null) {\n\
			            sb.append(line);\n\
			        }\n\
			        rd.close();\n\
			        conn.disconnect();\n\
					System.out.println("Response Body...");\n\
			        System.out.println(sb.toString());\n\
\n\
					// 결과 파일이 있는 경우 다운로드\n\
					if(map.containsKey("content-disposition")) {\n\
			        	String value = map.get("content-disposition").toString();\n\
			        	String fileName = value.replaceFirst("(?i)^.*filename=\\"?([^\\"]+)\\"?.*$", "$1");\n\
			        	String path = "c:\\\\temp\\\\";\n\
			        	String filePath = path + fileName;\n\
			        	File folder = new File(path);\n\
			        	if(!folder.exists()) {\n\
			        		folder.mkdir();\n\
			        	}\n\
\n\
			        	FileWriter fw = new FileWriter(filePath);\n\
			        	fw.write(sb.toString());\n\
			        	fw.close();\n\
				    }\n\
				}\n\
			}',

			deleteTemplate : '\n\
			/* Java 샘플 코드 */\n\
				\n\
			import java.io.InputStreamReader;\n\
			import java.net.HttpURLConnection;\n\
			import java.net.URL;\n\
			import java.net.URLEncoder;\n\
			import java.util.LinkedHashMap;\n\
			import java.util.Map;\n\
			import java.util.List;\n\
			import java.io.BufferedReader;\n\
			import java.io.File;\n\
			import java.io.FileWriter;\n\
			import java.io.IOException;\n\
				\n\
			public class ApiExplorer {\n\
			    public static void main(String[] args) throws IOException {\n\
			        StringBuilder urlBuilder = new StringBuilder("::URL::");	 /*URL*/\n\
				\n\
			        Map<String, Object> paramMap = new LinkedHashMap<>();\n\
::PARAMS::\n\
				\n\
			        StringBuilder paramBuilder = new StringBuilder();\n\
			        for(Map.Entry<String, Object> param : paramMap.entrySet()) {\n\
			        	if(paramBuilder.length() != 0) {\n\
			        		paramBuilder.append("&");\n\
			        	} else {\n\
			        		paramBuilder.append("?");\n\
			        	}\n\
				\n\
			        	paramBuilder.append(URLEncoder.encode(param.getKey(), "UTF-8"));\n\
			        	paramBuilder.append("=");\n\
			        	paramBuilder.append(URLEncoder.encode(String.valueOf(param.getValue()), "UTF-8"));\n\
			        }\n\
				\n\
			        URL url = new URL(urlBuilder.toString() + paramBuilder.toString());\n\
			        HttpURLConnection conn = (HttpURLConnection) url.openConnection();\n\
			        conn.setRequestMethod("DELETE");\n\
			        conn.setRequestProperty("Content-type", "application/json");\n\
\n\
			        // Response Code\n\
			        int responseCode = conn.getResponseCode();\n\
			        System.out.println("Response code: " + responseCode);\n\
\n\
			     	// Response Header\n\
			        Map<String, List<String>> map = conn.getHeaderFields();\n\
			        System.out.println("Response Header...");\n\
			        for(Map.Entry<String, List<String>> entry : map.entrySet()) {\n\
			        	System.out.println("Key : " + entry.getKey() + ", Value : " + entry.getValue());\n\
			        }\n\
\n\
					// Response Body\n\
			        BufferedReader rd;\n\
			        if(responseCode >= 200 && responseCode <= 300) {\n\
			            rd = new BufferedReader(new InputStreamReader(conn.getInputStream()));\n\
			        } else {\n\
			            rd = new BufferedReader(new InputStreamReader(conn.getErrorStream()));\n\
			        }\n\
			        StringBuilder sb = new StringBuilder();\n\
			        String line;\n\
			        while ((line = rd.readLine()) != null) {\n\
			            sb.append(line);\n\
			        }\n\
			        rd.close();\n\
			        conn.disconnect();\n\
					System.out.println("Response Body...");\n\
			        System.out.println(sb.toString());\n\
				}\n\
			}',

		paramTemplate: '			        ::COMMENT::paramMap.put("::KEY::", "::VALUE::");		/*::DESCRIPTION::*/\n',

		makeCode : function(apiUrl, apiKey, apiSpec) {
			//
			var url 			= javaCodeGenerator.makeUrl(apiUrl, apiSpec);
			var paramCode	= javaCodeGenerator.makeParam(apiKey, apiSpec);
			var template		= javaCodeGenerator.setTemplate(apiKey);
			var code			= template.replace("::URL::", url).replace("::PARAMS::", paramCode);

			return code;
		},

		makeUrl: function(apiUrl, apiSpec) {

			var url = apiUrl;

			if(apiSpec.parameters != null) {

				$.each(apiSpec.parameters, function(index, param) {

					if(param.name != null && param.in == "path") {

						url = url.replace("{" + param.name + "}", util.nullCheck(param.default));
					}
				});
			}

			return url;
		},

		makeParam: function (apiKey, apiSpec) {

			var paramCode = "";
			if(apiSpec.parameters != null) {

				$.each(apiSpec.parameters, function(index, param) {

					if(param.name != null && param.in == "query") {

						var description = param.description ? param.description.replace(/\r\n/gi, ' - ') : '';
						var value = util.nullCheck(param.example) != "" ? util.nullCheck(param.example) : util.nullCheck(param.default);
						value = value.toString().replace(/\"/gi, "\\\"").replace(/\r\n/gi, " ");
						var comment = util.nullCheck(value) == "" ? "//" : "";

						paramCode += javaCodeGenerator.paramTemplate
											.replace("::COMMENT::", comment)
											.replace("::KEY::", param.name)
											.replace("::VALUE::", value)
											.replace("::DESCRIPTION::", util.nullCheck(description) );
					} else if(param.name != null && param.in == "body") {

						if(param.schema != undefined) {

							var value = jsonRefParser.refParse(param.schema);
							value = JSON.stringify(value);
							value = value.toString().replace(/\"/gi, "'").replace(/\r\n/gi, " ");
							var description = param.description ? param.description.replace(/\r\n/gi, ' - ') : '';
							var comment = util.nullCheck(value) == "" ? "//" : "";

							paramCode += javaCodeGenerator.paramTemplate
											.replace("::COMMENT::", comment)
											.replace("::KEY::", param.name)
											.replace("::VALUE::", value)
											.replace("::DESCRIPTION::", util.nullCheck(description) );
						}
					}
				});
			}

			return paramCode;
		},

		setTemplate: function(apiKey) {

			var template = javaCodeGenerator.getTemplate;
			switch(apiKey) {
				case "get" : template = javaCodeGenerator.getTemplate; break;
				case "post" : template = javaCodeGenerator.postTemplate; break;
				case "put" : template =  javaCodeGenerator.putTemplate; break;
				case "delete" : template =  javaCodeGenerator.deleteTemplate; break;
			}

			return template.replace(/\</gi, "&lt;").replace(/\>/gi, "&gt;");
		}

}
