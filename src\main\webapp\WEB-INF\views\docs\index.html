<th:block data-layout-decorate="~{_layout/default.html}">

    <th:block layout:fragment="css">
    	<link rel="stylesheet" type="text/css" th:href="@{/vendor/swaggerui/swagger-ui.css}" >
    	<link rel="stylesheet" type="text/css" th:href="@{/css/swagger-over.css}" >
    	<link rel="stylesheet" th:href="@{/vendor/prism/prism.css}">
    </th:block>

	<th:block layout:fragment="content">
		<h2 class="hidden">본문 영역</h2>

        <div class="sideMenu">
            <!-- 검색창 -->
            <div class="innerBox">
                <input id="txt-search" type="text" placeholder="| 검색" />
                <button type="button" class="btnSearchRemove"><span class="hidden">지우기</span></button>
            </div>

            <!-- 서브메뉴 -->
            <div class="listArea mScroll">
                <div class="tab tab1" id="submenu">
                </div>
            </div>
        </div>

        <div id="content" class="mScroll">
			<div class="btnGroup">
                <button type="button" class="btnTop"><span class="hidden">맨 위로</span></button>
                <button type="button" class="btnBottom"><span class="hidden">맨 아래로</span></button>
            </div>
			<div id="api-doc" class="innerContent">
				<div id="swagger-ui"></div>
			</div>
		</div>
	</th:block>

	<!-- index.html 고유 스크립트 추가 -->
    <th:block layout:fragment="script-bottom">
    	<script th:inline="javascript">
			var contextPath = /*[[${@egovProperties.getProperty('Url.ContextPath')}]]*/ '';

    		var gwPaths = [

    			/*<![CDATA[*/

    			/*[# th:each="api : ${apiList}"]*/
    			{ path :  "/" + [[${api}]]},
    			/*[/]*/

    			/*]]>*/

    			];

    		var menuOrder = [
    			/*<![CDATA[*/
    			/*[# th:each="apiOrder : ${apiOrderList}"]*/
    			"[(${apiOrder})]",
    			/*[/]*/
    			/*]]>*/
    			];

    	</script>
	    <script th:src="@{/vendor/swaggerui/swagger-ui-bundle.js}"></script>
		<script th:src="@{/vendor/swaggerui/swagger-ui-standalone-preset.js}"></script>
		<script th:src="@{/vendor/prism/prism.js}"></script>
		<script th:src="@{/js/docs/javaCodeGenerator.js(ms=${ms})}"></script>
		<script th:src="@{/js/docs/jsCodeGenerator.js(ms=${ms})}"></script>
		<script th:src="@{/js/docs/jsonRefParser.js(ms=${ms})}"></script>
		<script th:src="@{/js/docs/index.js(ms=${ms})}"></script>
    </th:block>

</th:block>
